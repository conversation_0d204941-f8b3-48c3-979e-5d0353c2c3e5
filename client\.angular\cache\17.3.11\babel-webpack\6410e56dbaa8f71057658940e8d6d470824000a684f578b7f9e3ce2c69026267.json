{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@angular/router\";\nexport class SessionComponent {\n  constructor(primengConfig, renderer) {\n    this.primengConfig = primengConfig;\n    this.renderer = renderer;\n  }\n  ngOnInit() {\n    // Inject theme \n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\n    const link = this.renderer.createElement('link');\n    this.renderer.setAttribute(link, 'id', 'theme-link');\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\n    this.renderer.setAttribute(link, 'type', 'text/css');\n    this.renderer.setAttribute(link, 'href', href);\n    // Append the link tag to the head of the document\n    this.renderer.appendChild(document.head, link);\n    this.primengConfig.ripple = true; //enables core ripple functionality\n  }\n  ngOnDestroy() {\n    // Find and remove the link tag when the component is destroyed\n    const link = document.getElementById('theme-link');\n    if (link) {\n      link.remove();\n    }\n  }\n  static {\n    this.ɵfac = function SessionComponent_Factory(t) {\n      return new (t || SessionComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SessionComponent,\n      selectors: [[\"ng-component\"]],\n      decls: 1,\n      vars: 0,\n      template: function SessionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i2.RouterOutlet],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["SessionComponent", "constructor", "primengConfig", "renderer", "ngOnInit", "href", "link", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "document", "head", "ripple", "ngOnDestroy", "getElementById", "remove", "i0", "ɵɵdirectiveInject", "i1", "PrimeNGConfig", "Renderer2", "selectors", "decls", "vars", "template", "SessionComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\session\\session.component.ts"], "sourcesContent": ["import { Component, Renderer2 } from '@angular/core';\r\nimport { PrimeNGConfig } from 'primeng/api';\r\n\r\n@Component({\r\n  template: `<router-outlet></router-outlet>`,\r\n})\r\nexport class SessionComponent {\r\n  constructor(\r\n    private primengConfig: PrimeNGConfig,\r\n    private renderer: Renderer2\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    // Inject theme \r\n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\r\n    const link = this.renderer.createElement('link');\r\n    this.renderer.setAttribute(link, 'id', 'theme-link');\r\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\r\n    this.renderer.setAttribute(link, 'type', 'text/css');\r\n    this.renderer.setAttribute(link, 'href', href);\r\n\r\n    // Append the link tag to the head of the document\r\n    this.renderer.appendChild(document.head, link);\r\n\r\n    this.primengConfig.ripple = true; //enables core ripple functionality\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Find and remove the link tag when the component is destroyed\r\n    const link = document.getElementById('theme-link');\r\n    if (link) {\r\n      link.remove();\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;AAMA,OAAM,MAAOA,gBAAgB;EAC3BC,YACUC,aAA4B,EAC5BC,QAAmB;IADnB,KAAAD,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;EACf;EAEHC,QAAQA,CAAA;IACN;IACA,MAAMC,IAAI,GAAG,wDAAwD;IACrE,MAAMC,IAAI,GAAG,IAAI,CAACH,QAAQ,CAACI,aAAa,CAAC,MAAM,CAAC;IAChD,IAAI,CAACJ,QAAQ,CAACK,YAAY,CAACF,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC;IACpD,IAAI,CAACH,QAAQ,CAACK,YAAY,CAACF,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC;IACrD,IAAI,CAACH,QAAQ,CAACK,YAAY,CAACF,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC;IACpD,IAAI,CAACH,QAAQ,CAACK,YAAY,CAACF,IAAI,EAAE,MAAM,EAAED,IAAI,CAAC;IAE9C;IACA,IAAI,CAACF,QAAQ,CAACM,WAAW,CAACC,QAAQ,CAACC,IAAI,EAAEL,IAAI,CAAC;IAE9C,IAAI,CAACJ,aAAa,CAACU,MAAM,GAAG,IAAI,CAAC,CAAC;EACpC;EAEAC,WAAWA,CAAA;IACT;IACA,MAAMP,IAAI,GAAGI,QAAQ,CAACI,cAAc,CAAC,YAAY,CAAC;IAClD,IAAIR,IAAI,EAAE;MACRA,IAAI,CAACS,MAAM,EAAE;IACf;EACF;;;uBA3BWf,gBAAgB,EAAAgB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,SAAA;IAAA;EAAA;;;YAAhBpB,gBAAgB;MAAAqB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAFhBV,EAAA,CAAAY,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}