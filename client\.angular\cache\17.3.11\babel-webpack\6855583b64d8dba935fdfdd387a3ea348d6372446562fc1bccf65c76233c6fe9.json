{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../core/services/content-vendor.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/inputtext\";\nfunction HomeComponent_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 129)(1, \"a\", 130);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 131);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const menuItem_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", menuItem_r1.Link || \"#\", i0.ɵɵsanitizeUrl)(\"target\", menuItem_r1.Target || \"_self\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", menuItem_r1.Title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(menuItem_r1.Sub_Title);\n  }\n}\nexport class HomeComponent {\n  constructor(primengConfig, renderer, route, CMSservice) {\n    this.primengConfig = primengConfig;\n    this.renderer = renderer;\n    this.route = route;\n    this.CMSservice = CMSservice;\n    this.logo = '';\n    this.menuItems = [];\n    this.bannerData = null;\n    this.servicesData = [];\n  }\n  ngOnInit() {\n    this.commonContent = this.route.snapshot.data['commonContent'];\n    console.log('Common Content:', this.commonContent);\n    // Extract logo\n    const logoComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.logo\");\n    if (logoComponent?.length) {\n      this.logo = logoComponent[0].Logo?.url || '';\n    }\n    // Extract menu\n    const menuComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.menu\");\n    if (menuComponent?.length) {\n      this.menuItems = menuComponent[0].Menu_Item || [];\n    }\n    this.content = this.route.snapshot.data['content'];\n    console.log('Home Content:', this.content);\n    // Extract banner\n    const bannerComponent = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.banner\");\n    if (bannerComponent?.length) {\n      this.bannerData = bannerComponent[0];\n    }\n    // Extract services\n    const servicesComponents = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.services\");\n    if (servicesComponents?.length) {\n      this.servicesData = servicesComponents;\n    }\n    console.log('Logo:', this.logo);\n    console.log('Menu Items:', this.menuItems);\n    console.log('Banner Data:', this.bannerData);\n    console.log('Services Data:', this.servicesData);\n    // Inject theme \n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\n    const link = this.renderer.createElement('link');\n    this.renderer.setAttribute(link, 'id', 'theme-link');\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\n    this.renderer.setAttribute(link, 'type', 'text/css');\n    this.renderer.setAttribute(link, 'href', href);\n    // Append the link tag to the head of the document\n    this.renderer.appendChild(document.head, link);\n    this.primengConfig.ripple = true; //enables core ripple functionality\n  }\n  // Helper method to clean up HTML description for safe rendering\n  getCleanServiceDescription(htmlDescription) {\n    if (!htmlDescription) {\n      return `<ul class=\"m-0 mb-5 p-0 flex flex-1 flex-column gap-2 list-none\">\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\n                    Renew driver's license/ID card</li>\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\n                    Replace driver's license/ID card</li>\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\n                    Get a REAL ID</li>\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\n                    First-time driver's license/ID card</li>\n              </ul>`;\n    }\n    // Clean up the HTML by removing inline styles and fixing classes\n    let cleanHtml = htmlDescription.replace(/style=\"[^\"]*\"/g, '') // Remove inline styles\n    .replace(/class=\"[^\"]*\"/g, 'class=\"text-white flex align-items-center gap-2\"') // Replace with our classes\n    .replace(/<p[^>]*>/g, '') // Remove p tags\n    .replace(/<\\/p>/g, '') // Remove closing p tags\n    .replace(/<br\\s*\\/?>/g, '') // Remove br tags\n    .replace(/<span[^>]*>/g, '') // Remove span tags\n    .replace(/<\\/span>/g, ''); // Remove closing span tags\n    // Add arrow icons to list items\n    cleanHtml = cleanHtml.replace(/<li class=\"text-white flex align-items-center gap-2\">/g, '<li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>');\n    return cleanHtml;\n  }\n  // Helper method to get default service icons\n  getDefaultServiceIcon(index) {\n    const defaultIcons = ['/assets/layout/images/id-services.png', '/assets/layout/images/car-services.png', '/assets/layout/images/business-services.png', '/assets/layout/images/senior-services.png'];\n    return defaultIcons[index] || '/assets/layout/images/id-services.png';\n  }\n  ngOnDestroy() {\n    // Find and remove the link tag when the component is destroyed\n    const link = document.getElementById('theme-link');\n    if (link) {\n      link.remove();\n    }\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ContentService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 337,\n      vars: 15,\n      consts: [[1, \"main-header\", \"fixed\", \"top-0\", \"w-full\", \"bg-white\", \"z-5\"], [1, \"header-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"align-items-center\"], [1, \"header-logo\", \"relative\", \"pr-6\", \"flex\", \"align-items-center\", \"w-18rem\", \"h-8rem\", \"secondary-bg-color\"], [\"alt\", \"Logo\", 1, \"w-full\", \"h-fit\", 3, \"src\"], [1, \"header-menu-sec\", \"pl-5\", \"relative\", \"flex\", \"align-items-center\", \"justify-content-between\", \"flex-1\"], [1, \"menu-list\"], [1, \"p-0\", \"m-0\", \"flex\", \"align-items-center\", \"gap-5\"], [\"class\", \"flex\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-8rem\", \"h-3rem\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-bluegray-100\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"material-symbols-rounded\", \"text-xl\"], [1, \"banner-sec\", \"relative\"], [1, \"banner-body\", \"h-full\", \"flex\", \"align-items-center\"], [1, \"banner-box\", \"relative\", \"flex-1\", \"p-8\"], [1, \"banner-box\", \"relative\", \"flex-1\", \"bg-white\", \"p-8\", \"flex\", \"flex-column\", \"justify-content-center\"], [1, \"mb-4\", \"text-yellow-400\"], [1, \"m-0\", \"mb-5\", \"text-8xl\", \"line-height-1\", \"font-extrabold\", \"text-color\"], [1, \"m-0\", \"text-lg\", \"font-medium\"], [1, \"services-sec\", \"w-full\"], [1, \"services-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\"], [1, \"services-box\", \"s-box-1\", \"p-5\", \"flex-1\", \"flex\", \"flex-column\", \"gap-2\"], [1, \"h-fit\", \"w-5rem\", 3, \"src\", \"alt\"], [1, \"font-bold\", \"text-white\"], [3, \"innerHTML\"], [1, \"about-sec\", \"relative\"], [1, \"about-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"flex-wrap\"], [1, \"about-box\", \"pr-8\", \"w-6\", \"secondary-bg-color\"], [\"src\", \"/assets/layout/images/director-img.jpg\", \"alt\", \"\", 1, \"w-full\"], [1, \"about-box\", \"p-8\", \"pr-0\", \"w-6\", \"secondary-bg-color\", \"flex\", \"flex-column\", \"justify-content-start\"], [1, \"line\", \"m-0\", \"mb-6\", \"relative\", \"pb-5\", \"text-6xl\", \"line-height-2\", \"font-bold\", \"text-white\"], [1, \"m-0\", \"text-lg\", \"text-white\", \"flex-1\"], [\"type\", \"button\", 1, \"px-5\", \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"h-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-white\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"material-symbols-rounded\"], [1, \"city-members-sec\", \"relative\"], [1, \"city-members-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"flex-wrap\"], [1, \"city-members-left\", \"pt-8\", \"pr-7\", \"pb-8\", \"pl-0\", \"relative\", \"flex\", \"bg-orange-50\"], [1, \"cm-time-box\"], [\"src\", \"/assets/layout/images/time-screen.png\", \"alt\", \"\", 1, \"w-full\"], [1, \"cm-info\"], [1, \"line\", \"m-0\", \"mb-6\", \"relative\", \"pb-5\", \"text-4xl\", \"line-height-2\", \"font-bold\", \"text-color\"], [1, \"m-0\", \"text-lg\", \"text-color\", \"flex-1\"], [1, \"city-members-right\", \"p-7\", \"pr-0\", \"relative\", \"flex\", \"flex-column\", \"gap-8\"], [1, \"cm-right-box\"], [1, \"flex\", \"align-items-start\", \"text-8xl\", \"font-extrabold\", \"line-height-1\"], [1, \"relative\", \"text-4xl\", \"font-bold\", \"inline-block\"], [1, \"uppercase\"], [1, \"text-color-secondary\"], [1, \"quick-access-sec\", \"relative\"], [1, \"quick-access-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"flex-wrap\"], [1, \"quick-access-left\", \"relative\", \"flex\", \"flex-column\", \"pt-8\", \"pb-8\"], [1, \"mb-4\", \"flex\", \"align-items-start\", \"text-6xl\", \"font-extrabold\", \"line-height-1\"], [1, \"line-2\", \"inline-flex\", \"w-fit\", \"m-0\", \"mb-6\", \"relative\", \"pb-5\", \"text-xl\", \"line-height-2\", \"font-bold\", \"text-yellow-400\"], [1, \"quick-access-list\", \"d-grid\", \"w-full\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-bottom-none\", \"border-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/001-toll.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"m-0\", \"mb-4\", \"uppercase\", \"flex-1\"], [1, \"text-sm\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-none\", \"border-top-1\", \"border-right-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/006-virus.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [\"src\", \"/assets/layout/images/003-trash-can.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/004-parking.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-none\", \"border-top-1\", \"border-right-1\", \"border-bottom-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/005-lecture.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [\"src\", \"/assets/layout/images/008-basketball.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"quick-access-right\"], [1, \"news-sec\", \"relative\", \"pt-7\", \"pb-8\", \"secondary-bg-color\"], [1, \"news-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\"], [1, \"news-title\"], [1, \"line\", \"m-0\", \"mb-5\", \"pb-5\", \"relative\", \"inline-flex\", \"w-fit\", \"text-yellow-400\"], [1, \"m-0\", \"mb-6\", \"relative\", \"text-6xl\", \"line-height-2\", \"font-bold\", \"text-white\"], [1, \"news-list\", \"d-grid\", \"w-full\", \"gap-6\"], [1, \"news-box\", \"w-full\", \"flex\", \"flex-column\"], [1, \"new-img\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/new-img-1.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"text-white\", \"text-xl\", \"flex-1\"], [1, \"mb-3\", \"flex\", \"gap-3\", \"align-items-center\"], [1, \"text\", \"flex\", \"align-items-center\", \"font-medium\", \"text-sm\", \"gap-1\", \"text-white\"], [1, \"mb-5\", \"text-sm\", \"text-white\"], [\"type\", \"button\", 1, \"px-5\", \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"h-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-yellow-400\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"news-letter-sec\", \"relative\"], [1, \"news-letter-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\"], [1, \"news-letter-box-list\", \"flex\", \"bg-white\", \"shadow-1\"], [1, \"news-letter-box\", \"w-6\", \"p-7\", \"flex\", \"flex-column\", \"justify-content-center\"], [1, \"line-2\", \"relative\", \"mb-5\", \"pb-5\", \"inline-flex\", \"w-fit\", \"align-items-start\", \"text-6xl\", \"font-extrabold\", \"line-height-1\"], [1, \"news-letter-form\", \"mt-5\", \"relative\", \"flex\", \"align-items-center\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Your email address\", 1, \"h-3-3rem\", \"border-noround\", \"flex-1\"], [\"type\", \"button\", 1, \"px-6\", \"p-element\", \"p-ripple\", \"border-noround\", \"p-button\", \"p-component\", \"h-3-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-yellow-400\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"news-letter-img\", \"w-6\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/news-letter-img.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"what-happning-sec\", \"mt-8\", \"relative\", \"px-4\"], [1, \"what-happning-body\", \"relative\", \"w-full\", \"mx-auto\", \"flex\", \"bg-orange-50\"], [1, \"what-happning-img\", \"w-6\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/whats-happnening-mg.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"what-happning-right\", \"w-6\", \"p-8\"], [1, \"what-happning-title\"], [1, \"line-2\", \"m-0\", \"mb-5\", \"pb-5\", \"relative\", \"inline-flex\", \"w-fit\", \"text-yellow-400\"], [1, \"m-0\", \"mb-6\", \"relative\", \"text-6xl\", \"line-height-2\", \"font-bold\", \"text-color\"], [1, \"what-happning-list\"], [1, \"what-happning-box\", \"mb-4\", \"pb-4\", \"flex\", \"align-items-center\", \"gap-5\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"surface-border\"], [1, \"wh-img-box\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/arches-1.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"wh-cnt-sec\", \"flex-1\", \"flex\", \"align-items-center\", \"gap-6\"], [1, \"wh-cnt\", \"flex\", \"flex-column\"], [1, \"text-xl\", \"font-bold\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"flex-wrap\"], [1, \"flex\", \"w-fit\", \"align-items-center\", \"gap-2\", \"py-2\", \"p-3\", \"bg-orange-100\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"type\", \"button\", 1, \"px-4\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-noround\", \"p-button-outlined\", \"h-3-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"text-color\", \"font-semibold\"], [\"src\", \"/assets/layout/images/arches-2.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"what-happning-box\", \"flex\", \"align-items-center\", \"gap-5\"], [\"src\", \"/assets/layout/images/arches-3.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"footer-sec\", \"relative\", \"secondary-bg-color\"], [1, \"footer-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\"], [1, \"top-footer\", \"py-7\", \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-5\", \"md:col-5\"], [1, \"line\", \"flex\", \"mb-5\", \"pb-3\", \"relative\", \"text-white\", \"text-xl\"], [1, \"p-0\", \"m-0\", \"flex\", \"flex-column\", \"gap-4\"], [1, \"flex\", \"w-full\"], [\"href\", \"#\", 1, \"flex\", \"w-full\", \"text-white\"], [1, \"flex\", \"w-full\", \"text-white\"], [\"href\", \"\", 1, \"flex\", \"w-full\", \"text-white\"], [1, \"col-12\", \"lg:col-7\", \"md:col-7\", \"py-0\"], [1, \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\"], [1, \"middle-footer\", \"py-5\", \"w-full\", \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-100\"], [1, \"bottom-footer\", \"py-5\", \"w-full\", \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [1, \"m-0\", \"mb-3\", \"p-0\", \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-3\", \"list-none\"], [\"href\", \"#\", 1, \"inline-flex\", \"w-fit\", \"text-white\"], [1, \"m-0\", \"text-white\"], [1, \"flex\"], [1, \"flex\", \"flex-column\", \"gap-1\", \"text-lg\", \"font-semibold\", \"text-color\", \"line-height-1\", 3, \"href\", \"target\"], [1, \"text-sm\", \"font-normal\", \"text-color-secondary\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"header\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"img\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"ul\", 6);\n          i0.ɵɵtemplate(7, HomeComponent_li_7_Template, 5, 4, \"li\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"button\", 8)(9, \"span\", 9);\n          i0.ɵɵtext(10, \"login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11, \" Login \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"section\", 10)(13, \"div\", 11);\n          i0.ɵɵelement(14, \"div\", 12);\n          i0.ɵɵelementStart(15, \"div\", 13)(16, \"h4\", 14);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"h1\", 15);\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"p\", 16);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"section\", 17)(23, \"div\", 18)(24, \"div\", 19);\n          i0.ɵɵelement(25, \"img\", 20);\n          i0.ɵɵelementStart(26, \"h4\", 21);\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(28, \"div\", 22);\n          i0.ɵɵelementStart(29, \"section\", 23)(30, \"div\", 24)(31, \"div\", 25);\n          i0.ɵɵelement(32, \"img\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 27)(34, \"h4\", 14);\n          i0.ɵɵtext(35, \"Far away from the every day!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"h2\", 28);\n          i0.ɵɵtext(37, \"A Message From the Director \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"p\", 29);\n          i0.ɵɵtext(39, \"Old Town of Riverside is a special place with limitless potential, where everyone deserves equal access to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in every neighborhood. It exists to serve you and be a resource for you, your family, and your community.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"button\", 30);\n          i0.ɵɵtext(41, \" Learn More \");\n          i0.ɵɵelementStart(42, \"span\", 31);\n          i0.ɵɵtext(43, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(44, \"section\", 32)(45, \"div\", 33)(46, \"div\", 34)(47, \"div\", 35);\n          i0.ɵɵelement(48, \"img\", 36);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"div\", 37)(50, \"h4\", 14);\n          i0.ɵɵtext(51, \"Our city in numbers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"h2\", 38);\n          i0.ɵɵtext(53, \"Programs to help launch, grow and expand any business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"p\", 39);\n          i0.ɵɵtext(55, \"Old Town of Riverside is a special place with limitless potential, where everyone deserves equal access to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in every neighborhood. It exists to serve you and be a resource for you, your family, and your community.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(56, \"div\", 40)(57, \"div\", 41)(58, \"h3\", 42);\n          i0.ɵɵtext(59, \"41 \");\n          i0.ɵɵelementStart(60, \"sup\", 43);\n          i0.ɵɵtext(61, \"k\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"h5\", 44);\n          i0.ɵɵtext(63, \"Highly-educated creative workforce\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"p\", 45);\n          i0.ɵɵtext(65, \"A vibrant destination where innovations in design and technology are born. A place for you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(66, \"div\", 41)(67, \"h3\", 42);\n          i0.ɵɵtext(68, \"8 \");\n          i0.ɵɵelementStart(69, \"sup\", 43);\n          i0.ɵɵtext(70, \"th\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"h5\", 44);\n          i0.ɵɵtext(72, \"Highest Per Capita Income in Virginia\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"p\", 45);\n          i0.ɵɵtext(74, \"A vibrant destination where innovations in design and technology are born. A place for you.\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(75, \"section\", 46)(76, \"div\", 47)(77, \"div\", 48)(78, \"h2\", 49);\n          i0.ɵɵtext(79, \"Quick Access\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"h4\", 50);\n          i0.ɵɵtext(81, \" What can the City help you with today?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"div\", 51)(83, \"div\", 52);\n          i0.ɵɵelement(84, \"img\", 53);\n          i0.ɵɵelementStart(85, \"h6\", 54);\n          i0.ɵɵtext(86, \"Road construction\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"p\", 55);\n          i0.ɵɵtext(88, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(89, \"div\", 56);\n          i0.ɵɵelement(90, \"img\", 57);\n          i0.ɵɵelementStart(91, \"h6\", 54);\n          i0.ɵɵtext(92, \"COVID-19 updates\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"p\", 55);\n          i0.ɵɵtext(94, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(95, \"div\", 56);\n          i0.ɵɵelement(96, \"img\", 58);\n          i0.ɵɵelementStart(97, \"h6\", 54);\n          i0.ɵɵtext(98, \"Garbage pickup\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"p\", 55);\n          i0.ɵɵtext(100, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(101, \"div\", 59);\n          i0.ɵɵelement(102, \"img\", 60);\n          i0.ɵɵelementStart(103, \"h6\", 54);\n          i0.ɵɵtext(104, \"Parking\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"p\", 55);\n          i0.ɵɵtext(106, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(107, \"div\", 61);\n          i0.ɵɵelement(108, \"img\", 62);\n          i0.ɵɵelementStart(109, \"h6\", 54);\n          i0.ɵɵtext(110, \"Council meetings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"p\", 55);\n          i0.ɵɵtext(112, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(113, \"div\", 61);\n          i0.ɵɵelement(114, \"img\", 63);\n          i0.ɵɵelementStart(115, \"h6\", 54);\n          i0.ɵɵtext(116, \"Recreation and sport programs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"p\", 55);\n          i0.ɵɵtext(118, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(119, \"div\", 64);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(120, \"section\", 65)(121, \"div\", 66)(122, \"div\", 67)(123, \"h5\", 68);\n          i0.ɵɵtext(124, \"Find out what\\u2019s going on & stay up to date.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(125, \"h2\", 69);\n          i0.ɵɵtext(126, \"Latest News\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(127, \"div\", 70)(128, \"div\", 71)(129, \"div\", 72);\n          i0.ɵɵelement(130, \"img\", 73);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(131, \"h3\", 74);\n          i0.ɵɵtext(132, \"Proposed Downtown District Ordinance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(133, \"div\", 75)(134, \"div\", 76)(135, \"span\", 9);\n          i0.ɵɵtext(136, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(137, \" September 16, 2018 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(138, \"div\", 76)(139, \"span\", 9);\n          i0.ɵɵtext(140, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(141, \" 240 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(142, \"p\", 77);\n          i0.ɵɵtext(143, \"The goal of the proposed ordinance is to create safe venues for customers and to discourage illegal activities. The governing officials of...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(144, \"button\", 78);\n          i0.ɵɵtext(145, \" Learn More \");\n          i0.ɵɵelementStart(146, \"span\", 31);\n          i0.ɵɵtext(147, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(148, \"div\", 71)(149, \"div\", 72);\n          i0.ɵɵelement(150, \"img\", 73);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(151, \"h3\", 74);\n          i0.ɵɵtext(152, \"Annual Water Quality Report (Gallery Post)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(153, \"div\", 75)(154, \"div\", 76)(155, \"span\", 9);\n          i0.ɵɵtext(156, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(157, \" September 16, 2018 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(158, \"div\", 76)(159, \"span\", 9);\n          i0.ɵɵtext(160, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(161, \" 240 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(162, \"p\", 77);\n          i0.ɵɵtext(163, \"The goal of the proposed ordinance is to create safe venues for customers and to discourage illegal activities. The governing officials of...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(164, \"button\", 78);\n          i0.ɵɵtext(165, \" Learn More \");\n          i0.ɵɵelementStart(166, \"span\", 31);\n          i0.ɵɵtext(167, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(168, \"div\", 71)(169, \"div\", 72);\n          i0.ɵɵelement(170, \"img\", 73);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(171, \"h3\", 74);\n          i0.ɵɵtext(172, \"Waste Industries Garbage Pick Up: Embeds\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(173, \"div\", 75)(174, \"div\", 76)(175, \"span\", 9);\n          i0.ɵɵtext(176, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(177, \" September 16, 2018 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(178, \"div\", 76)(179, \"span\", 9);\n          i0.ɵɵtext(180, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(181, \" 240 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(182, \"p\", 77);\n          i0.ɵɵtext(183, \"The goal of the proposed ordinance is to create safe venues for customers and to discourage illegal activities. The governing officials of...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(184, \"button\", 78);\n          i0.ɵɵtext(185, \" Learn More \");\n          i0.ɵɵelementStart(186, \"span\", 31);\n          i0.ɵɵtext(187, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(188, \"section\", 79)(189, \"div\", 80)(190, \"div\", 81)(191, \"div\", 82)(192, \"h2\", 83);\n          i0.ɵɵtext(193, \" Sign up for News & Alerts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(194, \"p\");\n          i0.ɵɵtext(195, \"Stay connected to the City of Riverside by signing up to receive official news and alerts by email! \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(196, \"div\", 84);\n          i0.ɵɵelement(197, \"input\", 85);\n          i0.ɵɵelementStart(198, \"button\", 86);\n          i0.ɵɵtext(199, \" Sign Up \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(200, \"div\", 87);\n          i0.ɵɵelement(201, \"img\", 88);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(202, \"section\", 89)(203, \"div\", 90)(204, \"div\", 91);\n          i0.ɵɵelement(205, \"img\", 92);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(206, \"div\", 93)(207, \"div\", 94)(208, \"h5\", 95);\n          i0.ɵɵtext(209, \"Join the fun in our city! \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(210, \"h2\", 96);\n          i0.ɵɵtext(211, \"What's Happening\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(212, \"div\", 97)(213, \"div\", 98)(214, \"div\", 99);\n          i0.ɵɵelement(215, \"img\", 100);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(216, \"div\", 101)(217, \"div\", 102)(218, \"h3\", 103);\n          i0.ɵɵtext(219, \"Heritage 10th Anniversary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(220, \"div\", 104)(221, \"div\", 105)(222, \"span\");\n          i0.ɵɵtext(223, \"June 9, 2027\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(224, \" - \");\n          i0.ɵɵelementStart(225, \"span\");\n          i0.ɵɵtext(226, \"June 18, 2027\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(227, \"div\", 106);\n          i0.ɵɵtext(228, \"All Day at \");\n          i0.ɵɵelementStart(229, \"b\");\n          i0.ɵɵtext(230, \"Baker Hall\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(231, \"button\", 107);\n          i0.ɵɵtext(232, \" Find out more \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(233, \"div\", 98)(234, \"div\", 99);\n          i0.ɵɵelement(235, \"img\", 108);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(236, \"div\", 101)(237, \"div\", 102)(238, \"h3\", 103);\n          i0.ɵɵtext(239, \"Along Pines Run\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(240, \"div\", 104)(241, \"div\", 105)(242, \"span\");\n          i0.ɵɵtext(243, \"July 22, 2027\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(244, \"div\", 106);\n          i0.ɵɵtext(245, \"12:00 am at \");\n          i0.ɵɵelementStart(246, \"b\");\n          i0.ɵɵtext(247, \"Boulder City Council\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(248, \"button\", 107);\n          i0.ɵɵtext(249, \" Find out more \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(250, \"div\", 109)(251, \"div\", 99);\n          i0.ɵɵelement(252, \"img\", 110);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(253, \"div\", 101)(254, \"div\", 102)(255, \"h3\", 103);\n          i0.ɵɵtext(256, \"Touch A Truck\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(257, \"div\", 104)(258, \"div\", 105)(259, \"span\");\n          i0.ɵɵtext(260, \"June 9, 2027\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(261, \" - \");\n          i0.ɵɵelementStart(262, \"span\");\n          i0.ɵɵtext(263, \"June 18, 2027\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(264, \"div\", 106);\n          i0.ɵɵtext(265, \"9:00 am - 5:00 pm at \");\n          i0.ɵɵelementStart(266, \"b\");\n          i0.ɵɵtext(267, \"Baker Hall\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(268, \"button\", 107);\n          i0.ɵɵtext(269, \" Find out more \");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(270, \"section\", 111)(271, \"div\", 112)(272, \"div\", 113)(273, \"div\", 114)(274, \"h3\", 115);\n          i0.ɵɵtext(275, \"Riverside City Hall\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(276, \"ul\", 116)(277, \"li\", 117)(278, \"a\", 118);\n          i0.ɵɵtext(279, \"8353 Sierra Avenue \\u2022 Riverside, CA 91335\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(280, \"li\", 117)(281, \"a\", 118);\n          i0.ɵɵtext(282, \"Phone: (*************\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(283, \"li\", 117)(284, \"a\", 119);\n          i0.ɵɵtext(285, \"Monday - Thursday, 8:00 am - 6:00 pm\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(286, \"li\", 117)(287, \"a\", 120);\n          i0.ɵɵtext(288, \"Email : <EMAIL>\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(289, \"div\", 121)(290, \"div\", 122)(291, \"div\", 123)(292, \"h3\", 115);\n          i0.ɵɵtext(293, \"Services\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(294, \"ul\", 116)(295, \"li\", 117)(296, \"a\", 118);\n          i0.ɵɵtext(297, \"Driver & ID Services\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(298, \"li\", 117)(299, \"a\", 118);\n          i0.ɵɵtext(300, \"Vehicle & Plate Services\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(301, \"li\", 117)(302, \"a\", 119);\n          i0.ɵɵtext(303, \"Business Services\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(304, \"li\", 117)(305, \"a\", 120);\n          i0.ɵɵtext(306, \"Senior Services\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(307, \"div\", 123)(308, \"h3\", 115);\n          i0.ɵɵtext(309, \"Useful Links\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(310, \"ul\", 116)(311, \"li\", 117)(312, \"a\", 118);\n          i0.ɵɵtext(313, \"Frequently Asked Questions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(314, \"li\", 117)(315, \"a\", 118);\n          i0.ɵɵtext(316, \"Latest News\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(317, \"li\", 117)(318, \"a\", 119);\n          i0.ɵɵtext(319, \"Community\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(320, \"li\", 117)(321, \"a\", 120);\n          i0.ɵɵtext(322, \"Help Center\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(323, \"li\", 117)(324, \"a\", 120);\n          i0.ɵɵtext(325, \"Careers\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelement(326, \"div\", 124);\n          i0.ɵɵelementStart(327, \"div\", 125)(328, \"ul\", 126)(329, \"li\")(330, \"a\", 127);\n          i0.ɵɵtext(331, \"Term & Conditions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(332, \"li\")(333, \"a\", 127);\n          i0.ɵɵtext(334, \"Privacy Policy\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(335, \"p\", 128);\n          i0.ɵɵtext(336, \"\\u00A9 2025 SNJYA. All rights reserved.\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"src\", ctx.logo || \"/assets/layout/images/snjya-public-services-logo.png\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.menuItems);\n          i0.ɵɵadvance(7);\n          i0.ɵɵstyleProp(\"background-image\", (ctx.bannerData == null ? null : ctx.bannerData.Image == null ? null : ctx.bannerData.Image.url) ? \"url(\" + ctx.bannerData.Image.url + \")\" : null)(\"background-size\", \"cover\")(\"background-position\", \"center\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate((ctx.bannerData == null ? null : ctx.bannerData.Annotation) || \"Far away from the every day!\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate((ctx.bannerData == null ? null : ctx.bannerData.Title) || \"Community of endless beauty & Calm\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate((ctx.bannerData == null ? null : ctx.bannerData.Description) || \"Drawn by clean air and mythical light, visitors come to experience traditions, fine art, great cuisine and natural beauty of the landscape.\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", (ctx.servicesData[0] == null ? null : ctx.servicesData[0].Icon == null ? null : ctx.servicesData[0].Icon.url) || \"/assets/layout/images/id-services.png\", i0.ɵɵsanitizeUrl)(\"alt\", (ctx.servicesData[0] == null ? null : ctx.servicesData[0].Title) || \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate((ctx.servicesData[0] == null ? null : ctx.servicesData[0].Title) || \"Driver & ID Services\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"innerHTML\", ctx.getCleanServiceDescription(ctx.servicesData[0] == null ? null : ctx.servicesData[0].Description), i0.ɵɵsanitizeHtml);\n        }\n      },\n      dependencies: [i4.NgForOf, i5.InputText],\n      styles: [\".max-w-1200 {\\n  max-width: 1200px;\\n}\\n  .secondary-color {\\n  color: #030f5e !important;\\n}\\n  .secondary-bg-color {\\n  background: #030f5e !important;\\n}\\n  .font-extrabold {\\n  font-weight: 900 !important;\\n}\\n  .d-grid {\\n  display: grid !important;\\n}\\n  .object-fit-contain {\\n  object-fit: contain;\\n}\\n  .object-fit-cover {\\n  object-fit: cover;\\n}\\n  .h-3-3rem {\\n  height: 3.3rem !important;\\n}\\n  .bg-blue-75 {\\n  background: rgb(227, 243, 255) !important;\\n}\\n  .main-header:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  height: 8rem;\\n  background: #030f5e !important;\\n  width: 20%;\\n}\\n  .banner-sec {\\n  margin: 8rem 0 0 0;\\n  height: calc(100vh - 8rem);\\n  background: url('banner-img.jpg') center center no-repeat;\\n  background-size: cover;\\n}\\n  .banner-sec .banner-box {\\n  margin: 120px 0 0 0;\\n}\\n  .services-sec {\\n  margin: -87px 0 0 0;\\n}\\n  .services-sec .services-box.s-box-1 {\\n  background: #a8c1cd;\\n}\\n  .services-sec .services-box.s-box-2 {\\n  background: #e3cab0;\\n}\\n  .services-sec .services-box.s-box-3 {\\n  background: #c4a597;\\n}\\n  .services-sec .services-box.s-box-4 {\\n  background: #e8816e;\\n}\\n  .about-sec:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  right: 0;\\n  height: 100%;\\n  background: #030f5e !important;\\n  width: 20%;\\n}\\n  .line::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.2392156863) !important;\\n  width: 100%;\\n}\\n  .line::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgb(213, 200, 186) !important;\\n  width: 64px;\\n}\\n  .line-2::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgba(0, 0, 0, 0.07) !important;\\n  width: 100%;\\n}\\n  .line-2::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgb(213, 200, 186) !important;\\n  width: 64px;\\n}\\n  .city-members-sec:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  height: 100%;\\n  background: var(--orange-50) !important;\\n  width: 20%;\\n}\\n  .city-members-left {\\n  width: 70%;\\n}\\n  .city-members-left .cm-time-box {\\n  flex: 0 0 40%;\\n}\\n  .city-members-left .cm-info {\\n  flex: 0 0 60%;\\n  padding: 0 0 0 50px;\\n}\\n  .city-members-right {\\n  width: 30%;\\n}\\n  .city-members-right .cm-right-box h3 sup {\\n  top: 7px;\\n}\\n  .quick-access-sec {\\n  background: url('pexels-vitor-gusmao.jpg') center right no-repeat;\\n  background-size: 38% auto;\\n}\\n  .quick-access-sec .quick-access-left {\\n  flex: 0 0 67%;\\n  width: 67%;\\n}\\n  .quick-access-sec .quick-access-left .quick-access-list {\\n  grid-template-columns: repeat(auto-fill, minmax(33.333%, 1fr));\\n}\\n  .quick-access-sec .quick-access-right {\\n  flex: 0 0 33%;\\n  width: 33%;\\n}\\n  .news-sec .news-list {\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n}\\n  .news-letter-sec {\\n  background: linear-gradient(180deg, #030f5e 50%, transparent 50%);\\n}\\n  .news-letter-sec .news-letter-box-list .news-letter-img {\\n  height: 640px;\\n}\\n  .what-happning-sec .what-happning-body .what-happning-img {\\n  height: 800px;\\n}\\n  .what-happning-sec .what-happning-body .what-happning-box .wh-img-box {\\n  width: 120px;\\n  height: 120px;\\n}\\n  .what-happning-sec .what-happning-body .what-happning-box .wh-cnt {\\n  max-width: 70%;\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************ */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "menuItem_r1", "Link", "ɵɵsanitizeUrl", "Target", "ɵɵtextInterpolate1", "Title", "ɵɵtextInterpolate", "Sub_Title", "HomeComponent", "constructor", "primengConfig", "renderer", "route", "CMSservice", "logo", "menuItems", "bannerData", "servicesData", "ngOnInit", "commonContent", "snapshot", "data", "console", "log", "logoComponent", "getDataByComponentName", "body", "length", "Logo", "url", "menuComponent", "<PERSON><PERSON>_<PERSON><PERSON>", "content", "bannerComponent", "servicesComponents", "href", "link", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "document", "head", "ripple", "getCleanServiceDescription", "htmlDescription", "cleanHtml", "replace", "getDefaultServiceIcon", "index", "defaultIcons", "ngOnDestroy", "getElementById", "remove", "ɵɵdirectiveInject", "i1", "PrimeNGConfig", "Renderer2", "i2", "ActivatedRoute", "i3", "ContentService", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "HomeComponent_li_7_Template", "ɵɵstyleProp", "Image", "Annotation", "Description", "Icon", "ɵɵsanitizeHtml"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home.component.ts", "D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home.component.html"], "sourcesContent": ["import { Component, Renderer2 } from '@angular/core';\r\nimport { PrimeNGConfig } from 'primeng/api';\r\nimport { ContentService } from '../core/services/content-vendor.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrl: './home.component.scss'\r\n})\r\nexport class HomeComponent {\r\n\r\n  content!: any;\r\n  commonContent!: any;\r\n  logo: string = '';\r\n  menuItems: any[] = [];\r\n  bannerData: any = null;\r\n  servicesData: any[] = [];\r\n\r\n  constructor(\r\n    private primengConfig: PrimeNGConfig,\r\n    private renderer: Renderer2,\r\n    private route: ActivatedRoute,\r\n    private CMSservice: ContentService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.commonContent = this.route.snapshot.data['commonContent'];\r\n    console.log('Common Content:', this.commonContent);\r\n\r\n    // Extract logo\r\n    const logoComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.logo\");\r\n    if (logoComponent?.length) {\r\n      this.logo = logoComponent[0].Logo?.url || '';\r\n    }\r\n\r\n    // Extract menu\r\n    const menuComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.menu\");\r\n    if (menuComponent?.length) {\r\n      this.menuItems = menuComponent[0].Menu_Item || [];\r\n    }\r\n\r\n    this.content = this.route.snapshot.data['content'];\r\n    console.log('Home Content:', this.content);\r\n\r\n    // Extract banner\r\n    const bannerComponent = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.banner\");\r\n    if (bannerComponent?.length) {\r\n      this.bannerData = bannerComponent[0];\r\n    }\r\n\r\n    // Extract services\r\n    const servicesComponents = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.services\");\r\n    if (servicesComponents?.length) {\r\n      this.servicesData = servicesComponents;\r\n    }\r\n\r\n    console.log('Logo:', this.logo);\r\n    console.log('Menu Items:', this.menuItems);\r\n    console.log('Banner Data:', this.bannerData);\r\n    console.log('Services Data:', this.servicesData);\r\n\r\n    // Inject theme \r\n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\r\n    const link = this.renderer.createElement('link');\r\n    this.renderer.setAttribute(link, 'id', 'theme-link');\r\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\r\n    this.renderer.setAttribute(link, 'type', 'text/css');\r\n    this.renderer.setAttribute(link, 'href', href);\r\n\r\n    // Append the link tag to the head of the document\r\n    this.renderer.appendChild(document.head, link);\r\n\r\n    this.primengConfig.ripple = true; //enables core ripple functionality\r\n  }\r\n\r\n  // Helper method to clean up HTML description for safe rendering\r\n  getCleanServiceDescription(htmlDescription: string): string {\r\n    if (!htmlDescription) {\r\n      return `<ul class=\"m-0 mb-5 p-0 flex flex-1 flex-column gap-2 list-none\">\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Renew driver's license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Replace driver's license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Get a REAL ID</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    First-time driver's license/ID card</li>\r\n              </ul>`;\r\n    }\r\n\r\n    // Clean up the HTML by removing inline styles and fixing classes\r\n    let cleanHtml = htmlDescription\r\n      .replace(/style=\"[^\"]*\"/g, '') // Remove inline styles\r\n      .replace(/class=\"[^\"]*\"/g, 'class=\"text-white flex align-items-center gap-2\"') // Replace with our classes\r\n      .replace(/<p[^>]*>/g, '') // Remove p tags\r\n      .replace(/<\\/p>/g, '') // Remove closing p tags\r\n      .replace(/<br\\s*\\/?>/g, '') // Remove br tags\r\n      .replace(/<span[^>]*>/g, '') // Remove span tags\r\n      .replace(/<\\/span>/g, ''); // Remove closing span tags\r\n\r\n    // Add arrow icons to list items\r\n    cleanHtml = cleanHtml.replace(/<li class=\"text-white flex align-items-center gap-2\">/g,\r\n      '<li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>');\r\n\r\n    return cleanHtml;\r\n  }\r\n\r\n  // Helper method to get default service icons\r\n  getDefaultServiceIcon(index: number): string {\r\n    const defaultIcons = [\r\n      '/assets/layout/images/id-services.png',\r\n      '/assets/layout/images/car-services.png',\r\n      '/assets/layout/images/business-services.png',\r\n      '/assets/layout/images/senior-services.png'\r\n    ];\r\n    return defaultIcons[index] || '/assets/layout/images/id-services.png';\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Find and remove the link tag when the component is destroyed\r\n    const link = document.getElementById('theme-link');\r\n    if (link) {\r\n      link.remove();\r\n    }\r\n  }\r\n\r\n}\r\n", "<!--HEADER SEC-->\r\n<header class=\"main-header fixed top-0 w-full bg-white z-5\">\r\n    <div class=\"header-body relative max-w-1200 w-full mx-auto px-4 flex align-items-center\">\r\n        <div class=\"header-logo relative pr-6 flex align-items-center w-18rem h-8rem secondary-bg-color\">\r\n            <img [src]=\"logo || '/assets/layout/images/snjya-public-services-logo.png'\" class=\"w-full h-fit\" alt=\"Logo\" />\r\n        </div>\r\n        <div class=\"header-menu-sec pl-5 relative flex align-items-center justify-content-between flex-1\">\r\n            <div class=\"menu-list\">\r\n                <ul class=\"p-0 m-0 flex align-items-center gap-5\">\r\n                    <li class=\"flex\" *ngFor=\"let menuItem of menuItems\">\r\n                        <a [href]=\"menuItem.Link || '#'\"\r\n                           [target]=\"menuItem.Target || '_self'\"\r\n                           class=\"flex flex-column gap-1 text-lg font-semibold text-color line-height-1\">\r\n                            {{ menuItem.Title }}\r\n                            <span class=\"text-sm font-normal text-color-secondary\">{{ menuItem.Sub_Title }}</span>\r\n                        </a>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component w-8rem h-3rem justify-content-center gap-2 line-height-2 bg-bluegray-100 text-color font-semibold border-none\">\r\n                <span class=\"material-symbols-rounded text-xl\">login</span> Login\r\n            </button>\r\n        </div>\r\n    </div>\r\n</header>\r\n<!--HEADER SEC-->\r\n\r\n<!--BANNER SEC-->\r\n<section class=\"banner-sec relative\">\r\n    <div class=\"banner-body h-full flex align-items-center\">\r\n        <div class=\"banner-box relative flex-1 p-8\"\r\n             [style.background-image]=\"bannerData?.Image?.url ? 'url(' + bannerData.Image.url + ')' : null\"\r\n             [style.background-size]=\"'cover'\"\r\n             [style.background-position]=\"'center'\"></div>\r\n        <div class=\"banner-box relative flex-1 bg-white p-8 flex flex-column justify-content-center\">\r\n            <h4 class=\"mb-4 text-yellow-400\">{{ bannerData?.Annotation || 'Far away from the every day!' }}</h4>\r\n            <h1 class=\"m-0 mb-5 text-8xl line-height-1 font-extrabold text-color\">{{ bannerData?.Title || 'Community of endless beauty & Calm' }}</h1>\r\n            <p class=\"m-0 text-lg font-medium\">{{ bannerData?.Description || 'Drawn by clean air and mythical light, visitors come to experience traditions, fine art, great cuisine and natural beauty of the landscape.' }}</p>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--BANNER SEC-->\r\n\r\n<!--SERVICES SEC-->\r\n<section class=\"services-sec w-full\">\r\n    <div class=\"services-body relative max-w-1200 w-full mx-auto px-4 flex\">\r\n        <div class=\"services-box s-box-1 p-5 flex-1 flex flex-column gap-2\">\r\n            <img [src]=\"servicesData[0]?.Icon?.url || '/assets/layout/images/id-services.png'\" class=\"h-fit w-5rem\" [alt]=\"servicesData[0]?.Title || ''\" />\r\n            <h4 class=\"font-bold text-white\">{{ servicesData[0]?.Title || 'Driver & ID Services' }}</h4>\r\n            <div [innerHTML]=\"getCleanServiceDescription(servicesData[0]?.Description)\"></div>\r\n\r\n                <!-- <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Renew driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Replace driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Get a REAL ID</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    First-time driver’s license/ID card</li>\r\n            </ul>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component h-3rem justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                {{ servicesData[0]?.Button_Title || 'Driver & ID Services' }} <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n        <div class=\"services-box s-box-2 p-5 flex-1 flex flex-column gap-2\">\r\n            <img src=\"/assets/layout/images/car-services.png\" class=\"h-fit w-5rem\" alt=\"\" />\r\n            <h4 class=\"font-bold text-white\">Vehicle & Plate Services</h4>\r\n            <ul class=\"m-0 mb-5 p-0 flex flex-1 flex-column gap-2 list-none\">\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Renew driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Replace driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Get a REAL ID</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    First-time driver’s license/ID card</li>\r\n            </ul>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component h-3rem justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                Vehicle & Plate Services <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n        <div class=\"services-box s-box-3 p-5 flex-1 flex flex-column gap-2\">\r\n            <img src=\"/assets/layout/images/business-services.png\" class=\"h-fit w-5rem\" alt=\"\" />\r\n            <h4 class=\"font-bold text-white\">Business Services</h4>\r\n            <ul class=\"m-0 mb-5 p-0 flex flex-1 flex-column gap-2 list-none\">\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Renew driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Replace driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Get a REAL ID</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    First-time driver’s license/ID card</li>\r\n            </ul>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component h-3rem justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                Business Services <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n        <div class=\"services-box s-box-4 p-5 flex-1 flex flex-column gap-2\">\r\n            <img src=\"/assets/layout/images/senior-services.png\" class=\"h-fit w-5rem\" alt=\"\" />\r\n            <h4 class=\"font-bold text-white\">Senior Services</h4>\r\n            <ul class=\"m-0 mb-5 p-0 flex flex-1 flex-column gap-2 list-none\">\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Renew driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Replace driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Get a REAL ID</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    First-time driver’s license/ID card</li>\r\n            </ul>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component h-3rem justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                Senior Services <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--SERVICES SEC-->\r\n\r\n<!--ABOUT SEC-->\r\n<section class=\"about-sec relative\">\r\n    <div class=\"about-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap\">\r\n        <div class=\"about-box pr-8 w-6 secondary-bg-color\">\r\n            <img src=\"/assets/layout/images/director-img.jpg\" alt=\"\" class=\"w-full\" />\r\n        </div>\r\n        <div class=\"about-box p-8 pr-0 w-6 secondary-bg-color flex flex-column justify-content-start\">\r\n            <h4 class=\"mb-4 text-yellow-400\">Far away from the every day!</h4>\r\n            <h2 class=\"line m-0 mb-6 relative pb-5 text-6xl line-height-2 font-bold text-white\">A Message From the\r\n                Director\r\n            </h2>\r\n            <p class=\"m-0 text-lg text-white flex-1\">Old Town of Riverside is a special place with limitless potential,\r\n                where everyone deserves equal access\r\n                to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in every\r\n                neighborhood. It exists to serve you and be a resource for you, your family, and your community.</p>\r\n\r\n            <button type=\"button\"\r\n                class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--ABOUT SEC-->\r\n\r\n<!--CITY MEMBERS SEC-->\r\n<section class=\"city-members-sec relative\">\r\n    <div class=\"city-members-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap\">\r\n        <div class=\"city-members-left pt-8 pr-7 pb-8 pl-0 relative flex bg-orange-50\">\r\n            <div class=\"cm-time-box\">\r\n                <img src=\"/assets/layout/images/time-screen.png\" alt=\"\" class=\"w-full\" />\r\n            </div>\r\n            <div class=\"cm-info\">\r\n                <h4 class=\"mb-4 text-yellow-400\">Our city in numbers</h4>\r\n                <h2 class=\"line m-0 mb-6 relative pb-5 text-4xl line-height-2 font-bold text-color\">Programs to help\r\n                    launch,\r\n                    grow and expand any business</h2>\r\n                <p class=\"m-0 text-lg text-color flex-1\">Old Town of Riverside is a special place with limitless\r\n                    potential, where everyone deserves equal access\r\n                    to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in\r\n                    every\r\n                    neighborhood. It exists to serve you and be a resource for you, your family, and your community.</p>\r\n            </div>\r\n        </div>\r\n        <div class=\"city-members-right p-7 pr-0 relative flex flex-column gap-8\">\r\n            <div class=\"cm-right-box\">\r\n                <h3 class=\"flex align-items-start text-8xl font-extrabold line-height-1\">41 <sup\r\n                        class=\"relative text-4xl font-bold inline-block\">k</sup></h3>\r\n                <h5 class=\"uppercase\">Highly-educated creative workforce</h5>\r\n                <p class=\"text-color-secondary\">A vibrant destination where innovations in design and technology are\r\n                    born. A place for you.</p>\r\n            </div>\r\n            <div class=\"cm-right-box\">\r\n                <h3 class=\" flex align-items-start text-8xl font-extrabold line-height-1\">8 <sup\r\n                        class=\"relative text-4xl font-bold inline-block\">th</sup></h3>\r\n                <h5 class=\"uppercase\">Highest Per Capita Income in Virginia</h5>\r\n                <p class=\"text-color-secondary\">A vibrant destination where innovations in design and technology are\r\n                    born. A place for you.</p>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--CITY MEMBERS SEC-->\r\n\r\n<!--QUICK ACCESS SEC-->\r\n<section class=\"quick-access-sec relative\">\r\n    <div class=\"quick-access-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap\">\r\n        <div class=\"quick-access-left relative flex flex-column pt-8 pb-8\">\r\n            <h2 class=\"mb-4 flex align-items-start text-6xl font-extrabold line-height-1\">Quick Access</h2>\r\n            <h4 class=\"line-2 inline-flex w-fit m-0 mb-6 relative pb-5 text-xl line-height-2 font-bold text-yellow-400\">\r\n                What can the City help you with today?</h4>\r\n\r\n            <div class=\"quick-access-list d-grid w-full\">\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-bottom-none border-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/001-toll.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Road construction</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/006-virus.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">COVID-19 updates</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/003-trash-can.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Garbage pickup</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div class=\"quick-access-box p-4 w-full flex flex-column border-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/004-parking.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Parking</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-bottom-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/005-lecture.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Council meetings</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-bottom-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/008-basketball.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Recreation and sport programs</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"quick-access-right\"></div>\r\n    </div>\r\n</section>\r\n<!--QUICK ACCESS SEC-->\r\n\r\n<!--NEWS SEC-->\r\n<section class=\"news-sec relative pt-7 pb-8 secondary-bg-color\">\r\n    <div class=\"news-body relative max-w-1200 w-full mx-auto px-4\">\r\n        <div class=\"news-title\">\r\n            <h5 class=\"line m-0 mb-5 pb-5 relative inline-flex w-fit text-yellow-400\">Find out what’s going on & stay up\r\n                to date.</h5>\r\n            <h2 class=\"m-0 mb-6 relative text-6xl line-height-2 font-bold text-white\">Latest News</h2>\r\n        </div>\r\n        <div class=\"news-list d-grid w-full gap-6\">\r\n            <div class=\"news-box w-full flex flex-column\">\r\n                <div class=\"new-img flex align-items-center justify-content-center overflow-hidden\">\r\n                    <img src=\"/assets/layout/images/new-img-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                </div>\r\n                <h3 class=\"text-white text-xl flex-1\">Proposed Downtown District Ordinance</h3>\r\n                <div class=\"mb-3 flex gap-3 align-items-center\">\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">calendar_month</span> September 16, 2018\r\n                    </div>\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">visibility</span> 240\r\n                    </div>\r\n                </div>\r\n                <p class=\"mb-5 text-sm text-white\">The goal of the proposed ordinance is to create safe venues for\r\n                    customers\r\n                    and to discourage illegal activities. The governing officials of...</p>\r\n\r\n                <button type=\"button\"\r\n                    class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                    Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n                </button>\r\n            </div>\r\n            <div class=\"news-box w-full flex flex-column\">\r\n                <div class=\"new-img flex align-items-center justify-content-center overflow-hidden\">\r\n                    <img src=\"/assets/layout/images/new-img-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                </div>\r\n                <h3 class=\"text-white text-xl flex-1\">Annual Water Quality Report (Gallery Post)</h3>\r\n                <div class=\"mb-3 flex gap-3 align-items-center\">\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">calendar_month</span> September 16, 2018\r\n                    </div>\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">visibility</span> 240\r\n                    </div>\r\n                </div>\r\n                <p class=\"mb-5 text-sm text-white\">The goal of the proposed ordinance is to create safe venues for\r\n                    customers\r\n                    and to discourage illegal activities. The governing officials of...</p>\r\n\r\n                <button type=\"button\"\r\n                    class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                    Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n                </button>\r\n            </div>\r\n            <div class=\"news-box w-full flex flex-column\">\r\n                <div class=\"new-img flex align-items-center justify-content-center overflow-hidden\">\r\n                    <img src=\"/assets/layout/images/new-img-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                </div>\r\n                <h3 class=\"text-white text-xl flex-1\">Waste Industries Garbage Pick Up: Embeds</h3>\r\n                <div class=\"mb-3 flex gap-3 align-items-center\">\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">calendar_month</span> September 16, 2018\r\n                    </div>\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">visibility</span> 240\r\n                    </div>\r\n                </div>\r\n                <p class=\"mb-5 text-sm text-white\">The goal of the proposed ordinance is to create safe venues for\r\n                    customers\r\n                    and to discourage illegal activities. The governing officials of...</p>\r\n\r\n                <button type=\"button\"\r\n                    class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                    Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--NEWS SEC-->\r\n\r\n<!--NEWS LETTER SEC-->\r\n<section class=\"news-letter-sec relative\">\r\n    <div class=\"news-letter-body relative max-w-1200 w-full mx-auto px-4\">\r\n        <div class=\"news-letter-box-list flex bg-white shadow-1\">\r\n            <div class=\"news-letter-box w-6 p-7 flex flex-column justify-content-center\">\r\n                <h2\r\n                    class=\"line-2 relative mb-5 pb-5 inline-flex w-fit align-items-start text-6xl font-extrabold line-height-1\">\r\n                    Sign up for News & Alerts</h2>\r\n                <p>Stay connected to the City of Riverside by signing up to receive official news and alerts by email!\r\n                </p>\r\n                <div class=\"news-letter-form mt-5 relative flex align-items-center\">\r\n                    <input type=\"text\" pInputText class=\"h-3-3rem border-noround flex-1\"\r\n                        placeholder=\"Your email address\" />\r\n                    <button type=\"button\"\r\n                        class=\"px-6 p-element p-ripple border-noround p-button p-component h-3-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                        Sign Up\r\n                    </button>\r\n                </div>\r\n            </div>\r\n            <div class=\"news-letter-img w-6 flex align-items-center justify-content-center overflow-hidden\">\r\n                <img src=\"/assets/layout/images/news-letter-img.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--NEWS LETTER SEC-->\r\n\r\n<!--WHAT HAPPNENING SEC-->\r\n<section class=\"what-happning-sec mt-8 relative px-4\">\r\n    <div class=\"what-happning-body relative w-full mx-auto flex bg-orange-50\">\r\n        <div class=\"what-happning-img w-6 flex align-items-center justify-content-center overflow-hidden\">\r\n            <img src=\"/assets/layout/images/whats-happnening-mg.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n        </div>\r\n        <div class=\"what-happning-right w-6 p-8\">\r\n            <div class=\"what-happning-title\">\r\n                <h5 class=\"line-2 m-0 mb-5 pb-5 relative inline-flex w-fit text-yellow-400\">Join the fun in our city!\r\n                </h5>\r\n                <h2 class=\"m-0 mb-6 relative text-6xl line-height-2 font-bold text-color\">What's Happening</h2>\r\n            </div>\r\n            <div class=\"what-happning-list\">\r\n                <div\r\n                    class=\"what-happning-box mb-4 pb-4 flex align-items-center gap-5 border-none border-bottom-1 border-solid surface-border\">\r\n                    <div class=\"wh-img-box flex align-items-center justify-content-center overflow-hidden\">\r\n                        <img src=\"/assets/layout/images/arches-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                    </div>\r\n                    <div class=\"wh-cnt-sec flex-1 flex align-items-center gap-6\">\r\n                        <div class=\"wh-cnt flex flex-column\">\r\n                            <h3 class=\"text-xl font-bold\">Heritage 10th Anniversary</h3>\r\n                            <div class=\"flex align-items-center gap-3 flex-wrap\">\r\n                                <div class=\"flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100\">\r\n                                    <span>June 9, 2027</span> - <span>June 18, 2027</span>\r\n                                </div>\r\n                                <div class=\"flex align-items-center gap-2\">All Day at <b>Baker Hall</b></div>\r\n                            </div>\r\n                        </div>\r\n                        <button type=\"button\"\r\n                            class=\"px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold\">\r\n                            Find out more\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div\r\n                    class=\"what-happning-box mb-4 pb-4 flex align-items-center gap-5 border-none border-bottom-1 border-solid surface-border\">\r\n                    <div class=\"wh-img-box flex align-items-center justify-content-center overflow-hidden\">\r\n                        <img src=\"/assets/layout/images/arches-2.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                    </div>\r\n                    <div class=\"wh-cnt-sec flex-1 flex align-items-center gap-6\">\r\n                        <div class=\"wh-cnt flex flex-column\">\r\n                            <h3 class=\"text-xl font-bold\">Along Pines Run</h3>\r\n                            <div class=\"flex align-items-center gap-3 flex-wrap\">\r\n                                <div class=\"flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100\">\r\n                                    <span>July 22, 2027</span>\r\n                                </div>\r\n                                <div class=\"flex align-items-center gap-2\">12:00 am at <b>Boulder City Council</b></div>\r\n                            </div>\r\n                        </div>\r\n                        <button type=\"button\"\r\n                            class=\"px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold\">\r\n                            Find out more\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div class=\"what-happning-box flex align-items-center gap-5\">\r\n                    <div class=\"wh-img-box flex align-items-center justify-content-center overflow-hidden\">\r\n                        <img src=\"/assets/layout/images/arches-3.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                    </div>\r\n                    <div class=\"wh-cnt-sec flex-1 flex align-items-center gap-6\">\r\n                        <div class=\"wh-cnt flex flex-column\">\r\n                            <h3 class=\"text-xl font-bold\">Touch A Truck</h3>\r\n                            <div class=\"flex align-items-center gap-3 flex-wrap\">\r\n                                <div class=\"flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100\">\r\n                                    <span>June 9, 2027</span> - <span>June 18, 2027</span>\r\n                                </div>\r\n                                <div class=\"flex align-items-center gap-2\">9:00 am - 5:00 pm at <b>Baker Hall</b></div>\r\n                            </div>\r\n                        </div>\r\n                        <button type=\"button\"\r\n                            class=\"px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold\">\r\n                            Find out more\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--WHAT HAPPNENING SEC-->\r\n\r\n<!--FOOTER SEC-->\r\n<section class=\"footer-sec relative secondary-bg-color\">\r\n    <div class=\"footer-body relative max-w-1200 w-full mx-auto px-4\">\r\n        <div class=\"top-footer py-7 grid mt-0\">\r\n            <div class=\"col-12 lg:col-5 md:col-5\">\r\n                <h3 class=\"line flex mb-5 pb-3 relative text-white text-xl\">Riverside City Hall</h3>\r\n                <ul class=\"p-0 m-0 flex flex-column gap-4\">\r\n                    <li class=\"flex w-full\">\r\n                        <a href=\"#\" class=\"flex w-full text-white\">8353 Sierra Avenue • Riverside, CA 91335</a>\r\n                    </li>\r\n                    <li class=\"flex w-full\">\r\n                        <a href=\"#\" class=\"flex w-full text-white\">Phone: (*************</a>\r\n                    </li>\r\n                    <li class=\"flex w-full\">\r\n                        <a class=\"flex w-full text-white\">Monday - Thursday, 8:00 am - 6:00 pm</a>\r\n                    </li>\r\n                    <li class=\"flex w-full\">\r\n                        <a href=\"\" class=\"flex w-full text-white\">Email : info&#64;asardigital.com</a>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n            <div class=\"col-12 lg:col-7 md:col-7 py-0\">\r\n                <div class=\"grid mt-0\">\r\n                    <div class=\"col-12 lg:col-4 md:col-4\">\r\n                        <h3 class=\"line flex mb-5 pb-3 relative text-white text-xl\">Services</h3>\r\n                        <ul class=\"p-0 m-0 flex flex-column gap-4\">\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"#\" class=\"flex w-full text-white\">Driver & ID Services</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"#\" class=\"flex w-full text-white\">Vehicle & Plate Services</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a class=\"flex w-full text-white\">Business Services</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"\" class=\"flex w-full text-white\">Senior Services</a>\r\n                            </li>\r\n                        </ul>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-4 md:col-4\">\r\n                        <h3 class=\"line flex mb-5 pb-3 relative text-white text-xl\">Useful Links</h3>\r\n                        <ul class=\"p-0 m-0 flex flex-column gap-4\">\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"#\" class=\"flex w-full text-white\">Frequently Asked Questions</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"#\" class=\"flex w-full text-white\">Latest News</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a class=\"flex w-full text-white\">Community</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"\" class=\"flex w-full text-white\">Help Center</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"\" class=\"flex w-full text-white\">Careers</a>\r\n                            </li>\r\n                        </ul>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"middle-footer py-5 w-full flex align-items-center justify-content-center bg-blue-100\">\r\n\r\n        </div>\r\n        <div class=\"bottom-footer py-5 w-full flex flex-column align-items-center justify-content-center\">\r\n            <ul class=\"m-0 mb-3 p-0 flex align-items-center justify-content-center gap-3 list-none\">\r\n                <li><a href=\"#\" class=\"inline-flex w-fit text-white\">Term & Conditions</a></li>\r\n                <li><a href=\"#\" class=\"inline-flex w-fit text-white\">Privacy Policy</a></li>\r\n            </ul>\r\n            <p class=\"m-0 text-white\">© 2025 SNJYA. All rights reserved.</p>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--FOOTER SEC-->"], "mappings": ";;;;;;;;ICUwBA,EADJ,CAAAC,cAAA,cAAoD,aAGiC;IAC7ED,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,gBAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAEvFF,EAFuF,CAAAG,YAAA,EAAO,EACtF,EACH;;;;IANEH,EAAA,CAAAI,SAAA,EAA6B;IAC7BJ,EADA,CAAAK,UAAA,SAAAC,WAAA,CAAAC,IAAA,SAAAP,EAAA,CAAAQ,aAAA,CAA6B,WAAAF,WAAA,CAAAG,MAAA,YACQ;IAEpCT,EAAA,CAAAI,SAAA,EACA;IADAJ,EAAA,CAAAU,kBAAA,MAAAJ,WAAA,CAAAK,KAAA,MACA;IAAuDX,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAY,iBAAA,CAAAN,WAAA,CAAAO,SAAA,CAAwB;;;ADJ3G,OAAM,MAAOC,aAAa;EASxBC,YACUC,aAA4B,EAC5BC,QAAmB,EACnBC,KAAqB,EACrBC,UAA0B;IAH1B,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IATpB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,YAAY,GAAU,EAAE;EAOpB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACC,IAAI,CAAC,eAAe,CAAC;IAC9DC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACJ,aAAa,CAAC;IAElD;IACA,MAAMK,aAAa,GAAG,IAAI,CAACX,UAAU,CAACY,sBAAsB,CAAC,IAAI,CAACN,aAAa,CAACO,IAAI,EAAE,oBAAoB,CAAC;IAC3G,IAAIF,aAAa,EAAEG,MAAM,EAAE;MACzB,IAAI,CAACb,IAAI,GAAGU,aAAa,CAAC,CAAC,CAAC,CAACI,IAAI,EAAEC,GAAG,IAAI,EAAE;IAC9C;IAEA;IACA,MAAMC,aAAa,GAAG,IAAI,CAACjB,UAAU,CAACY,sBAAsB,CAAC,IAAI,CAACN,aAAa,CAACO,IAAI,EAAE,oBAAoB,CAAC;IAC3G,IAAII,aAAa,EAAEH,MAAM,EAAE;MACzB,IAAI,CAACZ,SAAS,GAAGe,aAAa,CAAC,CAAC,CAAC,CAACC,SAAS,IAAI,EAAE;IACnD;IAEA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACpB,KAAK,CAACQ,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;IAClDC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACS,OAAO,CAAC;IAE1C;IACA,MAAMC,eAAe,GAAG,IAAI,CAACpB,UAAU,CAACY,sBAAsB,CAAC,IAAI,CAACO,OAAO,CAACN,IAAI,EAAE,sBAAsB,CAAC;IACzG,IAAIO,eAAe,EAAEN,MAAM,EAAE;MAC3B,IAAI,CAACX,UAAU,GAAGiB,eAAe,CAAC,CAAC,CAAC;IACtC;IAEA;IACA,MAAMC,kBAAkB,GAAG,IAAI,CAACrB,UAAU,CAACY,sBAAsB,CAAC,IAAI,CAACO,OAAO,CAACN,IAAI,EAAE,wBAAwB,CAAC;IAC9G,IAAIQ,kBAAkB,EAAEP,MAAM,EAAE;MAC9B,IAAI,CAACV,YAAY,GAAGiB,kBAAkB;IACxC;IAEAZ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,IAAI,CAACT,IAAI,CAAC;IAC/BQ,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACR,SAAS,CAAC;IAC1CO,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACP,UAAU,CAAC;IAC5CM,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACN,YAAY,CAAC;IAEhD;IACA,MAAMkB,IAAI,GAAG,wDAAwD;IACrE,MAAMC,IAAI,GAAG,IAAI,CAACzB,QAAQ,CAAC0B,aAAa,CAAC,MAAM,CAAC;IAChD,IAAI,CAAC1B,QAAQ,CAAC2B,YAAY,CAACF,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC;IACpD,IAAI,CAACzB,QAAQ,CAAC2B,YAAY,CAACF,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC;IACrD,IAAI,CAACzB,QAAQ,CAAC2B,YAAY,CAACF,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC;IACpD,IAAI,CAACzB,QAAQ,CAAC2B,YAAY,CAACF,IAAI,EAAE,MAAM,EAAED,IAAI,CAAC;IAE9C;IACA,IAAI,CAACxB,QAAQ,CAAC4B,WAAW,CAACC,QAAQ,CAACC,IAAI,EAAEL,IAAI,CAAC;IAE9C,IAAI,CAAC1B,aAAa,CAACgC,MAAM,GAAG,IAAI,CAAC,CAAC;EACpC;EAEA;EACAC,0BAA0BA,CAACC,eAAuB;IAChD,IAAI,CAACA,eAAe,EAAE;MACpB,OAAO;;;;;;;;;oBASO;IAChB;IAEA;IACA,IAAIC,SAAS,GAAGD,eAAe,CAC5BE,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;IAAA,CAC9BA,OAAO,CAAC,gBAAgB,EAAE,kDAAkD,CAAC,CAAC;IAAA,CAC9EA,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;IAAA,CACzBA,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IAAA,CACtBA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;IAAA,CAC3BA,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;IAAA,CAC5BA,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC;IAE7B;IACAD,SAAS,GAAGA,SAAS,CAACC,OAAO,CAAC,wDAAwD,EACpF,0GAA0G,CAAC;IAE7G,OAAOD,SAAS;EAClB;EAEA;EACAE,qBAAqBA,CAACC,KAAa;IACjC,MAAMC,YAAY,GAAG,CACnB,uCAAuC,EACvC,wCAAwC,EACxC,6CAA6C,EAC7C,2CAA2C,CAC5C;IACD,OAAOA,YAAY,CAACD,KAAK,CAAC,IAAI,uCAAuC;EACvE;EAEAE,WAAWA,CAAA;IACT;IACA,MAAMd,IAAI,GAAGI,QAAQ,CAACW,cAAc,CAAC,YAAY,CAAC;IAClD,IAAIf,IAAI,EAAE;MACRA,IAAI,CAACgB,MAAM,EAAE;IACf;EACF;;;uBAnHW5C,aAAa,EAAAd,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA7D,EAAA,CAAA2D,iBAAA,CAAA3D,EAAA,CAAA8D,SAAA,GAAA9D,EAAA,CAAA2D,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAhE,EAAA,CAAA2D,iBAAA,CAAAM,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAbpD,aAAa;MAAAqD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPlBzE,EAFR,CAAAC,cAAA,gBAA4D,aACiC,aACY;UAC7FD,EAAA,CAAA2E,SAAA,aAA8G;UAClH3E,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,aAAkG,aACvE,YAC+B;UAC9CD,EAAA,CAAA4E,UAAA,IAAAC,2BAAA,gBAAoD;UAS5D7E,EADI,CAAAG,YAAA,EAAK,EACH;UAGFH,EAFJ,CAAAC,cAAA,gBACmL,cAChI;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,eAChE;UAGZF,EAHY,CAAAG,YAAA,EAAS,EACP,EACJ,EACD;UAKLH,EADJ,CAAAC,cAAA,mBAAqC,eACuB;UACpDD,EAAA,CAAA2E,SAAA,eAGkD;UAE9C3E,EADJ,CAAAC,cAAA,eAA6F,cACxD;UAAAD,EAAA,CAAAE,MAAA,IAA8D;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpGH,EAAA,CAAAC,cAAA,cAAsE;UAAAD,EAAA,CAAAE,MAAA,IAA+D;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1IH,EAAA,CAAAC,cAAA,aAAmC;UAAAD,EAAA,CAAAE,MAAA,IAA8K;UAG7NF,EAH6N,CAAAG,YAAA,EAAI,EACnN,EACJ,EACA;UAMFH,EAFR,CAAAC,cAAA,mBAAqC,eACuC,eACA;UAChED,EAAA,CAAA2E,SAAA,eAA+I;UAC/I3E,EAAA,CAAAC,cAAA,cAAiC;UAAAD,EAAA,CAAAE,MAAA,IAAsD;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5FH,EAAA,CAAA2E,SAAA,eAAkF;UA6EtF3E,EAFR,CAAAC,cAAA,mBAAoC,eAC+C,eACxB;UAC/CD,EAAA,CAAA2E,SAAA,eAA0E;UAC9E3E,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,eAA8F,cACzD;UAAAD,EAAA,CAAAE,MAAA,oCAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClEH,EAAA,CAAAC,cAAA,cAAoF;UAAAD,EAAA,CAAAE,MAAA,oCAEpF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAE,MAAA,sTAG2D;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAExGH,EAAA,CAAAC,cAAA,kBACgL;UAC5KD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAC,cAAA,gBAAuC;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAIjFF,EAJiF,CAAAG,YAAA,EAAO,EACnE,EACP,EACJ,EACA;UAOEH,EAHZ,CAAAC,cAAA,mBAA2C,eAC+C,eACJ,eACjD;UACrBD,EAAA,CAAA2E,SAAA,eAAyE;UAC7E3E,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,eAAqB,cACgB;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzDH,EAAA,CAAAC,cAAA,cAAoF;UAAAD,EAAA,CAAAE,MAAA,6DAEpD;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAE,MAAA,sTAI2D;UAE5GF,EAF4G,CAAAG,YAAA,EAAI,EACtG,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAyE,eAC3C,cACmD;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAC,cAAA,eACnB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAMF,EAAN,CAAAG,YAAA,EAAM,EAAK;UACrEH,EAAA,CAAAC,cAAA,cAAsB;UAAAD,EAAA,CAAAE,MAAA,0CAAkC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7DH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAE,MAAA,mGACN;UAC9BF,EAD8B,CAAAG,YAAA,EAAI,EAC5B;UAEFH,EADJ,CAAAC,cAAA,eAA0B,cACoD;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAC,cAAA,eACnB;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAMF,EAAN,CAAAG,YAAA,EAAM,EAAK;UACtEH,EAAA,CAAAC,cAAA,cAAsB;UAAAD,EAAA,CAAAE,MAAA,6CAAqC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChEH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAE,MAAA,mGACN;UAI1CF,EAJ0C,CAAAG,YAAA,EAAI,EAC5B,EACJ,EACJ,EACA;UAOEH,EAHZ,CAAAC,cAAA,mBAA2C,eAC+C,eACf,cACe;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/FH,EAAA,CAAAC,cAAA,cAA4G;UACxGD,EAAA,CAAAE,MAAA,+CAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG3CH,EADJ,CAAAC,cAAA,eAA6C,eAEwE;UAC7GD,EAAA,CAAA2E,SAAA,eAA2E;UAC3E3E,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5DH,EAAA,CAAAC,cAAA,aAAmB;UAAAD,EAAA,CAAAE,MAAA,yGACI;UAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;UACNH,EAAA,CAAAC,cAAA,eAC6H;UACzHD,EAAA,CAAA2E,SAAA,eAA4E;UAC5E3E,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3DH,EAAA,CAAAC,cAAA,aAAmB;UAAAD,EAAA,CAAAE,MAAA,yGACI;UAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;UACNH,EAAA,CAAAC,cAAA,eAC6H;UACzHD,EAAA,CAAA2E,SAAA,eAAgF;UAChF3E,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzDH,EAAA,CAAAC,cAAA,aAAmB;UAAAD,EAAA,CAAAE,MAAA,0GACI;UAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;UACNH,EAAA,CAAAC,cAAA,gBAA+F;UAC3FD,EAAA,CAAA2E,SAAA,gBAA8E;UAC9E3E,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,cAAmB;UAAAD,EAAA,CAAAE,MAAA,0GACI;UAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;UACNH,EAAA,CAAAC,cAAA,gBAC6I;UACzID,EAAA,CAAA2E,SAAA,gBAA8E;UAC9E3E,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,yBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3DH,EAAA,CAAAC,cAAA,cAAmB;UAAAD,EAAA,CAAAE,MAAA,0GACI;UAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;UACNH,EAAA,CAAAC,cAAA,gBAC6I;UACzID,EAAA,CAAA2E,SAAA,gBAAiF;UACjF3E,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,sCAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxEH,EAAA,CAAAC,cAAA,cAAmB;UAAAD,EAAA,CAAAE,MAAA,0GACI;UAGnCF,EAHmC,CAAAG,YAAA,EAAI,EACzB,EACJ,EACJ;UACNH,EAAA,CAAA2E,SAAA,gBAAsC;UAE9C3E,EADI,CAAAG,YAAA,EAAM,EACA;UAOEH,EAHZ,CAAAC,cAAA,oBAAgE,gBACG,gBACnC,eACsD;UAAAD,EAAA,CAAAE,MAAA,yDAC9D;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAC,cAAA,eAA0E;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UACzFF,EADyF,CAAAG,YAAA,EAAK,EACxF;UAGEH,EAFR,CAAAC,cAAA,gBAA2C,gBACO,gBAC0C;UAChFD,EAAA,CAAA2E,SAAA,gBAA6F;UACjG3E,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,6CAAoC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGvEH,EAFR,CAAAC,cAAA,gBAAgD,gBACmC,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,6BACzE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,gBAA+E,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,cACrE;UACJF,EADI,CAAAG,YAAA,EAAM,EACJ;UACNH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,sJAEoC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE3EH,EAAA,CAAAC,cAAA,mBACqL;UACjLD,EAAA,CAAAE,MAAA,qBAAW;UAAAF,EAAA,CAAAC,cAAA,iBAAuC;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAEzEF,EAFyE,CAAAG,YAAA,EAAO,EACnE,EACP;UAEFH,EADJ,CAAAC,cAAA,gBAA8C,gBAC0C;UAChFD,EAAA,CAAA2E,SAAA,gBAA6F;UACjG3E,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,mDAA0C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG7EH,EAFR,CAAAC,cAAA,gBAAgD,gBACmC,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,6BACzE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,gBAA+E,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,cACrE;UACJF,EADI,CAAAG,YAAA,EAAM,EACJ;UACNH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,sJAEoC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE3EH,EAAA,CAAAC,cAAA,mBACqL;UACjLD,EAAA,CAAAE,MAAA,qBAAW;UAAAF,EAAA,CAAAC,cAAA,iBAAuC;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAEzEF,EAFyE,CAAAG,YAAA,EAAO,EACnE,EACP;UAEFH,EADJ,CAAAC,cAAA,gBAA8C,gBAC0C;UAChFD,EAAA,CAAA2E,SAAA,gBAA6F;UACjG3E,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,iDAAwC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG3EH,EAFR,CAAAC,cAAA,gBAAgD,gBACmC,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,6BACzE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,gBAA+E,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,cACrE;UACJF,EADI,CAAAG,YAAA,EAAM,EACJ;UACNH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,sJAEoC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE3EH,EAAA,CAAAC,cAAA,mBACqL;UACjLD,EAAA,CAAAE,MAAA,qBAAW;UAAAF,EAAA,CAAAC,cAAA,iBAAuC;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAKrFF,EALqF,CAAAG,YAAA,EAAO,EACnE,EACP,EACJ,EACJ,EACA;UAQMH,EAJhB,CAAAC,cAAA,oBAA0C,gBACgC,gBACT,gBACwB,eAEuC;UAC5GD,EAAA,CAAAE,MAAA,mCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClCH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,6GACH;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,gBAAoE;UAChED,EAAA,CAAA2E,SAAA,kBACuC;UACvC3E,EAAA,CAAAC,cAAA,mBACqL;UACjLD,EAAA,CAAAE,MAAA,kBACJ;UAERF,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;UACNH,EAAA,CAAAC,cAAA,gBAAgG;UAC5FD,EAAA,CAAA2E,SAAA,gBAAmG;UAInH3E,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACA;UAMFH,EAFR,CAAAC,cAAA,oBAAsD,gBACwB,gBAC4B;UAC9FD,EAAA,CAAA2E,SAAA,gBAAuG;UAC3G3E,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,gBAAyC,gBACJ,eAC+C;UAAAD,EAAA,CAAAE,MAAA,mCAC5E;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,eAA0E;UAAAD,EAAA,CAAAE,MAAA,yBAAgB;UAC9FF,EAD8F,CAAAG,YAAA,EAAK,EAC7F;UAIEH,EAHR,CAAAC,cAAA,gBAAgC,gBAEkG,gBACnC;UACnFD,EAAA,CAAA2E,SAAA,iBAA4F;UAChG3E,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,iBAA6D,iBACpB,gBACH;UAAAD,EAAA,CAAAE,MAAA,kCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGpDH,EAFR,CAAAC,cAAA,iBAAqD,iBACuB,aAC9D;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,YAAE;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UACnDF,EADmD,CAAAG,YAAA,EAAO,EACpD;UACNH,EAAA,CAAAC,cAAA,iBAA2C;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAE3EF,EAF2E,CAAAG,YAAA,EAAI,EAAM,EAC3E,EACJ;UACNH,EAAA,CAAAC,cAAA,oBAC6K;UACzKD,EAAA,CAAAE,MAAA,wBACJ;UAERF,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;UAGFH,EAFJ,CAAAC,cAAA,gBAC8H,gBACnC;UACnFD,EAAA,CAAA2E,SAAA,iBAA4F;UAChG3E,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,iBAA6D,iBACpB,gBACH;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG1CH,EAFR,CAAAC,cAAA,iBAAqD,iBACuB,aAC9D;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UACvBF,EADuB,CAAAG,YAAA,EAAO,EACxB;UACNH,EAAA,CAAAC,cAAA,iBAA2C;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,6BAAoB;UAEtFF,EAFsF,CAAAG,YAAA,EAAI,EAAM,EACtF,EACJ;UACNH,EAAA,CAAAC,cAAA,oBAC6K;UACzKD,EAAA,CAAAE,MAAA,wBACJ;UAERF,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;UAEFH,EADJ,CAAAC,cAAA,iBAA6D,gBAC8B;UACnFD,EAAA,CAAA2E,SAAA,iBAA4F;UAChG3E,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,iBAA6D,iBACpB,gBACH;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGxCH,EAFR,CAAAC,cAAA,iBAAqD,iBACuB,aAC9D;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,YAAE;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UACnDF,EADmD,CAAAG,YAAA,EAAO,EACpD;UACNH,EAAA,CAAAC,cAAA,iBAA2C;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAErFF,EAFqF,CAAAG,YAAA,EAAI,EAAM,EACrF,EACJ;UACNH,EAAA,CAAAC,cAAA,oBAC6K;UACzKD,EAAA,CAAAE,MAAA,wBACJ;UAMxBF,EANwB,CAAAG,YAAA,EAAS,EACP,EACJ,EACJ,EACJ,EACJ,EACA;UAQMH,EAJhB,CAAAC,cAAA,qBAAwD,iBACa,iBACtB,iBACG,gBAC0B;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG5EH,EAFR,CAAAC,cAAA,gBAA2C,gBACf,eACuB;UAAAD,EAAA,CAAAE,MAAA,sDAAwC;UACvFF,EADuF,CAAAG,YAAA,EAAI,EACtF;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACuB;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UACpEF,EADoE,CAAAG,YAAA,EAAI,EACnE;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACc;UAAAD,EAAA,CAAAE,MAAA,6CAAoC;UAC1EF,EAD0E,CAAAG,YAAA,EAAI,EACzE;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACsB;UAAAD,EAAA,CAAAE,MAAA,qCAAgC;UAGtFF,EAHsF,CAAAG,YAAA,EAAI,EAC7E,EACJ,EACH;UAIMH,EAHZ,CAAAC,cAAA,iBAA2C,iBAChB,iBACmB,gBAC0B;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGjEH,EAFR,CAAAC,cAAA,gBAA2C,gBACf,eACuB;UAAAD,EAAA,CAAAE,MAAA,6BAAoB;UACnEF,EADmE,CAAAG,YAAA,EAAI,EAClE;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACuB;UAAAD,EAAA,CAAAE,MAAA,iCAAwB;UACvEF,EADuE,CAAAG,YAAA,EAAI,EACtE;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACc;UAAAD,EAAA,CAAAE,MAAA,0BAAiB;UACvDF,EADuD,CAAAG,YAAA,EAAI,EACtD;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACsB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAGrEF,EAHqE,CAAAG,YAAA,EAAI,EAC5D,EACJ,EACH;UAEFH,EADJ,CAAAC,cAAA,iBAAsC,gBAC0B;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGrEH,EAFR,CAAAC,cAAA,gBAA2C,gBACf,eACuB;UAAAD,EAAA,CAAAE,MAAA,mCAA0B;UACzEF,EADyE,CAAAG,YAAA,EAAI,EACxE;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACuB;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAC1DF,EAD0D,CAAAG,YAAA,EAAI,EACzD;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACc;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAC/CF,EAD+C,CAAAG,YAAA,EAAI,EAC9C;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACsB;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UACzDF,EADyD,CAAAG,YAAA,EAAI,EACxD;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACsB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAMzEF,EANyE,CAAAG,YAAA,EAAI,EACpD,EACJ,EACH,EACJ,EACJ,EACJ;UACNH,EAAA,CAAA2E,SAAA,iBAEM;UAGM3E,EAFZ,CAAAC,cAAA,iBAAkG,gBACN,WAChF,eAAiD;UAAAD,EAAA,CAAAE,MAAA,0BAAiB;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAK;UAC3EH,EAAJ,CAAAC,cAAA,WAAI,eAAiD;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UACvEF,EADuE,CAAAG,YAAA,EAAI,EAAK,EAC3E;UACLH,EAAA,CAAAC,cAAA,eAA0B;UAAAD,EAAA,CAAAE,MAAA,gDAAkC;UAGxEF,EAHwE,CAAAG,YAAA,EAAI,EAC9D,EACJ,EACA,EA5ckE,EADA,EADvC;;;UAzCpBH,EAAA,CAAAI,SAAA,GAAsE;UAAtEJ,EAAA,CAAAK,UAAA,QAAAqE,GAAA,CAAAtD,IAAA,4DAAApB,EAAA,CAAAQ,aAAA,CAAsE;UAK7BR,EAAA,CAAAI,SAAA,GAAY;UAAZJ,EAAA,CAAAK,UAAA,YAAAqE,GAAA,CAAArD,SAAA,CAAY;UAuBzDrB,EAAA,CAAAI,SAAA,GAA8F;UAE9FJ,EAFA,CAAA8E,WAAA,sBAAAJ,GAAA,CAAApD,UAAA,kBAAAoD,GAAA,CAAApD,UAAA,CAAAyD,KAAA,kBAAAL,GAAA,CAAApD,UAAA,CAAAyD,KAAA,CAAA5C,GAAA,aAAAuC,GAAA,CAAApD,UAAA,CAAAyD,KAAA,CAAA5C,GAAA,cAA8F,4BAC7D,iCACK;UAENnC,EAAA,CAAAI,SAAA,GAA8D;UAA9DJ,EAAA,CAAAY,iBAAA,EAAA8D,GAAA,CAAApD,UAAA,kBAAAoD,GAAA,CAAApD,UAAA,CAAA0D,UAAA,oCAA8D;UACzBhF,EAAA,CAAAI,SAAA,GAA+D;UAA/DJ,EAAA,CAAAY,iBAAA,EAAA8D,GAAA,CAAApD,UAAA,kBAAAoD,GAAA,CAAApD,UAAA,CAAAX,KAAA,0CAA+D;UAClGX,EAAA,CAAAI,SAAA,GAA8K;UAA9KJ,EAAA,CAAAY,iBAAA,EAAA8D,GAAA,CAAApD,UAAA,kBAAAoD,GAAA,CAAApD,UAAA,CAAA2D,WAAA,mJAA8K;UAU5MjF,EAAA,CAAAI,SAAA,GAA6E;UAAsBJ,EAAnG,CAAAK,UAAA,SAAAqE,GAAA,CAAAnD,YAAA,qBAAAmD,GAAA,CAAAnD,YAAA,IAAA2D,IAAA,kBAAAR,GAAA,CAAAnD,YAAA,IAAA2D,IAAA,CAAA/C,GAAA,8CAAAnC,EAAA,CAAAQ,aAAA,CAA6E,SAAAkE,GAAA,CAAAnD,YAAA,qBAAAmD,GAAA,CAAAnD,YAAA,IAAAZ,KAAA,QAA0D;UAC3GX,EAAA,CAAAI,SAAA,GAAsD;UAAtDJ,EAAA,CAAAY,iBAAA,EAAA8D,GAAA,CAAAnD,YAAA,qBAAAmD,GAAA,CAAAnD,YAAA,IAAAZ,KAAA,4BAAsD;UAClFX,EAAA,CAAAI,SAAA,EAAsE;UAAtEJ,EAAA,CAAAK,UAAA,cAAAqE,GAAA,CAAAzB,0BAAA,CAAAyB,GAAA,CAAAnD,YAAA,qBAAAmD,GAAA,CAAAnD,YAAA,IAAA0D,WAAA,GAAAjF,EAAA,CAAAmF,cAAA,CAAsE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}