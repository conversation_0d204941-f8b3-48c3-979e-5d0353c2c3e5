{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class FooterComponent {\n  static {\n    this.ɵfac = function FooterComponent_Factory(t) {\n      return new (t || FooterComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FooterComponent,\n      selectors: [[\"app-footer\"]],\n      decls: 67,\n      vars: 0,\n      consts: [[1, \"footer-sec\", \"relative\", \"secondary-bg-color\"], [1, \"footer-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\"], [1, \"top-footer\", \"py-7\", \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-5\", \"md:col-5\"], [1, \"line\", \"flex\", \"mb-5\", \"pb-3\", \"relative\", \"text-white\", \"text-xl\"], [1, \"p-0\", \"m-0\", \"flex\", \"flex-column\", \"gap-4\"], [1, \"flex\", \"w-full\"], [\"href\", \"#\", 1, \"flex\", \"w-full\", \"text-white\"], [1, \"flex\", \"w-full\", \"text-white\"], [\"href\", \"\", 1, \"flex\", \"w-full\", \"text-white\"], [1, \"col-12\", \"lg:col-7\", \"md:col-7\", \"py-0\"], [1, \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\"], [1, \"middle-footer\", \"py-5\", \"w-full\", \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-100\"], [1, \"bottom-footer\", \"py-5\", \"w-full\", \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [1, \"m-0\", \"mb-3\", \"p-0\", \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-3\", \"list-none\"], [\"href\", \"#\", 1, \"inline-flex\", \"w-fit\", \"text-white\"], [1, \"m-0\", \"text-white\"]],\n      template: function FooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h3\", 4);\n          i0.ɵɵtext(5, \"Riverside City Hall\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"ul\", 5)(7, \"li\", 6)(8, \"a\", 7);\n          i0.ɵɵtext(9, \"8353 Sierra Avenue \\u2022 Riverside, CA 91335\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"li\", 6)(11, \"a\", 7);\n          i0.ɵɵtext(12, \"Phone: (*************\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"li\", 6)(14, \"a\", 8);\n          i0.ɵɵtext(15, \"Monday - Thursday, 8:00 am - 6:00 pm\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"li\", 6)(17, \"a\", 9);\n          i0.ɵɵtext(18, \"Email : <EMAIL>\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(19, \"div\", 10)(20, \"div\", 11)(21, \"div\", 12)(22, \"h3\", 4);\n          i0.ɵɵtext(23, \"Services\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"ul\", 5)(25, \"li\", 6)(26, \"a\", 7);\n          i0.ɵɵtext(27, \"Driver & ID Services\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"li\", 6)(29, \"a\", 7);\n          i0.ɵɵtext(30, \"Vehicle & Plate Services\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"li\", 6)(32, \"a\", 8);\n          i0.ɵɵtext(33, \"Business Services\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"li\", 6)(35, \"a\", 9);\n          i0.ɵɵtext(36, \"Senior Services\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(37, \"div\", 12)(38, \"h3\", 4);\n          i0.ɵɵtext(39, \"Useful Links\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"ul\", 5)(41, \"li\", 6)(42, \"a\", 7);\n          i0.ɵɵtext(43, \"Frequently Asked Questions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"li\", 6)(45, \"a\", 7);\n          i0.ɵɵtext(46, \"Latest News\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"li\", 6)(48, \"a\", 8);\n          i0.ɵɵtext(49, \"Community\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"li\", 6)(51, \"a\", 9);\n          i0.ɵɵtext(52, \"Help Center\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(53, \"li\", 6)(54, \"a\", 9);\n          i0.ɵɵtext(55, \"Careers\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelement(56, \"div\", 13);\n          i0.ɵɵelementStart(57, \"div\", 14)(58, \"ul\", 15)(59, \"li\")(60, \"a\", 16);\n          i0.ɵɵtext(61, \"Term & Conditions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"li\")(63, \"a\", 16);\n          i0.ɵɵtext(64, \"Privacy Policy\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(65, \"p\", 17);\n          i0.ɵɵtext(66, \"\\u00A9 2025 SNJYA. All rights reserved.\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      styles: [\"\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvZm9vdGVyL2Zvb3Rlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxpRUFBQSIsInNvdXJjZXNDb250ZW50IjpbIi8qIEZvb3RlciBjb21wb25lbnQgc3BlY2lmaWMgc3R5bGVzIGNhbiBiZSBhZGRlZCBoZXJlIGlmIG5lZWRlZCAqL1xuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["FooterComponent", "selectors", "decls", "vars", "consts", "template", "FooterComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\shared\\components\\footer\\footer.component.ts", "D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\shared\\components\\footer\\footer.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-footer',\n  templateUrl: './footer.component.html',\n  styleUrl: './footer.component.scss'\n})\nexport class FooterComponent {\n}\n", "<!--FOOTER SEC-->\n<section class=\"footer-sec relative secondary-bg-color\">\n    <div class=\"footer-body relative max-w-1200 w-full mx-auto px-4\">\n        <div class=\"top-footer py-7 grid mt-0\">\n            <div class=\"col-12 lg:col-5 md:col-5\">\n                <h3 class=\"line flex mb-5 pb-3 relative text-white text-xl\">Riverside City Hall</h3>\n                <ul class=\"p-0 m-0 flex flex-column gap-4\">\n                    <li class=\"flex w-full\">\n                        <a href=\"#\" class=\"flex w-full text-white\">8353 Sierra Avenue • Riverside, CA 91335</a>\n                    </li>\n                    <li class=\"flex w-full\">\n                        <a href=\"#\" class=\"flex w-full text-white\">Phone: (*************</a>\n                    </li>\n                    <li class=\"flex w-full\">\n                        <a class=\"flex w-full text-white\">Monday - Thursday, 8:00 am - 6:00 pm</a>\n                    </li>\n                    <li class=\"flex w-full\">\n                        <a href=\"\" class=\"flex w-full text-white\">Email : info&#64;asardigital.com</a>\n                    </li>\n                </ul>\n            </div>\n            <div class=\"col-12 lg:col-7 md:col-7 py-0\">\n                <div class=\"grid mt-0\">\n                    <div class=\"col-12 lg:col-4 md:col-4\">\n                        <h3 class=\"line flex mb-5 pb-3 relative text-white text-xl\">Services</h3>\n                        <ul class=\"p-0 m-0 flex flex-column gap-4\">\n                            <li class=\"flex w-full\">\n                                <a href=\"#\" class=\"flex w-full text-white\">Driver & ID Services</a>\n                            </li>\n                            <li class=\"flex w-full\">\n                                <a href=\"#\" class=\"flex w-full text-white\">Vehicle & Plate Services</a>\n                            </li>\n                            <li class=\"flex w-full\">\n                                <a class=\"flex w-full text-white\">Business Services</a>\n                            </li>\n                            <li class=\"flex w-full\">\n                                <a href=\"\" class=\"flex w-full text-white\">Senior Services</a>\n                            </li>\n                        </ul>\n                    </div>\n                    <div class=\"col-12 lg:col-4 md:col-4\">\n                        <h3 class=\"line flex mb-5 pb-3 relative text-white text-xl\">Useful Links</h3>\n                        <ul class=\"p-0 m-0 flex flex-column gap-4\">\n                            <li class=\"flex w-full\">\n                                <a href=\"#\" class=\"flex w-full text-white\">Frequently Asked Questions</a>\n                            </li>\n                            <li class=\"flex w-full\">\n                                <a href=\"#\" class=\"flex w-full text-white\">Latest News</a>\n                            </li>\n                            <li class=\"flex w-full\">\n                                <a class=\"flex w-full text-white\">Community</a>\n                            </li>\n                            <li class=\"flex w-full\">\n                                <a href=\"\" class=\"flex w-full text-white\">Help Center</a>\n                            </li>\n                            <li class=\"flex w-full\">\n                                <a href=\"\" class=\"flex w-full text-white\">Careers</a>\n                            </li>\n                        </ul>\n                    </div>\n                </div>\n            </div>\n        </div>\n        <div class=\"middle-footer py-5 w-full flex align-items-center justify-content-center bg-blue-100\">\n\n        </div>\n        <div class=\"bottom-footer py-5 w-full flex flex-column align-items-center justify-content-center\">\n            <ul class=\"m-0 mb-3 p-0 flex align-items-center justify-content-center gap-3 list-none\">\n                <li><a href=\"#\" class=\"inline-flex w-fit text-white\">Term & Conditions</a></li>\n                <li><a href=\"#\" class=\"inline-flex w-fit text-white\">Privacy Policy</a></li>\n            </ul>\n            <p class=\"m-0 text-white\">© 2025 SNJYA. All rights reserved.</p>\n        </div>\n    </div>\n</section>\n<!--FOOTER SEC-->\n"], "mappings": ";AAOA,OAAM,MAAOA,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCFZE,EAJhB,CAAAC,cAAA,iBAAwD,aACa,aACtB,aACG,YAC0B;UAAAD,EAAA,CAAAE,MAAA,0BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG5EH,EAFR,CAAAC,cAAA,YAA2C,YACf,WACuB;UAAAD,EAAA,CAAAE,MAAA,oDAAwC;UACvFF,EADuF,CAAAG,YAAA,EAAI,EACtF;UAEDH,EADJ,CAAAC,cAAA,aAAwB,YACuB;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UACpEF,EADoE,CAAAG,YAAA,EAAI,EACnE;UAEDH,EADJ,CAAAC,cAAA,aAAwB,YACc;UAAAD,EAAA,CAAAE,MAAA,4CAAoC;UAC1EF,EAD0E,CAAAG,YAAA,EAAI,EACzE;UAEDH,EADJ,CAAAC,cAAA,aAAwB,YACsB;UAAAD,EAAA,CAAAE,MAAA,oCAAgC;UAGtFF,EAHsF,CAAAG,YAAA,EAAI,EAC7E,EACJ,EACH;UAIMH,EAHZ,CAAAC,cAAA,eAA2C,eAChB,eACmB,aAC0B;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGjEH,EAFR,CAAAC,cAAA,aAA2C,aACf,YACuB;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UACnEF,EADmE,CAAAG,YAAA,EAAI,EAClE;UAEDH,EADJ,CAAAC,cAAA,aAAwB,YACuB;UAAAD,EAAA,CAAAE,MAAA,gCAAwB;UACvEF,EADuE,CAAAG,YAAA,EAAI,EACtE;UAEDH,EADJ,CAAAC,cAAA,aAAwB,YACc;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UACvDF,EADuD,CAAAG,YAAA,EAAI,EACtD;UAEDH,EADJ,CAAAC,cAAA,aAAwB,YACsB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAGrEF,EAHqE,CAAAG,YAAA,EAAI,EAC5D,EACJ,EACH;UAEFH,EADJ,CAAAC,cAAA,eAAsC,aAC0B;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGrEH,EAFR,CAAAC,cAAA,aAA2C,aACf,YACuB;UAAAD,EAAA,CAAAE,MAAA,kCAA0B;UACzEF,EADyE,CAAAG,YAAA,EAAI,EACxE;UAEDH,EADJ,CAAAC,cAAA,aAAwB,YACuB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAC1DF,EAD0D,CAAAG,YAAA,EAAI,EACzD;UAEDH,EADJ,CAAAC,cAAA,aAAwB,YACc;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAC/CF,EAD+C,CAAAG,YAAA,EAAI,EAC9C;UAEDH,EADJ,CAAAC,cAAA,aAAwB,YACsB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACzDF,EADyD,CAAAG,YAAA,EAAI,EACxD;UAEDH,EADJ,CAAAC,cAAA,aAAwB,YACsB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAMzEF,EANyE,CAAAG,YAAA,EAAI,EACpD,EACJ,EACH,EACJ,EACJ,EACJ;UACNH,EAAA,CAAAI,SAAA,eAEM;UAGMJ,EAFZ,CAAAC,cAAA,eAAkG,cACN,UAChF,aAAiD;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAK;UAC3EH,EAAJ,CAAAC,cAAA,UAAI,aAAiD;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UACvEF,EADuE,CAAAG,YAAA,EAAI,EAAK,EAC3E;UACLH,EAAA,CAAAC,cAAA,aAA0B;UAAAD,EAAA,CAAAE,MAAA,+CAAkC;UAGxEF,EAHwE,CAAAG,YAAA,EAAI,EAC9D,EACJ,EACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}