{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Component, ViewEncapsulation, Input, Output, ViewChild, signal, effect, PLATFORM_ID, ChangeDetectionStrategy, Inject, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i5 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport { BarsIcon } from 'primeng/icons/bars';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport { Subject, interval } from 'rxjs';\nimport { debounce, filter } from 'rxjs/operators';\nconst _c0 = [\"menubar\"];\nconst _c1 = (a0, a1) => ({\n  \"p-submenu-list\": a0,\n  \"p-menubar-root-list\": a1\n});\nconst _c2 = a0 => ({\n  \"p-menuitem-link\": true,\n  \"p-disabled\": a0\n});\nconst _c3 = () => ({\n  exact: false\n});\nconst _c4 = a0 => ({\n  $implicit: a0\n});\nfunction MenubarSub_ng_template_2_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 8);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleMap(ctx_r2.getItemProp(processedItem_r2, \"style\"));\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getSeparatorItemClass(processedItem_r2));\n    i0.ɵɵattribute(\"id\", ctx_r2.getItemId(processedItem_r2))(\"data-pc-section\", \"separator\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"icon\"))(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"aria-hidden\", true)(\"tabindex\", -1);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getItemLabel(processedItem_r2), \" \");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getItemLabel(processedItem_r2), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"badgeStyleClass\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleDownIcon_1_Template, 1, 3, \"AngleDownIcon\", 24)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleRightIcon_2_Template, 1, 3, \"AngleRightIcon\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.root);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.root);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\", 26);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Template, 3, 2, \"ng-container\", 11)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template, 1, 2, null, 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(5);\n    const menubar_r5 = i0.ɵɵreference(1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !menubar_r5.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", menubar_r5.submenuIconTemplate);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 15);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_1_Template, 1, 5, \"span\", 16)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_2_Template, 2, 2, \"span\", 17)(3, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template, 1, 2, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(5, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_5_Template, 2, 2, \"span\", 18)(6, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template, 3, 2, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r6 = i0.ɵɵreference(4);\n    const processedItem_r2 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", ctx_r2.getItemProp(processedItem_r2, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(12, _c2, ctx_r2.getItemProp(processedItem_r2, \"disabled\")));\n    i0.ɵɵattribute(\"href\", ctx_r2.getItemProp(processedItem_r2, \"url\"), i0.ɵɵsanitizeUrl)(\"aria-hidden\", true)(\"data-automationid\", ctx_r2.getItemProp(processedItem_r2, \"automationId\"))(\"data-pc-section\", \"action\")(\"tabindex\", -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"escape\"))(\"ngIfElse\", htmlLabel_r6);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemGroup(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 19);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"icon\"))(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"iconStyle\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\")(\"aria-hidden\", true)(\"tabindex\", -1);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemLabel(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.getItemLabel(processedItem_r2), i0.ɵɵsanitizeHtml);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getItemProp(processedItem_r2, \"badgeStyleClass\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 25);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleDownIcon_1_Template, 1, 3, \"AngleDownIcon\", 24)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleRightIcon_2_Template, 1, 3, \"AngleRightIcon\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.root);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.root);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template(rf, ctx) {}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template, 0, 0, \"ng-template\", 26);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"data-pc-section\", \"submenuicon\")(\"aria-hidden\", true);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_Template, 3, 2, \"ng-container\", 11)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template, 1, 2, null, 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(5);\n    const menubar_r5 = i0.ɵɵreference(1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !menubar_r5.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", menubar_r5.submenuIconTemplate);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 27);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_1_Template, 1, 5, \"span\", 16)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_2_Template, 2, 1, \"span\", 17)(3, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template, 1, 2, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(5, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_5_Template, 2, 2, \"span\", 18)(6, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template, 3, 2, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlRouteLabel_r7 = i0.ɵɵreference(4);\n    const processedItem_r2 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", ctx_r2.getItemProp(processedItem_r2, \"routerLink\"))(\"queryParams\", ctx_r2.getItemProp(processedItem_r2, \"queryParams\"))(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", ctx_r2.getItemProp(processedItem_r2, \"routerLinkActiveOptions\") || i0.ɵɵpureFunction0(21, _c3))(\"target\", ctx_r2.getItemProp(processedItem_r2, \"target\"))(\"ngClass\", i0.ɵɵpureFunction1(22, _c2, ctx_r2.getItemProp(processedItem_r2, \"disabled\")))(\"fragment\", ctx_r2.getItemProp(processedItem_r2, \"fragment\"))(\"queryParamsHandling\", ctx_r2.getItemProp(processedItem_r2, \"queryParamsHandling\"))(\"preserveFragment\", ctx_r2.getItemProp(processedItem_r2, \"preserveFragment\"))(\"skipLocationChange\", ctx_r2.getItemProp(processedItem_r2, \"skipLocationChange\"))(\"replaceUrl\", ctx_r2.getItemProp(processedItem_r2, \"replaceUrl\"))(\"state\", ctx_r2.getItemProp(processedItem_r2, \"state\"));\n    i0.ɵɵattribute(\"data-automationid\", ctx_r2.getItemProp(processedItem_r2, \"automationId\"))(\"tabindex\", -1)(\"aria-hidden\", true)(\"data-pc-section\", \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"escape\"))(\"ngIfElse\", htmlRouteLabel_r7);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"badge\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemGroup(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_3_a_1_Template, 7, 14, \"a\", 13)(2, MenubarSub_ng_template_2_li_1_ng_container_3_a_2_Template, 7, 24, \"a\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.getItemProp(processedItem_r2, \"routerLink\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getItemProp(processedItem_r2, \"routerLink\"));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template(rf, ctx) {}\nfunction MenubarSub_ng_template_2_li_1_ng_container_4_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenubarSub_ng_template_2_li_1_ng_container_4_1_Template, 1, 0, null, 28);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, processedItem_r2.item));\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-menubarSub\", 29);\n    i0.ɵɵlistener(\"itemClick\", function MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template_p_menubarSub_itemClick_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.itemClick.emit($event));\n    })(\"itemMouseEnter\", function MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template_p_menubarSub_itemMouseEnter_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onItemMouseEnter($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r2 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"itemTemplate\", ctx_r2.itemTemplate)(\"items\", processedItem_r2.items)(\"mobileActive\", ctx_r2.mobileActive)(\"autoDisplay\", ctx_r2.autoDisplay)(\"menuId\", ctx_r2.menuId)(\"activeItemPath\", ctx_r2.activeItemPath)(\"focusedItemId\", ctx_r2.focusedItemId)(\"level\", ctx_r2.level + 1);\n  }\n}\nfunction MenubarSub_ng_template_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 9, 1)(2, \"div\", 10);\n    i0.ɵɵlistener(\"click\", function MenubarSub_ng_template_2_li_1_Template_div_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemClick($event, processedItem_r2));\n    })(\"mouseenter\", function MenubarSub_ng_template_2_li_1_Template_div_mouseenter_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const processedItem_r2 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemMouseEnter({\n        $event: $event,\n        processedItem: processedItem_r2\n      }));\n    });\n    i0.ɵɵtemplate(3, MenubarSub_ng_template_2_li_1_ng_container_3_Template, 3, 2, \"ng-container\", 11)(4, MenubarSub_ng_template_2_li_1_ng_container_4_Template, 2, 4, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template, 1, 8, \"p-menubarSub\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    const processedItem_r2 = ctx_r8.$implicit;\n    const index_r10 = ctx_r8.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.getItemProp(processedItem_r2, \"styleClass\"));\n    i0.ɵɵproperty(\"ngStyle\", ctx_r2.getItemProp(processedItem_r2, \"style\"))(\"ngClass\", ctx_r2.getItemClass(processedItem_r2))(\"tooltipOptions\", ctx_r2.getItemProp(processedItem_r2, \"tooltipOptions\"));\n    i0.ɵɵattribute(\"id\", ctx_r2.getItemId(processedItem_r2))(\"data-pc-section\", \"menuitem\")(\"data-p-highlight\", ctx_r2.isItemActive(processedItem_r2))(\"data-p-focused\", ctx_r2.isItemFocused(processedItem_r2))(\"data-p-disabled\", ctx_r2.isItemDisabled(processedItem_r2))(\"aria-label\", ctx_r2.getItemLabel(processedItem_r2))(\"aria-disabled\", ctx_r2.isItemDisabled(processedItem_r2) || undefined)(\"aria-haspopup\", ctx_r2.isItemGroup(processedItem_r2) && !ctx_r2.getItemProp(processedItem_r2, \"to\") ? \"menu\" : undefined)(\"aria-expanded\", ctx_r2.isItemGroup(processedItem_r2) ? ctx_r2.isItemActive(processedItem_r2) : undefined)(\"aria-level\", ctx_r2.level + 1)(\"aria-setsize\", ctx_r2.getAriaSetSize())(\"aria-posinset\", ctx_r2.getAriaPosInset(index_r10));\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r2) && ctx_r2.isItemGroup(processedItem_r2));\n  }\n}\nfunction MenubarSub_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MenubarSub_ng_template_2_li_0_Template, 1, 5, \"li\", 6)(1, MenubarSub_ng_template_2_li_1_Template, 6, 21, \"li\", 7);\n  }\n  if (rf & 2) {\n    const processedItem_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r2) && ctx_r2.getItemProp(processedItem_r2, \"separator\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isItemVisible(processedItem_r2) && !ctx_r2.getItemProp(processedItem_r2, \"separator\"));\n  }\n}\nconst _c5 = [\"menubutton\"];\nconst _c6 = [\"rootmenu\"];\nconst _c7 = [\"*\"];\nconst _c8 = a0 => ({\n  \"p-menubar p-component\": true,\n  \"p-menubar-mobile-active\": a0\n});\nfunction Menubar_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Menubar_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtemplate(1, Menubar_div_1_ng_container_1_Template, 1, 0, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.startTemplate);\n  }\n}\nfunction Menubar_a_2_BarsIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"BarsIcon\");\n  }\n}\nfunction Menubar_a_2_3_ng_template_0_Template(rf, ctx) {}\nfunction Menubar_a_2_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Menubar_a_2_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Menubar_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 10, 2);\n    i0.ɵɵlistener(\"click\", function Menubar_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.menuButtonClick($event));\n    })(\"keydown\", function Menubar_a_2_Template_a_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.menuButtonKeydown($event));\n    });\n    i0.ɵɵtemplate(2, Menubar_a_2_BarsIcon_2_Template, 1, 0, \"BarsIcon\", 11)(3, Menubar_a_2_3_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"aria-haspopup\", ctx_r1.model.length && ctx_r1.model.length > 0 ? true : false)(\"aria-expanded\", ctx_r1.mobileActive)(\"aria-controls\", ctx_r1.id)(\"aria-label\", ctx_r1.config.translation.aria.navigation)(\"data-pc-section\", \"button\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.menuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.menuIconTemplate);\n  }\n}\nfunction Menubar_div_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Menubar_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtemplate(1, Menubar_div_5_ng_container_1_Template, 1, 0, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.endTemplate);\n  }\n}\nfunction Menubar_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n}\nclass MenubarService {\n  autoHide;\n  autoHideDelay;\n  mouseLeaves = new Subject();\n  mouseLeft$ = this.mouseLeaves.pipe(debounce(() => interval(this.autoHideDelay)), filter(mouseLeft => this.autoHide && mouseLeft));\n  static ɵfac = function MenubarService_Factory(t) {\n    return new (t || MenubarService)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: MenubarService,\n    factory: MenubarService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenubarService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass MenubarSub {\n  el;\n  renderer;\n  cd;\n  menubarService;\n  items;\n  itemTemplate;\n  root = false;\n  autoZIndex = true;\n  baseZIndex = 0;\n  mobileActive;\n  autoDisplay;\n  menuId;\n  ariaLabel;\n  ariaLabelledBy;\n  level = 0;\n  focusedItemId;\n  activeItemPath;\n  itemClick = new EventEmitter();\n  itemMouseEnter = new EventEmitter();\n  menuFocus = new EventEmitter();\n  menuBlur = new EventEmitter();\n  menuKeydown = new EventEmitter();\n  menubarViewChild;\n  mouseLeaveSubscriber;\n  constructor(el, renderer, cd, menubarService) {\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.menubarService = menubarService;\n  }\n  ngOnInit() {\n    this.mouseLeaveSubscriber = this.menubarService.mouseLeft$.subscribe(() => {\n      this.cd.markForCheck();\n    });\n  }\n  onItemClick(event, processedItem) {\n    this.getItemProp(processedItem, 'command', {\n      originalEvent: event,\n      item: processedItem.item\n    });\n    this.itemClick.emit({\n      originalEvent: event,\n      processedItem,\n      isFocus: true\n    });\n  }\n  getItemProp(processedItem, name, params = null) {\n    return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n  }\n  getItemId(processedItem) {\n    return processedItem.item && processedItem.item?.id ? processedItem.item.id : `${this.menuId}_${processedItem.key}`;\n  }\n  getItemKey(processedItem) {\n    return this.getItemId(processedItem);\n  }\n  getItemClass(processedItem) {\n    return {\n      ...this.getItemProp(processedItem, 'class'),\n      'p-menuitem': true,\n      'p-highlight': this.isItemActive(processedItem),\n      'p-menuitem-active': this.isItemActive(processedItem),\n      'p-focus': this.isItemFocused(processedItem),\n      'p-disabled': this.isItemDisabled(processedItem)\n    };\n  }\n  getItemLabel(processedItem) {\n    return this.getItemProp(processedItem, 'label');\n  }\n  getSeparatorItemClass(processedItem) {\n    return {\n      ...this.getItemProp(processedItem, 'class'),\n      'p-menuitem-separator': true\n    };\n  }\n  isItemVisible(processedItem) {\n    return this.getItemProp(processedItem, 'visible') !== false;\n  }\n  isItemActive(processedItem) {\n    if (this.activeItemPath) {\n      return this.activeItemPath.some(path => path.key === processedItem.key);\n    }\n  }\n  isItemDisabled(processedItem) {\n    return this.getItemProp(processedItem, 'disabled');\n  }\n  isItemFocused(processedItem) {\n    return this.focusedItemId === this.getItemId(processedItem);\n  }\n  isItemGroup(processedItem) {\n    return ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  getAriaSetSize() {\n    return this.items.filter(processedItem => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n  }\n  getAriaPosInset(index) {\n    return index - this.items.slice(0, index).filter(processedItem => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n  }\n  onItemMouseLeave() {\n    this.menubarService.mouseLeaves.next(true);\n  }\n  onItemMouseEnter(param) {\n    if (this.autoDisplay) {\n      this.menubarService.mouseLeaves.next(false);\n      const {\n        event,\n        processedItem\n      } = param;\n      this.itemMouseEnter.emit({\n        originalEvent: event,\n        processedItem\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.mouseLeaveSubscriber?.unsubscribe();\n  }\n  static ɵfac = function MenubarSub_Factory(t) {\n    return new (t || MenubarSub)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MenubarService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MenubarSub,\n    selectors: [[\"p-menubarSub\"]],\n    viewQuery: function MenubarSub_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menubarViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      items: \"items\",\n      itemTemplate: \"itemTemplate\",\n      root: \"root\",\n      autoZIndex: \"autoZIndex\",\n      baseZIndex: \"baseZIndex\",\n      mobileActive: \"mobileActive\",\n      autoDisplay: \"autoDisplay\",\n      menuId: \"menuId\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      level: \"level\",\n      focusedItemId: \"focusedItemId\",\n      activeItemPath: \"activeItemPath\"\n    },\n    outputs: {\n      itemClick: \"itemClick\",\n      itemMouseEnter: \"itemMouseEnter\",\n      menuFocus: \"menuFocus\",\n      menuBlur: \"menuBlur\",\n      menuKeydown: \"menuKeydown\"\n    },\n    decls: 3,\n    vars: 11,\n    consts: [[\"menubar\", \"\"], [\"listItem\", \"\"], [\"htmlLabel\", \"\"], [\"htmlRouteLabel\", \"\"], [\"role\", \"menu\", 3, \"focus\", \"blur\", \"keydown\", \"ngClass\", \"tabindex\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"role\", \"separator\", 3, \"style\", \"ngClass\", 4, \"ngIf\"], [\"role\", \"menuitem\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"class\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 3, \"ngClass\"], [\"role\", \"menuitem\", \"pTooltip\", \"\", 3, \"ngStyle\", \"ngClass\", \"tooltipOptions\"], [1, \"p-menuitem-content\", 3, \"click\", \"mouseenter\"], [4, \"ngIf\"], [3, \"itemTemplate\", \"items\", \"mobileActive\", \"autoDisplay\", \"menuId\", \"activeItemPath\", \"focusedItemId\", \"level\", \"itemClick\", \"itemMouseEnter\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [\"pRipple\", \"\", 3, \"target\", \"ngClass\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [3, \"data-pc-section\", \"aria-hidden\"], [\"pRipple\", \"\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"ngClass\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"itemClick\", \"itemMouseEnter\", \"itemTemplate\", \"items\", \"mobileActive\", \"autoDisplay\", \"menuId\", \"activeItemPath\", \"focusedItemId\", \"level\"]],\n    template: function MenubarSub_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"ul\", 4, 0);\n        i0.ɵɵlistener(\"focus\", function MenubarSub_Template_ul_focus_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.menuFocus.emit($event));\n        })(\"blur\", function MenubarSub_Template_ul_blur_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.menuBlur.emit($event));\n        })(\"keydown\", function MenubarSub_Template_ul_keydown_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.menuKeydown.emit($event));\n        });\n        i0.ɵɵtemplate(2, MenubarSub_ng_template_2_Template, 2, 2, \"ng-template\", 5);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c1, !ctx.root, ctx.root))(\"tabindex\", 0);\n        i0.ɵɵattribute(\"data-pc-section\", \"menu\")(\"aria-label\", ctx.ariaLabel)(\"aria-labelledBy\", ctx.ariaLabelledBy)(\"id\", ctx.menuId)(\"aria-activedescendant\", ctx.focusedItemId);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.items);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.RouterLink, i2.RouterLinkActive, i3.Ripple, i4.Tooltip, AngleDownIcon, AngleRightIcon, MenubarSub],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenubarSub, [{\n    type: Component,\n    args: [{\n      selector: 'p-menubarSub',\n      template: `\n        <ul\n            #menubar\n            [ngClass]=\"{ 'p-submenu-list': !root, 'p-menubar-root-list': root }\"\n            [attr.data-pc-section]=\"'menu'\"\n            role=\"menu\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n            [tabindex]=\"0\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            [attr.id]=\"menuId\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [style]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div class=\"p-menuitem-content\" [attr.data-pc-section]=\"'content'\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({$event, processedItem})\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!menubar.submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"root\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"!root\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"menubar.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    class=\"p-menuitem-icon\"\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                ></span>\n                                <span class=\"p-menuitem-text\" *ngIf=\"getItemProp(processedItem, 'escape'); else htmlRouteLabel\">{{ getItemLabel(processedItem) }}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span></ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!menubar.submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" *ngIf=\"root\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" *ngIf=\"!root\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"menubar.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item }\"></ng-template>\n                        </ng-container>\n                    </div>\n                    <p-menubarSub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [items]=\"processedItem.items\"\n                        [mobileActive]=\"mobileActive\"\n                        [autoDisplay]=\"autoDisplay\"\n                        [menuId]=\"menuId\"\n                        [activeItemPath]=\"activeItemPath\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [level]=\"level + 1\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                    >\n                    </p-menubarSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: MenubarService\n  }], {\n    items: [{\n      type: Input\n    }],\n    itemTemplate: [{\n      type: Input\n    }],\n    root: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    mobileActive: [{\n      type: Input\n    }],\n    autoDisplay: [{\n      type: Input\n    }],\n    menuId: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    level: [{\n      type: Input\n    }],\n    focusedItemId: [{\n      type: Input\n    }],\n    activeItemPath: [{\n      type: Input\n    }],\n    itemClick: [{\n      type: Output\n    }],\n    itemMouseEnter: [{\n      type: Output\n    }],\n    menuFocus: [{\n      type: Output\n    }],\n    menuBlur: [{\n      type: Output\n    }],\n    menuKeydown: [{\n      type: Output\n    }],\n    menubarViewChild: [{\n      type: ViewChild,\n      args: ['menubar', {\n        static: true\n      }]\n    }]\n  });\n})();\n/**\n * Menubar is a horizontal menu component.\n * @group Components\n */\nclass Menubar {\n  document;\n  platformId;\n  el;\n  renderer;\n  cd;\n  config;\n  menubarService;\n  /**\n   * An array of menuitems.\n   * @group Props\n   */\n  set model(value) {\n    this._model = value;\n    this._processedItems = this.createProcessedItems(this._model || []);\n  }\n  get model() {\n    return this._model;\n  }\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Whether to automatically manage layering.\n   * @group Props\n   */\n  autoZIndex = true;\n  /**\n   * Base zIndex value to use in layering.\n   * @group Props\n   */\n  baseZIndex = 0;\n  /**\n   * Whether to show a root submenu on mouse over.\n   * @defaultValue true\n   * @group Props\n   */\n  autoDisplay = true;\n  /**\n   * Whether to hide a root submenu when mouse leaves.\n   * @group Props\n   */\n  autoHide;\n  /**\n   * Delay to hide the root submenu in milliseconds when mouse leaves.\n   * @group Props\n   */\n  autoHideDelay = 100;\n  /**\n   * Current id state as a string.\n   * @group Props\n   */\n  id;\n  /**\n   * Defines a string value that labels an interactive element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Identifier of the underlying input element.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * Callback to execute when button is focused.\n   * @param {FocusEvent} event - Focus event.\n   * @group Emits\n   */\n  onFocus = new EventEmitter();\n  /**\n   * Callback to execute when button loses focus.\n   * @param {FocusEvent} event - Focus event.\n   * @group Emits\n   */\n  onBlur = new EventEmitter();\n  templates;\n  menubutton;\n  rootmenu;\n  startTemplate;\n  endTemplate;\n  menuIconTemplate;\n  submenuIconTemplate;\n  itemTemplate;\n  mobileActive;\n  outsideClickListener;\n  resizeListener;\n  mouseLeaveSubscriber;\n  dirty = false;\n  focused = false;\n  activeItemPath = signal([]);\n  number = signal(0);\n  focusedItemInfo = signal({\n    index: -1,\n    level: 0,\n    parentKey: '',\n    item: null\n  });\n  searchValue = '';\n  searchTimeout;\n  _processedItems;\n  _model;\n  get visibleItems() {\n    const processedItem = this.activeItemPath().find(p => p.key === this.focusedItemInfo().parentKey);\n    return processedItem ? processedItem.items : this.processedItems;\n  }\n  get processedItems() {\n    if (!this._processedItems || !this._processedItems.length) {\n      this._processedItems = this.createProcessedItems(this.model || []);\n    }\n    return this._processedItems;\n  }\n  get focusedItemId() {\n    const focusedItem = this.focusedItemInfo();\n    return focusedItem.item && focusedItem.item?.id ? focusedItem.item.id : focusedItem.index !== -1 ? `${this.id}${ObjectUtils.isNotEmpty(focusedItem.parentKey) ? '_' + focusedItem.parentKey : ''}_${focusedItem.index}` : null;\n  }\n  constructor(document, platformId, el, renderer, cd, config, menubarService) {\n    this.document = document;\n    this.platformId = platformId;\n    this.el = el;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.config = config;\n    this.menubarService = menubarService;\n    effect(() => {\n      const path = this.activeItemPath();\n      if (ObjectUtils.isNotEmpty(path)) {\n        this.bindOutsideClickListener();\n        this.bindResizeListener();\n      } else {\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n      }\n    });\n  }\n  ngOnInit() {\n    this.menubarService.autoHide = this.autoHide;\n    this.menubarService.autoHideDelay = this.autoHideDelay;\n    this.mouseLeaveSubscriber = this.menubarService.mouseLeft$.subscribe(() => this.unbindOutsideClickListener());\n    this.id = this.id || UniqueComponentId();\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'start':\n          this.startTemplate = item.template;\n          break;\n        case 'end':\n          this.endTemplate = item.template;\n          break;\n        case 'menuicon':\n          this.menuIconTemplate = item.template;\n          break;\n        case 'submenuicon':\n          this.submenuIconTemplate = item.template;\n          break;\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  createProcessedItems(items, level = 0, parent = {}, parentKey = '') {\n    const processedItems = [];\n    items && items.forEach((item, index) => {\n      const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n      const newItem = {\n        item,\n        index,\n        level,\n        key,\n        parent,\n        parentKey\n      };\n      newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n      processedItems.push(newItem);\n    });\n    return processedItems;\n  }\n  getItemProp(item, name) {\n    return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n  }\n  menuButtonClick(event) {\n    this.toggle(event);\n  }\n  menuButtonKeydown(event) {\n    (event.code === 'Enter' || event.code === 'Space') && this.menuButtonClick(event);\n  }\n  onItemClick(event) {\n    const {\n      originalEvent,\n      processedItem\n    } = event;\n    const grouped = this.isProcessedItemGroup(processedItem);\n    const root = ObjectUtils.isEmpty(processedItem.parent);\n    const selected = this.isSelected(processedItem);\n    if (selected) {\n      const {\n        index,\n        key,\n        level,\n        parentKey,\n        item\n      } = processedItem;\n      this.activeItemPath.set(this.activeItemPath().filter(p => key !== p.key && key.startsWith(p.key)));\n      this.focusedItemInfo.set({\n        index,\n        level,\n        parentKey,\n        item\n      });\n      this.dirty = !root;\n      DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n    } else {\n      if (grouped) {\n        this.onItemChange(event);\n      } else {\n        const rootProcessedItem = root ? processedItem : this.activeItemPath().find(p => p.parentKey === '');\n        this.hide(originalEvent);\n        this.changeFocusedItemIndex(originalEvent, rootProcessedItem ? rootProcessedItem.index : -1);\n        this.mobileActive = false;\n        DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n      }\n    }\n  }\n  onItemMouseEnter(event) {\n    if (!DomHandler.isTouchDevice()) {\n      if (!this.mobileActive) {\n        this.onItemChange(event);\n      }\n    }\n  }\n  changeFocusedItemIndex(event, index) {\n    const processedItem = this.findVisibleItem(index);\n    if (this.focusedItemInfo().index !== index) {\n      const focusedItemInfo = this.focusedItemInfo();\n      this.focusedItemInfo.set({\n        ...focusedItemInfo,\n        item: processedItem.item,\n        index\n      });\n      this.scrollInView();\n    }\n  }\n  scrollInView(index = -1) {\n    const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n    const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${id}\"]`);\n    if (element) {\n      element.scrollIntoView && element.scrollIntoView({\n        block: 'nearest',\n        inline: 'nearest'\n      });\n    }\n  }\n  onItemChange(event) {\n    const {\n      processedItem,\n      isFocus\n    } = event;\n    if (ObjectUtils.isEmpty(processedItem)) return;\n    const {\n      index,\n      key,\n      level,\n      parentKey,\n      items,\n      item\n    } = processedItem;\n    const grouped = ObjectUtils.isNotEmpty(items);\n    const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== parentKey && p.parentKey !== key);\n    grouped && activeItemPath.push(processedItem);\n    this.focusedItemInfo.set({\n      index,\n      level,\n      parentKey,\n      item\n    });\n    this.activeItemPath.set(activeItemPath);\n    grouped && (this.dirty = true);\n    isFocus && DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n  }\n  toggle(event) {\n    if (this.mobileActive) {\n      this.mobileActive = false;\n      ZIndexUtils.clear(this.rootmenu.el.nativeElement);\n      this.hide();\n    } else {\n      this.mobileActive = true;\n      ZIndexUtils.set('menu', this.rootmenu.el.nativeElement, this.config.zIndex.menu);\n      setTimeout(() => {\n        this.show();\n      }, 0);\n    }\n    this.cd.markForCheck();\n    this.bindOutsideClickListener();\n    event.preventDefault();\n  }\n  hide(event, isFocus) {\n    if (this.mobileActive) {\n      setTimeout(() => {\n        DomHandler.focus(this.menubutton.nativeElement);\n      }, 0);\n    }\n    this.activeItemPath.set([]);\n    this.focusedItemInfo.set({\n      index: -1,\n      level: 0,\n      parentKey: '',\n      item: null\n    });\n    isFocus && DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n    this.dirty = false;\n  }\n  show() {\n    const processedItem = this.findVisibleItem(this.findFirstFocusedItemIndex());\n    this.focusedItemInfo.set({\n      index: this.findFirstFocusedItemIndex(),\n      level: 0,\n      parentKey: '',\n      item: processedItem.item\n    });\n    DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n  }\n  onMenuFocus(event) {\n    this.focused = true;\n    const processedItem = this.findVisibleItem(this.findFirstFocusedItemIndex());\n    const focusedItemInfo = this.focusedItemInfo().index !== -1 ? this.focusedItemInfo() : {\n      index: this.findFirstFocusedItemIndex(),\n      level: 0,\n      parentKey: '',\n      item: processedItem.item\n    };\n    this.focusedItemInfo.set(focusedItemInfo);\n    this.onFocus.emit(event);\n  }\n  onMenuBlur(event) {\n    this.focused = false;\n    this.focusedItemInfo.set({\n      index: -1,\n      level: 0,\n      parentKey: '',\n      item: null\n    });\n    this.searchValue = '';\n    this.dirty = false;\n    this.onBlur.emit(event);\n  }\n  onKeyDown(event) {\n    const metaKey = event.metaKey || event.ctrlKey;\n    switch (event.code) {\n      case 'ArrowDown':\n        this.onArrowDownKey(event);\n        break;\n      case 'ArrowUp':\n        this.onArrowUpKey(event);\n        break;\n      case 'ArrowLeft':\n        this.onArrowLeftKey(event);\n        break;\n      case 'ArrowRight':\n        this.onArrowRightKey(event);\n        break;\n      case 'Home':\n        this.onHomeKey(event);\n        break;\n      case 'End':\n        this.onEndKey(event);\n        break;\n      case 'Space':\n        this.onSpaceKey(event);\n        break;\n      case 'Enter':\n        this.onEnterKey(event);\n        break;\n      case 'Escape':\n        this.onEscapeKey(event);\n        break;\n      case 'Tab':\n        this.onTabKey(event);\n        break;\n      case 'PageDown':\n      case 'PageUp':\n      case 'Backspace':\n      case 'ShiftLeft':\n      case 'ShiftRight':\n        //NOOP\n        break;\n      default:\n        if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n          this.searchItems(event, event.key);\n        }\n        break;\n    }\n  }\n  findVisibleItem(index) {\n    return ObjectUtils.isNotEmpty(this.visibleItems) ? this.visibleItems[index] : null;\n  }\n  findFirstFocusedItemIndex() {\n    const selectedIndex = this.findSelectedItemIndex();\n    return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n  }\n  findFirstItemIndex() {\n    return this.visibleItems.findIndex(processedItem => this.isValidItem(processedItem));\n  }\n  findSelectedItemIndex() {\n    return this.visibleItems.findIndex(processedItem => this.isValidSelectedItem(processedItem));\n  }\n  isProcessedItemGroup(processedItem) {\n    return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  isSelected(processedItem) {\n    return this.activeItemPath().some(p => p.key === processedItem.key);\n  }\n  isValidSelectedItem(processedItem) {\n    return this.isValidItem(processedItem) && this.isSelected(processedItem);\n  }\n  isValidItem(processedItem) {\n    return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item);\n  }\n  isItemDisabled(item) {\n    return this.getItemProp(item, 'disabled');\n  }\n  isItemSeparator(item) {\n    return this.getItemProp(item, 'separator');\n  }\n  isItemMatched(processedItem) {\n    return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n  }\n  isProccessedItemGroup(processedItem) {\n    return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n  }\n  searchItems(event, char) {\n    this.searchValue = (this.searchValue || '') + char;\n    let itemIndex = -1;\n    let matched = false;\n    if (this.focusedItemInfo().index !== -1) {\n      itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem));\n      itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex(processedItem => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n    } else {\n      itemIndex = this.visibleItems.findIndex(processedItem => this.isItemMatched(processedItem));\n    }\n    if (itemIndex !== -1) {\n      matched = true;\n    }\n    if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n      itemIndex = this.findFirstFocusedItemIndex();\n    }\n    if (itemIndex !== -1) {\n      this.changeFocusedItemIndex(event, itemIndex);\n    }\n    if (this.searchTimeout) {\n      clearTimeout(this.searchTimeout);\n    }\n    this.searchTimeout = setTimeout(() => {\n      this.searchValue = '';\n      this.searchTimeout = null;\n    }, 500);\n    return matched;\n  }\n  getProccessedItemLabel(processedItem) {\n    return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n  }\n  getItemLabel(item) {\n    return this.getItemProp(item, 'label');\n  }\n  onArrowDownKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const root = processedItem ? ObjectUtils.isEmpty(processedItem.parent) : null;\n    if (root) {\n      const grouped = this.isProccessedItemGroup(processedItem);\n      if (grouped) {\n        this.onItemChange({\n          originalEvent: event,\n          processedItem\n        });\n        this.focusedItemInfo.set({\n          index: -1,\n          parentKey: processedItem.key,\n          item: processedItem.item\n        });\n        this.onArrowRightKey(event);\n      }\n    } else {\n      const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n      this.changeFocusedItemIndex(event, itemIndex);\n      event.preventDefault();\n    }\n  }\n  onArrowRightKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const parentItem = processedItem ? this.activeItemPath().find(p => p.key === processedItem.parentKey) : null;\n    if (parentItem) {\n      const grouped = this.isProccessedItemGroup(processedItem);\n      if (grouped) {\n        this.onItemChange({\n          originalEvent: event,\n          processedItem\n        });\n        this.focusedItemInfo.set({\n          index: -1,\n          parentKey: processedItem.key,\n          item: processedItem.item\n        });\n        this.onArrowDownKey(event);\n      }\n    } else {\n      const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n      this.changeFocusedItemIndex(event, itemIndex);\n      event.preventDefault();\n    }\n  }\n  onArrowUpKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const root = ObjectUtils.isEmpty(processedItem.parent);\n    if (root) {\n      const grouped = this.isProccessedItemGroup(processedItem);\n      if (grouped) {\n        this.onItemChange({\n          originalEvent: event,\n          processedItem\n        });\n        this.focusedItemInfo.set({\n          index: -1,\n          parentKey: processedItem.key,\n          item: processedItem.item\n        });\n        const itemIndex = this.findLastItemIndex();\n        this.changeFocusedItemIndex(event, itemIndex);\n      }\n    } else {\n      const parentItem = this.activeItemPath().find(p => p.key === processedItem.parentKey);\n      if (this.focusedItemInfo().index === 0) {\n        this.focusedItemInfo.set({\n          index: -1,\n          parentKey: parentItem ? parentItem.parentKey : '',\n          item: processedItem.item\n        });\n        this.searchValue = '';\n        this.onArrowLeftKey(event);\n        const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== this.focusedItemInfo().parentKey);\n        this.activeItemPath.set(activeItemPath);\n      } else {\n        const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n        this.changeFocusedItemIndex(event, itemIndex);\n      }\n    }\n    event.preventDefault();\n  }\n  onArrowLeftKey(event) {\n    const processedItem = this.visibleItems[this.focusedItemInfo().index];\n    const parentItem = processedItem ? this.activeItemPath().find(p => p.key === processedItem.parentKey) : null;\n    if (parentItem) {\n      this.onItemChange({\n        originalEvent: event,\n        processedItem: parentItem\n      });\n      const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== this.focusedItemInfo().parentKey);\n      this.activeItemPath.set(activeItemPath);\n      event.preventDefault();\n    } else {\n      const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n      this.changeFocusedItemIndex(event, itemIndex);\n      event.preventDefault();\n    }\n  }\n  onHomeKey(event) {\n    this.changeFocusedItemIndex(event, this.findFirstItemIndex());\n    event.preventDefault();\n  }\n  onEndKey(event) {\n    this.changeFocusedItemIndex(event, this.findLastItemIndex());\n    event.preventDefault();\n  }\n  onSpaceKey(event) {\n    this.onEnterKey(event);\n  }\n  onEscapeKey(event) {\n    this.hide(event, true);\n    this.focusedItemInfo().index = this.findFirstFocusedItemIndex();\n    event.preventDefault();\n  }\n  onTabKey(event) {\n    if (this.focusedItemInfo().index !== -1) {\n      const processedItem = this.visibleItems[this.focusedItemInfo().index];\n      const grouped = this.isProccessedItemGroup(processedItem);\n      !grouped && this.onItemChange({\n        originalEvent: event,\n        processedItem\n      });\n    }\n    this.hide();\n  }\n  onEnterKey(event) {\n    if (this.focusedItemInfo().index !== -1) {\n      const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n      const anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n      anchorElement ? anchorElement.click() : element && element.click();\n      const processedItem = this.visibleItems[this.focusedItemInfo().index];\n      const grouped = this.isProccessedItemGroup(processedItem);\n      !grouped && (this.focusedItemInfo().index = this.findFirstFocusedItemIndex());\n    }\n    event.preventDefault();\n  }\n  findLastFocusedItemIndex() {\n    const selectedIndex = this.findSelectedItemIndex();\n    return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n  }\n  findLastItemIndex() {\n    return ObjectUtils.findLastIndex(this.visibleItems, processedItem => this.isValidItem(processedItem));\n  }\n  findPrevItemIndex(index) {\n    const matchedItemIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleItems.slice(0, index), processedItem => this.isValidItem(processedItem)) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex : index;\n  }\n  findNextItemIndex(index) {\n    const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex(processedItem => this.isValidItem(processedItem)) : -1;\n    return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n  }\n  bindResizeListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.resizeListener) {\n        this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', event => {\n          if (!DomHandler.isTouchDevice()) {\n            this.hide(event, true);\n          }\n          this.mobileActive = false;\n        });\n      }\n    }\n  }\n  bindOutsideClickListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.outsideClickListener) {\n        this.outsideClickListener = this.renderer.listen(this.document, 'click', event => {\n          const isOutsideContainer = this.rootmenu.el.nativeElement !== event.target && !this.rootmenu.el.nativeElement.contains(event.target);\n          const isOutsideMenuButton = this.mobileActive && this.menubutton.nativeElement !== event.target && !this.menubutton.nativeElement.contains(event.target);\n          if (isOutsideContainer) {\n            isOutsideMenuButton ? this.mobileActive = false : this.hide();\n          }\n        });\n      }\n    }\n  }\n  unbindOutsideClickListener() {\n    if (this.outsideClickListener) {\n      this.outsideClickListener();\n      this.outsideClickListener = null;\n    }\n  }\n  unbindResizeListener() {\n    if (this.resizeListener) {\n      this.resizeListener();\n      this.resizeListener = null;\n    }\n  }\n  ngOnDestroy() {\n    this.mouseLeaveSubscriber?.unsubscribe();\n    this.unbindOutsideClickListener();\n    this.unbindResizeListener();\n  }\n  static ɵfac = function Menubar_Factory(t) {\n    return new (t || Menubar)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.PrimeNGConfig), i0.ɵɵdirectiveInject(MenubarService));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Menubar,\n    selectors: [[\"p-menubar\"]],\n    contentQueries: function Menubar_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Menubar_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c5, 5);\n        i0.ɵɵviewQuery(_c6, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menubutton = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rootmenu = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      model: \"model\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      autoZIndex: \"autoZIndex\",\n      baseZIndex: \"baseZIndex\",\n      autoDisplay: \"autoDisplay\",\n      autoHide: \"autoHide\",\n      autoHideDelay: \"autoHideDelay\",\n      id: \"id\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\"\n    },\n    outputs: {\n      onFocus: \"onFocus\",\n      onBlur: \"onBlur\"\n    },\n    features: [i0.ɵɵProvidersFeature([MenubarService])],\n    ngContentSelectors: _c7,\n    decls: 8,\n    vars: 24,\n    consts: [[\"rootmenu\", \"\"], [\"legacy\", \"\"], [\"menubutton\", \"\"], [3, \"ngClass\", \"ngStyle\"], [\"class\", \"p-menubar-start\", 4, \"ngIf\"], [\"tabindex\", \"0\", \"role\", \"button\", \"class\", \"p-menubar-button\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [3, \"itemClick\", \"menuFocus\", \"menuBlur\", \"menuKeydown\", \"itemMouseEnter\", \"items\", \"itemTemplate\", \"menuId\", \"root\", \"baseZIndex\", \"autoZIndex\", \"mobileActive\", \"autoDisplay\", \"ariaLabel\", \"ariaLabelledBy\", \"focusedItemId\", \"activeItemPath\"], [\"class\", \"p-menubar-end\", 4, \"ngIf\", \"ngIfElse\"], [1, \"p-menubar-start\"], [4, \"ngTemplateOutlet\"], [\"tabindex\", \"0\", \"role\", \"button\", 1, \"p-menubar-button\", 3, \"click\", \"keydown\"], [4, \"ngIf\"], [1, \"p-menubar-end\"]],\n    template: function Menubar_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 3);\n        i0.ɵɵtemplate(1, Menubar_div_1_Template, 2, 1, \"div\", 4)(2, Menubar_a_2_Template, 4, 7, \"a\", 5);\n        i0.ɵɵelementStart(3, \"p-menubarSub\", 6, 0);\n        i0.ɵɵlistener(\"itemClick\", function Menubar_Template_p_menubarSub_itemClick_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onItemClick($event));\n        })(\"menuFocus\", function Menubar_Template_p_menubarSub_menuFocus_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onMenuFocus($event));\n        })(\"menuBlur\", function Menubar_Template_p_menubarSub_menuBlur_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onMenuBlur($event));\n        })(\"menuKeydown\", function Menubar_Template_p_menubarSub_menuKeydown_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onKeyDown($event));\n        })(\"itemMouseEnter\", function Menubar_Template_p_menubarSub_itemMouseEnter_3_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onItemMouseEnter($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, Menubar_div_5_Template, 2, 1, \"div\", 7)(6, Menubar_ng_template_6_Template, 2, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        const legacy_r4 = i0.ɵɵreference(7);\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(22, _c8, ctx.mobileActive))(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"data-pc-section\", \"root\")(\"data-pc-name\", \"menubar\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.startTemplate);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.model && ctx.model.length > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"items\", ctx.processedItems)(\"itemTemplate\", ctx.itemTemplate)(\"menuId\", ctx.id)(\"root\", true)(\"baseZIndex\", ctx.baseZIndex)(\"autoZIndex\", ctx.autoZIndex)(\"mobileActive\", ctx.mobileActive)(\"autoDisplay\", ctx.autoDisplay)(\"ariaLabel\", ctx.ariaLabel)(\"ariaLabelledBy\", ctx.ariaLabelledBy)(\"focusedItemId\", ctx.focused ? ctx.focusedItemId : undefined)(\"activeItemPath\", ctx.activeItemPath());\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.endTemplate)(\"ngIfElse\", legacy_r4);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, BarsIcon, MenubarSub],\n    styles: [\"@layer primeng{.p-menubar{display:flex;align-items:center}.p-menubar ul{margin:0;padding:0;list-style:none}.p-menubar .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menubar .p-menuitem-text{line-height:1}.p-menubar .p-menuitem{position:relative}.p-menubar-root-list{display:flex;align-items:center;flex-wrap:wrap}.p-menubar-root-list>li ul{display:none;z-index:1}.p-menubar-root-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block}.p-menubar .p-submenu-list{display:none;position:absolute;z-index:2}.p-menubar .p-submenu-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block;left:100%;top:0}.p-menubar .p-submenu-list .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-menubar .p-menubar-root-list .p-icon-wrapper,.p-menubar .p-submenu-list .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-menubar .p-menubar-custom,.p-menubar .p-menubar-end{margin-left:auto;align-self:center}.p-menubar-button{display:none;cursor:pointer;align-items:center;justify-content:center}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Menubar, [{\n    type: Component,\n    args: [{\n      selector: 'p-menubar',\n      template: `\n        <div [ngClass]=\"{ 'p-menubar p-component': true, 'p-menubar-mobile-active': mobileActive }\" [class]=\"styleClass\" [ngStyle]=\"style\" [attr.data-pc-section]=\"'root'\" [attr.data-pc-name]=\"'menubar'\">\n            <div class=\"p-menubar-start\" *ngIf=\"startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <a\n                #menubutton\n                tabindex=\"0\"\n                role=\"button\"\n                [attr.aria-haspopup]=\"model.length && model.length > 0 ? true : false\"\n                [attr.aria-expanded]=\"mobileActive\"\n                [attr.aria-controls]=\"id\"\n                [attr.aria-label]=\"config.translation.aria.navigation\"\n                [attr.data-pc-section]=\"'button'\"\n                *ngIf=\"model && model.length > 0\"\n                class=\"p-menubar-button\"\n                (click)=\"menuButtonClick($event)\"\n                (keydown)=\"menuButtonKeydown($event)\"\n            >\n                <BarsIcon *ngIf=\"!menuIconTemplate\" />\n                <ng-template *ngTemplateOutlet=\"menuIconTemplate\"></ng-template>\n            </a>\n            <p-menubarSub\n                #rootmenu\n                [items]=\"processedItems\"\n                [itemTemplate]=\"itemTemplate\"\n                [menuId]=\"id\"\n                [root]=\"true\"\n                [baseZIndex]=\"baseZIndex\"\n                [autoZIndex]=\"autoZIndex\"\n                [mobileActive]=\"mobileActive\"\n                [autoDisplay]=\"autoDisplay\"\n                [ariaLabel]=\"ariaLabel\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [activeItemPath]=\"activeItemPath()\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-menubarSub>\n            <div class=\"p-menubar-end\" *ngIf=\"endTemplate; else legacy\">\n                <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n            </div>\n            <ng-template #legacy>\n                <div class=\"p-menubar-end\">\n                    <ng-content></ng-content>\n                </div>\n            </ng-template>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      providers: [MenubarService],\n      styles: [\"@layer primeng{.p-menubar{display:flex;align-items:center}.p-menubar ul{margin:0;padding:0;list-style:none}.p-menubar .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menubar .p-menuitem-text{line-height:1}.p-menubar .p-menuitem{position:relative}.p-menubar-root-list{display:flex;align-items:center;flex-wrap:wrap}.p-menubar-root-list>li ul{display:none;z-index:1}.p-menubar-root-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block}.p-menubar .p-submenu-list{display:none;position:absolute;z-index:2}.p-menubar .p-submenu-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block;left:100%;top:0}.p-menubar .p-submenu-list .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-menubar .p-menubar-root-list .p-icon-wrapper,.p-menubar .p-submenu-list .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-menubar .p-menubar-custom,.p-menubar .p-menubar-end{margin-left:auto;align-self:center}.p-menubar-button{display:none;cursor:pointer;align-items:center;justify-content:center}}\\n\"]\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i5.PrimeNGConfig\n  }, {\n    type: MenubarService\n  }], {\n    model: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    autoDisplay: [{\n      type: Input\n    }],\n    autoHide: [{\n      type: Input\n    }],\n    autoHideDelay: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    onFocus: [{\n      type: Output\n    }],\n    onBlur: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    menubutton: [{\n      type: ViewChild,\n      args: ['menubutton']\n    }],\n    rootmenu: [{\n      type: ViewChild,\n      args: ['rootmenu']\n    }]\n  });\n})();\nclass MenubarModule {\n  static ɵfac = function MenubarModule_Factory(t) {\n    return new (t || MenubarModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MenubarModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, BarsIcon, AngleDownIcon, AngleRightIcon, RouterModule, TooltipModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MenubarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, BarsIcon, AngleDownIcon, AngleRightIcon],\n      exports: [Menubar, RouterModule, TooltipModule, SharedModule],\n      declarations: [Menubar, MenubarSub]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Menubar, MenubarModule, MenubarService, MenubarSub };", "map": {"version": 3, "names": ["i1", "isPlatformBrowser", "DOCUMENT", "CommonModule", "i0", "Injectable", "EventEmitter", "Component", "ViewEncapsulation", "Input", "Output", "ViewChild", "signal", "effect", "PLATFORM_ID", "ChangeDetectionStrategy", "Inject", "ContentChildren", "NgModule", "i2", "RouterModule", "i5", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "AngleDownIcon", "AngleRightIcon", "BarsIcon", "i3", "RippleModule", "i4", "TooltipModule", "ObjectUtils", "UniqueComponentId", "ZIndexUtils", "Subject", "interval", "debounce", "filter", "_c0", "_c1", "a0", "a1", "_c2", "_c3", "exact", "_c4", "$implicit", "MenubarSub_ng_template_2_li_0_Template", "rf", "ctx", "ɵɵelement", "processedItem_r2", "ɵɵnextContext", "ctx_r2", "ɵɵstyleMap", "getItemProp", "ɵɵproperty", "getSeparatorItemClass", "ɵɵattribute", "getItemId", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_1_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_2_Template", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "getItemLabel", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_template_3_Template", "ɵɵsanitizeHtml", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_span_5_Template", "ɵɵtextInterpolate", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleDownIcon_1_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_AngleRightIcon_2_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "root", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_ng_template_0_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_2_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_ng_container_6_Template", "menubar_r5", "ɵɵreference", "submenuIconTemplate", "MenubarSub_ng_template_2_li_1_ng_container_3_a_1_Template", "ɵɵtemplateRefExtractor", "htmlLabel_r6", "ɵɵpureFunction1", "ɵɵsanitizeUrl", "isItemGroup", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_1_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_2_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_template_3_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_span_5_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleDownIcon_1_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_AngleRightIcon_2_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_ng_container_1_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_ng_template_0_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_2_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_ng_container_6_Template", "MenubarSub_ng_template_2_li_1_ng_container_3_a_2_Template", "htmlRouteLabel_r7", "ɵɵpureFunction0", "MenubarSub_ng_template_2_li_1_ng_container_3_Template", "MenubarSub_ng_template_2_li_1_ng_container_4_1_ng_template_0_Template", "MenubarSub_ng_template_2_li_1_ng_container_4_1_Template", "MenubarSub_ng_template_2_li_1_ng_container_4_Template", "itemTemplate", "item", "MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template", "_r8", "ɵɵgetCurrentView", "ɵɵlistener", "MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template_p_menubarSub_itemClick_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "itemClick", "emit", "MenubarSub_ng_template_2_li_1_p_menubarSub_5_Template_p_menubarSub_itemMouseEnter_0_listener", "onItemMouseEnter", "items", "mobileActive", "autoDisplay", "menuId", "activeItemPath", "focusedItemId", "level", "MenubarSub_ng_template_2_li_1_Template", "_r4", "MenubarSub_ng_template_2_li_1_Template_div_click_2_listener", "onItemClick", "MenubarSub_ng_template_2_li_1_Template_div_mouseenter_2_listener", "processedItem", "ctx_r8", "index_r10", "index", "ɵɵclassMap", "getItemClass", "isItemActive", "isItemFocused", "isItemDisabled", "undefined", "getAriaSetSize", "getAriaPosInset", "isItemVisible", "MenubarSub_ng_template_2_Template", "_c5", "_c6", "_c7", "_c8", "Menubar_div_1_ng_container_1_Template", "ɵɵelementContainer", "Menubar_div_1_Template", "ctx_r1", "startTemplate", "Menubar_a_2_BarsIcon_2_Template", "Menubar_a_2_3_ng_template_0_Template", "Menubar_a_2_3_Template", "Menubar_a_2_Template", "_r3", "Menubar_a_2_Template_a_click_0_listener", "menuButtonClick", "Menubar_a_2_Template_a_keydown_0_listener", "menuButtonKeydown", "model", "length", "id", "config", "translation", "aria", "navigation", "menuIconTemplate", "Menubar_div_5_ng_container_1_Template", "Menubar_div_5_Template", "endTemplate", "<PERSON><PERSON>r_ng_template_6_Template", "ɵɵprojection", "MenubarService", "autoHide", "autoHideDelay", "mouseLeaves", "mouseLeft$", "pipe", "mouseLeft", "ɵfac", "MenubarService_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "el", "renderer", "cd", "menubarService", "autoZIndex", "baseZIndex", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "itemMouseEnter", "menuFocus", "menuBlur", "menuKeydown", "menubarViewChild", "mouseLeaveSubscriber", "constructor", "ngOnInit", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "originalEvent", "isFocus", "name", "params", "getItemValue", "key", "getItemKey", "some", "path", "isNotEmpty", "slice", "onItemMouseLeave", "next", "param", "ngOnDestroy", "unsubscribe", "MenubarSub_Factory", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "selectors", "viewQuery", "MenubarSub_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "inputs", "outputs", "decls", "vars", "consts", "template", "MenubarSub_Template", "_r1", "MenubarSub_Template_ul_focus_0_listener", "MenubarSub_Template_ul_blur_0_listener", "MenubarSub_Template_ul_keydown_0_listener", "ɵɵpureFunction2", "dependencies", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "RouterLink", "RouterLinkActive", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "encapsulation", "args", "selector", "None", "host", "class", "static", "Men<PERSON><PERSON>", "document", "platformId", "value", "_model", "_processedItems", "createProcessedItems", "style", "styleClass", "onFocus", "onBlur", "templates", "menubutton", "rootmenu", "outsideClickListener", "resizeListener", "dirty", "focused", "number", "focusedItemInfo", "parent<PERSON><PERSON>", "searchValue", "searchTimeout", "visibleItems", "find", "p", "processedItems", "focusedItem", "bindOutsideClickListener", "bindResizeListener", "unbindOutsideClickListener", "unbindResizeListener", "ngAfterContentInit", "for<PERSON>ach", "getType", "parent", "newItem", "push", "toggle", "code", "grouped", "isProcessedItemGroup", "isEmpty", "selected", "isSelected", "set", "startsWith", "focus", "nativeElement", "onItemChange", "rootProcessedItem", "hide", "changeFocusedItemIndex", "isTouchDevice", "findVisibleItem", "scrollInView", "element", "findSingle", "scrollIntoView", "block", "inline", "clear", "zIndex", "menu", "setTimeout", "show", "preventDefault", "findFirstFocusedItemIndex", "onMenuFocus", "onMenuBlur", "onKeyDown", "metaKey", "ctrl<PERSON>ey", "onArrowDownKey", "onArrowUpKey", "onArrowLeftKey", "onArrowRightKey", "onHomeKey", "onEndKey", "onSpaceKey", "onEnterKey", "onEscapeKey", "onTabKey", "isPrintableCharacter", "searchItems", "selectedIndex", "findSelectedItemIndex", "findFirstItemIndex", "findIndex", "isValidItem", "isValidSelectedItem", "isItemSeparator", "isItemMatched", "getProccessedItemLabel", "toLocaleLowerCase", "isProccessedItemGroup", "char", "itemIndex", "matched", "clearTimeout", "findNextItemIndex", "parentItem", "findLastItemIndex", "findPrevItemIndex", "findLastFocusedItemIndex", "anchorElement", "click", "findLastIndex", "matchedItemIndex", "listen", "defaultView", "isOutsideContainer", "target", "contains", "isOutsideMenuButton", "Menubar_Factory", "PrimeNGConfig", "contentQueries", "Menubar_ContentQueries", "dirIndex", "ɵɵcontentQuery", "Menubar_Query", "features", "ɵɵProvidersFeature", "ngContentSelectors", "<PERSON><PERSON>r_Template", "ɵɵprojectionDef", "<PERSON><PERSON><PERSON>_Template_p_menubarSub_itemClick_3_listener", "<PERSON><PERSON><PERSON>_Template_p_menubarSub_menuFocus_3_listener", "<PERSON><PERSON><PERSON>_Template_p_menubarSub_menuBlur_3_listener", "<PERSON><PERSON><PERSON>_Template_p_menubarSub_menuKeydown_3_listener", "<PERSON><PERSON>r_Template_p_menubarSub_itemMouseEnter_3_listener", "legacy_r4", "styles", "changeDetection", "OnPush", "providers", "Document", "decorators", "MenubarModule", "MenubarModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/Code/ASAR/Azure/Public Service/SNJYA-PUBLIC-SERVICE/client/node_modules/primeng/fesm2022/primeng-menubar.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Component, ViewEncapsulation, Input, Output, ViewChild, signal, effect, PLATFORM_ID, ChangeDetectionStrategy, Inject, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport * as i5 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHand<PERSON> } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport { BarsIcon } from 'primeng/icons/bars';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport * as i4 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nimport { Subject, interval } from 'rxjs';\nimport { debounce, filter } from 'rxjs/operators';\n\nclass MenubarService {\n    autoHide;\n    autoHideDelay;\n    mouseLeaves = new Subject();\n    mouseLeft$ = this.mouseLeaves.pipe(debounce(() => interval(this.autoHideDelay)), filter((mouseLeft) => this.autoHide && mouseLeft));\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: MenubarService, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: MenubarService });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: MenubarService, decorators: [{\n            type: Injectable\n        }] });\nclass MenubarSub {\n    el;\n    renderer;\n    cd;\n    menubarService;\n    items;\n    itemTemplate;\n    root = false;\n    autoZIndex = true;\n    baseZIndex = 0;\n    mobileActive;\n    autoDisplay;\n    menuId;\n    ariaLabel;\n    ariaLabelledBy;\n    level = 0;\n    focusedItemId;\n    activeItemPath;\n    itemClick = new EventEmitter();\n    itemMouseEnter = new EventEmitter();\n    menuFocus = new EventEmitter();\n    menuBlur = new EventEmitter();\n    menuKeydown = new EventEmitter();\n    menubarViewChild;\n    mouseLeaveSubscriber;\n    constructor(el, renderer, cd, menubarService) {\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.menubarService = menubarService;\n    }\n    ngOnInit() {\n        this.mouseLeaveSubscriber = this.menubarService.mouseLeft$.subscribe(() => {\n            this.cd.markForCheck();\n        });\n    }\n    onItemClick(event, processedItem) {\n        this.getItemProp(processedItem, 'command', { originalEvent: event, item: processedItem.item });\n        this.itemClick.emit({ originalEvent: event, processedItem, isFocus: true });\n    }\n    getItemProp(processedItem, name, params = null) {\n        return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n    }\n    getItemId(processedItem) {\n        return processedItem.item && processedItem.item?.id ? processedItem.item.id : `${this.menuId}_${processedItem.key}`;\n    }\n    getItemKey(processedItem) {\n        return this.getItemId(processedItem);\n    }\n    getItemClass(processedItem) {\n        return {\n            ...this.getItemProp(processedItem, 'class'),\n            'p-menuitem': true,\n            'p-highlight': this.isItemActive(processedItem),\n            'p-menuitem-active': this.isItemActive(processedItem),\n            'p-focus': this.isItemFocused(processedItem),\n            'p-disabled': this.isItemDisabled(processedItem)\n        };\n    }\n    getItemLabel(processedItem) {\n        return this.getItemProp(processedItem, 'label');\n    }\n    getSeparatorItemClass(processedItem) {\n        return {\n            ...this.getItemProp(processedItem, 'class'),\n            'p-menuitem-separator': true\n        };\n    }\n    isItemVisible(processedItem) {\n        return this.getItemProp(processedItem, 'visible') !== false;\n    }\n    isItemActive(processedItem) {\n        if (this.activeItemPath) {\n            return this.activeItemPath.some((path) => path.key === processedItem.key);\n        }\n    }\n    isItemDisabled(processedItem) {\n        return this.getItemProp(processedItem, 'disabled');\n    }\n    isItemFocused(processedItem) {\n        return this.focusedItemId === this.getItemId(processedItem);\n    }\n    isItemGroup(processedItem) {\n        return ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    getAriaSetSize() {\n        return this.items.filter((processedItem) => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n    }\n    getAriaPosInset(index) {\n        return index - this.items.slice(0, index).filter((processedItem) => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n    }\n    onItemMouseLeave() {\n        this.menubarService.mouseLeaves.next(true);\n    }\n    onItemMouseEnter(param) {\n        if (this.autoDisplay) {\n            this.menubarService.mouseLeaves.next(false);\n            const { event, processedItem } = param;\n            this.itemMouseEnter.emit({ originalEvent: event, processedItem });\n        }\n    }\n    ngOnDestroy() {\n        this.mouseLeaveSubscriber?.unsubscribe();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: MenubarSub, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: MenubarService }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: MenubarSub, selector: \"p-menubarSub\", inputs: { items: \"items\", itemTemplate: \"itemTemplate\", root: \"root\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", mobileActive: \"mobileActive\", autoDisplay: \"autoDisplay\", menuId: \"menuId\", ariaLabel: \"ariaLabel\", ariaLabelledBy: \"ariaLabelledBy\", level: \"level\", focusedItemId: \"focusedItemId\", activeItemPath: \"activeItemPath\" }, outputs: { itemClick: \"itemClick\", itemMouseEnter: \"itemMouseEnter\", menuFocus: \"menuFocus\", menuBlur: \"menuBlur\", menuKeydown: \"menuKeydown\" }, host: { classAttribute: \"p-element\" }, viewQueries: [{ propertyName: \"menubarViewChild\", first: true, predicate: [\"menubar\"], descendants: true, static: true }], ngImport: i0, template: `\n        <ul\n            #menubar\n            [ngClass]=\"{ 'p-submenu-list': !root, 'p-menubar-root-list': root }\"\n            [attr.data-pc-section]=\"'menu'\"\n            role=\"menu\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n            [tabindex]=\"0\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            [attr.id]=\"menuId\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [style]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div class=\"p-menuitem-content\" [attr.data-pc-section]=\"'content'\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({$event, processedItem})\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!menubar.submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"root\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"!root\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"menubar.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    class=\"p-menuitem-icon\"\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                ></span>\n                                <span class=\"p-menuitem-text\" *ngIf=\"getItemProp(processedItem, 'escape'); else htmlRouteLabel\">{{ getItemLabel(processedItem) }}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span></ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!menubar.submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" *ngIf=\"root\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" *ngIf=\"!root\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"menubar.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item }\"></ng-template>\n                        </ng-container>\n                    </div>\n                    <p-menubarSub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [items]=\"processedItem.items\"\n                        [mobileActive]=\"mobileActive\"\n                        [autoDisplay]=\"autoDisplay\"\n                        [menuId]=\"menuId\"\n                        [activeItemPath]=\"activeItemPath\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [level]=\"level + 1\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                    >\n                    </p-menubarSub>\n                </li>\n            </ng-template>\n        </ul>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i1.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgForOf), selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.RouterLink), selector: \"[routerLink]\", inputs: [\"target\", \"queryParams\", \"fragment\", \"queryParamsHandling\", \"state\", \"relativeTo\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"routerLink\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.RouterLinkActive), selector: \"[routerLinkActive]\", inputs: [\"routerLinkActiveOptions\", \"ariaCurrentWhenActive\", \"routerLinkActive\"], outputs: [\"isActiveChange\"], exportAs: [\"routerLinkActive\"] }, { kind: \"directive\", type: i0.forwardRef(() => i3.Ripple), selector: \"[pRipple]\" }, { kind: \"directive\", type: i0.forwardRef(() => i4.Tooltip), selector: \"[pTooltip]\", inputs: [\"tooltipPosition\", \"tooltipEvent\", \"appendTo\", \"positionStyle\", \"tooltipStyleClass\", \"tooltipZIndex\", \"escape\", \"showDelay\", \"hideDelay\", \"life\", \"positionTop\", \"positionLeft\", \"autoHide\", \"fitContent\", \"hideOnEscape\", \"pTooltip\", \"tooltipDisabled\", \"tooltipOptions\"] }, { kind: \"component\", type: i0.forwardRef(() => AngleDownIcon), selector: \"AngleDownIcon\" }, { kind: \"component\", type: i0.forwardRef(() => AngleRightIcon), selector: \"AngleRightIcon\" }, { kind: \"component\", type: i0.forwardRef(() => MenubarSub), selector: \"p-menubarSub\", inputs: [\"items\", \"itemTemplate\", \"root\", \"autoZIndex\", \"baseZIndex\", \"mobileActive\", \"autoDisplay\", \"menuId\", \"ariaLabel\", \"ariaLabelledBy\", \"level\", \"focusedItemId\", \"activeItemPath\"], outputs: [\"itemClick\", \"itemMouseEnter\", \"menuFocus\", \"menuBlur\", \"menuKeydown\"] }], encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: MenubarSub, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-menubarSub',\n                    template: `\n        <ul\n            #menubar\n            [ngClass]=\"{ 'p-submenu-list': !root, 'p-menubar-root-list': root }\"\n            [attr.data-pc-section]=\"'menu'\"\n            role=\"menu\"\n            (focus)=\"menuFocus.emit($event)\"\n            (blur)=\"menuBlur.emit($event)\"\n            [tabindex]=\"0\"\n            [attr.aria-label]=\"ariaLabel\"\n            [attr.aria-labelledBy]=\"ariaLabelledBy\"\n            (keydown)=\"menuKeydown.emit($event)\"\n            [attr.id]=\"menuId\"\n            [attr.aria-activedescendant]=\"focusedItemId\"\n        >\n            <ng-template ngFor let-processedItem [ngForOf]=\"items\" let-index=\"index\">\n                <li\n                    *ngIf=\"isItemVisible(processedItem) && getItemProp(processedItem, 'separator')\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [style]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getSeparatorItemClass(processedItem)\"\n                    role=\"separator\"\n                    [attr.data-pc-section]=\"'separator'\"\n                ></li>\n                <li\n                    #listItem\n                    *ngIf=\"isItemVisible(processedItem) && !getItemProp(processedItem, 'separator')\"\n                    role=\"menuitem\"\n                    [attr.id]=\"getItemId(processedItem)\"\n                    [attr.data-pc-section]=\"'menuitem'\"\n                    [attr.data-p-highlight]=\"isItemActive(processedItem)\"\n                    [attr.data-p-focused]=\"isItemFocused(processedItem)\"\n                    [attr.data-p-disabled]=\"isItemDisabled(processedItem)\"\n                    [attr.aria-label]=\"getItemLabel(processedItem)\"\n                    [attr.aria-disabled]=\"isItemDisabled(processedItem) || undefined\"\n                    [attr.aria-haspopup]=\"isItemGroup(processedItem) && !getItemProp(processedItem, 'to') ? 'menu' : undefined\"\n                    [attr.aria-expanded]=\"isItemGroup(processedItem) ? isItemActive(processedItem) : undefined\"\n                    [attr.aria-level]=\"level + 1\"\n                    [attr.aria-setsize]=\"getAriaSetSize()\"\n                    [attr.aria-posinset]=\"getAriaPosInset(index)\"\n                    [ngStyle]=\"getItemProp(processedItem, 'style')\"\n                    [ngClass]=\"getItemClass(processedItem)\"\n                    [class]=\"getItemProp(processedItem, 'styleClass')\"\n                    pTooltip\n                    [tooltipOptions]=\"getItemProp(processedItem, 'tooltipOptions')\"\n                >\n                    <div class=\"p-menuitem-content\" [attr.data-pc-section]=\"'content'\" (click)=\"onItemClick($event, processedItem)\" (mouseenter)=\"onItemMouseEnter({$event, processedItem})\">\n                        <ng-container *ngIf=\"!itemTemplate\">\n                            <a\n                                *ngIf=\"!getItemProp(processedItem, 'routerLink')\"\n                                [attr.href]=\"getItemProp(processedItem, 'url')\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [attr.tabindex]=\"-1\"\n                                pRipple\n                            >\n                                <span\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    class=\"p-menuitem-icon\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                >\n                                </span>\n                                <span *ngIf=\"getItemProp(processedItem, 'escape'); else htmlLabel\" class=\"p-menuitem-text\" [attr.data-pc-section]=\"'label'\">\n                                    {{ getItemLabel(processedItem) }}\n                                </span>\n                                <ng-template #htmlLabel>\n                                    <span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span>\n                                </ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!menubar.submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"root\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" *ngIf=\"!root\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"menubar.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                            <a\n                                *ngIf=\"getItemProp(processedItem, 'routerLink')\"\n                                [routerLink]=\"getItemProp(processedItem, 'routerLink')\"\n                                [attr.data-automationid]=\"getItemProp(processedItem, 'automationId')\"\n                                [attr.tabindex]=\"-1\"\n                                [attr.aria-hidden]=\"true\"\n                                [attr.data-pc-section]=\"'action'\"\n                                [queryParams]=\"getItemProp(processedItem, 'queryParams')\"\n                                [routerLinkActive]=\"'p-menuitem-link-active'\"\n                                [routerLinkActiveOptions]=\"getItemProp(processedItem, 'routerLinkActiveOptions') || { exact: false }\"\n                                [target]=\"getItemProp(processedItem, 'target')\"\n                                [ngClass]=\"{ 'p-menuitem-link': true, 'p-disabled': getItemProp(processedItem, 'disabled') }\"\n                                [fragment]=\"getItemProp(processedItem, 'fragment')\"\n                                [queryParamsHandling]=\"getItemProp(processedItem, 'queryParamsHandling')\"\n                                [preserveFragment]=\"getItemProp(processedItem, 'preserveFragment')\"\n                                [skipLocationChange]=\"getItemProp(processedItem, 'skipLocationChange')\"\n                                [replaceUrl]=\"getItemProp(processedItem, 'replaceUrl')\"\n                                [state]=\"getItemProp(processedItem, 'state')\"\n                                pRipple\n                            >\n                                <span\n                                    class=\"p-menuitem-icon\"\n                                    *ngIf=\"getItemProp(processedItem, 'icon')\"\n                                    [ngClass]=\"getItemProp(processedItem, 'icon')\"\n                                    [ngStyle]=\"getItemProp(processedItem, 'iconStyle')\"\n                                    [attr.data-pc-section]=\"'icon'\"\n                                    [attr.aria-hidden]=\"true\"\n                                    [attr.tabindex]=\"-1\"\n                                ></span>\n                                <span class=\"p-menuitem-text\" *ngIf=\"getItemProp(processedItem, 'escape'); else htmlRouteLabel\">{{ getItemLabel(processedItem) }}</span>\n                                <ng-template #htmlRouteLabel><span class=\"p-menuitem-text\" [innerHTML]=\"getItemLabel(processedItem)\" [attr.data-pc-section]=\"'label'\"></span></ng-template>\n                                <span class=\"p-menuitem-badge\" *ngIf=\"getItemProp(processedItem, 'badge')\" [ngClass]=\"getItemProp(processedItem, 'badgeStyleClass')\">{{ getItemProp(processedItem, 'badge') }}</span>\n                                <ng-container *ngIf=\"isItemGroup(processedItem)\">\n                                    <ng-container *ngIf=\"!menubar.submenuIconTemplate\">\n                                        <AngleDownIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" *ngIf=\"root\" />\n                                        <AngleRightIcon [styleClass]=\"'p-submenu-icon'\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\" *ngIf=\"!root\" />\n                                    </ng-container>\n                                    <ng-template *ngTemplateOutlet=\"menubar.submenuIconTemplate\" [attr.data-pc-section]=\"'submenuicon'\" [attr.aria-hidden]=\"true\"></ng-template>\n                                </ng-container>\n                            </a>\n                        </ng-container>\n                        <ng-container *ngIf=\"itemTemplate\">\n                            <ng-template *ngTemplateOutlet=\"itemTemplate; context: { $implicit: processedItem.item }\"></ng-template>\n                        </ng-container>\n                    </div>\n                    <p-menubarSub\n                        *ngIf=\"isItemVisible(processedItem) && isItemGroup(processedItem)\"\n                        [itemTemplate]=\"itemTemplate\"\n                        [items]=\"processedItem.items\"\n                        [mobileActive]=\"mobileActive\"\n                        [autoDisplay]=\"autoDisplay\"\n                        [menuId]=\"menuId\"\n                        [activeItemPath]=\"activeItemPath\"\n                        [focusedItemId]=\"focusedItemId\"\n                        [level]=\"level + 1\"\n                        (itemClick)=\"itemClick.emit($event)\"\n                        (itemMouseEnter)=\"onItemMouseEnter($event)\"\n                    >\n                    </p-menubarSub>\n                </li>\n            </ng-template>\n        </ul>\n    `,\n                    encapsulation: ViewEncapsulation.None,\n                    host: {\n                        class: 'p-element'\n                    }\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: MenubarService }], propDecorators: { items: [{\n                type: Input\n            }], itemTemplate: [{\n                type: Input\n            }], root: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], mobileActive: [{\n                type: Input\n            }], autoDisplay: [{\n                type: Input\n            }], menuId: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], level: [{\n                type: Input\n            }], focusedItemId: [{\n                type: Input\n            }], activeItemPath: [{\n                type: Input\n            }], itemClick: [{\n                type: Output\n            }], itemMouseEnter: [{\n                type: Output\n            }], menuFocus: [{\n                type: Output\n            }], menuBlur: [{\n                type: Output\n            }], menuKeydown: [{\n                type: Output\n            }], menubarViewChild: [{\n                type: ViewChild,\n                args: ['menubar', { static: true }]\n            }] } });\n/**\n * Menubar is a horizontal menu component.\n * @group Components\n */\nclass Menubar {\n    document;\n    platformId;\n    el;\n    renderer;\n    cd;\n    config;\n    menubarService;\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    set model(value) {\n        this._model = value;\n        this._processedItems = this.createProcessedItems(this._model || []);\n    }\n    get model() {\n        return this._model;\n    }\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * Whether to show a root submenu on mouse over.\n     * @defaultValue true\n     * @group Props\n     */\n    autoDisplay = true;\n    /**\n     * Whether to hide a root submenu when mouse leaves.\n     * @group Props\n     */\n    autoHide;\n    /**\n     * Delay to hide the root submenu in milliseconds when mouse leaves.\n     * @group Props\n     */\n    autoHideDelay = 100;\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    id;\n    /**\n     * Defines a string value that labels an interactive element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Identifier of the underlying input element.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Callback to execute when button is focused.\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to execute when button loses focus.\n     * @param {FocusEvent} event - Focus event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    templates;\n    menubutton;\n    rootmenu;\n    startTemplate;\n    endTemplate;\n    menuIconTemplate;\n    submenuIconTemplate;\n    itemTemplate;\n    mobileActive;\n    outsideClickListener;\n    resizeListener;\n    mouseLeaveSubscriber;\n    dirty = false;\n    focused = false;\n    activeItemPath = signal([]);\n    number = signal(0);\n    focusedItemInfo = signal({ index: -1, level: 0, parentKey: '', item: null });\n    searchValue = '';\n    searchTimeout;\n    _processedItems;\n    _model;\n    get visibleItems() {\n        const processedItem = this.activeItemPath().find((p) => p.key === this.focusedItemInfo().parentKey);\n        return processedItem ? processedItem.items : this.processedItems;\n    }\n    get processedItems() {\n        if (!this._processedItems || !this._processedItems.length) {\n            this._processedItems = this.createProcessedItems(this.model || []);\n        }\n        return this._processedItems;\n    }\n    get focusedItemId() {\n        const focusedItem = this.focusedItemInfo();\n        return focusedItem.item && focusedItem.item?.id ? focusedItem.item.id : focusedItem.index !== -1 ? `${this.id}${ObjectUtils.isNotEmpty(focusedItem.parentKey) ? '_' + focusedItem.parentKey : ''}_${focusedItem.index}` : null;\n    }\n    constructor(document, platformId, el, renderer, cd, config, menubarService) {\n        this.document = document;\n        this.platformId = platformId;\n        this.el = el;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.config = config;\n        this.menubarService = menubarService;\n        effect(() => {\n            const path = this.activeItemPath();\n            if (ObjectUtils.isNotEmpty(path)) {\n                this.bindOutsideClickListener();\n                this.bindResizeListener();\n            }\n            else {\n                this.unbindOutsideClickListener();\n                this.unbindResizeListener();\n            }\n        });\n    }\n    ngOnInit() {\n        this.menubarService.autoHide = this.autoHide;\n        this.menubarService.autoHideDelay = this.autoHideDelay;\n        this.mouseLeaveSubscriber = this.menubarService.mouseLeft$.subscribe(() => this.unbindOutsideClickListener());\n        this.id = this.id || UniqueComponentId();\n    }\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'start':\n                    this.startTemplate = item.template;\n                    break;\n                case 'end':\n                    this.endTemplate = item.template;\n                    break;\n                case 'menuicon':\n                    this.menuIconTemplate = item.template;\n                    break;\n                case 'submenuicon':\n                    this.submenuIconTemplate = item.template;\n                    break;\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    createProcessedItems(items, level = 0, parent = {}, parentKey = '') {\n        const processedItems = [];\n        items &&\n            items.forEach((item, index) => {\n                const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n                const newItem = {\n                    item,\n                    index,\n                    level,\n                    key,\n                    parent,\n                    parentKey\n                };\n                newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n                processedItems.push(newItem);\n            });\n        return processedItems;\n    }\n    getItemProp(item, name) {\n        return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n    }\n    menuButtonClick(event) {\n        this.toggle(event);\n    }\n    menuButtonKeydown(event) {\n        (event.code === 'Enter' || event.code === 'Space') && this.menuButtonClick(event);\n    }\n    onItemClick(event) {\n        const { originalEvent, processedItem } = event;\n        const grouped = this.isProcessedItemGroup(processedItem);\n        const root = ObjectUtils.isEmpty(processedItem.parent);\n        const selected = this.isSelected(processedItem);\n        if (selected) {\n            const { index, key, level, parentKey, item } = processedItem;\n            this.activeItemPath.set(this.activeItemPath().filter((p) => key !== p.key && key.startsWith(p.key)));\n            this.focusedItemInfo.set({ index, level, parentKey, item });\n            this.dirty = !root;\n            DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n        }\n        else {\n            if (grouped) {\n                this.onItemChange(event);\n            }\n            else {\n                const rootProcessedItem = root ? processedItem : this.activeItemPath().find((p) => p.parentKey === '');\n                this.hide(originalEvent);\n                this.changeFocusedItemIndex(originalEvent, rootProcessedItem ? rootProcessedItem.index : -1);\n                this.mobileActive = false;\n                DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n            }\n        }\n    }\n    onItemMouseEnter(event) {\n        if (!DomHandler.isTouchDevice()) {\n            if (!this.mobileActive) {\n                this.onItemChange(event);\n            }\n        }\n    }\n    changeFocusedItemIndex(event, index) {\n        const processedItem = this.findVisibleItem(index);\n        if (this.focusedItemInfo().index !== index) {\n            const focusedItemInfo = this.focusedItemInfo();\n            this.focusedItemInfo.set({ ...focusedItemInfo, item: processedItem.item, index });\n            this.scrollInView();\n        }\n    }\n    scrollInView(index = -1) {\n        const id = index !== -1 ? `${this.id}_${index}` : this.focusedItemId;\n        const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${id}\"]`);\n        if (element) {\n            element.scrollIntoView && element.scrollIntoView({ block: 'nearest', inline: 'nearest' });\n        }\n    }\n    onItemChange(event) {\n        const { processedItem, isFocus } = event;\n        if (ObjectUtils.isEmpty(processedItem))\n            return;\n        const { index, key, level, parentKey, items, item } = processedItem;\n        const grouped = ObjectUtils.isNotEmpty(items);\n        const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== parentKey && p.parentKey !== key);\n        grouped && activeItemPath.push(processedItem);\n        this.focusedItemInfo.set({ index, level, parentKey, item });\n        this.activeItemPath.set(activeItemPath);\n        grouped && (this.dirty = true);\n        isFocus && DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n    }\n    toggle(event) {\n        if (this.mobileActive) {\n            this.mobileActive = false;\n            ZIndexUtils.clear(this.rootmenu.el.nativeElement);\n            this.hide();\n        }\n        else {\n            this.mobileActive = true;\n            ZIndexUtils.set('menu', this.rootmenu.el.nativeElement, this.config.zIndex.menu);\n            setTimeout(() => {\n                this.show();\n            }, 0);\n        }\n        this.cd.markForCheck();\n        this.bindOutsideClickListener();\n        event.preventDefault();\n    }\n    hide(event, isFocus) {\n        if (this.mobileActive) {\n            setTimeout(() => {\n                DomHandler.focus(this.menubutton.nativeElement);\n            }, 0);\n        }\n        this.activeItemPath.set([]);\n        this.focusedItemInfo.set({ index: -1, level: 0, parentKey: '', item: null });\n        isFocus && DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n        this.dirty = false;\n    }\n    show() {\n        const processedItem = this.findVisibleItem(this.findFirstFocusedItemIndex());\n        this.focusedItemInfo.set({ index: this.findFirstFocusedItemIndex(), level: 0, parentKey: '', item: processedItem.item });\n        DomHandler.focus(this.rootmenu.menubarViewChild.nativeElement);\n    }\n    onMenuFocus(event) {\n        this.focused = true;\n        const processedItem = this.findVisibleItem(this.findFirstFocusedItemIndex());\n        const focusedItemInfo = this.focusedItemInfo().index !== -1 ? this.focusedItemInfo() : { index: this.findFirstFocusedItemIndex(), level: 0, parentKey: '', item: processedItem.item };\n        this.focusedItemInfo.set(focusedItemInfo);\n        this.onFocus.emit(event);\n    }\n    onMenuBlur(event) {\n        this.focused = false;\n        this.focusedItemInfo.set({ index: -1, level: 0, parentKey: '', item: null });\n        this.searchValue = '';\n        this.dirty = false;\n        this.onBlur.emit(event);\n    }\n    onKeyDown(event) {\n        const metaKey = event.metaKey || event.ctrlKey;\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onArrowDownKey(event);\n                break;\n            case 'ArrowUp':\n                this.onArrowUpKey(event);\n                break;\n            case 'ArrowLeft':\n                this.onArrowLeftKey(event);\n                break;\n            case 'ArrowRight':\n                this.onArrowRightKey(event);\n                break;\n            case 'Home':\n                this.onHomeKey(event);\n                break;\n            case 'End':\n                this.onEndKey(event);\n                break;\n            case 'Space':\n                this.onSpaceKey(event);\n                break;\n            case 'Enter':\n                this.onEnterKey(event);\n                break;\n            case 'Escape':\n                this.onEscapeKey(event);\n                break;\n            case 'Tab':\n                this.onTabKey(event);\n                break;\n            case 'PageDown':\n            case 'PageUp':\n            case 'Backspace':\n            case 'ShiftLeft':\n            case 'ShiftRight':\n                //NOOP\n                break;\n            default:\n                if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n                    this.searchItems(event, event.key);\n                }\n                break;\n        }\n    }\n    findVisibleItem(index) {\n        return ObjectUtils.isNotEmpty(this.visibleItems) ? this.visibleItems[index] : null;\n    }\n    findFirstFocusedItemIndex() {\n        const selectedIndex = this.findSelectedItemIndex();\n        return selectedIndex < 0 ? this.findFirstItemIndex() : selectedIndex;\n    }\n    findFirstItemIndex() {\n        return this.visibleItems.findIndex((processedItem) => this.isValidItem(processedItem));\n    }\n    findSelectedItemIndex() {\n        return this.visibleItems.findIndex((processedItem) => this.isValidSelectedItem(processedItem));\n    }\n    isProcessedItemGroup(processedItem) {\n        return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    isSelected(processedItem) {\n        return this.activeItemPath().some((p) => p.key === processedItem.key);\n    }\n    isValidSelectedItem(processedItem) {\n        return this.isValidItem(processedItem) && this.isSelected(processedItem);\n    }\n    isValidItem(processedItem) {\n        return !!processedItem && !this.isItemDisabled(processedItem.item) && !this.isItemSeparator(processedItem.item);\n    }\n    isItemDisabled(item) {\n        return this.getItemProp(item, 'disabled');\n    }\n    isItemSeparator(item) {\n        return this.getItemProp(item, 'separator');\n    }\n    isItemMatched(processedItem) {\n        return this.isValidItem(processedItem) && this.getProccessedItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n    }\n    isProccessedItemGroup(processedItem) {\n        return processedItem && ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    searchItems(event, char) {\n        this.searchValue = (this.searchValue || '') + char;\n        let itemIndex = -1;\n        let matched = false;\n        if (this.focusedItemInfo().index !== -1) {\n            itemIndex = this.visibleItems.slice(this.focusedItemInfo().index).findIndex((processedItem) => this.isItemMatched(processedItem));\n            itemIndex = itemIndex === -1 ? this.visibleItems.slice(0, this.focusedItemInfo().index).findIndex((processedItem) => this.isItemMatched(processedItem)) : itemIndex + this.focusedItemInfo().index;\n        }\n        else {\n            itemIndex = this.visibleItems.findIndex((processedItem) => this.isItemMatched(processedItem));\n        }\n        if (itemIndex !== -1) {\n            matched = true;\n        }\n        if (itemIndex === -1 && this.focusedItemInfo().index === -1) {\n            itemIndex = this.findFirstFocusedItemIndex();\n        }\n        if (itemIndex !== -1) {\n            this.changeFocusedItemIndex(event, itemIndex);\n        }\n        if (this.searchTimeout) {\n            clearTimeout(this.searchTimeout);\n        }\n        this.searchTimeout = setTimeout(() => {\n            this.searchValue = '';\n            this.searchTimeout = null;\n        }, 500);\n        return matched;\n    }\n    getProccessedItemLabel(processedItem) {\n        return processedItem ? this.getItemLabel(processedItem.item) : undefined;\n    }\n    getItemLabel(item) {\n        return this.getItemProp(item, 'label');\n    }\n    onArrowDownKey(event) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const root = processedItem ? ObjectUtils.isEmpty(processedItem.parent) : null;\n        if (root) {\n            const grouped = this.isProccessedItemGroup(processedItem);\n            if (grouped) {\n                this.onItemChange({ originalEvent: event, processedItem });\n                this.focusedItemInfo.set({ index: -1, parentKey: processedItem.key, item: processedItem.item });\n                this.onArrowRightKey(event);\n            }\n        }\n        else {\n            const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n            this.changeFocusedItemIndex(event, itemIndex);\n            event.preventDefault();\n        }\n    }\n    onArrowRightKey(event) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const parentItem = processedItem ? this.activeItemPath().find((p) => p.key === processedItem.parentKey) : null;\n        if (parentItem) {\n            const grouped = this.isProccessedItemGroup(processedItem);\n            if (grouped) {\n                this.onItemChange({ originalEvent: event, processedItem });\n                this.focusedItemInfo.set({ index: -1, parentKey: processedItem.key, item: processedItem.item });\n                this.onArrowDownKey(event);\n            }\n        }\n        else {\n            const itemIndex = this.focusedItemInfo().index !== -1 ? this.findNextItemIndex(this.focusedItemInfo().index) : this.findFirstFocusedItemIndex();\n            this.changeFocusedItemIndex(event, itemIndex);\n            event.preventDefault();\n        }\n    }\n    onArrowUpKey(event) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const root = ObjectUtils.isEmpty(processedItem.parent);\n        if (root) {\n            const grouped = this.isProccessedItemGroup(processedItem);\n            if (grouped) {\n                this.onItemChange({ originalEvent: event, processedItem });\n                this.focusedItemInfo.set({ index: -1, parentKey: processedItem.key, item: processedItem.item });\n                const itemIndex = this.findLastItemIndex();\n                this.changeFocusedItemIndex(event, itemIndex);\n            }\n        }\n        else {\n            const parentItem = this.activeItemPath().find((p) => p.key === processedItem.parentKey);\n            if (this.focusedItemInfo().index === 0) {\n                this.focusedItemInfo.set({ index: -1, parentKey: parentItem ? parentItem.parentKey : '', item: processedItem.item });\n                this.searchValue = '';\n                this.onArrowLeftKey(event);\n                const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== this.focusedItemInfo().parentKey);\n                this.activeItemPath.set(activeItemPath);\n            }\n            else {\n                const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n                this.changeFocusedItemIndex(event, itemIndex);\n            }\n        }\n        event.preventDefault();\n    }\n    onArrowLeftKey(event) {\n        const processedItem = this.visibleItems[this.focusedItemInfo().index];\n        const parentItem = processedItem ? this.activeItemPath().find((p) => p.key === processedItem.parentKey) : null;\n        if (parentItem) {\n            this.onItemChange({ originalEvent: event, processedItem: parentItem });\n            const activeItemPath = this.activeItemPath().filter((p) => p.parentKey !== this.focusedItemInfo().parentKey);\n            this.activeItemPath.set(activeItemPath);\n            event.preventDefault();\n        }\n        else {\n            const itemIndex = this.focusedItemInfo().index !== -1 ? this.findPrevItemIndex(this.focusedItemInfo().index) : this.findLastFocusedItemIndex();\n            this.changeFocusedItemIndex(event, itemIndex);\n            event.preventDefault();\n        }\n    }\n    onHomeKey(event) {\n        this.changeFocusedItemIndex(event, this.findFirstItemIndex());\n        event.preventDefault();\n    }\n    onEndKey(event) {\n        this.changeFocusedItemIndex(event, this.findLastItemIndex());\n        event.preventDefault();\n    }\n    onSpaceKey(event) {\n        this.onEnterKey(event);\n    }\n    onEscapeKey(event) {\n        this.hide(event, true);\n        this.focusedItemInfo().index = this.findFirstFocusedItemIndex();\n        event.preventDefault();\n    }\n    onTabKey(event) {\n        if (this.focusedItemInfo().index !== -1) {\n            const processedItem = this.visibleItems[this.focusedItemInfo().index];\n            const grouped = this.isProccessedItemGroup(processedItem);\n            !grouped && this.onItemChange({ originalEvent: event, processedItem });\n        }\n        this.hide();\n    }\n    onEnterKey(event) {\n        if (this.focusedItemInfo().index !== -1) {\n            const element = DomHandler.findSingle(this.rootmenu.el.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n            const anchorElement = element && DomHandler.findSingle(element, 'a[data-pc-section=\"action\"]');\n            anchorElement ? anchorElement.click() : element && element.click();\n            const processedItem = this.visibleItems[this.focusedItemInfo().index];\n            const grouped = this.isProccessedItemGroup(processedItem);\n            !grouped && (this.focusedItemInfo().index = this.findFirstFocusedItemIndex());\n        }\n        event.preventDefault();\n    }\n    findLastFocusedItemIndex() {\n        const selectedIndex = this.findSelectedItemIndex();\n        return selectedIndex < 0 ? this.findLastItemIndex() : selectedIndex;\n    }\n    findLastItemIndex() {\n        return ObjectUtils.findLastIndex(this.visibleItems, (processedItem) => this.isValidItem(processedItem));\n    }\n    findPrevItemIndex(index) {\n        const matchedItemIndex = index > 0 ? ObjectUtils.findLastIndex(this.visibleItems.slice(0, index), (processedItem) => this.isValidItem(processedItem)) : -1;\n        return matchedItemIndex > -1 ? matchedItemIndex : index;\n    }\n    findNextItemIndex(index) {\n        const matchedItemIndex = index < this.visibleItems.length - 1 ? this.visibleItems.slice(index + 1).findIndex((processedItem) => this.isValidItem(processedItem)) : -1;\n        return matchedItemIndex > -1 ? matchedItemIndex + index + 1 : index;\n    }\n    bindResizeListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.resizeListener) {\n                this.resizeListener = this.renderer.listen(this.document.defaultView, 'resize', (event) => {\n                    if (!DomHandler.isTouchDevice()) {\n                        this.hide(event, true);\n                    }\n                    this.mobileActive = false;\n                });\n            }\n        }\n    }\n    bindOutsideClickListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.outsideClickListener) {\n                this.outsideClickListener = this.renderer.listen(this.document, 'click', (event) => {\n                    const isOutsideContainer = this.rootmenu.el.nativeElement !== event.target && !this.rootmenu.el.nativeElement.contains(event.target);\n                    const isOutsideMenuButton = this.mobileActive && this.menubutton.nativeElement !== event.target && !this.menubutton.nativeElement.contains(event.target);\n                    if (isOutsideContainer) {\n                        isOutsideMenuButton ? (this.mobileActive = false) : this.hide();\n                    }\n                });\n            }\n        }\n    }\n    unbindOutsideClickListener() {\n        if (this.outsideClickListener) {\n            this.outsideClickListener();\n            this.outsideClickListener = null;\n        }\n    }\n    unbindResizeListener() {\n        if (this.resizeListener) {\n            this.resizeListener();\n            this.resizeListener = null;\n        }\n    }\n    ngOnDestroy() {\n        this.mouseLeaveSubscriber?.unsubscribe();\n        this.unbindOutsideClickListener();\n        this.unbindResizeListener();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Menubar, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i5.PrimeNGConfig }, { token: MenubarService }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: Menubar, selector: \"p-menubar\", inputs: { model: \"model\", style: \"style\", styleClass: \"styleClass\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", autoDisplay: \"autoDisplay\", autoHide: \"autoHide\", autoHideDelay: \"autoHideDelay\", id: \"id\", ariaLabel: \"ariaLabel\", ariaLabelledBy: \"ariaLabelledBy\" }, outputs: { onFocus: \"onFocus\", onBlur: \"onBlur\" }, host: { classAttribute: \"p-element\" }, providers: [MenubarService], queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"menubutton\", first: true, predicate: [\"menubutton\"], descendants: true }, { propertyName: \"rootmenu\", first: true, predicate: [\"rootmenu\"], descendants: true }], ngImport: i0, template: `\n        <div [ngClass]=\"{ 'p-menubar p-component': true, 'p-menubar-mobile-active': mobileActive }\" [class]=\"styleClass\" [ngStyle]=\"style\" [attr.data-pc-section]=\"'root'\" [attr.data-pc-name]=\"'menubar'\">\n            <div class=\"p-menubar-start\" *ngIf=\"startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <a\n                #menubutton\n                tabindex=\"0\"\n                role=\"button\"\n                [attr.aria-haspopup]=\"model.length && model.length > 0 ? true : false\"\n                [attr.aria-expanded]=\"mobileActive\"\n                [attr.aria-controls]=\"id\"\n                [attr.aria-label]=\"config.translation.aria.navigation\"\n                [attr.data-pc-section]=\"'button'\"\n                *ngIf=\"model && model.length > 0\"\n                class=\"p-menubar-button\"\n                (click)=\"menuButtonClick($event)\"\n                (keydown)=\"menuButtonKeydown($event)\"\n            >\n                <BarsIcon *ngIf=\"!menuIconTemplate\" />\n                <ng-template *ngTemplateOutlet=\"menuIconTemplate\"></ng-template>\n            </a>\n            <p-menubarSub\n                #rootmenu\n                [items]=\"processedItems\"\n                [itemTemplate]=\"itemTemplate\"\n                [menuId]=\"id\"\n                [root]=\"true\"\n                [baseZIndex]=\"baseZIndex\"\n                [autoZIndex]=\"autoZIndex\"\n                [mobileActive]=\"mobileActive\"\n                [autoDisplay]=\"autoDisplay\"\n                [ariaLabel]=\"ariaLabel\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [activeItemPath]=\"activeItemPath()\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-menubarSub>\n            <div class=\"p-menubar-end\" *ngIf=\"endTemplate; else legacy\">\n                <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n            </div>\n            <ng-template #legacy>\n                <div class=\"p-menubar-end\">\n                    <ng-content></ng-content>\n                </div>\n            </ng-template>\n        </div>\n    `, isInline: true, styles: [\"@layer primeng{.p-menubar{display:flex;align-items:center}.p-menubar ul{margin:0;padding:0;list-style:none}.p-menubar .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menubar .p-menuitem-text{line-height:1}.p-menubar .p-menuitem{position:relative}.p-menubar-root-list{display:flex;align-items:center;flex-wrap:wrap}.p-menubar-root-list>li ul{display:none;z-index:1}.p-menubar-root-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block}.p-menubar .p-submenu-list{display:none;position:absolute;z-index:2}.p-menubar .p-submenu-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block;left:100%;top:0}.p-menubar .p-submenu-list .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-menubar .p-menubar-root-list .p-icon-wrapper,.p-menubar .p-submenu-list .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-menubar .p-menubar-custom,.p-menubar .p-menubar-end{margin-left:auto;align-self:center}.p-menubar-button{display:none;cursor:pointer;align-items:center;justify-content:center}}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i1.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: i0.forwardRef(() => BarsIcon), selector: \"BarsIcon\" }, { kind: \"component\", type: i0.forwardRef(() => MenubarSub), selector: \"p-menubarSub\", inputs: [\"items\", \"itemTemplate\", \"root\", \"autoZIndex\", \"baseZIndex\", \"mobileActive\", \"autoDisplay\", \"menuId\", \"ariaLabel\", \"ariaLabelledBy\", \"level\", \"focusedItemId\", \"activeItemPath\"], outputs: [\"itemClick\", \"itemMouseEnter\", \"menuFocus\", \"menuBlur\", \"menuKeydown\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Menubar, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-menubar', template: `\n        <div [ngClass]=\"{ 'p-menubar p-component': true, 'p-menubar-mobile-active': mobileActive }\" [class]=\"styleClass\" [ngStyle]=\"style\" [attr.data-pc-section]=\"'root'\" [attr.data-pc-name]=\"'menubar'\">\n            <div class=\"p-menubar-start\" *ngIf=\"startTemplate\">\n                <ng-container *ngTemplateOutlet=\"startTemplate\"></ng-container>\n            </div>\n            <a\n                #menubutton\n                tabindex=\"0\"\n                role=\"button\"\n                [attr.aria-haspopup]=\"model.length && model.length > 0 ? true : false\"\n                [attr.aria-expanded]=\"mobileActive\"\n                [attr.aria-controls]=\"id\"\n                [attr.aria-label]=\"config.translation.aria.navigation\"\n                [attr.data-pc-section]=\"'button'\"\n                *ngIf=\"model && model.length > 0\"\n                class=\"p-menubar-button\"\n                (click)=\"menuButtonClick($event)\"\n                (keydown)=\"menuButtonKeydown($event)\"\n            >\n                <BarsIcon *ngIf=\"!menuIconTemplate\" />\n                <ng-template *ngTemplateOutlet=\"menuIconTemplate\"></ng-template>\n            </a>\n            <p-menubarSub\n                #rootmenu\n                [items]=\"processedItems\"\n                [itemTemplate]=\"itemTemplate\"\n                [menuId]=\"id\"\n                [root]=\"true\"\n                [baseZIndex]=\"baseZIndex\"\n                [autoZIndex]=\"autoZIndex\"\n                [mobileActive]=\"mobileActive\"\n                [autoDisplay]=\"autoDisplay\"\n                [ariaLabel]=\"ariaLabel\"\n                [ariaLabelledBy]=\"ariaLabelledBy\"\n                [focusedItemId]=\"focused ? focusedItemId : undefined\"\n                [activeItemPath]=\"activeItemPath()\"\n                (itemClick)=\"onItemClick($event)\"\n                (menuFocus)=\"onMenuFocus($event)\"\n                (menuBlur)=\"onMenuBlur($event)\"\n                (menuKeydown)=\"onKeyDown($event)\"\n                (itemMouseEnter)=\"onItemMouseEnter($event)\"\n            ></p-menubarSub>\n            <div class=\"p-menubar-end\" *ngIf=\"endTemplate; else legacy\">\n                <ng-container *ngTemplateOutlet=\"endTemplate\"></ng-container>\n            </div>\n            <ng-template #legacy>\n                <div class=\"p-menubar-end\">\n                    <ng-content></ng-content>\n                </div>\n            </ng-template>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, providers: [MenubarService], styles: [\"@layer primeng{.p-menubar{display:flex;align-items:center}.p-menubar ul{margin:0;padding:0;list-style:none}.p-menubar .p-menuitem-link{cursor:pointer;display:flex;align-items:center;text-decoration:none;overflow:hidden;position:relative}.p-menubar .p-menuitem-text{line-height:1}.p-menubar .p-menuitem{position:relative}.p-menubar-root-list{display:flex;align-items:center;flex-wrap:wrap}.p-menubar-root-list>li ul{display:none;z-index:1}.p-menubar-root-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block}.p-menubar .p-submenu-list{display:none;position:absolute;z-index:2}.p-menubar .p-submenu-list>.p-menuitem-active>p-menubarsub>.p-submenu-list{display:block;left:100%;top:0}.p-menubar .p-submenu-list .p-menuitem-link .p-submenu-icon:not(svg){margin-left:auto}.p-menubar .p-menubar-root-list .p-icon-wrapper,.p-menubar .p-submenu-list .p-menuitem-link .p-icon-wrapper{margin-left:auto}.p-menubar .p-menubar-custom,.p-menubar .p-menubar-end{margin-left:auto;align-self:center}.p-menubar-button{display:none;cursor:pointer;align-items:center;justify-content:center}}\\n\"] }]\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i5.PrimeNGConfig }, { type: MenubarService }], propDecorators: { model: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], autoDisplay: [{\n                type: Input\n            }], autoHide: [{\n                type: Input\n            }], autoHideDelay: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], onFocus: [{\n                type: Output\n            }], onBlur: [{\n                type: Output\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], menubutton: [{\n                type: ViewChild,\n                args: ['menubutton']\n            }], rootmenu: [{\n                type: ViewChild,\n                args: ['rootmenu']\n            }] } });\nclass MenubarModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: MenubarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: MenubarModule, declarations: [Menubar, MenubarSub], imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, BarsIcon, AngleDownIcon, AngleRightIcon], exports: [Menubar, RouterModule, TooltipModule, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: MenubarModule, imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, BarsIcon, AngleDownIcon, AngleRightIcon, RouterModule, TooltipModule, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: MenubarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, RouterModule, RippleModule, TooltipModule, SharedModule, BarsIcon, AngleDownIcon, AngleRightIcon],\n                    exports: [Menubar, RouterModule, TooltipModule, SharedModule],\n                    declarations: [Menubar, MenubarSub]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Menubar, MenubarModule, MenubarService, MenubarSub };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,MAAM,EAAEC,WAAW,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACzM,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,aAAa;AACjC,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,EAAEC,iBAAiB,EAAEC,WAAW,QAAQ,eAAe;AAC3E,SAASC,OAAO,EAAEC,QAAQ,QAAQ,MAAM;AACxC,SAASC,QAAQ,EAAEC,MAAM,QAAQ,gBAAgB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA;EAAA,kBAAAD,EAAA;EAAA,uBAAAC;AAAA;AAAA,MAAAC,GAAA,GAAAF,EAAA;EAAA;EAAA,cAAAA;AAAA;AAAA,MAAAG,GAAA,GAAAA,CAAA;EAAAC,KAAA;AAAA;AAAA,MAAAC,GAAA,GAAAL,EAAA;EAAAM,SAAA,EAAAN;AAAA;AAAA,SAAAO,uCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAO2C7C,EAAE,CAAA+C,SAAA,WAsI1E,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GAtIuEhD,EAAE,CAAAiD,aAAA,GAAAN,SAAA;IAAA,MAAAO,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAAmD,UAAA,CAAAD,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UAkI/B,CAAC;IAlI4BhD,EAAE,CAAAqD,UAAA,YAAAH,MAAA,CAAAI,qBAAA,CAAAN,gBAAA,CAmI5B,CAAC;IAnIyBhD,EAAE,CAAAuD,WAAA,OAAAL,MAAA,CAAAM,SAAA,CAAAR,gBAAA;EAAA;AAAA;AAAA,SAAAS,iEAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF7C,EAAE,CAAA+C,SAAA,cAmLzD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GAnLsDhD,EAAE,CAAAiD,aAAA,IAAAN,SAAA;IAAA,MAAAO,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAAqD,UAAA,YAAAH,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,SA6Kd,CAAC,YAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cACI,CAAC;IA9KMhD,EAAE,CAAAuD,WAAA;EAAA;AAAA;AAAA,SAAAG,iEAAAb,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF7C,EAAE,CAAA2D,cAAA,cAoL4D,CAAC;IApL/D3D,EAAE,CAAA4D,MAAA,EAsLhE,CAAC;IAtL6D5D,EAAE,CAAA6D,YAAA,CAsLzD,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAG,gBAAA,GAtLsDhD,EAAE,CAAAiD,aAAA,IAAAN,SAAA;IAAA,MAAAO,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAAuD,WAAA;IAAFvD,EAAE,CAAA8D,SAAA,CAsLhE,CAAC;IAtL6D9D,EAAE,CAAA+D,kBAAA,MAAAb,MAAA,CAAAc,YAAA,CAAAhB,gBAAA,MAsLhE,CAAC;EAAA;AAAA;AAAA,SAAAiB,wEAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtL6D7C,EAAE,CAAA+C,SAAA,cAwLoD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GAxLvDhD,EAAE,CAAAiD,aAAA,IAAAN,SAAA;IAAA,MAAAO,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAAqD,UAAA,cAAAH,MAAA,CAAAc,YAAA,CAAAhB,gBAAA,GAAFhD,EAAE,CAAAkE,cAwLW,CAAC;IAxLdlE,EAAE,CAAAuD,WAAA;EAAA;AAAA;AAAA,SAAAY,iEAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF7C,EAAE,CAAA2D,cAAA,cA0LqE,CAAC;IA1LxE3D,EAAE,CAAA4D,MAAA,EA0L8G,CAAC;IA1LjH5D,EAAE,CAAA6D,YAAA,CA0LqH,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAG,gBAAA,GA1LxHhD,EAAE,CAAAiD,aAAA,IAAAN,SAAA;IAAA,MAAAO,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAAqD,UAAA,YAAAH,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,oBA0LoE,CAAC;IA1LvEhD,EAAE,CAAA8D,SAAA,CA0L8G,CAAC;IA1LjH9D,EAAE,CAAAoE,iBAAA,CAAAlB,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UA0L8G,CAAC;EAAA;AAAA;AAAA,SAAAqB,wGAAAxB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1LjH7C,EAAE,CAAA+C,SAAA,uBA8LuE,CAAC;EAAA;EAAA,IAAAF,EAAA;IA9L1E7C,EAAE,CAAAqD,UAAA,+BA8LV,CAAC;IA9LOrD,EAAE,CAAAuD,WAAA;EAAA;AAAA;AAAA,SAAAe,yGAAAzB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF7C,EAAE,CAAA+C,SAAA,wBA+LyE,CAAC;EAAA;EAAA,IAAAF,EAAA;IA/L5E7C,EAAE,CAAAqD,UAAA,+BA+LT,CAAC;IA/LMrD,EAAE,CAAAuD,WAAA;EAAA;AAAA;AAAA,SAAAgB,wFAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF7C,EAAE,CAAAwE,uBAAA,EA6LT,CAAC;IA7LMxE,EAAE,CAAAyE,UAAA,IAAAJ,uGAAA,2BA8LuE,CAAC,IAAAC,wGAAA,4BACC,CAAC;IA/L5EtE,EAAE,CAAA0E,qBAAA;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAK,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAA8D,SAAA,CA8LE,CAAC;IA9LL9D,EAAE,CAAAqD,UAAA,SAAAH,MAAA,CAAAyB,IA8LE,CAAC;IA9LL3E,EAAE,CAAA8D,SAAA,CA+LI,CAAC;IA/LP9D,EAAE,CAAAqD,UAAA,UAAAH,MAAA,CAAAyB,IA+LI,CAAC;EAAA;AAAA;AAAA,SAAAC,yFAAA/B,EAAA,EAAAC,GAAA;AAAA,SAAA+B,2EAAAhC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/LP7C,EAAE,CAAAyE,UAAA,IAAAG,wFAAA,yBAiMkE,CAAC;EAAA;EAAA,IAAA/B,EAAA;IAjMrE7C,EAAE,CAAAqD,UAAA,iCAiMuC,CAAC,oBAAyB,CAAC;EAAA;AAAA;AAAA,SAAAyB,yEAAAjC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjMpE7C,EAAE,CAAAwE,uBAAA,EA4Lf,CAAC;IA5LYxE,EAAE,CAAAyE,UAAA,IAAAF,uFAAA,0BA6LT,CAAC,IAAAM,0EAAA,gBAI0E,CAAC;IAjMrE7E,EAAE,CAAA0E,qBAAA;EAAA;EAAA,IAAA7B,EAAA;IAAF7C,EAAE,CAAAiD,aAAA;IAAA,MAAA8B,UAAA,GAAF/E,EAAE,CAAAgF,WAAA;IAAFhF,EAAE,CAAA8D,SAAA,CA6LX,CAAC;IA7LQ9D,EAAE,CAAAqD,UAAA,UAAA0B,UAAA,CAAAE,mBA6LX,CAAC;IA7LQjF,EAAE,CAAA8D,SAAA,CAiMD,CAAC;IAjMF9D,EAAE,CAAAqD,UAAA,qBAAA0B,UAAA,CAAAE,mBAiMD,CAAC;EAAA;AAAA;AAAA,SAAAC,0DAAArC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjMF7C,EAAE,CAAA2D,cAAA,WAyKnE,CAAC;IAzKgE3D,EAAE,CAAAyE,UAAA,IAAAhB,gEAAA,kBAkL/D,CAAC,IAAAC,gEAAA,kBAE0H,CAAC,IAAAO,uEAAA,gCApL/DjE,EAAE,CAAAmF,sBAuLxC,CAAC,IAAAhB,gEAAA,kBAG4G,CAAC,IAAAW,wEAAA,0BAErF,CAAC;IA5LY9E,EAAE,CAAA6D,YAAA,CAmMhE,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAuC,YAAA,GAnM6DpF,EAAE,CAAAgF,WAAA;IAAA,MAAAhC,gBAAA,GAAFhD,EAAE,CAAAiD,aAAA,IAAAN,SAAA;IAAA,MAAAO,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAAqD,UAAA,WAAAH,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,WAqKjB,CAAC,YArKchD,EAAE,CAAAqF,eAAA,KAAA9C,GAAA,EAAAW,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cAsK6B,CAAC;IAtKhChD,EAAE,CAAAuD,WAAA,SAAAL,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UAAFhD,EAAE,CAAAsF,aAAA,4CAAApC,MAAA,CAAAE,WAAA,CAAAJ,gBAAA;IAAFhD,EAAE,CAAA8D,SAAA,CA2KnB,CAAC;IA3KgB9D,EAAE,CAAAqD,UAAA,SAAAH,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,SA2KnB,CAAC;IA3KgBhD,EAAE,CAAA8D,SAAA,CAoLb,CAAC;IApLU9D,EAAE,CAAAqD,UAAA,SAAAH,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,WAoLb,CAAC,aAAAoC,YAAa,CAAC;IApLJpF,EAAE,CAAA8D,SAAA,EA0LS,CAAC;IA1LZ9D,EAAE,CAAAqD,UAAA,SAAAH,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UA0LS,CAAC;IA1LZhD,EAAE,CAAA8D,SAAA,CA4LjB,CAAC;IA5Lc9D,EAAE,CAAAqD,UAAA,SAAAH,MAAA,CAAAqC,WAAA,CAAAvC,gBAAA,CA4LjB,CAAC;EAAA;AAAA;AAAA,SAAAwC,iEAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5Lc7C,EAAE,CAAA+C,SAAA,cAgOxD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GAhOqDhD,EAAE,CAAAiD,aAAA,IAAAN,SAAA;IAAA,MAAAO,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAAqD,UAAA,YAAAH,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,SA2Nd,CAAC,YAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cACI,CAAC;IA5NMhD,EAAE,CAAAuD,WAAA;EAAA;AAAA;AAAA,SAAAkC,iEAAA5C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF7C,EAAE,CAAA2D,cAAA,cAiOgC,CAAC;IAjOnC3D,EAAE,CAAA4D,MAAA,EAiOiE,CAAC;IAjOpE5D,EAAE,CAAA6D,YAAA,CAiOwE,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAG,gBAAA,GAjO3EhD,EAAE,CAAAiD,aAAA,IAAAN,SAAA;IAAA,MAAAO,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAA8D,SAAA,CAiOiE,CAAC;IAjOpE9D,EAAE,CAAAoE,iBAAA,CAAAlB,MAAA,CAAAc,YAAA,CAAAhB,gBAAA,CAiOiE,CAAC;EAAA;AAAA;AAAA,SAAA0C,wEAAA7C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjOpE7C,EAAE,CAAA+C,SAAA,cAkO6E,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,gBAAA,GAlOhFhD,EAAE,CAAAiD,aAAA,IAAAN,SAAA;IAAA,MAAAO,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAAqD,UAAA,cAAAH,MAAA,CAAAc,YAAA,CAAAhB,gBAAA,GAAFhD,EAAE,CAAAkE,cAkOoC,CAAC;IAlOvClE,EAAE,CAAAuD,WAAA;EAAA;AAAA;AAAA,SAAAoC,iEAAA9C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF7C,EAAE,CAAA2D,cAAA,cAmOqE,CAAC;IAnOxE3D,EAAE,CAAA4D,MAAA,EAmO8G,CAAC;IAnOjH5D,EAAE,CAAA6D,YAAA,CAmOqH,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAG,gBAAA,GAnOxHhD,EAAE,CAAAiD,aAAA,IAAAN,SAAA;IAAA,MAAAO,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAAqD,UAAA,YAAAH,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,oBAmOoE,CAAC;IAnOvEhD,EAAE,CAAA8D,SAAA,CAmO8G,CAAC;IAnOjH9D,EAAE,CAAAoE,iBAAA,CAAAlB,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UAmO8G,CAAC;EAAA;AAAA;AAAA,SAAA4C,wGAAA/C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnOjH7C,EAAE,CAAA+C,SAAA,uBAsOuE,CAAC;EAAA;EAAA,IAAAF,EAAA;IAtO1E7C,EAAE,CAAAqD,UAAA,+BAsOV,CAAC;IAtOOrD,EAAE,CAAAuD,WAAA;EAAA;AAAA;AAAA,SAAAsC,yGAAAhD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF7C,EAAE,CAAA+C,SAAA,wBAuOyE,CAAC;EAAA;EAAA,IAAAF,EAAA;IAvO5E7C,EAAE,CAAAqD,UAAA,+BAuOT,CAAC;IAvOMrD,EAAE,CAAAuD,WAAA;EAAA;AAAA;AAAA,SAAAuC,wFAAAjD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF7C,EAAE,CAAAwE,uBAAA,EAqOT,CAAC;IArOMxE,EAAE,CAAAyE,UAAA,IAAAmB,uGAAA,2BAsOuE,CAAC,IAAAC,wGAAA,4BACC,CAAC;IAvO5E7F,EAAE,CAAA0E,qBAAA;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAK,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAA8D,SAAA,CAsOmE,CAAC;IAtOtE9D,EAAE,CAAAqD,UAAA,SAAAH,MAAA,CAAAyB,IAsOmE,CAAC;IAtOtE3E,EAAE,CAAA8D,SAAA,CAuOqE,CAAC;IAvOxE9D,EAAE,CAAAqD,UAAA,UAAAH,MAAA,CAAAyB,IAuOqE,CAAC;EAAA;AAAA;AAAA,SAAAoB,yFAAAlD,EAAA,EAAAC,GAAA;AAAA,SAAAkD,2EAAAnD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvOxE7C,EAAE,CAAAyE,UAAA,IAAAsB,wFAAA,yBAyOkE,CAAC;EAAA;EAAA,IAAAlD,EAAA;IAzOrE7C,EAAE,CAAAqD,UAAA,iCAyOuC,CAAC,oBAAyB,CAAC;EAAA;AAAA;AAAA,SAAA4C,yEAAApD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzOpE7C,EAAE,CAAAwE,uBAAA,EAoOf,CAAC;IApOYxE,EAAE,CAAAyE,UAAA,IAAAqB,uFAAA,0BAqOT,CAAC,IAAAE,0EAAA,gBAI0E,CAAC;IAzOrEhG,EAAE,CAAA0E,qBAAA;EAAA;EAAA,IAAA7B,EAAA;IAAF7C,EAAE,CAAAiD,aAAA;IAAA,MAAA8B,UAAA,GAAF/E,EAAE,CAAAgF,WAAA;IAAFhF,EAAE,CAAA8D,SAAA,CAqOX,CAAC;IArOQ9D,EAAE,CAAAqD,UAAA,UAAA0B,UAAA,CAAAE,mBAqOX,CAAC;IArOQjF,EAAE,CAAA8D,SAAA,CAyOD,CAAC;IAzOF9D,EAAE,CAAAqD,UAAA,qBAAA0B,UAAA,CAAAE,mBAyOD,CAAC;EAAA;AAAA;AAAA,SAAAiB,0DAAArD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzOF7C,EAAE,CAAA2D,cAAA,WAuNnE,CAAC;IAvNgE3D,EAAE,CAAAyE,UAAA,IAAAe,gEAAA,kBAgO/D,CAAC,IAAAC,gEAAA,kBAC8F,CAAC,IAAAC,uEAAA,gCAjOnC1F,EAAE,CAAAmF,sBAkOnC,CAAC,IAAAQ,gEAAA,kBACuG,CAAC,IAAAM,wEAAA,0BACrF,CAAC;IApOYjG,EAAE,CAAA6D,YAAA,CA2OhE,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAsD,iBAAA,GA3O6DnG,EAAE,CAAAgF,WAAA;IAAA,MAAAhC,gBAAA,GAAFhD,EAAE,CAAAiD,aAAA,IAAAN,SAAA;IAAA,MAAAO,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAAqD,UAAA,eAAAH,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,eAsMT,CAAC,gBAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,gBAKC,CAAC,6CACb,CAAC,4BAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,gCA5MgBhD,EAAE,CAAAoG,eAAA,KAAA5D,GAAA,CA6MqC,CAAC,WAAAU,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,WACvD,CAAC,YA9MchD,EAAE,CAAAqF,eAAA,KAAA9C,GAAA,EAAAW,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cA+M6B,CAAC,aAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,aAC3C,CAAC,wBAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,wBACqB,CAAC,qBAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,qBACP,CAAC,uBAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,uBACG,CAAC,eAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,eACjB,CAAC,UAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UACX,CAAC;IArNgBhD,EAAE,CAAAuD,WAAA,sBAAAL,MAAA,CAAAE,WAAA,CAAAJ,gBAAA;IAAFhD,EAAE,CAAA8D,SAAA,CA0NnB,CAAC;IA1NgB9D,EAAE,CAAAqD,UAAA,SAAAH,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,SA0NnB,CAAC;IA1NgBhD,EAAE,CAAA8D,SAAA,CAiOW,CAAC;IAjOd9D,EAAE,CAAAqD,UAAA,SAAAH,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,WAiOW,CAAC,aAAAmD,iBAAkB,CAAC;IAjOjCnG,EAAE,CAAA8D,SAAA,EAmOS,CAAC;IAnOZ9D,EAAE,CAAAqD,UAAA,SAAAH,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UAmOS,CAAC;IAnOZhD,EAAE,CAAA8D,SAAA,CAoOjB,CAAC;IApOc9D,EAAE,CAAAqD,UAAA,SAAAH,MAAA,CAAAqC,WAAA,CAAAvC,gBAAA,CAoOjB,CAAC;EAAA;AAAA;AAAA,SAAAqD,sDAAAxD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApOc7C,EAAE,CAAAwE,uBAAA,EA8JpC,CAAC;IA9JiCxE,EAAE,CAAAyE,UAAA,IAAAS,yDAAA,gBAyKnE,CAAC,IAAAgB,yDAAA,gBA8CD,CAAC;IAvNgElG,EAAE,CAAA0E,qBAAA;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAG,gBAAA,GAAFhD,EAAE,CAAAiD,aAAA,IAAAN,SAAA;IAAA,MAAAO,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAA8D,SAAA,CAgKhB,CAAC;IAhKa9D,EAAE,CAAAqD,UAAA,UAAAH,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,eAgKhB,CAAC;IAhKahD,EAAE,CAAA8D,SAAA,CAqMjB,CAAC;IArMc9D,EAAE,CAAAqD,UAAA,SAAAH,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,eAqMjB,CAAC;EAAA;AAAA;AAAA,SAAAsD,sEAAAzD,EAAA,EAAAC,GAAA;AAAA,SAAAyD,wDAAA1D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArMc7C,EAAE,CAAAyE,UAAA,IAAA6B,qEAAA,qBA8OsB,CAAC;EAAA;AAAA;AAAA,SAAAE,sDAAA3D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9OzB7C,EAAE,CAAAwE,uBAAA,EA6OrC,CAAC;IA7OkCxE,EAAE,CAAAyE,UAAA,IAAA8B,uDAAA,gBA8OsB,CAAC;IA9OzBvG,EAAE,CAAA0E,qBAAA;EAAA;EAAA,IAAA7B,EAAA;IAAA,MAAAG,gBAAA,GAAFhD,EAAE,CAAAiD,aAAA,IAAAN,SAAA;IAAA,MAAAO,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAA8D,SAAA,CA8OtB,CAAC;IA9OmB9D,EAAE,CAAAqD,UAAA,qBAAAH,MAAA,CAAAuD,YA8OtB,CAAC,4BA9OmBzG,EAAE,CAAAqF,eAAA,IAAA3C,GAAA,EAAAM,gBAAA,CAAA0D,IAAA,CA8OoB,CAAC;EAAA;AAAA;AAAA,SAAAC,sDAAA9D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+D,GAAA,GA9OvB5G,EAAE,CAAA6G,gBAAA;IAAF7G,EAAE,CAAA2D,cAAA,sBA6P3E,CAAC;IA7PwE3D,EAAE,CAAA8G,UAAA,uBAAAC,wFAAAC,MAAA;MAAFhH,EAAE,CAAAiH,aAAA,CAAAL,GAAA;MAAA,MAAA1D,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;MAAA,OAAFjD,EAAE,CAAAkH,WAAA,CA2P1DhE,MAAA,CAAAiE,SAAA,CAAAC,IAAA,CAAAJ,MAAqB,CAAC;IAAA,EAAC,4BAAAK,6FAAAL,MAAA;MA3PiChH,EAAE,CAAAiH,aAAA,CAAAL,GAAA;MAAA,MAAA1D,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;MAAA,OAAFjD,EAAE,CAAAkH,WAAA,CA4PrDhE,MAAA,CAAAoE,gBAAA,CAAAN,MAAuB,CAAC;IAAA,EAAC;IA5P0BhH,EAAE,CAAA6D,YAAA,CA8P7D,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAG,gBAAA,GA9P0DhD,EAAE,CAAAiD,aAAA,IAAAN,SAAA;IAAA,MAAAO,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAAqD,UAAA,iBAAAH,MAAA,CAAAuD,YAmP3C,CAAC,UAAAzD,gBAAA,CAAAuE,KACD,CAAC,iBAAArE,MAAA,CAAAsE,YACD,CAAC,gBAAAtE,MAAA,CAAAuE,WACH,CAAC,WAAAvE,MAAA,CAAAwE,MACX,CAAC,mBAAAxE,MAAA,CAAAyE,cACe,CAAC,kBAAAzE,MAAA,CAAA0E,aACH,CAAC,UAAA1E,MAAA,CAAA2E,KAAA,IACb,CAAC;EAAA;AAAA;AAAA,SAAAC,uCAAAjF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAkF,GAAA,GA1PkD/H,EAAE,CAAA6G,gBAAA;IAAF7G,EAAE,CAAA2D,cAAA,cA4J/E,CAAC,aAC2K,CAAC;IA7JhG3D,EAAE,CAAA8G,UAAA,mBAAAkB,4DAAAhB,MAAA;MAAFhH,EAAE,CAAAiH,aAAA,CAAAc,GAAA;MAAA,MAAA/E,gBAAA,GAAFhD,EAAE,CAAAiD,aAAA,GAAAN,SAAA;MAAA,MAAAO,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;MAAA,OAAFjD,EAAE,CAAAkH,WAAA,CA6JChE,MAAA,CAAA+E,WAAA,CAAAjB,MAAA,EAAAhE,gBAAiC,CAAC;IAAA,EAAC,wBAAAkF,iEAAAlB,MAAA;MA7JtChH,EAAE,CAAAiH,aAAA,CAAAc,GAAA;MAAA,MAAA/E,gBAAA,GAAFhD,EAAE,CAAAiD,aAAA,GAAAN,SAAA;MAAA,MAAAO,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;MAAA,OAAFjD,EAAE,CAAAkH,WAAA,CA6JmDhE,MAAA,CAAAoE,gBAAA,CAAiB;QAAAN,MAAA,EAAAA,MAAA;QAAAmB,aAAA,EAAAnF;MAAsB,CAAC,CAAC;IAAA,EAAC;IA7J/FhD,EAAE,CAAAyE,UAAA,IAAA4B,qDAAA,0BA8JpC,CAAC,IAAAG,qDAAA,0BA+EF,CAAC;IA7OkCxG,EAAE,CAAA6D,YAAA,CAgPtE,CAAC;IAhPmE7D,EAAE,CAAAyE,UAAA,IAAAkC,qDAAA,0BA6P3E,CAAC;IA7PwE3G,EAAE,CAAA6D,YAAA,CA+P3E,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAuF,MAAA,GA/PwEpI,EAAE,CAAAiD,aAAA;IAAA,MAAAD,gBAAA,GAAAoF,MAAA,CAAAzF,SAAA;IAAA,MAAA0F,SAAA,GAAAD,MAAA,CAAAE,KAAA;IAAA,MAAApF,MAAA,GAAFlD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAAuI,UAAA,CAAArF,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,eAyJ1B,CAAC;IAzJuBhD,EAAE,CAAAqD,UAAA,YAAAH,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,UAuJ7B,CAAC,YAAAE,MAAA,CAAAsF,YAAA,CAAAxF,gBAAA,CACT,CAAC,mBAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,mBAGuB,CAAC;IA3JUhD,EAAE,CAAAuD,WAAA,OAAAL,MAAA,CAAAM,SAAA,CAAAR,gBAAA,sDAAAE,MAAA,CAAAuF,YAAA,CAAAzF,gBAAA,qBAAAE,MAAA,CAAAwF,aAAA,CAAA1F,gBAAA,sBAAAE,MAAA,CAAAyF,cAAA,CAAA3F,gBAAA,iBAAAE,MAAA,CAAAc,YAAA,CAAAhB,gBAAA,oBAAAE,MAAA,CAAAyF,cAAA,CAAA3F,gBAAA,KAAA4F,SAAA,mBAAA1F,MAAA,CAAAqC,WAAA,CAAAvC,gBAAA,MAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,mBAAA4F,SAAA,mBAAA1F,MAAA,CAAAqC,WAAA,CAAAvC,gBAAA,IAAAE,MAAA,CAAAuF,YAAA,CAAAzF,gBAAA,IAAA4F,SAAA,gBAAA1F,MAAA,CAAA2E,KAAA,sBAAA3E,MAAA,CAAA2F,cAAA,qBAAA3F,MAAA,CAAA4F,eAAA,CAAAT,SAAA;IAAFrI,EAAE,CAAA8D,SAAA,EA6JV,CAAC;IA7JO9D,EAAE,CAAAuD,WAAA;IAAFvD,EAAE,CAAA8D,SAAA,CA8JtC,CAAC;IA9JmC9D,EAAE,CAAAqD,UAAA,UAAAH,MAAA,CAAAuD,YA8JtC,CAAC;IA9JmCzG,EAAE,CAAA8D,SAAA,CA6OvC,CAAC;IA7OoC9D,EAAE,CAAAqD,UAAA,SAAAH,MAAA,CAAAuD,YA6OvC,CAAC;IA7OoCzG,EAAE,CAAA8D,SAAA,CAkPP,CAAC;IAlPI9D,EAAE,CAAAqD,UAAA,SAAAH,MAAA,CAAA6F,aAAA,CAAA/F,gBAAA,KAAAE,MAAA,CAAAqC,WAAA,CAAAvC,gBAAA,CAkPP,CAAC;EAAA;AAAA;AAAA,SAAAgG,kCAAAnG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlPI7C,EAAE,CAAAyE,UAAA,IAAA7B,sCAAA,eAsI/E,CAAC,IAAAkF,sCAAA,gBAsBD,CAAC;EAAA;EAAA,IAAAjF,EAAA;IAAA,MAAAG,gBAAA,GAAAF,GAAA,CAAAH,SAAA;IAAA,MAAAO,MAAA,GA5J4ElD,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAAqD,UAAA,SAAAH,MAAA,CAAA6F,aAAA,CAAA/F,gBAAA,KAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cAgIE,CAAC;IAhILhD,EAAE,CAAA8D,SAAA,CAyIG,CAAC;IAzIN9D,EAAE,CAAAqD,UAAA,SAAAH,MAAA,CAAA6F,aAAA,CAAA/F,gBAAA,MAAAE,MAAA,CAAAE,WAAA,CAAAJ,gBAAA,cAyIG,CAAC;EAAA;AAAA;AAAA,MAAAiG,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAA/G,EAAA;EAAA;EAAA,2BAAAA;AAAA;AAAA,SAAAgH,sCAAAxG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzIN7C,EAAE,CAAAsJ,kBAAA,EA8hCjB,CAAC;EAAA;AAAA;AAAA,SAAAC,uBAAA1G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9hCc7C,EAAE,CAAA2D,cAAA,YA6hCjC,CAAC;IA7hC8B3D,EAAE,CAAAyE,UAAA,IAAA4E,qCAAA,yBA8hChC,CAAC;IA9hC6BrJ,EAAE,CAAA6D,YAAA,CA+hC9E,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAA2G,MAAA,GA/hC2ExJ,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAA8D,SAAA,CA8hClC,CAAC;IA9hC+B9D,EAAE,CAAAqD,UAAA,qBAAAmG,MAAA,CAAAC,aA8hClC,CAAC;EAAA;AAAA;AAAA,SAAAC,gCAAA7G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9hC+B7C,EAAE,CAAA+C,SAAA,cA8iC1C,CAAC;EAAA;AAAA;AAAA,SAAA4G,qCAAA9G,EAAA,EAAAC,GAAA;AAAA,SAAA8G,uBAAA/G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9iCuC7C,EAAE,CAAAyE,UAAA,IAAAkF,oCAAA,qBA+iC9B,CAAC;EAAA;AAAA;AAAA,SAAAE,qBAAAhH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiH,GAAA,GA/iC2B9J,EAAE,CAAA6G,gBAAA;IAAF7G,EAAE,CAAA2D,cAAA,cA6iCnF,CAAC;IA7iCgF3D,EAAE,CAAA8G,UAAA,mBAAAiD,wCAAA/C,MAAA;MAAFhH,EAAE,CAAAiH,aAAA,CAAA6C,GAAA;MAAA,MAAAN,MAAA,GAAFxJ,EAAE,CAAAiD,aAAA;MAAA,OAAFjD,EAAE,CAAAkH,WAAA,CA2iCtEsC,MAAA,CAAAQ,eAAA,CAAAhD,MAAsB,CAAC;IAAA,EAAC,qBAAAiD,0CAAAjD,MAAA;MA3iC4ChH,EAAE,CAAAiH,aAAA,CAAA6C,GAAA;MAAA,MAAAN,MAAA,GAAFxJ,EAAE,CAAAiD,aAAA;MAAA,OAAFjD,EAAE,CAAAkH,WAAA,CA4iCpEsC,MAAA,CAAAU,iBAAA,CAAAlD,MAAwB,CAAC;IAAA,EAAC;IA5iCwChH,EAAE,CAAAyE,UAAA,IAAAiF,+BAAA,sBA8iC1C,CAAC,IAAAE,sBAAA,eACW,CAAC;IA/iC2B5J,EAAE,CAAA6D,YAAA,CAgjChF,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAA2G,MAAA,GAhjC6ExJ,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAAuD,WAAA,kBAAAiG,MAAA,CAAAW,KAAA,CAAAC,MAAA,IAAAZ,MAAA,CAAAW,KAAA,CAAAC,MAAA,sCAAAZ,MAAA,CAAAhC,YAAA,mBAAAgC,MAAA,CAAAa,EAAA,gBAAAb,MAAA,CAAAc,MAAA,CAAAC,WAAA,CAAAC,IAAA,CAAAC,UAAA;IAAFzK,EAAE,CAAA8D,SAAA,EA8iC9C,CAAC;IA9iC2C9D,EAAE,CAAAqD,UAAA,UAAAmG,MAAA,CAAAkB,gBA8iC9C,CAAC;IA9iC2C1K,EAAE,CAAA8D,SAAA,CA+iChC,CAAC;IA/iC6B9D,EAAE,CAAAqD,UAAA,qBAAAmG,MAAA,CAAAkB,gBA+iChC,CAAC;EAAA;AAAA;AAAA,SAAAC,sCAAA9H,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/iC6B7C,EAAE,CAAAsJ,kBAAA,EAskCnB,CAAC;EAAA;AAAA;AAAA,SAAAsB,uBAAA/H,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtkCgB7C,EAAE,CAAA2D,cAAA,aAqkCxB,CAAC;IArkCqB3D,EAAE,CAAAyE,UAAA,IAAAkG,qCAAA,yBAskClC,CAAC;IAtkC+B3K,EAAE,CAAA6D,YAAA,CAukC9E,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAA2G,MAAA,GAvkC2ExJ,EAAE,CAAAiD,aAAA;IAAFjD,EAAE,CAAA8D,SAAA,CAskCpC,CAAC;IAtkCiC9D,EAAE,CAAAqD,UAAA,qBAAAmG,MAAA,CAAAqB,WAskCpC,CAAC;EAAA;AAAA;AAAA,SAAAC,+BAAAjI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtkCiC7C,EAAE,CAAA2D,cAAA,aAykCrD,CAAC;IAzkCkD3D,EAAE,CAAA+K,YAAA,EA0kCnD,CAAC;IA1kCgD/K,EAAE,CAAA6D,YAAA,CA2kC1E,CAAC;EAAA;AAAA;AAhlCtB,MAAMmH,cAAc,CAAC;EACjBC,QAAQ;EACRC,aAAa;EACbC,WAAW,GAAG,IAAIpJ,OAAO,CAAC,CAAC;EAC3BqJ,UAAU,GAAG,IAAI,CAACD,WAAW,CAACE,IAAI,CAACpJ,QAAQ,CAAC,MAAMD,QAAQ,CAAC,IAAI,CAACkJ,aAAa,CAAC,CAAC,EAAEhJ,MAAM,CAAEoJ,SAAS,IAAK,IAAI,CAACL,QAAQ,IAAIK,SAAS,CAAC,CAAC;EACnI,OAAOC,IAAI,YAAAC,uBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFT,cAAc;EAAA;EACjH,OAAOU,KAAK,kBAD6E1L,EAAE,CAAA2L,kBAAA;IAAAC,KAAA,EACYZ,cAAc;IAAAa,OAAA,EAAdb,cAAc,CAAAO;EAAA;AACzH;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAH6F9L,EAAE,CAAA+L,iBAAA,CAGJf,cAAc,EAAc,CAAC;IAC5GgB,IAAI,EAAE/L;EACV,CAAC,CAAC;AAAA;AACV,MAAMgM,UAAU,CAAC;EACbC,EAAE;EACFC,QAAQ;EACRC,EAAE;EACFC,cAAc;EACd9E,KAAK;EACLd,YAAY;EACZ9B,IAAI,GAAG,KAAK;EACZ2H,UAAU,GAAG,IAAI;EACjBC,UAAU,GAAG,CAAC;EACd/E,YAAY;EACZC,WAAW;EACXC,MAAM;EACN8E,SAAS;EACTC,cAAc;EACd5E,KAAK,GAAG,CAAC;EACTD,aAAa;EACbD,cAAc;EACdR,SAAS,GAAG,IAAIjH,YAAY,CAAC,CAAC;EAC9BwM,cAAc,GAAG,IAAIxM,YAAY,CAAC,CAAC;EACnCyM,SAAS,GAAG,IAAIzM,YAAY,CAAC,CAAC;EAC9B0M,QAAQ,GAAG,IAAI1M,YAAY,CAAC,CAAC;EAC7B2M,WAAW,GAAG,IAAI3M,YAAY,CAAC,CAAC;EAChC4M,gBAAgB;EAChBC,oBAAoB;EACpBC,WAAWA,CAACd,EAAE,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,cAAc,EAAE;IAC1C,IAAI,CAACH,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,cAAc,GAAGA,cAAc;EACxC;EACAY,QAAQA,CAAA,EAAG;IACP,IAAI,CAACF,oBAAoB,GAAG,IAAI,CAACV,cAAc,CAACjB,UAAU,CAAC8B,SAAS,CAAC,MAAM;MACvE,IAAI,CAACd,EAAE,CAACe,YAAY,CAAC,CAAC;IAC1B,CAAC,CAAC;EACN;EACAlF,WAAWA,CAACmF,KAAK,EAAEjF,aAAa,EAAE;IAC9B,IAAI,CAAC/E,WAAW,CAAC+E,aAAa,EAAE,SAAS,EAAE;MAAEkF,aAAa,EAAED,KAAK;MAAE1G,IAAI,EAAEyB,aAAa,CAACzB;IAAK,CAAC,CAAC;IAC9F,IAAI,CAACS,SAAS,CAACC,IAAI,CAAC;MAAEiG,aAAa,EAAED,KAAK;MAAEjF,aAAa;MAAEmF,OAAO,EAAE;IAAK,CAAC,CAAC;EAC/E;EACAlK,WAAWA,CAAC+E,aAAa,EAAEoF,IAAI,EAAEC,MAAM,GAAG,IAAI,EAAE;IAC5C,OAAOrF,aAAa,IAAIA,aAAa,CAACzB,IAAI,GAAG9E,WAAW,CAAC6L,YAAY,CAACtF,aAAa,CAACzB,IAAI,CAAC6G,IAAI,CAAC,EAAEC,MAAM,CAAC,GAAG5E,SAAS;EACvH;EACApF,SAASA,CAAC2E,aAAa,EAAE;IACrB,OAAOA,aAAa,CAACzB,IAAI,IAAIyB,aAAa,CAACzB,IAAI,EAAE2D,EAAE,GAAGlC,aAAa,CAACzB,IAAI,CAAC2D,EAAE,GAAI,GAAE,IAAI,CAAC3C,MAAO,IAAGS,aAAa,CAACuF,GAAI,EAAC;EACvH;EACAC,UAAUA,CAACxF,aAAa,EAAE;IACtB,OAAO,IAAI,CAAC3E,SAAS,CAAC2E,aAAa,CAAC;EACxC;EACAK,YAAYA,CAACL,aAAa,EAAE;IACxB,OAAO;MACH,GAAG,IAAI,CAAC/E,WAAW,CAAC+E,aAAa,EAAE,OAAO,CAAC;MAC3C,YAAY,EAAE,IAAI;MAClB,aAAa,EAAE,IAAI,CAACM,YAAY,CAACN,aAAa,CAAC;MAC/C,mBAAmB,EAAE,IAAI,CAACM,YAAY,CAACN,aAAa,CAAC;MACrD,SAAS,EAAE,IAAI,CAACO,aAAa,CAACP,aAAa,CAAC;MAC5C,YAAY,EAAE,IAAI,CAACQ,cAAc,CAACR,aAAa;IACnD,CAAC;EACL;EACAnE,YAAYA,CAACmE,aAAa,EAAE;IACxB,OAAO,IAAI,CAAC/E,WAAW,CAAC+E,aAAa,EAAE,OAAO,CAAC;EACnD;EACA7E,qBAAqBA,CAAC6E,aAAa,EAAE;IACjC,OAAO;MACH,GAAG,IAAI,CAAC/E,WAAW,CAAC+E,aAAa,EAAE,OAAO,CAAC;MAC3C,sBAAsB,EAAE;IAC5B,CAAC;EACL;EACAY,aAAaA,CAACZ,aAAa,EAAE;IACzB,OAAO,IAAI,CAAC/E,WAAW,CAAC+E,aAAa,EAAE,SAAS,CAAC,KAAK,KAAK;EAC/D;EACAM,YAAYA,CAACN,aAAa,EAAE;IACxB,IAAI,IAAI,CAACR,cAAc,EAAE;MACrB,OAAO,IAAI,CAACA,cAAc,CAACiG,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACH,GAAG,KAAKvF,aAAa,CAACuF,GAAG,CAAC;IAC7E;EACJ;EACA/E,cAAcA,CAACR,aAAa,EAAE;IAC1B,OAAO,IAAI,CAAC/E,WAAW,CAAC+E,aAAa,EAAE,UAAU,CAAC;EACtD;EACAO,aAAaA,CAACP,aAAa,EAAE;IACzB,OAAO,IAAI,CAACP,aAAa,KAAK,IAAI,CAACpE,SAAS,CAAC2E,aAAa,CAAC;EAC/D;EACA5C,WAAWA,CAAC4C,aAAa,EAAE;IACvB,OAAOvG,WAAW,CAACkM,UAAU,CAAC3F,aAAa,CAACZ,KAAK,CAAC;EACtD;EACAsB,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACtB,KAAK,CAACrF,MAAM,CAAEiG,aAAa,IAAK,IAAI,CAACY,aAAa,CAACZ,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC/E,WAAW,CAAC+E,aAAa,EAAE,WAAW,CAAC,CAAC,CAACiC,MAAM;EAC1I;EACAtB,eAAeA,CAACR,KAAK,EAAE;IACnB,OAAOA,KAAK,GAAG,IAAI,CAACf,KAAK,CAACwG,KAAK,CAAC,CAAC,EAAEzF,KAAK,CAAC,CAACpG,MAAM,CAAEiG,aAAa,IAAK,IAAI,CAACY,aAAa,CAACZ,aAAa,CAAC,IAAI,IAAI,CAAC/E,WAAW,CAAC+E,aAAa,EAAE,WAAW,CAAC,CAAC,CAACiC,MAAM,GAAG,CAAC;EACrK;EACA4D,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC3B,cAAc,CAAClB,WAAW,CAAC8C,IAAI,CAAC,IAAI,CAAC;EAC9C;EACA3G,gBAAgBA,CAAC4G,KAAK,EAAE;IACpB,IAAI,IAAI,CAACzG,WAAW,EAAE;MAClB,IAAI,CAAC4E,cAAc,CAAClB,WAAW,CAAC8C,IAAI,CAAC,KAAK,CAAC;MAC3C,MAAM;QAAEb,KAAK;QAAEjF;MAAc,CAAC,GAAG+F,KAAK;MACtC,IAAI,CAACxB,cAAc,CAACtF,IAAI,CAAC;QAAEiG,aAAa,EAAED,KAAK;QAAEjF;MAAc,CAAC,CAAC;IACrE;EACJ;EACAgG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACpB,oBAAoB,EAAEqB,WAAW,CAAC,CAAC;EAC5C;EACA,OAAO7C,IAAI,YAAA8C,mBAAA5C,CAAA;IAAA,YAAAA,CAAA,IAAwFQ,UAAU,EA9GpBjM,EAAE,CAAAsO,iBAAA,CA8GoCtO,EAAE,CAACuO,UAAU,GA9GnDvO,EAAE,CAAAsO,iBAAA,CA8G8DtO,EAAE,CAACwO,SAAS,GA9G5ExO,EAAE,CAAAsO,iBAAA,CA8GuFtO,EAAE,CAACyO,iBAAiB,GA9G7GzO,EAAE,CAAAsO,iBAAA,CA8GwHtD,cAAc;EAAA;EACjO,OAAO0D,IAAI,kBA/G8E1O,EAAE,CAAA2O,iBAAA;IAAA3C,IAAA,EA+GJC,UAAU;IAAA2C,SAAA;IAAAC,SAAA,WAAAC,iBAAAjM,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA/GR7C,EAAE,CAAA+O,WAAA,CAAA5M,GAAA;MAAA;MAAA,IAAAU,EAAA;QAAA,IAAAmM,EAAA;QAAFhP,EAAE,CAAAiP,cAAA,CAAAD,EAAA,GAAFhP,EAAE,CAAAkP,WAAA,QAAApM,GAAA,CAAAgK,gBAAA,GAAAkC,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,MAAA;MAAA9H,KAAA;MAAAd,YAAA;MAAA9B,IAAA;MAAA2H,UAAA;MAAAC,UAAA;MAAA/E,YAAA;MAAAC,WAAA;MAAAC,MAAA;MAAA8E,SAAA;MAAAC,cAAA;MAAA5E,KAAA;MAAAD,aAAA;MAAAD,cAAA;IAAA;IAAA2H,OAAA;MAAAnI,SAAA;MAAAuF,cAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,WAAA;IAAA;IAAA0C,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,oBAAA9M,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAA+M,GAAA,GAAF5P,EAAE,CAAA6G,gBAAA;QAAF7G,EAAE,CAAA2D,cAAA,cA6HvF,CAAC;QA7HoF3D,EAAE,CAAA8G,UAAA,mBAAA+I,wCAAA7I,MAAA;UAAFhH,EAAE,CAAAiH,aAAA,CAAA2I,GAAA;UAAA,OAAF5P,EAAE,CAAAkH,WAAA,CAqH1EpE,GAAA,CAAA6J,SAAA,CAAAvF,IAAA,CAAAJ,MAAqB,CAAC;QAAA,EAAC,kBAAA8I,uCAAA9I,MAAA;UArHiDhH,EAAE,CAAAiH,aAAA,CAAA2I,GAAA;UAAA,OAAF5P,EAAE,CAAAkH,WAAA,CAsH3EpE,GAAA,CAAA8J,QAAA,CAAAxF,IAAA,CAAAJ,MAAoB,CAAC;QAAA,EAAC,qBAAA+I,0CAAA/I,MAAA;UAtHmDhH,EAAE,CAAAiH,aAAA,CAAA2I,GAAA;UAAA,OAAF5P,EAAE,CAAAkH,WAAA,CA0HxEpE,GAAA,CAAA+J,WAAA,CAAAzF,IAAA,CAAAJ,MAAuB,CAAC;QAAA,EAAC;QA1H6ChH,EAAE,CAAAyE,UAAA,IAAAuE,iCAAA,wBA8HX,CAAC;QA9HQhJ,EAAE,CAAA6D,YAAA,CAiQnF,CAAC;MAAA;MAAA,IAAAhB,EAAA;QAjQgF7C,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAgQ,eAAA,IAAA5N,GAAA,GAAAU,GAAA,CAAA6B,IAAA,EAAA7B,GAAA,CAAA6B,IAAA,CAkHhB,CAAC,cAKvD,CAAC;QAvHmE3E,EAAE,CAAAuD,WAAA,0CAAAT,GAAA,CAAA0J,SAAA,qBAAA1J,GAAA,CAAA2J,cAAA,QAAA3J,GAAA,CAAA4E,MAAA,2BAAA5E,GAAA,CAAA8E,aAAA;QAAF5H,EAAE,CAAA8D,SAAA,EA8H9B,CAAC;QA9H2B9D,EAAE,CAAAqD,UAAA,YAAAP,GAAA,CAAAyE,KA8H9B,CAAC;MAAA;IAAA;IAAA0I,YAAA,EAAAA,CAAA,MAoImBrQ,EAAE,CAACsQ,OAAO,EAAyGtQ,EAAE,CAACuQ,OAAO,EAAwIvQ,EAAE,CAACwQ,IAAI,EAAkHxQ,EAAE,CAACyQ,gBAAgB,EAAyKzQ,EAAE,CAAC0Q,OAAO,EAAgGvP,EAAE,CAACwP,UAAU,EAAiPxP,EAAE,CAACyP,gBAAgB,EAAmOhP,EAAE,CAACiP,MAAM,EAA2E/O,EAAE,CAACgP,OAAO,EAAkWrP,aAAa,EAA+EC,cAAc,EAAgF2K,UAAU;IAAA0E,aAAA;EAAA;AACj3D;AACA;EAAA,QAAA7E,SAAA,oBAAAA,SAAA,KApQ6F9L,EAAE,CAAA+L,iBAAA,CAoQJE,UAAU,EAAc,CAAC;IACxGD,IAAI,EAAE7L,SAAS;IACfyQ,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBnB,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MACeiB,aAAa,EAAEvQ,iBAAiB,CAAC0Q,IAAI;MACrCC,IAAI,EAAE;QACFC,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEhF,IAAI,EAAEhM,EAAE,CAACuO;EAAW,CAAC,EAAE;IAAEvC,IAAI,EAAEhM,EAAE,CAACwO;EAAU,CAAC,EAAE;IAAExC,IAAI,EAAEhM,EAAE,CAACyO;EAAkB,CAAC,EAAE;IAAEzC,IAAI,EAAEhB;EAAe,CAAC,CAAC,EAAkB;IAAEzD,KAAK,EAAE,CAAC;MACzJyE,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEoG,YAAY,EAAE,CAAC;MACfuF,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEsE,IAAI,EAAE,CAAC;MACPqH,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEiM,UAAU,EAAE,CAAC;MACbN,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEkM,UAAU,EAAE,CAAC;MACbP,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEmH,YAAY,EAAE,CAAC;MACfwE,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEoH,WAAW,EAAE,CAAC;MACduE,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEqH,MAAM,EAAE,CAAC;MACTsE,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEmM,SAAS,EAAE,CAAC;MACZR,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEoM,cAAc,EAAE,CAAC;MACjBT,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEwH,KAAK,EAAE,CAAC;MACRmE,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEuH,aAAa,EAAE,CAAC;MAChBoE,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEsH,cAAc,EAAE,CAAC;MACjBqE,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAE8G,SAAS,EAAE,CAAC;MACZ6E,IAAI,EAAE1L;IACV,CAAC,CAAC;IAAEoM,cAAc,EAAE,CAAC;MACjBV,IAAI,EAAE1L;IACV,CAAC,CAAC;IAAEqM,SAAS,EAAE,CAAC;MACZX,IAAI,EAAE1L;IACV,CAAC,CAAC;IAAEsM,QAAQ,EAAE,CAAC;MACXZ,IAAI,EAAE1L;IACV,CAAC,CAAC;IAAEuM,WAAW,EAAE,CAAC;MACdb,IAAI,EAAE1L;IACV,CAAC,CAAC;IAAEwM,gBAAgB,EAAE,CAAC;MACnBd,IAAI,EAAEzL,SAAS;MACfqQ,IAAI,EAAE,CAAC,SAAS,EAAE;QAAEK,MAAM,EAAE;MAAK,CAAC;IACtC,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMC,OAAO,CAAC;EACVC,QAAQ;EACRC,UAAU;EACVlF,EAAE;EACFC,QAAQ;EACRC,EAAE;EACF9B,MAAM;EACN+B,cAAc;EACd;AACJ;AACA;AACA;EACI,IAAIlC,KAAKA,CAACkH,KAAK,EAAE;IACb,IAAI,CAACC,MAAM,GAAGD,KAAK;IACnB,IAAI,CAACE,eAAe,GAAG,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACF,MAAM,IAAI,EAAE,CAAC;EACvE;EACA,IAAInH,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACmH,MAAM;EACtB;EACA;AACJ;AACA;AACA;EACIG,KAAK;EACL;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;AACA;EACIpF,UAAU,GAAG,IAAI;EACjB;AACJ;AACA;AACA;EACIC,UAAU,GAAG,CAAC;EACd;AACJ;AACA;AACA;AACA;EACI9E,WAAW,GAAG,IAAI;EAClB;AACJ;AACA;AACA;EACIwD,QAAQ;EACR;AACJ;AACA;AACA;EACIC,aAAa,GAAG,GAAG;EACnB;AACJ;AACA;AACA;EACIb,EAAE;EACF;AACJ;AACA;AACA;EACImC,SAAS;EACT;AACJ;AACA;AACA;EACIC,cAAc;EACd;AACJ;AACA;AACA;AACA;EACIkF,OAAO,GAAG,IAAIzR,YAAY,CAAC,CAAC;EAC5B;AACJ;AACA;AACA;AACA;EACI0R,MAAM,GAAG,IAAI1R,YAAY,CAAC,CAAC;EAC3B2R,SAAS;EACTC,UAAU;EACVC,QAAQ;EACRtI,aAAa;EACboB,WAAW;EACXH,gBAAgB;EAChBzF,mBAAmB;EACnBwB,YAAY;EACZe,YAAY;EACZwK,oBAAoB;EACpBC,cAAc;EACdlF,oBAAoB;EACpBmF,KAAK,GAAG,KAAK;EACbC,OAAO,GAAG,KAAK;EACfxK,cAAc,GAAGnH,MAAM,CAAC,EAAE,CAAC;EAC3B4R,MAAM,GAAG5R,MAAM,CAAC,CAAC,CAAC;EAClB6R,eAAe,GAAG7R,MAAM,CAAC;IAAE8H,KAAK,EAAE,CAAC,CAAC;IAAET,KAAK,EAAE,CAAC;IAAEyK,SAAS,EAAE,EAAE;IAAE5L,IAAI,EAAE;EAAK,CAAC,CAAC;EAC5E6L,WAAW,GAAG,EAAE;EAChBC,aAAa;EACbjB,eAAe;EACfD,MAAM;EACN,IAAImB,YAAYA,CAAA,EAAG;IACf,MAAMtK,aAAa,GAAG,IAAI,CAACR,cAAc,CAAC,CAAC,CAAC+K,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACjF,GAAG,KAAK,IAAI,CAAC2E,eAAe,CAAC,CAAC,CAACC,SAAS,CAAC;IACnG,OAAOnK,aAAa,GAAGA,aAAa,CAACZ,KAAK,GAAG,IAAI,CAACqL,cAAc;EACpE;EACA,IAAIA,cAAcA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACrB,eAAe,IAAI,CAAC,IAAI,CAACA,eAAe,CAACnH,MAAM,EAAE;MACvD,IAAI,CAACmH,eAAe,GAAG,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACrH,KAAK,IAAI,EAAE,CAAC;IACtE;IACA,OAAO,IAAI,CAACoH,eAAe;EAC/B;EACA,IAAI3J,aAAaA,CAAA,EAAG;IAChB,MAAMiL,WAAW,GAAG,IAAI,CAACR,eAAe,CAAC,CAAC;IAC1C,OAAOQ,WAAW,CAACnM,IAAI,IAAImM,WAAW,CAACnM,IAAI,EAAE2D,EAAE,GAAGwI,WAAW,CAACnM,IAAI,CAAC2D,EAAE,GAAGwI,WAAW,CAACvK,KAAK,KAAK,CAAC,CAAC,GAAI,GAAE,IAAI,CAAC+B,EAAG,GAAEzI,WAAW,CAACkM,UAAU,CAAC+E,WAAW,CAACP,SAAS,CAAC,GAAG,GAAG,GAAGO,WAAW,CAACP,SAAS,GAAG,EAAG,IAAGO,WAAW,CAACvK,KAAM,EAAC,GAAG,IAAI;EAClO;EACA0E,WAAWA,CAACmE,QAAQ,EAAEC,UAAU,EAAElF,EAAE,EAAEC,QAAQ,EAAEC,EAAE,EAAE9B,MAAM,EAAE+B,cAAc,EAAE;IACxE,IAAI,CAAC8E,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAClF,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAAC9B,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC+B,cAAc,GAAGA,cAAc;IACpC5L,MAAM,CAAC,MAAM;MACT,MAAMoN,IAAI,GAAG,IAAI,CAAClG,cAAc,CAAC,CAAC;MAClC,IAAI/F,WAAW,CAACkM,UAAU,CAACD,IAAI,CAAC,EAAE;QAC9B,IAAI,CAACiF,wBAAwB,CAAC,CAAC;QAC/B,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAC7B,CAAC,MACI;QACD,IAAI,CAACC,0BAA0B,CAAC,CAAC;QACjC,IAAI,CAACC,oBAAoB,CAAC,CAAC;MAC/B;IACJ,CAAC,CAAC;EACN;EACAhG,QAAQA,CAAA,EAAG;IACP,IAAI,CAACZ,cAAc,CAACpB,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC5C,IAAI,CAACoB,cAAc,CAACnB,aAAa,GAAG,IAAI,CAACA,aAAa;IACtD,IAAI,CAAC6B,oBAAoB,GAAG,IAAI,CAACV,cAAc,CAACjB,UAAU,CAAC8B,SAAS,CAAC,MAAM,IAAI,CAAC8F,0BAA0B,CAAC,CAAC,CAAC;IAC7G,IAAI,CAAC3I,EAAE,GAAG,IAAI,CAACA,EAAE,IAAIxI,iBAAiB,CAAC,CAAC;EAC5C;EACAqR,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACrB,SAAS,EAAEsB,OAAO,CAAEzM,IAAI,IAAK;MAC9B,QAAQA,IAAI,CAAC0M,OAAO,CAAC,CAAC;QAClB,KAAK,OAAO;UACR,IAAI,CAAC3J,aAAa,GAAG/C,IAAI,CAACgJ,QAAQ;UAClC;QACJ,KAAK,KAAK;UACN,IAAI,CAAC7E,WAAW,GAAGnE,IAAI,CAACgJ,QAAQ;UAChC;QACJ,KAAK,UAAU;UACX,IAAI,CAAChF,gBAAgB,GAAGhE,IAAI,CAACgJ,QAAQ;UACrC;QACJ,KAAK,aAAa;UACd,IAAI,CAACzK,mBAAmB,GAAGyB,IAAI,CAACgJ,QAAQ;UACxC;QACJ,KAAK,MAAM;UACP,IAAI,CAACjJ,YAAY,GAAGC,IAAI,CAACgJ,QAAQ;UACjC;QACJ;UACI,IAAI,CAACjJ,YAAY,GAAGC,IAAI,CAACgJ,QAAQ;UACjC;MACR;IACJ,CAAC,CAAC;EACN;EACA8B,oBAAoBA,CAACjK,KAAK,EAAEM,KAAK,GAAG,CAAC,EAAEwL,MAAM,GAAG,CAAC,CAAC,EAAEf,SAAS,GAAG,EAAE,EAAE;IAChE,MAAMM,cAAc,GAAG,EAAE;IACzBrL,KAAK,IACDA,KAAK,CAAC4L,OAAO,CAAC,CAACzM,IAAI,EAAE4B,KAAK,KAAK;MAC3B,MAAMoF,GAAG,GAAG,CAAC4E,SAAS,KAAK,EAAE,GAAGA,SAAS,GAAG,GAAG,GAAG,EAAE,IAAIhK,KAAK;MAC7D,MAAMgL,OAAO,GAAG;QACZ5M,IAAI;QACJ4B,KAAK;QACLT,KAAK;QACL6F,GAAG;QACH2F,MAAM;QACNf;MACJ,CAAC;MACDgB,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC9B,oBAAoB,CAAC9K,IAAI,CAACa,KAAK,EAAEM,KAAK,GAAG,CAAC,EAAEyL,OAAO,EAAE5F,GAAG,CAAC;MACjFkF,cAAc,CAACW,IAAI,CAACD,OAAO,CAAC;IAChC,CAAC,CAAC;IACN,OAAOV,cAAc;EACzB;EACAxP,WAAWA,CAACsD,IAAI,EAAE6G,IAAI,EAAE;IACpB,OAAO7G,IAAI,GAAG9E,WAAW,CAAC6L,YAAY,CAAC/G,IAAI,CAAC6G,IAAI,CAAC,CAAC,GAAG3E,SAAS;EAClE;EACAoB,eAAeA,CAACoD,KAAK,EAAE;IACnB,IAAI,CAACoG,MAAM,CAACpG,KAAK,CAAC;EACtB;EACAlD,iBAAiBA,CAACkD,KAAK,EAAE;IACrB,CAACA,KAAK,CAACqG,IAAI,KAAK,OAAO,IAAIrG,KAAK,CAACqG,IAAI,KAAK,OAAO,KAAK,IAAI,CAACzJ,eAAe,CAACoD,KAAK,CAAC;EACrF;EACAnF,WAAWA,CAACmF,KAAK,EAAE;IACf,MAAM;MAAEC,aAAa;MAAElF;IAAc,CAAC,GAAGiF,KAAK;IAC9C,MAAMsG,OAAO,GAAG,IAAI,CAACC,oBAAoB,CAACxL,aAAa,CAAC;IACxD,MAAMxD,IAAI,GAAG/C,WAAW,CAACgS,OAAO,CAACzL,aAAa,CAACkL,MAAM,CAAC;IACtD,MAAMQ,QAAQ,GAAG,IAAI,CAACC,UAAU,CAAC3L,aAAa,CAAC;IAC/C,IAAI0L,QAAQ,EAAE;MACV,MAAM;QAAEvL,KAAK;QAAEoF,GAAG;QAAE7F,KAAK;QAAEyK,SAAS;QAAE5L;MAAK,CAAC,GAAGyB,aAAa;MAC5D,IAAI,CAACR,cAAc,CAACoM,GAAG,CAAC,IAAI,CAACpM,cAAc,CAAC,CAAC,CAACzF,MAAM,CAAEyQ,CAAC,IAAKjF,GAAG,KAAKiF,CAAC,CAACjF,GAAG,IAAIA,GAAG,CAACsG,UAAU,CAACrB,CAAC,CAACjF,GAAG,CAAC,CAAC,CAAC;MACpG,IAAI,CAAC2E,eAAe,CAAC0B,GAAG,CAAC;QAAEzL,KAAK;QAAET,KAAK;QAAEyK,SAAS;QAAE5L;MAAK,CAAC,CAAC;MAC3D,IAAI,CAACwL,KAAK,GAAG,CAACvN,IAAI;MAClBvD,UAAU,CAAC6S,KAAK,CAAC,IAAI,CAAClC,QAAQ,CAACjF,gBAAgB,CAACoH,aAAa,CAAC;IAClE,CAAC,MACI;MACD,IAAIR,OAAO,EAAE;QACT,IAAI,CAACS,YAAY,CAAC/G,KAAK,CAAC;MAC5B,CAAC,MACI;QACD,MAAMgH,iBAAiB,GAAGzP,IAAI,GAAGwD,aAAa,GAAG,IAAI,CAACR,cAAc,CAAC,CAAC,CAAC+K,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACL,SAAS,KAAK,EAAE,CAAC;QACtG,IAAI,CAAC+B,IAAI,CAAChH,aAAa,CAAC;QACxB,IAAI,CAACiH,sBAAsB,CAACjH,aAAa,EAAE+G,iBAAiB,GAAGA,iBAAiB,CAAC9L,KAAK,GAAG,CAAC,CAAC,CAAC;QAC5F,IAAI,CAACd,YAAY,GAAG,KAAK;QACzBpG,UAAU,CAAC6S,KAAK,CAAC,IAAI,CAAClC,QAAQ,CAACjF,gBAAgB,CAACoH,aAAa,CAAC;MAClE;IACJ;EACJ;EACA5M,gBAAgBA,CAAC8F,KAAK,EAAE;IACpB,IAAI,CAAChM,UAAU,CAACmT,aAAa,CAAC,CAAC,EAAE;MAC7B,IAAI,CAAC,IAAI,CAAC/M,YAAY,EAAE;QACpB,IAAI,CAAC2M,YAAY,CAAC/G,KAAK,CAAC;MAC5B;IACJ;EACJ;EACAkH,sBAAsBA,CAAClH,KAAK,EAAE9E,KAAK,EAAE;IACjC,MAAMH,aAAa,GAAG,IAAI,CAACqM,eAAe,CAAClM,KAAK,CAAC;IACjD,IAAI,IAAI,CAAC+J,eAAe,CAAC,CAAC,CAAC/J,KAAK,KAAKA,KAAK,EAAE;MACxC,MAAM+J,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC,CAAC;MAC9C,IAAI,CAACA,eAAe,CAAC0B,GAAG,CAAC;QAAE,GAAG1B,eAAe;QAAE3L,IAAI,EAAEyB,aAAa,CAACzB,IAAI;QAAE4B;MAAM,CAAC,CAAC;MACjF,IAAI,CAACmM,YAAY,CAAC,CAAC;IACvB;EACJ;EACAA,YAAYA,CAACnM,KAAK,GAAG,CAAC,CAAC,EAAE;IACrB,MAAM+B,EAAE,GAAG/B,KAAK,KAAK,CAAC,CAAC,GAAI,GAAE,IAAI,CAAC+B,EAAG,IAAG/B,KAAM,EAAC,GAAG,IAAI,CAACV,aAAa;IACpE,MAAM8M,OAAO,GAAGtT,UAAU,CAACuT,UAAU,CAAC,IAAI,CAAC5C,QAAQ,CAAC7F,EAAE,CAACgI,aAAa,EAAG,UAAS7J,EAAG,IAAG,CAAC;IACvF,IAAIqK,OAAO,EAAE;MACTA,OAAO,CAACE,cAAc,IAAIF,OAAO,CAACE,cAAc,CAAC;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAU,CAAC,CAAC;IAC7F;EACJ;EACAX,YAAYA,CAAC/G,KAAK,EAAE;IAChB,MAAM;MAAEjF,aAAa;MAAEmF;IAAQ,CAAC,GAAGF,KAAK;IACxC,IAAIxL,WAAW,CAACgS,OAAO,CAACzL,aAAa,CAAC,EAClC;IACJ,MAAM;MAAEG,KAAK;MAAEoF,GAAG;MAAE7F,KAAK;MAAEyK,SAAS;MAAE/K,KAAK;MAAEb;IAAK,CAAC,GAAGyB,aAAa;IACnE,MAAMuL,OAAO,GAAG9R,WAAW,CAACkM,UAAU,CAACvG,KAAK,CAAC;IAC7C,MAAMI,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAACzF,MAAM,CAAEyQ,CAAC,IAAKA,CAAC,CAACL,SAAS,KAAKA,SAAS,IAAIK,CAAC,CAACL,SAAS,KAAK5E,GAAG,CAAC;IAC5GgG,OAAO,IAAI/L,cAAc,CAAC4L,IAAI,CAACpL,aAAa,CAAC;IAC7C,IAAI,CAACkK,eAAe,CAAC0B,GAAG,CAAC;MAAEzL,KAAK;MAAET,KAAK;MAAEyK,SAAS;MAAE5L;IAAK,CAAC,CAAC;IAC3D,IAAI,CAACiB,cAAc,CAACoM,GAAG,CAACpM,cAAc,CAAC;IACvC+L,OAAO,KAAK,IAAI,CAACxB,KAAK,GAAG,IAAI,CAAC;IAC9B5E,OAAO,IAAIlM,UAAU,CAAC6S,KAAK,CAAC,IAAI,CAAClC,QAAQ,CAACjF,gBAAgB,CAACoH,aAAa,CAAC;EAC7E;EACAV,MAAMA,CAACpG,KAAK,EAAE;IACV,IAAI,IAAI,CAAC5F,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,GAAG,KAAK;MACzB1F,WAAW,CAACiT,KAAK,CAAC,IAAI,CAAChD,QAAQ,CAAC7F,EAAE,CAACgI,aAAa,CAAC;MACjD,IAAI,CAACG,IAAI,CAAC,CAAC;IACf,CAAC,MACI;MACD,IAAI,CAAC7M,YAAY,GAAG,IAAI;MACxB1F,WAAW,CAACiS,GAAG,CAAC,MAAM,EAAE,IAAI,CAAChC,QAAQ,CAAC7F,EAAE,CAACgI,aAAa,EAAE,IAAI,CAAC5J,MAAM,CAAC0K,MAAM,CAACC,IAAI,CAAC;MAChFC,UAAU,CAAC,MAAM;QACb,IAAI,CAACC,IAAI,CAAC,CAAC;MACf,CAAC,EAAE,CAAC,CAAC;IACT;IACA,IAAI,CAAC/I,EAAE,CAACe,YAAY,CAAC,CAAC;IACtB,IAAI,CAAC2F,wBAAwB,CAAC,CAAC;IAC/B1F,KAAK,CAACgI,cAAc,CAAC,CAAC;EAC1B;EACAf,IAAIA,CAACjH,KAAK,EAAEE,OAAO,EAAE;IACjB,IAAI,IAAI,CAAC9F,YAAY,EAAE;MACnB0N,UAAU,CAAC,MAAM;QACb9T,UAAU,CAAC6S,KAAK,CAAC,IAAI,CAACnC,UAAU,CAACoC,aAAa,CAAC;MACnD,CAAC,EAAE,CAAC,CAAC;IACT;IACA,IAAI,CAACvM,cAAc,CAACoM,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAI,CAAC1B,eAAe,CAAC0B,GAAG,CAAC;MAAEzL,KAAK,EAAE,CAAC,CAAC;MAAET,KAAK,EAAE,CAAC;MAAEyK,SAAS,EAAE,EAAE;MAAE5L,IAAI,EAAE;IAAK,CAAC,CAAC;IAC5E4G,OAAO,IAAIlM,UAAU,CAAC6S,KAAK,CAAC,IAAI,CAAClC,QAAQ,CAACjF,gBAAgB,CAACoH,aAAa,CAAC;IACzE,IAAI,CAAChC,KAAK,GAAG,KAAK;EACtB;EACAiD,IAAIA,CAAA,EAAG;IACH,MAAMhN,aAAa,GAAG,IAAI,CAACqM,eAAe,CAAC,IAAI,CAACa,yBAAyB,CAAC,CAAC,CAAC;IAC5E,IAAI,CAAChD,eAAe,CAAC0B,GAAG,CAAC;MAAEzL,KAAK,EAAE,IAAI,CAAC+M,yBAAyB,CAAC,CAAC;MAAExN,KAAK,EAAE,CAAC;MAAEyK,SAAS,EAAE,EAAE;MAAE5L,IAAI,EAAEyB,aAAa,CAACzB;IAAK,CAAC,CAAC;IACxHtF,UAAU,CAAC6S,KAAK,CAAC,IAAI,CAAClC,QAAQ,CAACjF,gBAAgB,CAACoH,aAAa,CAAC;EAClE;EACAoB,WAAWA,CAAClI,KAAK,EAAE;IACf,IAAI,CAAC+E,OAAO,GAAG,IAAI;IACnB,MAAMhK,aAAa,GAAG,IAAI,CAACqM,eAAe,CAAC,IAAI,CAACa,yBAAyB,CAAC,CAAC,CAAC;IAC5E,MAAMhD,eAAe,GAAG,IAAI,CAACA,eAAe,CAAC,CAAC,CAAC/J,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC+J,eAAe,CAAC,CAAC,GAAG;MAAE/J,KAAK,EAAE,IAAI,CAAC+M,yBAAyB,CAAC,CAAC;MAAExN,KAAK,EAAE,CAAC;MAAEyK,SAAS,EAAE,EAAE;MAAE5L,IAAI,EAAEyB,aAAa,CAACzB;IAAK,CAAC;IACrL,IAAI,CAAC2L,eAAe,CAAC0B,GAAG,CAAC1B,eAAe,CAAC;IACzC,IAAI,CAACV,OAAO,CAACvK,IAAI,CAACgG,KAAK,CAAC;EAC5B;EACAmI,UAAUA,CAACnI,KAAK,EAAE;IACd,IAAI,CAAC+E,OAAO,GAAG,KAAK;IACpB,IAAI,CAACE,eAAe,CAAC0B,GAAG,CAAC;MAAEzL,KAAK,EAAE,CAAC,CAAC;MAAET,KAAK,EAAE,CAAC;MAAEyK,SAAS,EAAE,EAAE;MAAE5L,IAAI,EAAE;IAAK,CAAC,CAAC;IAC5E,IAAI,CAAC6L,WAAW,GAAG,EAAE;IACrB,IAAI,CAACL,KAAK,GAAG,KAAK;IAClB,IAAI,CAACN,MAAM,CAACxK,IAAI,CAACgG,KAAK,CAAC;EAC3B;EACAoI,SAASA,CAACpI,KAAK,EAAE;IACb,MAAMqI,OAAO,GAAGrI,KAAK,CAACqI,OAAO,IAAIrI,KAAK,CAACsI,OAAO;IAC9C,QAAQtI,KAAK,CAACqG,IAAI;MACd,KAAK,WAAW;QACZ,IAAI,CAACkC,cAAc,CAACvI,KAAK,CAAC;QAC1B;MACJ,KAAK,SAAS;QACV,IAAI,CAACwI,YAAY,CAACxI,KAAK,CAAC;QACxB;MACJ,KAAK,WAAW;QACZ,IAAI,CAACyI,cAAc,CAACzI,KAAK,CAAC;QAC1B;MACJ,KAAK,YAAY;QACb,IAAI,CAAC0I,eAAe,CAAC1I,KAAK,CAAC;QAC3B;MACJ,KAAK,MAAM;QACP,IAAI,CAAC2I,SAAS,CAAC3I,KAAK,CAAC;QACrB;MACJ,KAAK,KAAK;QACN,IAAI,CAAC4I,QAAQ,CAAC5I,KAAK,CAAC;QACpB;MACJ,KAAK,OAAO;QACR,IAAI,CAAC6I,UAAU,CAAC7I,KAAK,CAAC;QACtB;MACJ,KAAK,OAAO;QACR,IAAI,CAAC8I,UAAU,CAAC9I,KAAK,CAAC;QACtB;MACJ,KAAK,QAAQ;QACT,IAAI,CAAC+I,WAAW,CAAC/I,KAAK,CAAC;QACvB;MACJ,KAAK,KAAK;QACN,IAAI,CAACgJ,QAAQ,CAAChJ,KAAK,CAAC;QACpB;MACJ,KAAK,UAAU;MACf,KAAK,QAAQ;MACb,KAAK,WAAW;MAChB,KAAK,WAAW;MAChB,KAAK,YAAY;QACb;QACA;MACJ;QACI,IAAI,CAACqI,OAAO,IAAI7T,WAAW,CAACyU,oBAAoB,CAACjJ,KAAK,CAACM,GAAG,CAAC,EAAE;UACzD,IAAI,CAAC4I,WAAW,CAAClJ,KAAK,EAAEA,KAAK,CAACM,GAAG,CAAC;QACtC;QACA;IACR;EACJ;EACA8G,eAAeA,CAAClM,KAAK,EAAE;IACnB,OAAO1G,WAAW,CAACkM,UAAU,CAAC,IAAI,CAAC2E,YAAY,CAAC,GAAG,IAAI,CAACA,YAAY,CAACnK,KAAK,CAAC,GAAG,IAAI;EACtF;EACA+M,yBAAyBA,CAAA,EAAG;IACxB,MAAMkB,aAAa,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAClD,OAAOD,aAAa,GAAG,CAAC,GAAG,IAAI,CAACE,kBAAkB,CAAC,CAAC,GAAGF,aAAa;EACxE;EACAE,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAChE,YAAY,CAACiE,SAAS,CAAEvO,aAAa,IAAK,IAAI,CAACwO,WAAW,CAACxO,aAAa,CAAC,CAAC;EAC1F;EACAqO,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAAC/D,YAAY,CAACiE,SAAS,CAAEvO,aAAa,IAAK,IAAI,CAACyO,mBAAmB,CAACzO,aAAa,CAAC,CAAC;EAClG;EACAwL,oBAAoBA,CAACxL,aAAa,EAAE;IAChC,OAAOA,aAAa,IAAIvG,WAAW,CAACkM,UAAU,CAAC3F,aAAa,CAACZ,KAAK,CAAC;EACvE;EACAuM,UAAUA,CAAC3L,aAAa,EAAE;IACtB,OAAO,IAAI,CAACR,cAAc,CAAC,CAAC,CAACiG,IAAI,CAAE+E,CAAC,IAAKA,CAAC,CAACjF,GAAG,KAAKvF,aAAa,CAACuF,GAAG,CAAC;EACzE;EACAkJ,mBAAmBA,CAACzO,aAAa,EAAE;IAC/B,OAAO,IAAI,CAACwO,WAAW,CAACxO,aAAa,CAAC,IAAI,IAAI,CAAC2L,UAAU,CAAC3L,aAAa,CAAC;EAC5E;EACAwO,WAAWA,CAACxO,aAAa,EAAE;IACvB,OAAO,CAAC,CAACA,aAAa,IAAI,CAAC,IAAI,CAACQ,cAAc,CAACR,aAAa,CAACzB,IAAI,CAAC,IAAI,CAAC,IAAI,CAACmQ,eAAe,CAAC1O,aAAa,CAACzB,IAAI,CAAC;EACnH;EACAiC,cAAcA,CAACjC,IAAI,EAAE;IACjB,OAAO,IAAI,CAACtD,WAAW,CAACsD,IAAI,EAAE,UAAU,CAAC;EAC7C;EACAmQ,eAAeA,CAACnQ,IAAI,EAAE;IAClB,OAAO,IAAI,CAACtD,WAAW,CAACsD,IAAI,EAAE,WAAW,CAAC;EAC9C;EACAoQ,aAAaA,CAAC3O,aAAa,EAAE;IACzB,OAAO,IAAI,CAACwO,WAAW,CAACxO,aAAa,CAAC,IAAI,IAAI,CAAC4O,sBAAsB,CAAC5O,aAAa,CAAC,CAAC6O,iBAAiB,CAAC,CAAC,CAAChD,UAAU,CAAC,IAAI,CAACzB,WAAW,CAACyE,iBAAiB,CAAC,CAAC,CAAC;EAC7J;EACAC,qBAAqBA,CAAC9O,aAAa,EAAE;IACjC,OAAOA,aAAa,IAAIvG,WAAW,CAACkM,UAAU,CAAC3F,aAAa,CAACZ,KAAK,CAAC;EACvE;EACA+O,WAAWA,CAAClJ,KAAK,EAAE8J,IAAI,EAAE;IACrB,IAAI,CAAC3E,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW,IAAI,EAAE,IAAI2E,IAAI;IAClD,IAAIC,SAAS,GAAG,CAAC,CAAC;IAClB,IAAIC,OAAO,GAAG,KAAK;IACnB,IAAI,IAAI,CAAC/E,eAAe,CAAC,CAAC,CAAC/J,KAAK,KAAK,CAAC,CAAC,EAAE;MACrC6O,SAAS,GAAG,IAAI,CAAC1E,YAAY,CAAC1E,KAAK,CAAC,IAAI,CAACsE,eAAe,CAAC,CAAC,CAAC/J,KAAK,CAAC,CAACoO,SAAS,CAAEvO,aAAa,IAAK,IAAI,CAAC2O,aAAa,CAAC3O,aAAa,CAAC,CAAC;MACjIgP,SAAS,GAAGA,SAAS,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC1E,YAAY,CAAC1E,KAAK,CAAC,CAAC,EAAE,IAAI,CAACsE,eAAe,CAAC,CAAC,CAAC/J,KAAK,CAAC,CAACoO,SAAS,CAAEvO,aAAa,IAAK,IAAI,CAAC2O,aAAa,CAAC3O,aAAa,CAAC,CAAC,GAAGgP,SAAS,GAAG,IAAI,CAAC9E,eAAe,CAAC,CAAC,CAAC/J,KAAK;IACtM,CAAC,MACI;MACD6O,SAAS,GAAG,IAAI,CAAC1E,YAAY,CAACiE,SAAS,CAAEvO,aAAa,IAAK,IAAI,CAAC2O,aAAa,CAAC3O,aAAa,CAAC,CAAC;IACjG;IACA,IAAIgP,SAAS,KAAK,CAAC,CAAC,EAAE;MAClBC,OAAO,GAAG,IAAI;IAClB;IACA,IAAID,SAAS,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC9E,eAAe,CAAC,CAAC,CAAC/J,KAAK,KAAK,CAAC,CAAC,EAAE;MACzD6O,SAAS,GAAG,IAAI,CAAC9B,yBAAyB,CAAC,CAAC;IAChD;IACA,IAAI8B,SAAS,KAAK,CAAC,CAAC,EAAE;MAClB,IAAI,CAAC7C,sBAAsB,CAAClH,KAAK,EAAE+J,SAAS,CAAC;IACjD;IACA,IAAI,IAAI,CAAC3E,aAAa,EAAE;MACpB6E,YAAY,CAAC,IAAI,CAAC7E,aAAa,CAAC;IACpC;IACA,IAAI,CAACA,aAAa,GAAG0C,UAAU,CAAC,MAAM;MAClC,IAAI,CAAC3C,WAAW,GAAG,EAAE;MACrB,IAAI,CAACC,aAAa,GAAG,IAAI;IAC7B,CAAC,EAAE,GAAG,CAAC;IACP,OAAO4E,OAAO;EAClB;EACAL,sBAAsBA,CAAC5O,aAAa,EAAE;IAClC,OAAOA,aAAa,GAAG,IAAI,CAACnE,YAAY,CAACmE,aAAa,CAACzB,IAAI,CAAC,GAAGkC,SAAS;EAC5E;EACA5E,YAAYA,CAAC0C,IAAI,EAAE;IACf,OAAO,IAAI,CAACtD,WAAW,CAACsD,IAAI,EAAE,OAAO,CAAC;EAC1C;EACAiP,cAAcA,CAACvI,KAAK,EAAE;IAClB,MAAMjF,aAAa,GAAG,IAAI,CAACsK,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAAC/J,KAAK,CAAC;IACrE,MAAM3D,IAAI,GAAGwD,aAAa,GAAGvG,WAAW,CAACgS,OAAO,CAACzL,aAAa,CAACkL,MAAM,CAAC,GAAG,IAAI;IAC7E,IAAI1O,IAAI,EAAE;MACN,MAAM+O,OAAO,GAAG,IAAI,CAACuD,qBAAqB,CAAC9O,aAAa,CAAC;MACzD,IAAIuL,OAAO,EAAE;QACT,IAAI,CAACS,YAAY,CAAC;UAAE9G,aAAa,EAAED,KAAK;UAAEjF;QAAc,CAAC,CAAC;QAC1D,IAAI,CAACkK,eAAe,CAAC0B,GAAG,CAAC;UAAEzL,KAAK,EAAE,CAAC,CAAC;UAAEgK,SAAS,EAAEnK,aAAa,CAACuF,GAAG;UAAEhH,IAAI,EAAEyB,aAAa,CAACzB;QAAK,CAAC,CAAC;QAC/F,IAAI,CAACoP,eAAe,CAAC1I,KAAK,CAAC;MAC/B;IACJ,CAAC,MACI;MACD,MAAM+J,SAAS,GAAG,IAAI,CAAC9E,eAAe,CAAC,CAAC,CAAC/J,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAACgP,iBAAiB,CAAC,IAAI,CAACjF,eAAe,CAAC,CAAC,CAAC/J,KAAK,CAAC,GAAG,IAAI,CAAC+M,yBAAyB,CAAC,CAAC;MAC/I,IAAI,CAACf,sBAAsB,CAAClH,KAAK,EAAE+J,SAAS,CAAC;MAC7C/J,KAAK,CAACgI,cAAc,CAAC,CAAC;IAC1B;EACJ;EACAU,eAAeA,CAAC1I,KAAK,EAAE;IACnB,MAAMjF,aAAa,GAAG,IAAI,CAACsK,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAAC/J,KAAK,CAAC;IACrE,MAAMiP,UAAU,GAAGpP,aAAa,GAAG,IAAI,CAACR,cAAc,CAAC,CAAC,CAAC+K,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACjF,GAAG,KAAKvF,aAAa,CAACmK,SAAS,CAAC,GAAG,IAAI;IAC9G,IAAIiF,UAAU,EAAE;MACZ,MAAM7D,OAAO,GAAG,IAAI,CAACuD,qBAAqB,CAAC9O,aAAa,CAAC;MACzD,IAAIuL,OAAO,EAAE;QACT,IAAI,CAACS,YAAY,CAAC;UAAE9G,aAAa,EAAED,KAAK;UAAEjF;QAAc,CAAC,CAAC;QAC1D,IAAI,CAACkK,eAAe,CAAC0B,GAAG,CAAC;UAAEzL,KAAK,EAAE,CAAC,CAAC;UAAEgK,SAAS,EAAEnK,aAAa,CAACuF,GAAG;UAAEhH,IAAI,EAAEyB,aAAa,CAACzB;QAAK,CAAC,CAAC;QAC/F,IAAI,CAACiP,cAAc,CAACvI,KAAK,CAAC;MAC9B;IACJ,CAAC,MACI;MACD,MAAM+J,SAAS,GAAG,IAAI,CAAC9E,eAAe,CAAC,CAAC,CAAC/J,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAACgP,iBAAiB,CAAC,IAAI,CAACjF,eAAe,CAAC,CAAC,CAAC/J,KAAK,CAAC,GAAG,IAAI,CAAC+M,yBAAyB,CAAC,CAAC;MAC/I,IAAI,CAACf,sBAAsB,CAAClH,KAAK,EAAE+J,SAAS,CAAC;MAC7C/J,KAAK,CAACgI,cAAc,CAAC,CAAC;IAC1B;EACJ;EACAQ,YAAYA,CAACxI,KAAK,EAAE;IAChB,MAAMjF,aAAa,GAAG,IAAI,CAACsK,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAAC/J,KAAK,CAAC;IACrE,MAAM3D,IAAI,GAAG/C,WAAW,CAACgS,OAAO,CAACzL,aAAa,CAACkL,MAAM,CAAC;IACtD,IAAI1O,IAAI,EAAE;MACN,MAAM+O,OAAO,GAAG,IAAI,CAACuD,qBAAqB,CAAC9O,aAAa,CAAC;MACzD,IAAIuL,OAAO,EAAE;QACT,IAAI,CAACS,YAAY,CAAC;UAAE9G,aAAa,EAAED,KAAK;UAAEjF;QAAc,CAAC,CAAC;QAC1D,IAAI,CAACkK,eAAe,CAAC0B,GAAG,CAAC;UAAEzL,KAAK,EAAE,CAAC,CAAC;UAAEgK,SAAS,EAAEnK,aAAa,CAACuF,GAAG;UAAEhH,IAAI,EAAEyB,aAAa,CAACzB;QAAK,CAAC,CAAC;QAC/F,MAAMyQ,SAAS,GAAG,IAAI,CAACK,iBAAiB,CAAC,CAAC;QAC1C,IAAI,CAAClD,sBAAsB,CAAClH,KAAK,EAAE+J,SAAS,CAAC;MACjD;IACJ,CAAC,MACI;MACD,MAAMI,UAAU,GAAG,IAAI,CAAC5P,cAAc,CAAC,CAAC,CAAC+K,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACjF,GAAG,KAAKvF,aAAa,CAACmK,SAAS,CAAC;MACvF,IAAI,IAAI,CAACD,eAAe,CAAC,CAAC,CAAC/J,KAAK,KAAK,CAAC,EAAE;QACpC,IAAI,CAAC+J,eAAe,CAAC0B,GAAG,CAAC;UAAEzL,KAAK,EAAE,CAAC,CAAC;UAAEgK,SAAS,EAAEiF,UAAU,GAAGA,UAAU,CAACjF,SAAS,GAAG,EAAE;UAAE5L,IAAI,EAAEyB,aAAa,CAACzB;QAAK,CAAC,CAAC;QACpH,IAAI,CAAC6L,WAAW,GAAG,EAAE;QACrB,IAAI,CAACsD,cAAc,CAACzI,KAAK,CAAC;QAC1B,MAAMzF,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAACzF,MAAM,CAAEyQ,CAAC,IAAKA,CAAC,CAACL,SAAS,KAAK,IAAI,CAACD,eAAe,CAAC,CAAC,CAACC,SAAS,CAAC;QAC5G,IAAI,CAAC3K,cAAc,CAACoM,GAAG,CAACpM,cAAc,CAAC;MAC3C,CAAC,MACI;QACD,MAAMwP,SAAS,GAAG,IAAI,CAAC9E,eAAe,CAAC,CAAC,CAAC/J,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAACmP,iBAAiB,CAAC,IAAI,CAACpF,eAAe,CAAC,CAAC,CAAC/J,KAAK,CAAC,GAAG,IAAI,CAACoP,wBAAwB,CAAC,CAAC;QAC9I,IAAI,CAACpD,sBAAsB,CAAClH,KAAK,EAAE+J,SAAS,CAAC;MACjD;IACJ;IACA/J,KAAK,CAACgI,cAAc,CAAC,CAAC;EAC1B;EACAS,cAAcA,CAACzI,KAAK,EAAE;IAClB,MAAMjF,aAAa,GAAG,IAAI,CAACsK,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAAC/J,KAAK,CAAC;IACrE,MAAMiP,UAAU,GAAGpP,aAAa,GAAG,IAAI,CAACR,cAAc,CAAC,CAAC,CAAC+K,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACjF,GAAG,KAAKvF,aAAa,CAACmK,SAAS,CAAC,GAAG,IAAI;IAC9G,IAAIiF,UAAU,EAAE;MACZ,IAAI,CAACpD,YAAY,CAAC;QAAE9G,aAAa,EAAED,KAAK;QAAEjF,aAAa,EAAEoP;MAAW,CAAC,CAAC;MACtE,MAAM5P,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC,CAAC,CAACzF,MAAM,CAAEyQ,CAAC,IAAKA,CAAC,CAACL,SAAS,KAAK,IAAI,CAACD,eAAe,CAAC,CAAC,CAACC,SAAS,CAAC;MAC5G,IAAI,CAAC3K,cAAc,CAACoM,GAAG,CAACpM,cAAc,CAAC;MACvCyF,KAAK,CAACgI,cAAc,CAAC,CAAC;IAC1B,CAAC,MACI;MACD,MAAM+B,SAAS,GAAG,IAAI,CAAC9E,eAAe,CAAC,CAAC,CAAC/J,KAAK,KAAK,CAAC,CAAC,GAAG,IAAI,CAACmP,iBAAiB,CAAC,IAAI,CAACpF,eAAe,CAAC,CAAC,CAAC/J,KAAK,CAAC,GAAG,IAAI,CAACoP,wBAAwB,CAAC,CAAC;MAC9I,IAAI,CAACpD,sBAAsB,CAAClH,KAAK,EAAE+J,SAAS,CAAC;MAC7C/J,KAAK,CAACgI,cAAc,CAAC,CAAC;IAC1B;EACJ;EACAW,SAASA,CAAC3I,KAAK,EAAE;IACb,IAAI,CAACkH,sBAAsB,CAAClH,KAAK,EAAE,IAAI,CAACqJ,kBAAkB,CAAC,CAAC,CAAC;IAC7DrJ,KAAK,CAACgI,cAAc,CAAC,CAAC;EAC1B;EACAY,QAAQA,CAAC5I,KAAK,EAAE;IACZ,IAAI,CAACkH,sBAAsB,CAAClH,KAAK,EAAE,IAAI,CAACoK,iBAAiB,CAAC,CAAC,CAAC;IAC5DpK,KAAK,CAACgI,cAAc,CAAC,CAAC;EAC1B;EACAa,UAAUA,CAAC7I,KAAK,EAAE;IACd,IAAI,CAAC8I,UAAU,CAAC9I,KAAK,CAAC;EAC1B;EACA+I,WAAWA,CAAC/I,KAAK,EAAE;IACf,IAAI,CAACiH,IAAI,CAACjH,KAAK,EAAE,IAAI,CAAC;IACtB,IAAI,CAACiF,eAAe,CAAC,CAAC,CAAC/J,KAAK,GAAG,IAAI,CAAC+M,yBAAyB,CAAC,CAAC;IAC/DjI,KAAK,CAACgI,cAAc,CAAC,CAAC;EAC1B;EACAgB,QAAQA,CAAChJ,KAAK,EAAE;IACZ,IAAI,IAAI,CAACiF,eAAe,CAAC,CAAC,CAAC/J,KAAK,KAAK,CAAC,CAAC,EAAE;MACrC,MAAMH,aAAa,GAAG,IAAI,CAACsK,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAAC/J,KAAK,CAAC;MACrE,MAAMoL,OAAO,GAAG,IAAI,CAACuD,qBAAqB,CAAC9O,aAAa,CAAC;MACzD,CAACuL,OAAO,IAAI,IAAI,CAACS,YAAY,CAAC;QAAE9G,aAAa,EAAED,KAAK;QAAEjF;MAAc,CAAC,CAAC;IAC1E;IACA,IAAI,CAACkM,IAAI,CAAC,CAAC;EACf;EACA6B,UAAUA,CAAC9I,KAAK,EAAE;IACd,IAAI,IAAI,CAACiF,eAAe,CAAC,CAAC,CAAC/J,KAAK,KAAK,CAAC,CAAC,EAAE;MACrC,MAAMoM,OAAO,GAAGtT,UAAU,CAACuT,UAAU,CAAC,IAAI,CAAC5C,QAAQ,CAAC7F,EAAE,CAACgI,aAAa,EAAG,UAAU,GAAE,IAAI,CAACtM,aAAc,EAAE,IAAG,CAAC;MAC5G,MAAM+P,aAAa,GAAGjD,OAAO,IAAItT,UAAU,CAACuT,UAAU,CAACD,OAAO,EAAE,6BAA6B,CAAC;MAC9FiD,aAAa,GAAGA,aAAa,CAACC,KAAK,CAAC,CAAC,GAAGlD,OAAO,IAAIA,OAAO,CAACkD,KAAK,CAAC,CAAC;MAClE,MAAMzP,aAAa,GAAG,IAAI,CAACsK,YAAY,CAAC,IAAI,CAACJ,eAAe,CAAC,CAAC,CAAC/J,KAAK,CAAC;MACrE,MAAMoL,OAAO,GAAG,IAAI,CAACuD,qBAAqB,CAAC9O,aAAa,CAAC;MACzD,CAACuL,OAAO,KAAK,IAAI,CAACrB,eAAe,CAAC,CAAC,CAAC/J,KAAK,GAAG,IAAI,CAAC+M,yBAAyB,CAAC,CAAC,CAAC;IACjF;IACAjI,KAAK,CAACgI,cAAc,CAAC,CAAC;EAC1B;EACAsC,wBAAwBA,CAAA,EAAG;IACvB,MAAMnB,aAAa,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAClD,OAAOD,aAAa,GAAG,CAAC,GAAG,IAAI,CAACiB,iBAAiB,CAAC,CAAC,GAAGjB,aAAa;EACvE;EACAiB,iBAAiBA,CAAA,EAAG;IAChB,OAAO5V,WAAW,CAACiW,aAAa,CAAC,IAAI,CAACpF,YAAY,EAAGtK,aAAa,IAAK,IAAI,CAACwO,WAAW,CAACxO,aAAa,CAAC,CAAC;EAC3G;EACAsP,iBAAiBA,CAACnP,KAAK,EAAE;IACrB,MAAMwP,gBAAgB,GAAGxP,KAAK,GAAG,CAAC,GAAG1G,WAAW,CAACiW,aAAa,CAAC,IAAI,CAACpF,YAAY,CAAC1E,KAAK,CAAC,CAAC,EAAEzF,KAAK,CAAC,EAAGH,aAAa,IAAK,IAAI,CAACwO,WAAW,CAACxO,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC;IAC1J,OAAO2P,gBAAgB,GAAG,CAAC,CAAC,GAAGA,gBAAgB,GAAGxP,KAAK;EAC3D;EACAgP,iBAAiBA,CAAChP,KAAK,EAAE;IACrB,MAAMwP,gBAAgB,GAAGxP,KAAK,GAAG,IAAI,CAACmK,YAAY,CAACrI,MAAM,GAAG,CAAC,GAAG,IAAI,CAACqI,YAAY,CAAC1E,KAAK,CAACzF,KAAK,GAAG,CAAC,CAAC,CAACoO,SAAS,CAAEvO,aAAa,IAAK,IAAI,CAACwO,WAAW,CAACxO,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC;IACrK,OAAO2P,gBAAgB,GAAG,CAAC,CAAC,GAAGA,gBAAgB,GAAGxP,KAAK,GAAG,CAAC,GAAGA,KAAK;EACvE;EACAyK,kBAAkBA,CAAA,EAAG;IACjB,IAAIlT,iBAAiB,CAAC,IAAI,CAACuR,UAAU,CAAC,EAAE;MACpC,IAAI,CAAC,IAAI,CAACa,cAAc,EAAE;QACtB,IAAI,CAACA,cAAc,GAAG,IAAI,CAAC9F,QAAQ,CAAC4L,MAAM,CAAC,IAAI,CAAC5G,QAAQ,CAAC6G,WAAW,EAAE,QAAQ,EAAG5K,KAAK,IAAK;UACvF,IAAI,CAAChM,UAAU,CAACmT,aAAa,CAAC,CAAC,EAAE;YAC7B,IAAI,CAACF,IAAI,CAACjH,KAAK,EAAE,IAAI,CAAC;UAC1B;UACA,IAAI,CAAC5F,YAAY,GAAG,KAAK;QAC7B,CAAC,CAAC;MACN;IACJ;EACJ;EACAsL,wBAAwBA,CAAA,EAAG;IACvB,IAAIjT,iBAAiB,CAAC,IAAI,CAACuR,UAAU,CAAC,EAAE;MACpC,IAAI,CAAC,IAAI,CAACY,oBAAoB,EAAE;QAC5B,IAAI,CAACA,oBAAoB,GAAG,IAAI,CAAC7F,QAAQ,CAAC4L,MAAM,CAAC,IAAI,CAAC5G,QAAQ,EAAE,OAAO,EAAG/D,KAAK,IAAK;UAChF,MAAM6K,kBAAkB,GAAG,IAAI,CAAClG,QAAQ,CAAC7F,EAAE,CAACgI,aAAa,KAAK9G,KAAK,CAAC8K,MAAM,IAAI,CAAC,IAAI,CAACnG,QAAQ,CAAC7F,EAAE,CAACgI,aAAa,CAACiE,QAAQ,CAAC/K,KAAK,CAAC8K,MAAM,CAAC;UACpI,MAAME,mBAAmB,GAAG,IAAI,CAAC5Q,YAAY,IAAI,IAAI,CAACsK,UAAU,CAACoC,aAAa,KAAK9G,KAAK,CAAC8K,MAAM,IAAI,CAAC,IAAI,CAACpG,UAAU,CAACoC,aAAa,CAACiE,QAAQ,CAAC/K,KAAK,CAAC8K,MAAM,CAAC;UACxJ,IAAID,kBAAkB,EAAE;YACpBG,mBAAmB,GAAI,IAAI,CAAC5Q,YAAY,GAAG,KAAK,GAAI,IAAI,CAAC6M,IAAI,CAAC,CAAC;UACnE;QACJ,CAAC,CAAC;MACN;IACJ;EACJ;EACArB,0BAA0BA,CAAA,EAAG;IACzB,IAAI,IAAI,CAAChB,oBAAoB,EAAE;MAC3B,IAAI,CAACA,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACpC;EACJ;EACAiB,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAChB,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAAC,CAAC;MACrB,IAAI,CAACA,cAAc,GAAG,IAAI;IAC9B;EACJ;EACA9D,WAAWA,CAAA,EAAG;IACV,IAAI,CAACpB,oBAAoB,EAAEqB,WAAW,CAAC,CAAC;IACxC,IAAI,CAAC4E,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA,OAAO1H,IAAI,YAAA8M,gBAAA5M,CAAA;IAAA,YAAAA,CAAA,IAAwFyF,OAAO,EA1hCjBlR,EAAE,CAAAsO,iBAAA,CA0hCiCxO,QAAQ,GA1hC3CE,EAAE,CAAAsO,iBAAA,CA0hCsD5N,WAAW,GA1hCnEV,EAAE,CAAAsO,iBAAA,CA0hC8EtO,EAAE,CAACuO,UAAU,GA1hC7FvO,EAAE,CAAAsO,iBAAA,CA0hCwGtO,EAAE,CAACwO,SAAS,GA1hCtHxO,EAAE,CAAAsO,iBAAA,CA0hCiItO,EAAE,CAACyO,iBAAiB,GA1hCvJzO,EAAE,CAAAsO,iBAAA,CA0hCkKrN,EAAE,CAACqX,aAAa,GA1hCpLtY,EAAE,CAAAsO,iBAAA,CA0hC+LtD,cAAc;EAAA;EACxS,OAAO0D,IAAI,kBA3hC8E1O,EAAE,CAAA2O,iBAAA;IAAA3C,IAAA,EA2hCJkF,OAAO;IAAAtC,SAAA;IAAA2J,cAAA,WAAAC,uBAAA3V,EAAA,EAAAC,GAAA,EAAA2V,QAAA;MAAA,IAAA5V,EAAA;QA3hCL7C,EAAE,CAAA0Y,cAAA,CAAAD,QAAA,EA2hCqdvX,aAAa;MAAA;MAAA,IAAA2B,EAAA;QAAA,IAAAmM,EAAA;QA3hCpehP,EAAE,CAAAiP,cAAA,CAAAD,EAAA,GAAFhP,EAAE,CAAAkP,WAAA,QAAApM,GAAA,CAAA+O,SAAA,GAAA7C,EAAA;MAAA;IAAA;IAAAH,SAAA,WAAA8J,cAAA9V,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAF7C,EAAE,CAAA+O,WAAA,CAAA9F,GAAA;QAAFjJ,EAAE,CAAA+O,WAAA,CAAA7F,GAAA;MAAA;MAAA,IAAArG,EAAA;QAAA,IAAAmM,EAAA;QAAFhP,EAAE,CAAAiP,cAAA,CAAAD,EAAA,GAAFhP,EAAE,CAAAkP,WAAA,QAAApM,GAAA,CAAAgP,UAAA,GAAA9C,EAAA,CAAAG,KAAA;QAAFnP,EAAE,CAAAiP,cAAA,CAAAD,EAAA,GAAFhP,EAAE,CAAAkP,WAAA,QAAApM,GAAA,CAAAiP,QAAA,GAAA/C,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,MAAA;MAAAlF,KAAA;MAAAsH,KAAA;MAAAC,UAAA;MAAApF,UAAA;MAAAC,UAAA;MAAA9E,WAAA;MAAAwD,QAAA;MAAAC,aAAA;MAAAb,EAAA;MAAAmC,SAAA;MAAAC,cAAA;IAAA;IAAA6C,OAAA;MAAAqC,OAAA;MAAAC,MAAA;IAAA;IAAAgH,QAAA,GAAF5Y,EAAE,CAAA6Y,kBAAA,CA2hCiZ,CAAC7N,cAAc,CAAC;IAAA8N,kBAAA,EAAA3P,GAAA;IAAAoG,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAqJ,iBAAAlW,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAA+M,GAAA,GA3hCna5P,EAAE,CAAA6G,gBAAA;QAAF7G,EAAE,CAAAgZ,eAAA;QAAFhZ,EAAE,CAAA2D,cAAA,YA4hC2G,CAAC;QA5hC9G3D,EAAE,CAAAyE,UAAA,IAAA8E,sBAAA,gBA6hCjC,CAAC,IAAAM,oBAAA,cAgBnD,CAAC;QA7iCgF7J,EAAE,CAAA2D,cAAA,wBAokCnF,CAAC;QApkCgF3D,EAAE,CAAA8G,UAAA,uBAAAmS,mDAAAjS,MAAA;UAAFhH,EAAE,CAAAiH,aAAA,CAAA2I,GAAA;UAAA,OAAF5P,EAAE,CAAAkH,WAAA,CA+jClEpE,GAAA,CAAAmF,WAAA,CAAAjB,MAAkB,CAAC;QAAA,EAAC,uBAAAkS,mDAAAlS,MAAA;UA/jC4ChH,EAAE,CAAAiH,aAAA,CAAA2I,GAAA;UAAA,OAAF5P,EAAE,CAAAkH,WAAA,CAgkClEpE,GAAA,CAAAwS,WAAA,CAAAtO,MAAkB,CAAC;QAAA,EAAC,sBAAAmS,kDAAAnS,MAAA;UAhkC4ChH,EAAE,CAAAiH,aAAA,CAAA2I,GAAA;UAAA,OAAF5P,EAAE,CAAAkH,WAAA,CAikCnEpE,GAAA,CAAAyS,UAAA,CAAAvO,MAAiB,CAAC;QAAA,EAAC,yBAAAoS,qDAAApS,MAAA;UAjkC8ChH,EAAE,CAAAiH,aAAA,CAAA2I,GAAA;UAAA,OAAF5P,EAAE,CAAAkH,WAAA,CAkkChEpE,GAAA,CAAA0S,SAAA,CAAAxO,MAAgB,CAAC;QAAA,EAAC,4BAAAqS,wDAAArS,MAAA;UAlkC4ChH,EAAE,CAAAiH,aAAA,CAAA2I,GAAA;UAAA,OAAF5P,EAAE,CAAAkH,WAAA,CAmkC7DpE,GAAA,CAAAwE,gBAAA,CAAAN,MAAuB,CAAC;QAAA,EAAC;QAnkCkChH,EAAE,CAAA6D,YAAA,CAokCpE,CAAC;QApkCiE7D,EAAE,CAAAyE,UAAA,IAAAmG,sBAAA,gBAqkCxB,CAAC,IAAAE,8BAAA,gCArkCqB9K,EAAE,CAAAmF,sBAwkC/D,CAAC;QAxkC4DnF,EAAE,CAAA6D,YAAA,CA6kClF,CAAC;MAAA;MAAA,IAAAhB,EAAA;QAAA,MAAAyW,SAAA,GA7kC+EtZ,EAAE,CAAAgF,WAAA;QAAFhF,EAAE,CAAAuI,UAAA,CAAAzF,GAAA,CAAA4O,UA4hCwB,CAAC;QA5hC3B1R,EAAE,CAAAqD,UAAA,YAAFrD,EAAE,CAAAqF,eAAA,KAAA+D,GAAA,EAAAtG,GAAA,CAAA0E,YAAA,CA4hCG,CAAC,YAAA1E,GAAA,CAAA2O,KAAsC,CAAC;QA5hC7CzR,EAAE,CAAAuD,WAAA;QAAFvD,EAAE,CAAA8D,SAAA,CA6hCnC,CAAC;QA7hCgC9D,EAAE,CAAAqD,UAAA,SAAAP,GAAA,CAAA2G,aA6hCnC,CAAC;QA7hCgCzJ,EAAE,CAAA8D,SAAA,CAyiChD,CAAC;QAziC6C9D,EAAE,CAAAqD,UAAA,SAAAP,GAAA,CAAAqH,KAAA,IAAArH,GAAA,CAAAqH,KAAA,CAAAC,MAAA,IAyiChD,CAAC;QAziC6CpK,EAAE,CAAA8D,SAAA,CAmjCxD,CAAC;QAnjCqD9D,EAAE,CAAAqD,UAAA,UAAAP,GAAA,CAAA8P,cAmjCxD,CAAC,iBAAA9P,GAAA,CAAA2D,YACI,CAAC,WAAA3D,GAAA,CAAAuH,EACjB,CAAC,aACD,CAAC,eAAAvH,GAAA,CAAAyJ,UACW,CAAC,eAAAzJ,GAAA,CAAAwJ,UACD,CAAC,iBAAAxJ,GAAA,CAAA0E,YACG,CAAC,gBAAA1E,GAAA,CAAA2E,WACH,CAAC,cAAA3E,GAAA,CAAA0J,SACL,CAAC,mBAAA1J,GAAA,CAAA2J,cACS,CAAC,kBAAA3J,GAAA,CAAAqP,OAAA,GAAArP,GAAA,CAAA8E,aAAA,GAAAgB,SACmB,CAAC,mBAAA9F,GAAA,CAAA6E,cAAA,EACnB,CAAC;QA9jC0C3H,EAAE,CAAA8D,SAAA,EAqkCrC,CAAC;QArkCkC9D,EAAE,CAAAqD,UAAA,SAAAP,GAAA,CAAA+H,WAqkCrC,CAAC,aAAAyO,SAAU,CAAC;MAAA;IAAA;IAAArJ,YAAA,EAAAA,CAAA,MAS2lCrQ,EAAE,CAACsQ,OAAO,EAAyGtQ,EAAE,CAACwQ,IAAI,EAAkHxQ,EAAE,CAACyQ,gBAAgB,EAAyKzQ,EAAE,CAAC0Q,OAAO,EAAgG/O,QAAQ,EAA0E0K,UAAU;IAAAsN,MAAA;IAAA5I,aAAA;IAAA6I,eAAA;EAAA;AAC/wD;AACA;EAAA,QAAA1N,SAAA,oBAAAA,SAAA,KAhlC6F9L,EAAE,CAAA+L,iBAAA,CAglCJmF,OAAO,EAAc,CAAC;IACrGlF,IAAI,EAAE7L,SAAS;IACfyQ,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,WAAW;MAAEnB,QAAQ,EAAG;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAE8J,eAAe,EAAE7Y,uBAAuB,CAAC8Y,MAAM;MAAE9I,aAAa,EAAEvQ,iBAAiB,CAAC0Q,IAAI;MAAEC,IAAI,EAAE;QAC7EC,KAAK,EAAE;MACX,CAAC;MAAE0I,SAAS,EAAE,CAAC1O,cAAc,CAAC;MAAEuO,MAAM,EAAE,CAAC,gkCAAgkC;IAAE,CAAC;EACxnC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEvN,IAAI,EAAE2N,QAAQ;IAAEC,UAAU,EAAE,CAAC;MAC9C5N,IAAI,EAAEpL,MAAM;MACZgQ,IAAI,EAAE,CAAC9Q,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEkM,IAAI,EAAEpD,SAAS;IAAEgR,UAAU,EAAE,CAAC;MAClC5N,IAAI,EAAEpL,MAAM;MACZgQ,IAAI,EAAE,CAAClQ,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEsL,IAAI,EAAEhM,EAAE,CAACuO;EAAW,CAAC,EAAE;IAAEvC,IAAI,EAAEhM,EAAE,CAACwO;EAAU,CAAC,EAAE;IAAExC,IAAI,EAAEhM,EAAE,CAACyO;EAAkB,CAAC,EAAE;IAAEzC,IAAI,EAAE/K,EAAE,CAACqX;EAAc,CAAC,EAAE;IAAEtM,IAAI,EAAEhB;EAAe,CAAC,CAAC,EAAkB;IAAEb,KAAK,EAAE,CAAC;MACxK6B,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEoR,KAAK,EAAE,CAAC;MACRzF,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEqR,UAAU,EAAE,CAAC;MACb1F,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEiM,UAAU,EAAE,CAAC;MACbN,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEkM,UAAU,EAAE,CAAC;MACbP,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEoH,WAAW,EAAE,CAAC;MACduE,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAE4K,QAAQ,EAAE,CAAC;MACXe,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAE6K,aAAa,EAAE,CAAC;MAChBc,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEgK,EAAE,EAAE,CAAC;MACL2B,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEmM,SAAS,EAAE,CAAC;MACZR,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEoM,cAAc,EAAE,CAAC;MACjBT,IAAI,EAAE3L;IACV,CAAC,CAAC;IAAEsR,OAAO,EAAE,CAAC;MACV3F,IAAI,EAAE1L;IACV,CAAC,CAAC;IAAEsR,MAAM,EAAE,CAAC;MACT5F,IAAI,EAAE1L;IACV,CAAC,CAAC;IAAEuR,SAAS,EAAE,CAAC;MACZ7F,IAAI,EAAEnL,eAAe;MACrB+P,IAAI,EAAE,CAAC1P,aAAa;IACxB,CAAC,CAAC;IAAE4Q,UAAU,EAAE,CAAC;MACb9F,IAAI,EAAEzL,SAAS;MACfqQ,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEmB,QAAQ,EAAE,CAAC;MACX/F,IAAI,EAAEzL,SAAS;MACfqQ,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMiJ,aAAa,CAAC;EAChB,OAAOtO,IAAI,YAAAuO,sBAAArO,CAAA;IAAA,YAAAA,CAAA,IAAwFoO,aAAa;EAAA;EAChH,OAAOE,IAAI,kBAprC8E/Z,EAAE,CAAAga,gBAAA;IAAAhO,IAAA,EAorCS6N;EAAa;EACjH,OAAOI,IAAI,kBArrC8Eja,EAAE,CAAAka,gBAAA;IAAAC,OAAA,GAqrCkCpa,YAAY,EAAEiB,YAAY,EAAES,YAAY,EAAEE,aAAa,EAAER,YAAY,EAAEI,QAAQ,EAAEF,aAAa,EAAEC,cAAc,EAAEN,YAAY,EAAEW,aAAa,EAAER,YAAY;EAAA;AAC1R;AACA;EAAA,QAAA2K,SAAA,oBAAAA,SAAA,KAvrC6F9L,EAAE,CAAA+L,iBAAA,CAurCJ8N,aAAa,EAAc,CAAC;IAC3G7N,IAAI,EAAElL,QAAQ;IACd8P,IAAI,EAAE,CAAC;MACCuJ,OAAO,EAAE,CAACpa,YAAY,EAAEiB,YAAY,EAAES,YAAY,EAAEE,aAAa,EAAER,YAAY,EAAEI,QAAQ,EAAEF,aAAa,EAAEC,cAAc,CAAC;MACzH8Y,OAAO,EAAE,CAAClJ,OAAO,EAAElQ,YAAY,EAAEW,aAAa,EAAER,YAAY,CAAC;MAC7DkZ,YAAY,EAAE,CAACnJ,OAAO,EAAEjF,UAAU;IACtC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASiF,OAAO,EAAE2I,aAAa,EAAE7O,cAAc,EAAEiB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}