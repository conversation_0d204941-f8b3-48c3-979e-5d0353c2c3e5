{"ast": null, "code": "import { APP_INITIALIZER } from '@angular/core';\nimport { jwtDecode } from 'jwt-decode';\nimport { AuthService } from '../authentication/auth.service';\nexport function initializeApp(service) {\n  const urlParams = new URLSearchParams(window.location.search);\n  if (urlParams.has('code')) {\n    const token = urlParams.get('code');\n    const decoded = jwtDecode(token);\n    const user = {\n      id: decoded.id,\n      documentId: decoded.documentId,\n      isAdmin: true\n    };\n    service.setAuth(token, user, false);\n  }\n  return () => service.checkAdminUser();\n}\nexport const appInitializerProviders = [{\n  provide: APP_INITIALIZER,\n  useFactory: initializeApp,\n  deps: [AuthService],\n  multi: true\n}];", "map": {"version": 3, "names": ["APP_INITIALIZER", "jwtDecode", "AuthService", "initializeApp", "service", "urlParams", "URLSearchParams", "window", "location", "search", "has", "token", "get", "decoded", "user", "id", "documentId", "isAdmin", "setAuth", "checkAdminUser", "appInitializerProviders", "provide", "useFactory", "deps", "multi"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\core\\bootstrap\\initializers.ts"], "sourcesContent": ["import { APP_INITIALIZER } from '@angular/core';\r\nimport { jwtDecode } from 'jwt-decode';\r\nimport { AuthService } from '../authentication/auth.service';\r\n\r\nexport function initializeApp(service: AuthService) {\r\n  const urlParams = new URLSearchParams(window.location.search);\r\n  if (urlParams.has('code')) {\r\n    const token: any = urlParams.get('code');\r\n    const decoded: any = jwtDecode(token);\r\n    const user = { id: decoded.id, documentId: decoded.documentId, isAdmin: true };\r\n    service.setAuth(token, user, false);\r\n  }\r\n\r\n  return () => service.checkAdminUser();\r\n}\r\n\r\nexport const appInitializerProviders = [\r\n  {\r\n    provide: APP_INITIALIZER,\r\n    useFactory: initializeApp,\r\n    deps: [AuthService],\r\n    multi: true,\r\n  },\r\n];\r\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,eAAe;AAC/C,SAASC,SAAS,QAAQ,YAAY;AACtC,SAASC,WAAW,QAAQ,gCAAgC;AAE5D,OAAM,SAAUC,aAAaA,CAACC,OAAoB;EAChD,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;EAC7D,IAAIJ,SAAS,CAACK,GAAG,CAAC,MAAM,CAAC,EAAE;IACzB,MAAMC,KAAK,GAAQN,SAAS,CAACO,GAAG,CAAC,MAAM,CAAC;IACxC,MAAMC,OAAO,GAAQZ,SAAS,CAACU,KAAK,CAAC;IACrC,MAAMG,IAAI,GAAG;MAAEC,EAAE,EAAEF,OAAO,CAACE,EAAE;MAAEC,UAAU,EAAEH,OAAO,CAACG,UAAU;MAAEC,OAAO,EAAE;IAAI,CAAE;IAC9Eb,OAAO,CAACc,OAAO,CAACP,KAAK,EAAEG,IAAI,EAAE,KAAK,CAAC;EACrC;EAEA,OAAO,MAAMV,OAAO,CAACe,cAAc,EAAE;AACvC;AAEA,OAAO,MAAMC,uBAAuB,GAAG,CACrC;EACEC,OAAO,EAAErB,eAAe;EACxBsB,UAAU,EAAEnB,aAAa;EACzBoB,IAAI,EAAE,CAACrB,WAAW,CAAC;EACnBsB,KAAK,EAAE;CACR,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}