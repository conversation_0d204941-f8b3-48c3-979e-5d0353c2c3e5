<!--HEADER SEC-->
<header class="main-header fixed top-0 w-full bg-white z-5">
    <div class="header-body relative max-w-1200 w-full mx-auto px-4 flex align-items-center">
        <div class="header-logo relative pr-6 flex align-items-center w-18rem h-8rem secondary-bg-color">
            <img [src]="logo || '/assets/layout/images/snjya-public-services-logo.png'" class="w-full h-fit" alt="Logo" />
        </div>
        <div class="header-menu-sec pl-5 relative flex align-items-center justify-content-between flex-1 h-8rem bg-white">
            <div class="menu-list">
                <ul class="p-0 m-0 flex align-items-center gap-5">
                    <li class="flex" *ngFor="let menuItem of menuItems">
                        <a [href]="menuItem.Link || '#'"
                           [target]="menuItem.Target || '_self'"
                           class="flex flex-column gap-1 text-lg font-semibold text-color line-height-1">
                            {{ menuItem.Title }}
                            <span class="text-sm font-normal text-color-secondary">{{ menuItem.Sub_Title }}</span>
                        </a>
                    </li>
                </ul>
            </div>
            <button type="button"
                class="p-element p-ripple p-button-rounded p-button p-component w-8rem h-3rem justify-content-center gap-2 line-height-2 bg-bluegray-100 text-color font-semibold border-none">
                <span class="material-symbols-rounded text-xl">login</span> Login
            </button>
        </div>
    </div>
</header>
<!--HEADER SEC-->
