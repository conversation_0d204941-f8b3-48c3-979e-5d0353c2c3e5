{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./service/app.layout.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/core/services/content-vendor.service\";\nimport * as i4 from \"./app.menu.component\";\nconst _c0 = [\"menuContainer\"];\nconst _c1 = () => [\"/\"];\nexport class AppSidebarComponent {\n  constructor(layoutService, el, route, CMSservice) {\n    this.layoutService = layoutService;\n    this.el = el;\n    this.route = route;\n    this.CMSservice = CMSservice;\n    this.timeout = null;\n    this.logo = '';\n  }\n  ngOnInit() {\n    const commonContent = this.route.snapshot.data['commonContent'];\n    if (commonContent?.body) {\n      const logoComponent = this.CMSservice.getDataByComponentName(commonContent.body, \"crm.logo\");\n      if (logoComponent?.length) {\n        this.logo = logoComponent[0].Logo?.url || '';\n      }\n    }\n  }\n  onMouseEnter() {\n    if (!this.layoutService.state.anchored) {\n      if (this.timeout) {\n        clearTimeout(this.timeout);\n        this.timeout = null;\n      }\n      this.layoutService.state.sidebarActive = true;\n    }\n  }\n  onMouseLeave() {\n    if (!this.layoutService.state.anchored) {\n      if (!this.timeout) {\n        this.timeout = setTimeout(() => this.layoutService.state.sidebarActive = false, 300);\n      }\n    }\n  }\n  anchor() {\n    this.layoutService.state.anchored = !this.layoutService.state.anchored;\n  }\n  static {\n    this.ɵfac = function AppSidebarComponent_Factory(t) {\n      return new (t || AppSidebarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ContentService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppSidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      viewQuery: function AppSidebarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuContainer = _t.first);\n        }\n      },\n      decls: 14,\n      vars: 3,\n      consts: [[\"menuContainer\", \"\"], [1, \"layout-sidebar\", 3, \"mouseenter\", \"mouseleave\"], [1, \"sidebar-header\"], [1, \"app-logo\", 3, \"routerLink\"], [1, \"arrow-icon\", \"flex\", \"flex-row-reverse\", \"align-items-center\", \"justify-content-center\", \"w-3rem\", \"h-3rem\"], [1, \"material-symbols-rounded\"], [1, \"app-logo-normal\", \"w-full\"], [3, \"src\"], [1, \"layout-menu-container\"], [1, \"portal-name\", \"py-3\", \"flex\", \"justify-content-end\"], [1, \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"p-button-outlined\", \"cursor-auto\", \"justify-content-center\", \"m-0\", \"text-base\", \"w-3rem\", \"h-3rem\"]],\n      template: function AppSidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵlistener(\"mouseenter\", function AppSidebarComponent_Template_div_mouseenter_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMouseEnter());\n          })(\"mouseleave\", function AppSidebarComponent_Template_div_mouseleave_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMouseLeave());\n          });\n          i0.ɵɵelementStart(1, \"div\", 2)(2, \"a\", 3)(3, \"div\", 4)(4, \"span\", 5);\n          i0.ɵɵtext(5, \"menu_open\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵelement(7, \"img\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 8, 0);\n          i0.ɵɵelement(10, \"app-menu\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 9)(12, \"h5\", 10);\n          i0.ɵɵtext(13, \" CRM\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(2, _c1));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"src\", ctx.logo, i0.ɵɵsanitizeUrl);\n        }\n      },\n      dependencies: [i2.RouterLink, i4.AppMenuComponent],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppSidebarComponent", "constructor", "layoutService", "el", "route", "CMSservice", "timeout", "logo", "ngOnInit", "commonContent", "snapshot", "data", "body", "logoComponent", "getDataByComponentName", "length", "Logo", "url", "onMouseEnter", "state", "anchored", "clearTimeout", "sidebarActive", "onMouseLeave", "setTimeout", "anchor", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "ElementRef", "i2", "ActivatedRoute", "i3", "ContentService", "selectors", "viewQuery", "AppSidebarComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "AppSidebarComponent_Template_div_mouseenter_0_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "AppSidebarComponent_Template_div_mouseleave_0_listener", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c1", "ɵɵsanitizeUrl"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\store\\layout\\app.sidebar.component.ts", "D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\store\\layout\\app.sidebar.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { LayoutService } from './service/app.layout.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { ContentService } from 'src/app/core/services/content-vendor.service';\r\n\r\n@Component({\r\n    selector: 'app-sidebar',\r\n    templateUrl: './app.sidebar.component.html'\r\n})\r\nexport class AppSidebarComponent implements OnInit {\r\n    timeout: any = null;\r\n    public logo = '';\r\n\r\n    @ViewChild('menuContainer') menuContainer!: ElementRef;\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        public el: ElementRef,\r\n        private route: ActivatedRoute,\r\n        private CMSservice: ContentService\r\n    ) { }\r\n\r\n    ngOnInit(): void {\r\n        const commonContent = this.route.snapshot.data['commonContent'];\r\n        if (commonContent?.body) {\r\n            const logoComponent = this.CMSservice.getDataByComponentName(commonContent.body, \"crm.logo\");\r\n            if (logoComponent?.length) {\r\n                this.logo = logoComponent[0].Logo?.url || '';\r\n            }\r\n        }\r\n    }\r\n\r\n    onMouseEnter() {\r\n        if (!this.layoutService.state.anchored) {\r\n            if (this.timeout) {\r\n                clearTimeout(this.timeout);\r\n                this.timeout = null;\r\n            }\r\n            this.layoutService.state.sidebarActive = true;\r\n\r\n\r\n        }\r\n    }\r\n\r\n    onMouseLeave() {\r\n        if (!this.layoutService.state.anchored) {\r\n            if (!this.timeout) {\r\n                this.timeout = setTimeout(() => this.layoutService.state.sidebarActive = false, 300);\r\n            }\r\n        }\r\n    }\r\n\r\n    anchor() {\r\n        this.layoutService.state.anchored = !this.layoutService.state.anchored;\r\n    }\r\n\r\n}\r\n", "<div class=\"layout-sidebar\" (mouseenter)=\"onMouseEnter()\" (mouseleave)=\"onMouseLeave()\">\r\n    <div class=\"sidebar-header\">\r\n        <a [routerLink]=\"['/']\" class=\"app-logo\">\r\n            <div class=\"arrow-icon flex flex-row-reverse align-items-center justify-content-center w-3rem h-3rem\">\r\n                <span class=\"material-symbols-rounded\">menu_open</span>\r\n            </div>\r\n            <div class=\"app-logo-normal w-full\">\r\n                <img\r\n                    [src]=\"logo\">\r\n            </div>\r\n        </a>\r\n    </div>\r\n\r\n\r\n    <div #menuContainer class=\"layout-menu-container\">\r\n        <app-menu></app-menu>\r\n    </div>\r\n    <div class=\"portal-name py-3 flex justify-content-end\">\r\n        <h5\r\n            class=\"p-element p-ripple p-button p-component p-button-outlined cursor-auto justify-content-center m-0 text-base w-3rem h-3rem\">\r\n            CRM</h5>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;AASA,OAAM,MAAOA,mBAAmB;EAK5BC,YACWC,aAA4B,EAC5BC,EAAc,EACbC,KAAqB,EACrBC,UAA0B;IAH3B,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,EAAE,GAAFA,EAAE;IACD,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IARtB,KAAAC,OAAO,GAAQ,IAAI;IACZ,KAAAC,IAAI,GAAG,EAAE;EAQZ;EAEJC,QAAQA,CAAA;IACJ,MAAMC,aAAa,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,IAAI,CAAC,eAAe,CAAC;IAC/D,IAAIF,aAAa,EAAEG,IAAI,EAAE;MACrB,MAAMC,aAAa,GAAG,IAAI,CAACR,UAAU,CAACS,sBAAsB,CAACL,aAAa,CAACG,IAAI,EAAE,UAAU,CAAC;MAC5F,IAAIC,aAAa,EAAEE,MAAM,EAAE;QACvB,IAAI,CAACR,IAAI,GAAGM,aAAa,CAAC,CAAC,CAAC,CAACG,IAAI,EAAEC,GAAG,IAAI,EAAE;MAChD;IACJ;EACJ;EAEAC,YAAYA,CAAA;IACR,IAAI,CAAC,IAAI,CAAChB,aAAa,CAACiB,KAAK,CAACC,QAAQ,EAAE;MACpC,IAAI,IAAI,CAACd,OAAO,EAAE;QACde,YAAY,CAAC,IAAI,CAACf,OAAO,CAAC;QAC1B,IAAI,CAACA,OAAO,GAAG,IAAI;MACvB;MACA,IAAI,CAACJ,aAAa,CAACiB,KAAK,CAACG,aAAa,GAAG,IAAI;IAGjD;EACJ;EAEAC,YAAYA,CAAA;IACR,IAAI,CAAC,IAAI,CAACrB,aAAa,CAACiB,KAAK,CAACC,QAAQ,EAAE;MACpC,IAAI,CAAC,IAAI,CAACd,OAAO,EAAE;QACf,IAAI,CAACA,OAAO,GAAGkB,UAAU,CAAC,MAAM,IAAI,CAACtB,aAAa,CAACiB,KAAK,CAACG,aAAa,GAAG,KAAK,EAAE,GAAG,CAAC;MACxF;IACJ;EACJ;EAEAG,MAAMA,CAAA;IACF,IAAI,CAACvB,aAAa,CAACiB,KAAK,CAACC,QAAQ,GAAG,CAAC,IAAI,CAAClB,aAAa,CAACiB,KAAK,CAACC,QAAQ;EAC1E;;;uBA5CSpB,mBAAmB,EAAA0B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,UAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAnBlC,mBAAmB;MAAAmC,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCThCZ,EAAA,CAAAc,cAAA,aAAwF;UAA9Bd,EAA9B,CAAAe,UAAA,wBAAAC,uDAAA;YAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;YAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAcN,GAAA,CAAArB,YAAA,EAAc;UAAA,EAAC,wBAAA4B,uDAAA;YAAApB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;YAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAeN,GAAA,CAAAhB,YAAA,EAAc;UAAA,EAAC;UAIvEG,EAHZ,CAAAc,cAAA,aAA4B,WACiB,aACiE,cAC3D;UAAAd,EAAA,CAAAqB,MAAA,gBAAS;UACpDrB,EADoD,CAAAsB,YAAA,EAAO,EACrD;UACNtB,EAAA,CAAAc,cAAA,aAAoC;UAChCd,EAAA,CAAAuB,SAAA,aACiB;UAG7BvB,EAFQ,CAAAsB,YAAA,EAAM,EACN,EACF;UAGNtB,EAAA,CAAAc,cAAA,gBAAkD;UAC9Cd,EAAA,CAAAuB,SAAA,gBAAqB;UACzBvB,EAAA,CAAAsB,YAAA,EAAM;UAEFtB,EADJ,CAAAc,cAAA,cAAuD,cAEkF;UACjId,EAAA,CAAAqB,MAAA,YAAG;UAEfrB,EAFe,CAAAsB,YAAA,EAAK,EACV,EACJ;;;UApBKtB,EAAA,CAAAwB,SAAA,GAAoB;UAApBxB,EAAA,CAAAyB,UAAA,eAAAzB,EAAA,CAAA0B,eAAA,IAAAC,GAAA,EAAoB;UAMX3B,EAAA,CAAAwB,SAAA,GAAY;UAAZxB,EAAA,CAAAyB,UAAA,QAAAZ,GAAA,CAAAhC,IAAA,EAAAmB,EAAA,CAAA4B,aAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}