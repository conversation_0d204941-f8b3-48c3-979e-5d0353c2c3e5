import { Http<PERSON>lient, HttpErrorResponse, HttpHandler, HttpInterceptor, HttpRequest, HttpResponse } from "@angular/common/http"; // Import HttpClient
import { Injectable } from "@angular/core";
import { tap, switchMap, catchError, Observable, map, of, throwError } from "rxjs";
import { ApiConstant, CMS_APIContstant, ENDPOINT } from "src/app/constants/api.constants";
import { AuthService } from "./auth.service";

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private escapeTokenUrls = [
    CMS_APIContstant.SINGIN,
    CMS_APIContstant.RESET_PASSWORD_REQUEST,
    CMS_APIContstant.RESET_PASSWORD,
    CMS_APIContstant.CONTENT_PS,
    CMS_APIContstant.MAIN_MENU_API_DETAILS,
  ];

  constructor(private authService: AuthService, private http: HttpClient) { }

  intercept(req: HttpRequest<any>, next: <PERSON>tt<PERSON><PERSON><PERSON><PERSON>) {
    if (this.authService.isLoggedIn && (req.url.includes(ENDPOINT.NODE) || (!this.startsWithAnyUrl(req.url, this.escapeTokenUrls)))) {
      return this.getAuthToken(req.url).pipe(
        tap((token) => {
          if (token) {
            const headers: any = {
              Authorization: "Bearer " + token,
            };
            if (this.startsWithAnyUrl(req.url, [...Object.values(ApiConstant)])) {
              const details = this.authService.userDetail;
              headers.documentId = details.documentId;
            }
            req = req.clone({
              setHeaders: headers,
            });
          }
        }),
        switchMap(() => next.handle(req)),
        tap((event: any) => {
          if (event instanceof HttpResponse) {
            const newToken = event.headers.get("refreshtoken");
            const auth = this.authService.getAuth();
            if (newToken && auth) {
              this.authService.setAuth(
                newToken,
                auth[this.authService.UserDetailsKey],
                this.authService.isRememberMeSelected()
              );
            }
          }
        }),
        catchError((x) => this.handleAuthError(x))
      );
    }
    return next.handle(req).pipe(catchError((x) => this.handleAuthError(x)));
  }

  private getAuthToken(url: string): Observable<string> {
    const authToken = this.authService.getToken();
    const isAdmin = this.authService.userDetail?.isAdmin;
    if (isAdmin && this.startsWithAnyUrl(url, [...Object.values(CMS_APIContstant)])) {
      return this.authService.cmsToken.pipe(
        switchMap((token) => {
          if (token) {
            return of(token)
          }
          return this.authService.getCMSToken();
        })
      )
    }
    return of(authToken);
  }

  private startsWithAnyUrl(newUrl: string, urls: string[]) {
    return urls.some((url) => newUrl.startsWith(url));
  }

  private handleAuthError(err: HttpErrorResponse): Observable<any> {
    if (err.status === 401 || err.status === 403) {
      this.authService.removeAuthToken();
      window.location.href = "#/auth/login";
      window.location.reload();
      return of(err.message);
    }
    return throwError(() => err);
  }
}
