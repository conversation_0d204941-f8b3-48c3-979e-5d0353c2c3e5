# Azure Pipeline for Building and Deploying Angular and Strapi Applications

resources:
  containers:
    - container: cfdockercli
      image: "nirmaltodev/node20cfdockercli:v1"
      options: --user 0:0 --privileged

trigger:
  - main

variables:
  - group: CF_SNJAY_SecretKeys

stages:
  - stage: deploy
    displayName: Build and Deploy to SAP Cloud Platform
    jobs:
      - job: build_and_deploy
        displayName: Build and Deploy Using Docker
        pool:
          vmImage: "ubuntu-latest"
        container: cfdockercli

        steps:
          # Step 1: Checkout the code
          - checkout: self
            displayName: "Checkout Repository"

          # Step 2: Configure Git User Identity
          - script: |
              git config --global user.email "<EMAIL>"
              git config --global user.name "Azure DevOps Pipeline"
            displayName: "Configure Git User Identity"

          # Step 3: Add and Pull from Central Repository
          - script: |
              git remote add snjyacentral $(SNJYA_CMS_REPO_URL)
              git config pull.rebase false  # Set merge strategy
              git pull snjyacentral main --allow-unrelated-histories
            displayName: "Pull Changes from Central Repository"

          # Step 4: Build Angular Client
          - script: |
              cd ./client
              npm install
              npm run build
            displayName: "Build Angular Client"

          # Step 5: Copy Angular Build to Public Folder
          - script: |
              cp -r ./client/dist/snjya-ps/* ./public/  # Copy built files to public folder
            displayName: "Copy Angular Build to Public Folder"

          # Step 6: Build Strapi Backend
          - script: |
              rm -f package-lock.json
              npm install
              npm run build
            displayName: "Build Strapi Backend"

          # Step 7: Verify Docker is available
          - script: docker --version
            displayName: "Verify Docker Installation"

          # Step 8: Login to Docker Hub
          - script: |
              echo "$(DOCKER_HUB_ACCESS_TOKEN)" | docker login -u "$(DOCKER_HUB_USERNAME)" --password-stdin
            displayName: "Login to Docker Hub"

          # Step 9: Build Docker Image and tag it for Docker Hub
          - script: |
              docker build --no-cache -t $(DOCKER_HUB_USERNAME)/snjya:snjyaps .
            displayName: "Build Docker Image"

          # Step 10: Push Docker Image to Docker Hub
          - script: |
              docker push $(DOCKER_HUB_USERNAME)/snjya:snjyaps
            displayName: "Push Docker Image to Docker Hub"

          # Step 11: Login to Cloud Foundry
          - script: |
              cf login -a $(API_ENDPOINT) -u $(USER) -p $(PASSWORD) -o "ASAR AMERICA INC._cf" -s new_dev
            displayName: "Cloud Foundry Login"

          # Step 12: Deploy Docker Image to SAP BTP via Cloud Foundry
          - script: |
              cf push snjyaps --docker-image $(DOCKER_HUB_USERNAME)/snjya:snjyaps
            displayName: "Deploy Docker Image to Cloud Foundry"

          # Step 13: Delete Docker Image Tag from Docker Hub
          #- script: |
          #    curl -X DELETE \
          #      -H "Authorization: Bearer $(DOCKER_HUB_ACCESS_TOKEN)" \
          #      https://hub.docker.com/v2/repositories/$(DOCKER_HUB_USERNAME)/snjya/

          #  displayName: 'Delete Docker Image Tag from Docker Hub'
