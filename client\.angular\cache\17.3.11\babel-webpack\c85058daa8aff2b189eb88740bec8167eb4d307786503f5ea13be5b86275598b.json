{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/dropdown\";\nconst _c0 = () => [\"/login\"];\nexport class SignupComponent {\n  constructor(routter) {\n    this.routter = routter;\n    this.cities = [{\n      name: \"What's was your first car?\",\n      code: \"NY\"\n    }, {\n      name: \"What was your favorite school teacher's name?\",\n      code: \"RM\"\n    }, {\n      name: 'What is your date of birth?',\n      code: 'LDN'\n    }, {\n      name: 'What’s your favorite movie?',\n      code: 'IST'\n    }, {\n      name: 'What is your astrological sign?',\n      code: 'PRS'\n    }];\n  }\n  static {\n    this.ɵfac = function SignupComponent_Factory(t) {\n      return new (t || SignupComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SignupComponent,\n      selectors: [[\"app-signup\"]],\n      decls: 109,\n      vars: 12,\n      consts: [[1, \"login-sec\", \"bg-white\", \"h-screen\", \"pt-6\", \"pb-6\"], [1, \"login-page-body\", \"flex\", \"flex-column\", \"justify-content-between\", \"p-20\", \"gap-8\", \"m-auto\", \"h-full\", \"px-5\"], [1, \"login-header\", \"relative\", \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"logo\", \"w-full\", \"max-w-15rem\"], [\"href\", \"#\", 1, \"flex\", \"w-full\"], [\"src\", \"/assets/layout/images/chs-logo.svg\", \"alt\", \"Logo\", 1, \"w-full\"], [1, \"sign-up\", \"flex\", \"align-items-center\", \"justify-content-center\", \"text-primary\", \"gap-3\", \"text-base\", \"font-medium\"], [\"type\", \"button\", 1, \"sign-up-btn\", \"p-element\", \"p-ripple\", \"p-button-outlined\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-8rem\", \"justify-content-center\", \"gap-1\", 3, \"routerLink\"], [1, \"material-symbols-rounded\", \"text-2xl\"], [1, \"login-form\", \"mx-auto\", \"p-5\", \"max-w-30rem\", \"w-full\", \"bg-white\", \"border-round-3xl\", \"shadow-2\"], [1, \"flex\", \"flex-column\", \"position-relative\"], [1, \"mb-2\", \"flex\", \"justify-content-center\", \"text-4xl\", \"font-bold\", \"text-primary\"], [1, \"mb-5\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\"], [1, \"p-fluid\", \"p-formgrid\", \"grid\"], [1, \"field\", \"col-12\", \"md:col-6\", \"mb-0\"], [1, \"text-base\", \"font-medium\", \"text-gray-600\"], [\"type\", \"text\", \"id\", \"username\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\"], [1, \"field\", \"col-12\", \"md:col-12\", \"mb-0\"], [\"type\", \"email\", \"id\", \"username\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\"], [1, \"form-group\", \"relative\"], [\"type\", \"password\", 1, \"p-inputtext\", \"p-component\", \"p-element\", \"w-full\", \"bg-gray-50\"], [\"type\", \"button\", 1, \"pass-show-btn\", \"absolute\", \"top-0\", \"bottom-0\", \"m-auto\", \"p-0\", \"border-none\", \"bg-white-alpha-10\", \"text-gray-500\", \"cursor-pointer\"], [1, \"material-symbols-rounded\"], [\"optionLabel\", \"name\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"showClear\", \"styleClass\"], [1, \"form-footer\", \"mt-4\"], [\"type\", \"button\", 1, \"p-button-outlined\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"h-3-3rem\", \"justify-content-center\", \"gap-3\", \"font-semibold\", \"border-2\", \"border-black-alpha-20\", 3, \"routerLink\"], [\"type\", \"button\", 1, \"p-button-rounded\", \"p-button\", \"p-component\", \"w-full\", \"justify-content-center\", \"h-3-3rem\", \"font-semibold\"], [1, \"copyright-sec\", \"flex\", \"flex-column\", \"position-relative\"], [1, \"m-0\", \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-gray-900\"], [1, \"p-0\", \"flex\", \"position-relative\", \"align-items-center\", \"justify-content-center\", \"list-none\", \"gap-3\"], [\"target\", \"_blank\", 1, \"flex\", \"justify-content-center\", \"text-base\", \"font-medium\", \"text-primary\", \"underline\"]],\n      template: function SignupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"a\", 4);\n          i0.ɵɵelement(5, \"img\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵtext(7, \" Do you have an account? \");\n          i0.ɵɵelementStart(8, \"button\", 7)(9, \"span\", 8);\n          i0.ɵɵtext(10, \"input\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11, \" Login \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 9)(13, \"form\", 10)(14, \"h1\", 11);\n          i0.ɵɵtext(15, \"Registration \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\", 12);\n          i0.ɵɵtext(17, \"Enter your details below to create an account and get started.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 13)(19, \"div\", 14)(20, \"label\", 15);\n          i0.ɵɵtext(21, \"First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 14)(24, \"label\", 15);\n          i0.ɵɵtext(25, \"Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(26, \"input\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 14)(28, \"label\", 15);\n          i0.ɵɵtext(29, \"Phone Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(30, \"input\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 14)(32, \"label\", 15);\n          i0.ɵɵtext(33, \"Extension\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(34, \"input\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 17)(36, \"label\", 15);\n          i0.ɵɵtext(37, \"E-mail\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(38, \"input\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 17)(40, \"label\", 15);\n          i0.ɵɵtext(41, \"Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(42, \"input\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 14)(44, \"label\", 15);\n          i0.ɵɵtext(45, \"Country\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(46, \"input\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 14)(48, \"label\", 15);\n          i0.ɵɵtext(49, \"State\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(50, \"input\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"div\", 14)(52, \"label\", 15);\n          i0.ɵɵtext(53, \"City\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(54, \"input\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"div\", 14)(56, \"label\", 15);\n          i0.ɵɵtext(57, \"Zip Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(58, \"input\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"div\", 14)(60, \"label\", 15);\n          i0.ɵɵtext(61, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"div\", 19);\n          i0.ɵɵelement(63, \"input\", 20);\n          i0.ɵɵelementStart(64, \"button\", 21)(65, \"span\", 22);\n          i0.ɵɵtext(66, \"visibility\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(67, \"div\", 14)(68, \"label\", 15);\n          i0.ɵɵtext(69, \"Retype Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"div\", 19);\n          i0.ɵɵelement(71, \"input\", 20);\n          i0.ɵɵelementStart(72, \"button\", 21)(73, \"span\", 22);\n          i0.ɵɵtext(74, \"visibility\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(75, \"div\", 17)(76, \"label\", 15);\n          i0.ɵɵtext(77, \"Security Question 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"p-dropdown\", 23);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SignupComponent_Template_p_dropdown_ngModelChange_78_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCity, $event) || (ctx.selectedCity = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(79, \"div\", 17)(80, \"label\", 15);\n          i0.ɵɵtext(81, \"Answer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(82, \"input\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"div\", 17)(84, \"label\", 15);\n          i0.ɵɵtext(85, \"Security Question 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"p-dropdown\", 23);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function SignupComponent_Template_p_dropdown_ngModelChange_86_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCity, $event) || (ctx.selectedCity = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(87, \"div\", 17)(88, \"label\", 15);\n          i0.ɵɵtext(89, \"Answer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(90, \"input\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(91, \"div\", 24)(92, \"div\", 13)(93, \"div\", 14)(94, \"button\", 25);\n          i0.ɵɵtext(95, \" Go Back\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(96, \"div\", 14)(97, \"button\", 26);\n          i0.ɵɵtext(98, \" Login\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(99, \"div\", 27)(100, \"p\", 28);\n          i0.ɵɵtext(101, \"\\u00A9 2024 Consolidated Hospitality Supplies\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"ul\", 29)(103, \"li\")(104, \"a\", 30);\n          i0.ɵɵtext(105, \"Terms & Conditions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(106, \"li\")(107, \"a\", 30);\n          i0.ɵɵtext(108, \"Privacy Policy\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(10, _c0));\n          i0.ɵɵadvance(70);\n          i0.ɵɵproperty(\"options\", ctx.cities);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCity);\n          i0.ɵɵproperty(\"showClear\", true)(\"styleClass\", \"p-inputtext p-component p-element w-full bg-gray-50\");\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"options\", ctx.cities);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedCity);\n          i0.ɵɵproperty(\"showClear\", true)(\"styleClass\", \"p-inputtext p-component p-element w-full bg-gray-50\");\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(11, _c0));\n        }\n      },\n      dependencies: [i2.ɵNgNoValidate, i2.NgControlStatus, i2.NgControlStatusGroup, i2.NgModel, i2.NgForm, i1.RouterLink, i3.Dropdown],\n      styles: [\".login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%] {\\n  max-width: 1440px;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%] {\\n  max-width: 600px !important;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .pass-show-btn[_ngcontent-%COMP%] {\\n  right: 12px;\\n  height: 24px;\\n  width: 24px;\\n}\\n.login-sec[_ngcontent-%COMP%]   .login-page-body[_ngcontent-%COMP%]   .login-form[_ngcontent-%COMP%]   form[_ngcontent-%COMP%]   .form-group[_ngcontent-%COMP%]   .form-check-box[_ngcontent-%COMP%] {\\n  accent-color: var(--primarycolor);\\n}\\n\\n.p-inputtext[_ngcontent-%COMP%] {\\n  height: 3rem;\\n  appearance: auto !important;\\n}\\n\\n.h-3-3rem[_ngcontent-%COMP%] {\\n  height: 3.3rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SignupComponent", "constructor", "routter", "cities", "name", "code", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "SignupComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵtwoWayListener", "SignupComponent_Template_p_dropdown_ngModelChange_78_listener", "$event", "ɵɵtwoWayBindingSet", "selectedCity", "SignupComponent_Template_p_dropdown_ngModelChange_86_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtwoWayProperty"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\session\\signup\\signup.component.ts", "D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\session\\signup\\signup.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Router } from \"@angular/router\";\r\n\r\ninterface City {\r\n  name: string,\r\n  code: string\r\n}\r\n@Component({\r\n  selector: 'app-signup',\r\n  templateUrl: './signup.component.html',\r\n  styleUrl: './signup.component.scss'\r\n})\r\nexport class SignupComponent {\r\n\r\n  cities: City[];\r\n  selectedCity: City | any;\r\n\r\n  constructor(\r\n    public routter: Router,\r\n  ) {\r\n    this.cities = [\r\n      { name: \"What's was your first car?\", code: \"NY\" },\r\n      { name: \"What was your favorite school teacher's name?\", code: \"RM\" },\r\n      { name: 'What is your date of birth?', code: 'LDN' },\r\n      { name: 'What’s your favorite movie?', code: 'IST' },\r\n      { name: 'What is your astrological sign?', code: 'PRS' }\r\n    ];\r\n  }\r\n\r\n}\r\n", "<section class=\"login-sec bg-white h-screen pt-6 pb-6\">\r\n  <div class=\"login-page-body flex flex-column justify-content-between p-20 gap-8 m-auto h-full px-5\">\r\n    <div class=\"login-header relative flex align-items-center justify-content-between\">\r\n      <div class=\"logo w-full max-w-15rem\"><a href=\"#\" class=\"flex w-full\"><img src=\"/assets/layout/images/chs-logo.svg\"\r\n            alt=\"Logo\" class=\"w-full\" /></a></div>\r\n      <div class=\"sign-up flex align-items-center justify-content-center text-primary gap-3 text-base font-medium\">\r\n        Do you have an account?\r\n        <button type=\"button\" [routerLink]=\"['/login']\"\r\n          class=\"sign-up-btn p-element p-ripple p-button-outlined p-button-rounded p-button p-component w-8rem justify-content-center gap-1\">\r\n          <span class=\"material-symbols-rounded text-2xl\">input</span> Login\r\n        </button>\r\n      </div>\r\n    </div>\r\n    <div class=\"login-form mx-auto p-5 max-w-30rem w-full bg-white border-round-3xl shadow-2\">\r\n      <form class=\"flex flex-column position-relative\">\r\n        <h1 class=\"mb-2 flex justify-content-center text-4xl font-bold text-primary\">Registration\r\n        </h1>\r\n        <p class=\"mb-5 flex justify-content-center text-base font-medium text-gray-900\">Enter your details below to create\r\n          an account and get started.</p>\r\n        <div class=\"p-fluid p-formgrid grid\">\r\n          <div class=\"field col-12 md:col-6 mb-0\">\r\n            <label class=\"text-base font-medium text-gray-600\">First Name</label>\r\n            <input type=\"text\" class=\"p-inputtext p-component p-element w-full bg-gray-50\" id=\"username\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-6 mb-0\">\r\n            <label class=\"text-base font-medium text-gray-600\">Last Name</label>\r\n            <input type=\"text\" class=\"p-inputtext p-component p-element w-full bg-gray-50\" id=\"username\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-6 mb-0\">\r\n            <label class=\"text-base font-medium text-gray-600\">Phone Number</label>\r\n            <input type=\"text\" class=\"p-inputtext p-component p-element w-full bg-gray-50\" id=\"username\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-6 mb-0\">\r\n            <label class=\"text-base font-medium text-gray-600\">Extension</label>\r\n            <input type=\"text\" class=\"p-inputtext p-component p-element w-full bg-gray-50\" id=\"username\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-12 mb-0\">\r\n            <label class=\"text-base font-medium text-gray-600\">E-mail</label>\r\n            <input type=\"email\" class=\"p-inputtext p-component p-element w-full bg-gray-50\" id=\"username\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-12 mb-0\">\r\n            <label class=\"text-base font-medium text-gray-600\">Address</label>\r\n            <input type=\"text\" class=\"p-inputtext p-component p-element w-full bg-gray-50\" id=\"username\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-6 mb-0\">\r\n            <label class=\"text-base font-medium text-gray-600\">Country</label>\r\n            <input type=\"text\" class=\"p-inputtext p-component p-element w-full bg-gray-50\" id=\"username\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-6 mb-0\">\r\n            <label class=\"text-base font-medium text-gray-600\">State</label>\r\n            <input type=\"text\" class=\"p-inputtext p-component p-element w-full bg-gray-50\" id=\"username\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-6 mb-0\">\r\n            <label class=\"text-base font-medium text-gray-600\">City</label>\r\n            <input type=\"text\" class=\"p-inputtext p-component p-element w-full bg-gray-50\" id=\"username\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-6 mb-0\">\r\n            <label class=\"text-base font-medium text-gray-600\">Zip Code</label>\r\n            <input type=\"text\" class=\"p-inputtext p-component p-element w-full bg-gray-50\" id=\"username\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-6 mb-0\">\r\n            <label class=\"text-base font-medium text-gray-600\">Password</label>\r\n            <div class=\"form-group relative\">\r\n              <input type=\"password\" class=\"p-inputtext p-component p-element w-full bg-gray-50\" />\r\n              <button type=\"button\"\r\n                class=\"pass-show-btn absolute top-0 bottom-0 m-auto p-0 border-none bg-white-alpha-10 text-gray-500 cursor-pointer\"><span\r\n                  class=\"material-symbols-rounded\">visibility</span></button>\r\n            </div>\r\n          </div>\r\n          <div class=\"field col-12 md:col-6 mb-0\">\r\n            <label class=\"text-base font-medium text-gray-600\">Retype Password</label>\r\n            <div class=\"form-group relative\">\r\n              <input type=\"password\" class=\"p-inputtext p-component p-element w-full bg-gray-50\" />\r\n              <button type=\"button\"\r\n                class=\"pass-show-btn absolute top-0 bottom-0 m-auto p-0 border-none bg-white-alpha-10 text-gray-500 cursor-pointer\"><span\r\n                  class=\"material-symbols-rounded\">visibility</span></button>\r\n            </div>\r\n          </div>\r\n          <div class=\"field col-12 md:col-12 mb-0\">\r\n            <label class=\"text-base font-medium text-gray-600\">Security Question 1</label>\r\n            <p-dropdown [options]=\"cities\" [(ngModel)]=\"selectedCity\" optionLabel=\"name\" [showClear]=\"true\" [styleClass]=\"'p-inputtext p-component p-element w-full bg-gray-50'\"></p-dropdown>\r\n          </div>\r\n          <div class=\"field col-12 md:col-12 mb-0\">\r\n            <label class=\"text-base font-medium text-gray-600\">Answer</label>\r\n            <input type=\"text\" class=\"p-inputtext p-component p-element w-full bg-gray-50\" id=\"username\" />\r\n          </div>\r\n          <div class=\"field col-12 md:col-12 mb-0\">\r\n            <label class=\"text-base font-medium text-gray-600\">Security Question 2</label>\r\n            <p-dropdown [options]=\"cities\" [(ngModel)]=\"selectedCity\" optionLabel=\"name\" [showClear]=\"true\" [styleClass]=\"'p-inputtext p-component p-element w-full bg-gray-50'\"></p-dropdown>\r\n          </div>\r\n          <div class=\"field col-12 md:col-12 mb-0\">\r\n            <label class=\"text-base font-medium text-gray-600\">Answer</label>\r\n            <input type=\"text\" class=\"p-inputtext p-component p-element w-full bg-gray-50\" id=\"username\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"form-footer mt-4\">\r\n          <div class=\"p-fluid p-formgrid grid\">\r\n            <div class=\"field col-12 md:col-6 mb-0\">\r\n              <button type=\"button\" [routerLink]=\"['/login']\"\r\n                class=\"p-button-outlined p-button-rounded p-button p-component w-full h-3-3rem justify-content-center gap-3 font-semibold border-2 border-black-alpha-20\">\r\n                Go Back</button>\r\n            </div>\r\n            <div class=\"field col-12 md:col-6 mb-0\">\r\n              <button type=\"button\"\r\n                class=\"p-button-rounded p-button p-component w-full justify-content-center h-3-3rem font-semibold\">\r\n                Login</button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </form>\r\n    </div>\r\n    <div class=\"copyright-sec flex flex-column position-relative\">\r\n      <p class=\"m-0 flex justify-content-center text-base font-medium text-gray-900\">© 2024 Consolidated Hospitality\r\n        Supplies</p>\r\n      <ul class=\"p-0 flex position-relative align-items-center justify-content-center list-none gap-3\">\r\n        <li><a target=\"_blank\" class=\"flex justify-content-center text-base font-medium text-primary underline\">Terms &\r\n            Conditions</a></li>\r\n        <li><a target=\"_blank\" class=\"flex justify-content-center text-base font-medium text-primary underline\">Privacy\r\n            Policy</a></li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n</section>"], "mappings": ";;;;;AAYA,OAAM,MAAOA,eAAe;EAK1BC,YACSC,OAAe;IAAf,KAAAA,OAAO,GAAPA,OAAO;IAEd,IAAI,CAACC,MAAM,GAAG,CACZ;MAAEC,IAAI,EAAE,4BAA4B;MAAEC,IAAI,EAAE;IAAI,CAAE,EAClD;MAAED,IAAI,EAAE,+CAA+C;MAAEC,IAAI,EAAE;IAAI,CAAE,EACrE;MAAED,IAAI,EAAE,6BAA6B;MAAEC,IAAI,EAAE;IAAK,CAAE,EACpD;MAAED,IAAI,EAAE,6BAA6B;MAAEC,IAAI,EAAE;IAAK,CAAE,EACpD;MAAED,IAAI,EAAE,iCAAiC;MAAEC,IAAI,EAAE;IAAK,CAAE,CACzD;EACH;;;uBAfWL,eAAe,EAAAM,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfT,eAAe;MAAAU,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTeV,EAH3C,CAAAY,cAAA,iBAAuD,aAC+C,aACf,aAC5C,WAAgC;UAAAZ,EAAA,CAAAa,SAAA,aACnC;UAAIb,EAAJ,CAAAc,YAAA,EAAI,EAAM;UAC5Cd,EAAA,CAAAY,cAAA,aAA6G;UAC3GZ,EAAA,CAAAe,MAAA,gCACA;UAEEf,EAFF,CAAAY,cAAA,gBACqI,cACnF;UAAAZ,EAAA,CAAAe,MAAA,aAAK;UAAAf,EAAA,CAAAc,YAAA,EAAO;UAACd,EAAA,CAAAe,MAAA,eAC/D;UAEJf,EAFI,CAAAc,YAAA,EAAS,EACL,EACF;UAGFd,EAFJ,CAAAY,cAAA,cAA0F,gBACvC,cAC8B;UAAAZ,EAAA,CAAAe,MAAA,qBAC7E;UAAAf,EAAA,CAAAc,YAAA,EAAK;UACLd,EAAA,CAAAY,cAAA,aAAgF;UAAAZ,EAAA,CAAAe,MAAA,sEACnD;UAAAf,EAAA,CAAAc,YAAA,EAAI;UAG7Bd,EAFJ,CAAAY,cAAA,eAAqC,eACK,iBACa;UAAAZ,EAAA,CAAAe,MAAA,kBAAU;UAAAf,EAAA,CAAAc,YAAA,EAAQ;UACrEd,EAAA,CAAAa,SAAA,iBAA+F;UACjGb,EAAA,CAAAc,YAAA,EAAM;UAEJd,EADF,CAAAY,cAAA,eAAwC,iBACa;UAAAZ,EAAA,CAAAe,MAAA,iBAAS;UAAAf,EAAA,CAAAc,YAAA,EAAQ;UACpEd,EAAA,CAAAa,SAAA,iBAA+F;UACjGb,EAAA,CAAAc,YAAA,EAAM;UAEJd,EADF,CAAAY,cAAA,eAAwC,iBACa;UAAAZ,EAAA,CAAAe,MAAA,oBAAY;UAAAf,EAAA,CAAAc,YAAA,EAAQ;UACvEd,EAAA,CAAAa,SAAA,iBAA+F;UACjGb,EAAA,CAAAc,YAAA,EAAM;UAEJd,EADF,CAAAY,cAAA,eAAwC,iBACa;UAAAZ,EAAA,CAAAe,MAAA,iBAAS;UAAAf,EAAA,CAAAc,YAAA,EAAQ;UACpEd,EAAA,CAAAa,SAAA,iBAA+F;UACjGb,EAAA,CAAAc,YAAA,EAAM;UAEJd,EADF,CAAAY,cAAA,eAAyC,iBACY;UAAAZ,EAAA,CAAAe,MAAA,cAAM;UAAAf,EAAA,CAAAc,YAAA,EAAQ;UACjEd,EAAA,CAAAa,SAAA,iBAAgG;UAClGb,EAAA,CAAAc,YAAA,EAAM;UAEJd,EADF,CAAAY,cAAA,eAAyC,iBACY;UAAAZ,EAAA,CAAAe,MAAA,eAAO;UAAAf,EAAA,CAAAc,YAAA,EAAQ;UAClEd,EAAA,CAAAa,SAAA,iBAA+F;UACjGb,EAAA,CAAAc,YAAA,EAAM;UAEJd,EADF,CAAAY,cAAA,eAAwC,iBACa;UAAAZ,EAAA,CAAAe,MAAA,eAAO;UAAAf,EAAA,CAAAc,YAAA,EAAQ;UAClEd,EAAA,CAAAa,SAAA,iBAA+F;UACjGb,EAAA,CAAAc,YAAA,EAAM;UAEJd,EADF,CAAAY,cAAA,eAAwC,iBACa;UAAAZ,EAAA,CAAAe,MAAA,aAAK;UAAAf,EAAA,CAAAc,YAAA,EAAQ;UAChEd,EAAA,CAAAa,SAAA,iBAA+F;UACjGb,EAAA,CAAAc,YAAA,EAAM;UAEJd,EADF,CAAAY,cAAA,eAAwC,iBACa;UAAAZ,EAAA,CAAAe,MAAA,YAAI;UAAAf,EAAA,CAAAc,YAAA,EAAQ;UAC/Dd,EAAA,CAAAa,SAAA,iBAA+F;UACjGb,EAAA,CAAAc,YAAA,EAAM;UAEJd,EADF,CAAAY,cAAA,eAAwC,iBACa;UAAAZ,EAAA,CAAAe,MAAA,gBAAQ;UAAAf,EAAA,CAAAc,YAAA,EAAQ;UACnEd,EAAA,CAAAa,SAAA,iBAA+F;UACjGb,EAAA,CAAAc,YAAA,EAAM;UAEJd,EADF,CAAAY,cAAA,eAAwC,iBACa;UAAAZ,EAAA,CAAAe,MAAA,gBAAQ;UAAAf,EAAA,CAAAc,YAAA,EAAQ;UACnEd,EAAA,CAAAY,cAAA,eAAiC;UAC/BZ,EAAA,CAAAa,SAAA,iBAAqF;UAEiCb,EADtH,CAAAY,cAAA,kBACsH,gBACjF;UAAAZ,EAAA,CAAAe,MAAA,kBAAU;UAEnDf,EAFmD,CAAAc,YAAA,EAAO,EAAS,EAC3D,EACF;UAEJd,EADF,CAAAY,cAAA,eAAwC,iBACa;UAAAZ,EAAA,CAAAe,MAAA,uBAAe;UAAAf,EAAA,CAAAc,YAAA,EAAQ;UAC1Ed,EAAA,CAAAY,cAAA,eAAiC;UAC/BZ,EAAA,CAAAa,SAAA,iBAAqF;UAEiCb,EADtH,CAAAY,cAAA,kBACsH,gBACjF;UAAAZ,EAAA,CAAAe,MAAA,kBAAU;UAEnDf,EAFmD,CAAAc,YAAA,EAAO,EAAS,EAC3D,EACF;UAEJd,EADF,CAAAY,cAAA,eAAyC,iBACY;UAAAZ,EAAA,CAAAe,MAAA,2BAAmB;UAAAf,EAAA,CAAAc,YAAA,EAAQ;UAC9Ed,EAAA,CAAAY,cAAA,sBAAqK;UAAtIZ,EAAA,CAAAgB,gBAAA,2BAAAC,8DAAAC,MAAA;YAAAlB,EAAA,CAAAmB,kBAAA,CAAAR,GAAA,CAAAS,YAAA,EAAAF,MAAA,MAAAP,GAAA,CAAAS,YAAA,GAAAF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA0B;UAC3DlB,EADuK,CAAAc,YAAA,EAAa,EAC9K;UAEJd,EADF,CAAAY,cAAA,eAAyC,iBACY;UAAAZ,EAAA,CAAAe,MAAA,cAAM;UAAAf,EAAA,CAAAc,YAAA,EAAQ;UACjEd,EAAA,CAAAa,SAAA,iBAA+F;UACjGb,EAAA,CAAAc,YAAA,EAAM;UAEJd,EADF,CAAAY,cAAA,eAAyC,iBACY;UAAAZ,EAAA,CAAAe,MAAA,2BAAmB;UAAAf,EAAA,CAAAc,YAAA,EAAQ;UAC9Ed,EAAA,CAAAY,cAAA,sBAAqK;UAAtIZ,EAAA,CAAAgB,gBAAA,2BAAAK,8DAAAH,MAAA;YAAAlB,EAAA,CAAAmB,kBAAA,CAAAR,GAAA,CAAAS,YAAA,EAAAF,MAAA,MAAAP,GAAA,CAAAS,YAAA,GAAAF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA0B;UAC3DlB,EADuK,CAAAc,YAAA,EAAa,EAC9K;UAEJd,EADF,CAAAY,cAAA,eAAyC,iBACY;UAAAZ,EAAA,CAAAe,MAAA,cAAM;UAAAf,EAAA,CAAAc,YAAA,EAAQ;UACjEd,EAAA,CAAAa,SAAA,iBAA+F;UAEnGb,EADE,CAAAc,YAAA,EAAM,EACF;UAIAd,EAHN,CAAAY,cAAA,eAA8B,eACS,eACK,kBAEsH;UAC1JZ,EAAA,CAAAe,MAAA,gBAAO;UACXf,EADW,CAAAc,YAAA,EAAS,EACd;UAEJd,EADF,CAAAY,cAAA,eAAwC,kBAE+D;UACnGZ,EAAA,CAAAe,MAAA,cAAK;UAKjBf,EALiB,CAAAc,YAAA,EAAS,EACZ,EACF,EACF,EACD,EACH;UAEJd,EADF,CAAAY,cAAA,eAA8D,cACmB;UAAAZ,EAAA,CAAAe,MAAA,sDACrE;UAAAf,EAAA,CAAAc,YAAA,EAAI;UAERd,EADN,CAAAY,cAAA,eAAiG,WAC3F,cAAoG;UAAAZ,EAAA,CAAAe,MAAA,2BAC1F;UAAIf,EAAJ,CAAAc,YAAA,EAAI,EAAK;UACnBd,EAAJ,CAAAY,cAAA,WAAI,cAAoG;UAAAZ,EAAA,CAAAe,MAAA,uBAC9F;UAIlBf,EAJkB,CAAAc,YAAA,EAAI,EAAK,EAChB,EACD,EACF,EACE;;;UAnHoBd,EAAA,CAAAsB,SAAA,GAAyB;UAAzBtB,EAAA,CAAAuB,UAAA,eAAAvB,EAAA,CAAAwB,eAAA,KAAAC,GAAA,EAAyB;UAyE/BzB,EAAA,CAAAsB,SAAA,IAAkB;UAAlBtB,EAAA,CAAAuB,UAAA,YAAAZ,GAAA,CAAAd,MAAA,CAAkB;UAACG,EAAA,CAAA0B,gBAAA,YAAAf,GAAA,CAAAS,YAAA,CAA0B;UAAuCpB,EAAnB,CAAAuB,UAAA,mBAAkB,qEAAqE;UAQxJvB,EAAA,CAAAsB,SAAA,GAAkB;UAAlBtB,EAAA,CAAAuB,UAAA,YAAAZ,GAAA,CAAAd,MAAA,CAAkB;UAACG,EAAA,CAAA0B,gBAAA,YAAAf,GAAA,CAAAS,YAAA,CAA0B;UAAuCpB,EAAnB,CAAAuB,UAAA,mBAAkB,qEAAqE;UAU5IvB,EAAA,CAAAsB,SAAA,GAAyB;UAAzBtB,EAAA,CAAAuB,UAAA,eAAAvB,EAAA,CAAAwB,eAAA,KAAAC,GAAA,EAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}