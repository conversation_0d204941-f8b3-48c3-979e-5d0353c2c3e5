import { Component } from '@angular/core';
import { ContentService } from '../core/services/content-vendor.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent {

  content!: any;
  bannerData: any = null;
  servicesData: any[] = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private CMSservice: ContentService
  ) { }

  ngOnInit(): void {
    this.content = this.route.snapshot.data['content'];
    console.log('Home Content:', this.content);

    // Extract banner
    const bannerComponent = this.CMSservice.getDataByComponentName(this.content.body, "public-sector.banner");
    if (bannerComponent?.length) {
      this.bannerData = bannerComponent[0];
    }

    // Extract services
    const servicesComponents = this.CMSservice.getDataByComponentName(this.content.body, "public-sector.services");
    if (servicesComponents?.length) {
      this.servicesData = servicesComponents;
    }
  }

  // Method to navigate to appointment page with service ID
  bookAppointment(serviceIndex: number) {
    // Use the service index as ID, or you could use service.id if available
    const serviceId = serviceIndex + 1; // Adding 1 to make it 1-based instead of 0-based
    this.router.navigate(['/ps/appointment', serviceId]);
  }

}
