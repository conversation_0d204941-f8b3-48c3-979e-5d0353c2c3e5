{"ast": null, "code": "import { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ForgotPasswordService {\n  constructor(http) {\n    this.http = http;\n  }\n  forgotPassword(data) {\n    return this.http.post(CMS_APIContstant.RESET_PASSWORD_REQUEST, data);\n  }\n  static {\n    this.ɵfac = function ForgotPasswordService_Factory(t) {\n      return new (t || ForgotPasswordService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ForgotPasswordService,\n      factory: ForgotPasswordService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["CMS_APIContstant", "ForgotPasswordService", "constructor", "http", "forgotPassword", "data", "post", "RESET_PASSWORD_REQUEST", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\session\\forgot-password\\forgot-password.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ForgotPasswordService {\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  forgotPassword(data: any) {\r\n    return this.http.post(CMS_APIContstant.RESET_PASSWORD_REQUEST, data);\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,qBAAqB;EAEhCC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAgB;EAExCC,cAAcA,CAACC,IAAS;IACtB,OAAO,IAAI,CAACF,IAAI,CAACG,IAAI,CAACN,gBAAgB,CAACO,sBAAsB,EAAEF,IAAI,CAAC;EACtE;;;uBANWJ,qBAAqB,EAAAO,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAArBV,qBAAqB;MAAAW,OAAA,EAArBX,qBAAqB,CAAAY,IAAA;MAAAC,UAAA,EAFpB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}