import { Component, Input, OnInit } from '@angular/core';
import { ContentService } from '../../../core/services/content-vendor.service';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss'
})
export class HeaderComponent implements OnInit {
  @Input() commonContent: any;

  logo: string = '';
  menuItems: any[] = [];

  constructor(private CMSservice: ContentService) { }

  ngOnInit(): void {
    if (this.commonContent) {
      // Extract logo
      const logoComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, "public-sector.logo");
      if (logoComponent?.length) {
        this.logo = logoComponent[0].Logo?.url || '';
      }

      // Extract menu
      const menuComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, "public-sector.menu");
      if (menuComponent?.length) {
        this.menuItems = menuComponent[0].Menu_Item || [];
      }
    }
  }
}
