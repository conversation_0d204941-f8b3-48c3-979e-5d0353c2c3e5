{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { CustomerComponent } from './customer.component';\nimport { CustomerDetailsComponent } from './customer-details/customer-details.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: CustomerComponent\n}, {\n  path: ':id',\n  component: CustomerDetailsComponent\n}];\nexport class CustomerRoutingModule {\n  static {\n    this.ɵfac = function CustomerRoutingModule_Factory(t) {\n      return new (t || CustomerRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CustomerRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CustomerRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "CustomerComponent", "CustomerDetailsComponent", "routes", "path", "component", "CustomerRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\backoffice\\customer\\customer-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { CustomerComponent } from './customer.component';\r\nimport { CustomerDetailsComponent } from './customer-details/customer-details.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: CustomerComponent },\r\n  { path: ':id', component: CustomerDetailsComponent }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class CustomerRoutingModule {}\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,wBAAwB,QAAQ,+CAA+C;;;AAExF,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEJ;AAAiB,CAAE,EAC1C;EAAEG,IAAI,EAAE,KAAK;EAAEC,SAAS,EAAEH;AAAwB,CAAE,CACrD;AAMD,OAAM,MAAOI,qBAAqB;;;uBAArBA,qBAAqB;IAAA;EAAA;;;YAArBA;IAAqB;EAAA;;;gBAHtBN,YAAY,CAACO,QAAQ,CAACJ,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXM,qBAAqB;IAAAE,OAAA,GAAAC,EAAA,CAAAT,YAAA;IAAAU,OAAA,GAFtBV,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}