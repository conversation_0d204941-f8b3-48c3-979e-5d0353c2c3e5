import { Injectable } from "@angular/core";
import {
  ActivatedRouteSnapshot,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from "@angular/router";
import { AuthService } from "./auth.service";

@Injectable({
  providedIn: "root",
})
export class AuthGuard {
  constructor(
    private router: Router,
    private auth: AuthService,
  ) { }

  async canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<boolean | UrlTree | any> {
    return await this.authenticate(route, state.url);
  }

  async canActivateChild(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<boolean | UrlTree | any> {
    return await this.authenticate(childRoute, state.url);
  }

  private async authenticate(
    route: ActivatedRouteSnapshot,
    url: string,
  ): Promise<boolean | UrlTree | any> {
    if (this.auth.isLoggedIn) {
      // If User already login and try to access auth pages.
      if (url.startsWith("/auth")) {
        return this.router.parseUrl("/store");
      }
      // const routeData: any = route?.data || null;
      // const permission: any = routeData?.permission || null;
      // let permissions = this.auth.getPermissions;
      // if (!permissions.length) {
      //   permissions = await this.auth.getUserPermissions();
      //   if (!permissions.length) {
      //     return this.router.parseUrl("/store");
      //   }
      // }
      // if (permission !== null && !permissions.includes(permission)) {
      //   return this.router.parseUrl("/store");
      // }

      return true;
    } else {
      if (!url.startsWith("/auth")) {
        return this.router.parseUrl("/auth/login");
      }
    }

    return true;
  }

}
