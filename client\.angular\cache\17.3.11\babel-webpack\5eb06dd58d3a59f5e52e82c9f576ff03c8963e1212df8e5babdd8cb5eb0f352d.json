{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../shared/components/header/header.component\";\nimport * as i4 from \"../../shared/components/footer/footer.component\";\nexport class HomeLayoutComponent {\n  constructor(primengConfig, renderer, route) {\n    this.primengConfig = primengConfig;\n    this.renderer = renderer;\n    this.route = route;\n  }\n  ngOnInit() {\n    // Get common content from route data\n    this.commonContent = this.route.snapshot.data['commonContent'];\n    console.log('Common Content:', this.commonContent);\n    // Inject theme \n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\n    const link = this.renderer.createElement('link');\n    this.renderer.setAttribute(link, 'id', 'theme-link');\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\n    this.renderer.setAttribute(link, 'type', 'text/css');\n    this.renderer.setAttribute(link, 'href', href);\n    // Append the link tag to the head of the document\n    this.renderer.appendChild(document.head, link);\n    this.primengConfig.ripple = true; //enables core ripple functionality\n  }\n  ngOnDestroy() {\n    // Find and remove the link tag when the component is destroyed\n    const link = document.getElementById('theme-link');\n    if (link) {\n      link.remove();\n    }\n  }\n  static {\n    this.ɵfac = function HomeLayoutComponent_Factory(t) {\n      return new (t || HomeLayoutComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeLayoutComponent,\n      selectors: [[\"app-home-layout\"]],\n      decls: 4,\n      vars: 1,\n      consts: [[3, \"commonContent\"], [1, \"home-layout-content\"]],\n      template: function HomeLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-header\", 0);\n          i0.ɵɵelementStart(1, \"main\", 1);\n          i0.ɵɵelement(2, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"app-footer\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"commonContent\", ctx.commonContent);\n        }\n      },\n      dependencies: [i2.RouterOutlet, i3.HeaderComponent, i4.FooterComponent],\n      styles: [\".max-w-1200[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n}\\n\\n.secondary-color[_ngcontent-%COMP%] {\\n  color: #030f5e !important;\\n}\\n\\n.secondary-bg-color[_ngcontent-%COMP%] {\\n  background: #030f5e !important;\\n}\\n\\n.font-extrabold[_ngcontent-%COMP%] {\\n  font-weight: 900 !important;\\n}\\n\\n.d-grid[_ngcontent-%COMP%] {\\n  display: grid !important;\\n}\\n\\n.object-fit-contain[_ngcontent-%COMP%] {\\n  object-fit: contain;\\n}\\n\\n.object-fit-cover[_ngcontent-%COMP%] {\\n  object-fit: cover;\\n}\\n\\n.h-3-3rem[_ngcontent-%COMP%] {\\n  height: 3.3rem !important;\\n}\\n\\n.bg-blue-75[_ngcontent-%COMP%] {\\n  background: rgb(227, 243, 255) !important;\\n}\\n\\n.line[_ngcontent-%COMP%]::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.2392156863) !important;\\n  width: 100%;\\n}\\n.line[_ngcontent-%COMP%]::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgb(213, 200, 186) !important;\\n  width: 64px;\\n}\\n\\n.line-2[_ngcontent-%COMP%]::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgba(0, 0, 0, 0.07) !important;\\n  width: 100%;\\n}\\n.line-2[_ngcontent-%COMP%]::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgb(213, 200, 186) !important;\\n  width: 64px;\\n}\\n\\n[_nghost-%COMP%]     .max-w-1200 {\\n  max-width: 1200px;\\n}\\n[_nghost-%COMP%]     .secondary-color {\\n  color: #030f5e !important;\\n}\\n[_nghost-%COMP%]     .secondary-bg-color {\\n  background: #030f5e !important;\\n}\\n[_nghost-%COMP%]     .font-extrabold {\\n  font-weight: 900 !important;\\n}\\n[_nghost-%COMP%]     .d-grid {\\n  display: grid !important;\\n}\\n[_nghost-%COMP%]     .object-fit-contain {\\n  object-fit: contain;\\n}\\n[_nghost-%COMP%]     .object-fit-cover {\\n  object-fit: cover;\\n}\\n[_nghost-%COMP%]     .h-3-3rem {\\n  height: 3.3rem !important;\\n}\\n[_nghost-%COMP%]     .bg-blue-75 {\\n  background: rgb(227, 243, 255) !important;\\n}\\n[_nghost-%COMP%]     .line::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.2392156863) !important;\\n  width: 100%;\\n}\\n[_nghost-%COMP%]     .line::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgb(213, 200, 186) !important;\\n  width: 64px;\\n}\\n[_nghost-%COMP%]     .line-2::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgba(0, 0, 0, 0.07) !important;\\n  width: 100%;\\n}\\n[_nghost-%COMP%]     .line-2::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgb(213, 200, 186) !important;\\n  width: 64px;\\n}\\n\\n.home-layout-content[_ngcontent-%COMP%] {\\n  min-height: calc(100vh - 200px);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["HomeLayoutComponent", "constructor", "primengConfig", "renderer", "route", "ngOnInit", "commonContent", "snapshot", "data", "console", "log", "href", "link", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "document", "head", "ripple", "ngOnDestroy", "getElementById", "remove", "i0", "ɵɵdirectiveInject", "i1", "PrimeNGConfig", "Renderer2", "i2", "ActivatedRoute", "selectors", "decls", "vars", "consts", "template", "HomeLayoutComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵproperty"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home-layout\\home-layout.component.ts", "D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home-layout\\home-layout.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, Renderer2 } from '@angular/core';\nimport { ActivatedRoute } from '@angular/router';\nimport { PrimeNGConfig } from 'primeng/api';\n\n@Component({\n  selector: 'app-home-layout',\n  templateUrl: './home-layout.component.html',\n  styleUrl: './home-layout.component.scss'\n})\nexport class HomeLayoutComponent implements OnInit, OnDestroy {\n\n  commonContent!: any;\n\n  constructor(\n    private primengConfig: PrimeNGConfig,\n    private renderer: Renderer2,\n    private route: ActivatedRoute\n  ) { }\n\n  ngOnInit(): void {\n    // Get common content from route data\n    this.commonContent = this.route.snapshot.data['commonContent'];\n    console.log('Common Content:', this.commonContent);\n\n    // Inject theme \n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\n    const link = this.renderer.createElement('link');\n    this.renderer.setAttribute(link, 'id', 'theme-link');\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\n    this.renderer.setAttribute(link, 'type', 'text/css');\n    this.renderer.setAttribute(link, 'href', href);\n\n    // Append the link tag to the head of the document\n    this.renderer.appendChild(document.head, link);\n\n    this.primengConfig.ripple = true; //enables core ripple functionality\n  }\n\n  ngOnDestroy(): void {\n    // Find and remove the link tag when the component is destroyed\n    const link = document.getElementById('theme-link');\n    if (link) {\n      link.remove();\n    }\n  }\n}\n", "<app-header [commonContent]=\"commonContent\"></app-header>\n\n<!-- Main content area where child routes will be rendered -->\n<main class=\"home-layout-content\">\n  <router-outlet></router-outlet>\n</main>\n\n<app-footer></app-footer>\n"], "mappings": ";;;;;AASA,OAAM,MAAOA,mBAAmB;EAI9BC,YACUC,aAA4B,EAC5BC,QAAmB,EACnBC,KAAqB;IAFrB,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,KAAK,GAALA,KAAK;EACX;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,aAAa,GAAG,IAAI,CAACF,KAAK,CAACG,QAAQ,CAACC,IAAI,CAAC,eAAe,CAAC;IAC9DC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACJ,aAAa,CAAC;IAElD;IACA,MAAMK,IAAI,GAAG,wDAAwD;IACrE,MAAMC,IAAI,GAAG,IAAI,CAACT,QAAQ,CAACU,aAAa,CAAC,MAAM,CAAC;IAChD,IAAI,CAACV,QAAQ,CAACW,YAAY,CAACF,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC;IACpD,IAAI,CAACT,QAAQ,CAACW,YAAY,CAACF,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC;IACrD,IAAI,CAACT,QAAQ,CAACW,YAAY,CAACF,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC;IACpD,IAAI,CAACT,QAAQ,CAACW,YAAY,CAACF,IAAI,EAAE,MAAM,EAAED,IAAI,CAAC;IAE9C;IACA,IAAI,CAACR,QAAQ,CAACY,WAAW,CAACC,QAAQ,CAACC,IAAI,EAAEL,IAAI,CAAC;IAE9C,IAAI,CAACV,aAAa,CAACgB,MAAM,GAAG,IAAI,CAAC,CAAC;EACpC;EAEAC,WAAWA,CAAA;IACT;IACA,MAAMP,IAAI,GAAGI,QAAQ,CAACI,cAAc,CAAC,YAAY,CAAC;IAClD,IAAIR,IAAI,EAAE;MACRA,IAAI,CAACS,MAAM,EAAE;IACf;EACF;;;uBAnCWrB,mBAAmB,EAAAsB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,SAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAnB5B,mBAAmB;MAAA6B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCThCb,EAAA,CAAAe,SAAA,oBAAyD;UAGzDf,EAAA,CAAAgB,cAAA,cAAkC;UAChChB,EAAA,CAAAe,SAAA,oBAA+B;UACjCf,EAAA,CAAAiB,YAAA,EAAO;UAEPjB,EAAA,CAAAe,SAAA,iBAAyB;;;UAPbf,EAAA,CAAAkB,UAAA,kBAAAJ,GAAA,CAAA9B,aAAA,CAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}