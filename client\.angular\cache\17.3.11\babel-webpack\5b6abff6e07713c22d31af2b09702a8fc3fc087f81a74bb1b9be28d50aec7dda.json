{"ast": null, "code": "import { effect, signal } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class LayoutService {\n  constructor() {\n    this._config = {\n      ripple: false,\n      menuMode: 'static',\n      colorScheme: 'dark',\n      theme: 'magenta',\n      scale: 14\n    };\n    this.config = signal(this._config);\n    this.state = {\n      staticMenuDesktopInactive: false,\n      overlayMenuActive: false,\n      rightMenuActive: false,\n      configSidebarVisible: false,\n      staticMenuMobileActive: false,\n      menuHoverActive: false,\n      sidebarActive: false,\n      anchored: false\n    };\n    this.configUpdate = new Subject();\n    this.overlayOpen = new Subject();\n    this.configUpdate$ = this.configUpdate.asObservable();\n    this.overlayOpen$ = this.overlayOpen.asObservable();\n    effect(() => {\n      const config = this.config();\n      if (this.updateStyle(config)) {\n        this.changeTheme();\n      }\n      this.changeScale(config.scale);\n      this.onConfigUpdate();\n    });\n  }\n  updateStyle(config) {\n    return config.theme !== this._config.theme || config.colorScheme !== this._config.colorScheme;\n  }\n  changeTheme() {\n    const config = this.config();\n    const themeLink = document.getElementById('theme-link');\n    const themeLinkHref = themeLink.getAttribute('href');\n    const newHref = themeLinkHref.split('/').map(el => el == this._config.theme ? el = config.theme : el == `theme-${this._config.colorScheme}` ? el = `theme-${config.colorScheme}` : el).join('/');\n    this.replaceThemeLink(newHref);\n  }\n  replaceThemeLink(href) {\n    const id = 'theme-link';\n    let themeLink = document.getElementById(id);\n    const cloneLinkElement = themeLink.cloneNode(true);\n    cloneLinkElement.setAttribute('href', href);\n    cloneLinkElement.setAttribute('id', id + '-clone');\n    themeLink.parentNode.insertBefore(cloneLinkElement, themeLink.nextSibling);\n    cloneLinkElement.addEventListener('load', () => {\n      themeLink.remove();\n      cloneLinkElement.setAttribute('id', id);\n    });\n  }\n  changeScale(value) {\n    document.documentElement.style.fontSize = `${value}px`;\n  }\n  onMenuToggle() {\n    if (this.isOverlay()) {\n      this.state.overlayMenuActive = !this.state.overlayMenuActive;\n      if (this.state.overlayMenuActive) {\n        this.overlayOpen.next(null);\n      }\n    }\n    if (this.isDesktop()) {\n      this.state.staticMenuDesktopInactive = !this.state.staticMenuDesktopInactive;\n    } else {\n      this.state.staticMenuMobileActive = !this.state.staticMenuMobileActive;\n      if (this.state.staticMenuMobileActive) {\n        this.overlayOpen.next(null);\n      }\n    }\n  }\n  onOverlaySubmenuOpen() {\n    this.overlayOpen.next(null);\n  }\n  showConfigSidebar() {\n    this.state.configSidebarVisible = true;\n  }\n  showSidebar() {\n    this.state.rightMenuActive = true;\n  }\n  isOverlay() {\n    return this.config().menuMode === 'overlay';\n  }\n  isDesktop() {\n    return window.innerWidth > 991;\n  }\n  isSlim() {\n    return this.config().menuMode === 'slim';\n  }\n  isSlimPlus() {\n    return this.config().menuMode === 'slim-plus';\n  }\n  isHorizontal() {\n    return this.config().menuMode === 'horizontal';\n  }\n  isMobile() {\n    return !this.isDesktop();\n  }\n  onConfigUpdate() {\n    this._config = {\n      ...this.config()\n    };\n    this.configUpdate.next(this.config());\n  }\n  static {\n    this.ɵfac = function LayoutService_Factory(t) {\n      return new (t || LayoutService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LayoutService,\n      factory: LayoutService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["effect", "signal", "Subject", "LayoutService", "constructor", "_config", "ripple", "menuMode", "colorScheme", "theme", "scale", "config", "state", "staticMenuDesktopInactive", "overlayMenuActive", "rightMenuActive", "configSidebarVisible", "staticMenuMobileActive", "menuHoverActive", "sidebarActive", "anchored", "configUpdate", "overlayOpen", "configUpdate$", "asObservable", "overlayOpen$", "updateStyle", "changeTheme", "changeScale", "onConfigUpdate", "themeLink", "document", "getElementById", "themeLinkHref", "getAttribute", "newHref", "split", "map", "el", "join", "replaceThemeLink", "href", "id", "cloneLinkElement", "cloneNode", "setAttribute", "parentNode", "insertBefore", "nextS<PERSON>ling", "addEventListener", "remove", "value", "documentElement", "style", "fontSize", "onMenuToggle", "isOverlay", "next", "isDesktop", "onOverlaySubmenuOpen", "showConfigSidebar", "showSidebar", "window", "innerWidth", "isSlim", "isSlimPlus", "isHorizontal", "isMobile", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\backoffice\\layout\\service\\app.layout.service.ts"], "sourcesContent": ["import { Injectable, effect, signal } from '@angular/core';\r\nimport { Subject } from 'rxjs';\r\n\r\nexport type MenuMode =\r\n    | 'static'\r\n    | 'overlay'\r\n    | 'horizontal'\r\n    | 'slim'\r\n    | 'slim-plus'\r\n    | 'reveal'\r\n    | 'drawer';\r\n\r\nexport type ColorScheme = 'light' | 'dark';\r\n\r\nexport interface AppConfig {\r\n    colorScheme: ColorScheme;\r\n    theme: string;\r\n    ripple: boolean;\r\n    menuMode: MenuMode;\r\n    scale: number;\r\n}\r\n\r\ninterface LayoutState {\r\n    staticMenuDesktopInactive: boolean;\r\n    overlayMenuActive: boolean;\r\n    rightMenuActive: boolean;\r\n    configSidebarVisible: boolean;\r\n    staticMenuMobileActive: boolean;\r\n    menuHoverActive: boolean;\r\n    sidebarActive: boolean;\r\n    anchored: boolean;\r\n}\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class LayoutService {\r\n    _config: AppConfig = {\r\n        ripple: false,\r\n        menuMode: 'static',\r\n        colorScheme: 'dark',\r\n        theme: 'magenta',\r\n        scale: 14,\r\n    };\r\n\r\n    config = signal<AppConfig>(this._config);\r\n\r\n    state: LayoutState = {\r\n        staticMenuDesktopInactive: false,\r\n        overlayMenuActive: false,\r\n        rightMenuActive: false,\r\n        configSidebarVisible: false,\r\n        staticMenuMobileActive: false,\r\n        menuHoverActive: false,\r\n        sidebarActive: false,\r\n        anchored: false,\r\n    };\r\n\r\n    private configUpdate = new Subject<AppConfig>();\r\n\r\n    private overlayOpen = new Subject<any>();\r\n\r\n    configUpdate$ = this.configUpdate.asObservable();\r\n\r\n    overlayOpen$ = this.overlayOpen.asObservable();\r\n    constructor() {\r\n        effect(() => {\r\n            const config = this.config();\r\n            if (this.updateStyle(config)) {\r\n                this.changeTheme();\r\n            }\r\n            this.changeScale(config.scale);\r\n            this.onConfigUpdate();\r\n        });\r\n    }\r\n\r\n    updateStyle(config: AppConfig) {\r\n        return (\r\n            config.theme !== this._config.theme ||\r\n            config.colorScheme !== this._config.colorScheme\r\n        );\r\n    }\r\n\r\n    changeTheme() {\r\n        const config = this.config();\r\n        const themeLink = <HTMLLinkElement>(\r\n            document.getElementById('theme-link')\r\n        );\r\n        const themeLinkHref = themeLink.getAttribute('href')!;\r\n        const newHref = themeLinkHref\r\n            .split('/')\r\n            .map((el) =>\r\n                el == this._config.theme\r\n                    ? (el = config.theme)\r\n                    : el == `theme-${this._config.colorScheme}`\r\n                    ? (el = `theme-${config.colorScheme}`)\r\n                    : el\r\n            )\r\n            .join('/');\r\n\r\n        this.replaceThemeLink(newHref);\r\n    }\r\n\r\n    replaceThemeLink(href: string) {\r\n        const id = 'theme-link';\r\n        let themeLink = <HTMLLinkElement>document.getElementById(id);\r\n        const cloneLinkElement = <HTMLLinkElement>themeLink.cloneNode(true);\r\n\r\n        cloneLinkElement.setAttribute('href', href);\r\n        cloneLinkElement.setAttribute('id', id + '-clone');\r\n\r\n        themeLink.parentNode!.insertBefore(\r\n            cloneLinkElement,\r\n            themeLink.nextSibling\r\n        );\r\n        cloneLinkElement.addEventListener('load', () => {\r\n            themeLink.remove();\r\n            cloneLinkElement.setAttribute('id', id);\r\n        });\r\n    }\r\n\r\n    changeScale(value: number) {\r\n        document.documentElement.style.fontSize = `${value}px`;\r\n    }\r\n\r\n    onMenuToggle() {\r\n        if (this.isOverlay()) {\r\n            this.state.overlayMenuActive = !this.state.overlayMenuActive;\r\n\r\n            if (this.state.overlayMenuActive) {\r\n                this.overlayOpen.next(null);\r\n            }\r\n        }\r\n\r\n        if (this.isDesktop()) {\r\n            this.state.staticMenuDesktopInactive =\r\n                !this.state.staticMenuDesktopInactive;\r\n        } else {\r\n            this.state.staticMenuMobileActive =\r\n                !this.state.staticMenuMobileActive;\r\n\r\n            if (this.state.staticMenuMobileActive) {\r\n                this.overlayOpen.next(null);\r\n            }\r\n        }\r\n    }\r\n\r\n    onOverlaySubmenuOpen() {\r\n        this.overlayOpen.next(null);\r\n    }\r\n\r\n    showConfigSidebar() {\r\n        this.state.configSidebarVisible = true;\r\n    }\r\n\r\n    showSidebar() {\r\n        this.state.rightMenuActive = true;\r\n    }\r\n\r\n    isOverlay() {\r\n        return this.config().menuMode === 'overlay';\r\n    }\r\n\r\n    isDesktop() {\r\n        return window.innerWidth > 991;\r\n    }\r\n\r\n    isSlim() {\r\n        return this.config().menuMode === 'slim';\r\n    }\r\n\r\n    isSlimPlus() {\r\n        return this.config().menuMode === 'slim-plus';\r\n    }\r\n\r\n    isHorizontal() {\r\n        return this.config().menuMode === 'horizontal';\r\n    }\r\n\r\n    isMobile() {\r\n        return !this.isDesktop();\r\n    }\r\n\r\n    onConfigUpdate() {\r\n        this._config = { ...this.config() };\r\n        this.configUpdate.next(this.config());\r\n    }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,MAAM,EAAEC,MAAM,QAAQ,eAAe;AAC1D,SAASC,OAAO,QAAQ,MAAM;;AAmC9B,OAAM,MAAOC,aAAa;EA6BtBC,YAAA;IA5BA,KAAAC,OAAO,GAAc;MACjBC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE,MAAM;MACnBC,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAE;KACV;IAED,KAAAC,MAAM,GAAGV,MAAM,CAAY,IAAI,CAACI,OAAO,CAAC;IAExC,KAAAO,KAAK,GAAgB;MACjBC,yBAAyB,EAAE,KAAK;MAChCC,iBAAiB,EAAE,KAAK;MACxBC,eAAe,EAAE,KAAK;MACtBC,oBAAoB,EAAE,KAAK;MAC3BC,sBAAsB,EAAE,KAAK;MAC7BC,eAAe,EAAE,KAAK;MACtBC,aAAa,EAAE,KAAK;MACpBC,QAAQ,EAAE;KACb;IAEO,KAAAC,YAAY,GAAG,IAAInB,OAAO,EAAa;IAEvC,KAAAoB,WAAW,GAAG,IAAIpB,OAAO,EAAO;IAExC,KAAAqB,aAAa,GAAG,IAAI,CAACF,YAAY,CAACG,YAAY,EAAE;IAEhD,KAAAC,YAAY,GAAG,IAAI,CAACH,WAAW,CAACE,YAAY,EAAE;IAE1CxB,MAAM,CAAC,MAAK;MACR,MAAMW,MAAM,GAAG,IAAI,CAACA,MAAM,EAAE;MAC5B,IAAI,IAAI,CAACe,WAAW,CAACf,MAAM,CAAC,EAAE;QAC1B,IAAI,CAACgB,WAAW,EAAE;MACtB;MACA,IAAI,CAACC,WAAW,CAACjB,MAAM,CAACD,KAAK,CAAC;MAC9B,IAAI,CAACmB,cAAc,EAAE;IACzB,CAAC,CAAC;EACN;EAEAH,WAAWA,CAACf,MAAiB;IACzB,OACIA,MAAM,CAACF,KAAK,KAAK,IAAI,CAACJ,OAAO,CAACI,KAAK,IACnCE,MAAM,CAACH,WAAW,KAAK,IAAI,CAACH,OAAO,CAACG,WAAW;EAEvD;EAEAmB,WAAWA,CAAA;IACP,MAAMhB,MAAM,GAAG,IAAI,CAACA,MAAM,EAAE;IAC5B,MAAMmB,SAAS,GACXC,QAAQ,CAACC,cAAc,CAAC,YAAY,CACvC;IACD,MAAMC,aAAa,GAAGH,SAAS,CAACI,YAAY,CAAC,MAAM,CAAE;IACrD,MAAMC,OAAO,GAAGF,aAAa,CACxBG,KAAK,CAAC,GAAG,CAAC,CACVC,GAAG,CAAEC,EAAE,IACJA,EAAE,IAAI,IAAI,CAACjC,OAAO,CAACI,KAAK,GACjB6B,EAAE,GAAG3B,MAAM,CAACF,KAAK,GAClB6B,EAAE,IAAI,SAAS,IAAI,CAACjC,OAAO,CAACG,WAAW,EAAE,GACxC8B,EAAE,GAAG,SAAS3B,MAAM,CAACH,WAAW,EAAE,GACnC8B,EAAE,CACX,CACAC,IAAI,CAAC,GAAG,CAAC;IAEd,IAAI,CAACC,gBAAgB,CAACL,OAAO,CAAC;EAClC;EAEAK,gBAAgBA,CAACC,IAAY;IACzB,MAAMC,EAAE,GAAG,YAAY;IACvB,IAAIZ,SAAS,GAAoBC,QAAQ,CAACC,cAAc,CAACU,EAAE,CAAC;IAC5D,MAAMC,gBAAgB,GAAoBb,SAAS,CAACc,SAAS,CAAC,IAAI,CAAC;IAEnED,gBAAgB,CAACE,YAAY,CAAC,MAAM,EAAEJ,IAAI,CAAC;IAC3CE,gBAAgB,CAACE,YAAY,CAAC,IAAI,EAAEH,EAAE,GAAG,QAAQ,CAAC;IAElDZ,SAAS,CAACgB,UAAW,CAACC,YAAY,CAC9BJ,gBAAgB,EAChBb,SAAS,CAACkB,WAAW,CACxB;IACDL,gBAAgB,CAACM,gBAAgB,CAAC,MAAM,EAAE,MAAK;MAC3CnB,SAAS,CAACoB,MAAM,EAAE;MAClBP,gBAAgB,CAACE,YAAY,CAAC,IAAI,EAAEH,EAAE,CAAC;IAC3C,CAAC,CAAC;EACN;EAEAd,WAAWA,CAACuB,KAAa;IACrBpB,QAAQ,CAACqB,eAAe,CAACC,KAAK,CAACC,QAAQ,GAAG,GAAGH,KAAK,IAAI;EAC1D;EAEAI,YAAYA,CAAA;IACR,IAAI,IAAI,CAACC,SAAS,EAAE,EAAE;MAClB,IAAI,CAAC5C,KAAK,CAACE,iBAAiB,GAAG,CAAC,IAAI,CAACF,KAAK,CAACE,iBAAiB;MAE5D,IAAI,IAAI,CAACF,KAAK,CAACE,iBAAiB,EAAE;QAC9B,IAAI,CAACQ,WAAW,CAACmC,IAAI,CAAC,IAAI,CAAC;MAC/B;IACJ;IAEA,IAAI,IAAI,CAACC,SAAS,EAAE,EAAE;MAClB,IAAI,CAAC9C,KAAK,CAACC,yBAAyB,GAChC,CAAC,IAAI,CAACD,KAAK,CAACC,yBAAyB;IAC7C,CAAC,MAAM;MACH,IAAI,CAACD,KAAK,CAACK,sBAAsB,GAC7B,CAAC,IAAI,CAACL,KAAK,CAACK,sBAAsB;MAEtC,IAAI,IAAI,CAACL,KAAK,CAACK,sBAAsB,EAAE;QACnC,IAAI,CAACK,WAAW,CAACmC,IAAI,CAAC,IAAI,CAAC;MAC/B;IACJ;EACJ;EAEAE,oBAAoBA,CAAA;IAChB,IAAI,CAACrC,WAAW,CAACmC,IAAI,CAAC,IAAI,CAAC;EAC/B;EAEAG,iBAAiBA,CAAA;IACb,IAAI,CAAChD,KAAK,CAACI,oBAAoB,GAAG,IAAI;EAC1C;EAEA6C,WAAWA,CAAA;IACP,IAAI,CAACjD,KAAK,CAACG,eAAe,GAAG,IAAI;EACrC;EAEAyC,SAASA,CAAA;IACL,OAAO,IAAI,CAAC7C,MAAM,EAAE,CAACJ,QAAQ,KAAK,SAAS;EAC/C;EAEAmD,SAASA,CAAA;IACL,OAAOI,MAAM,CAACC,UAAU,GAAG,GAAG;EAClC;EAEAC,MAAMA,CAAA;IACF,OAAO,IAAI,CAACrD,MAAM,EAAE,CAACJ,QAAQ,KAAK,MAAM;EAC5C;EAEA0D,UAAUA,CAAA;IACN,OAAO,IAAI,CAACtD,MAAM,EAAE,CAACJ,QAAQ,KAAK,WAAW;EACjD;EAEA2D,YAAYA,CAAA;IACR,OAAO,IAAI,CAACvD,MAAM,EAAE,CAACJ,QAAQ,KAAK,YAAY;EAClD;EAEA4D,QAAQA,CAAA;IACJ,OAAO,CAAC,IAAI,CAACT,SAAS,EAAE;EAC5B;EAEA7B,cAAcA,CAAA;IACV,IAAI,CAACxB,OAAO,GAAG;MAAE,GAAG,IAAI,CAACM,MAAM;IAAE,CAAE;IACnC,IAAI,CAACU,YAAY,CAACoC,IAAI,CAAC,IAAI,CAAC9C,MAAM,EAAE,CAAC;EACzC;;;uBAtJSR,aAAa;IAAA;EAAA;;;aAAbA,aAAa;MAAAiE,OAAA,EAAbjE,aAAa,CAAAkE,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}