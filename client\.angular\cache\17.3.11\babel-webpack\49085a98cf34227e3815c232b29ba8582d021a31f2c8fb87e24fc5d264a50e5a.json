{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { HomeComponent } from './home.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: HomeComponent\n}];\nexport class HomeRoutingModule {\n  static {\n    this.ɵfac = function HomeRoutingModule_Factory(t) {\n      return new (t || HomeRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: HomeRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HomeRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "HomeComponent", "routes", "path", "component", "HomeRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { HomeComponent } from './home.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: HomeComponent }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class HomeRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,aAAa,QAAQ,kBAAkB;;;AAEhD,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;AAAa,CAAE,CACvC;AAMD,OAAM,MAAOI,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAHlBL,YAAY,CAACM,QAAQ,CAACJ,MAAM,CAAC,EAC7BF,YAAY;IAAA;EAAA;;;2EAEXK,iBAAiB;IAAAE,OAAA,GAAAC,EAAA,CAAAR,YAAA;IAAAS,OAAA,GAFlBT,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}