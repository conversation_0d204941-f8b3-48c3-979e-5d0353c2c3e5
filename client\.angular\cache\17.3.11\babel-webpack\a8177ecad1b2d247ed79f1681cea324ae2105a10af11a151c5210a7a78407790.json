{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { contentResolver } from './core/content-resolver';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routerOptions = {\n  anchorScrolling: 'enabled',\n  scrollPositionRestoration: 'top',\n  enableTracing: true,\n  useHash: true\n};\nconst routes = [{\n  path: 'ps',\n  // canActivate: [AuthGuard],\n  loadChildren: () => import('./home/<USER>').then(m => m.HomeModule),\n  resolve: {\n    commonContent: contentResolver\n  },\n  data: {\n    slug: 'common'\n  }\n}, {\n  path: \"auth\",\n  loadChildren: () => import(\"./session/session.module\").then(mod => mod.SessionModule),\n  resolve: {\n    commonContent: contentResolver\n  },\n  data: {\n    slug: 'common'\n  }\n}, {\n  path: '',\n  redirectTo: 'ps',\n  pathMatch: 'full'\n}, {\n  path: '**',\n  redirectTo: 'ps'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes, routerOptions), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "contentResolver", "routerOptions", "anchorScrolling", "scrollPositionRestoration", "enableTracing", "useHash", "routes", "path", "loadChildren", "then", "m", "HomeModule", "resolve", "commonContent", "data", "slug", "mod", "SessionModule", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { ExtraOptions, RouterModule, Routes } from '@angular/router';\r\nimport { contentResolver } from './core/content-resolver';\r\n\r\nconst routerOptions: ExtraOptions = {\r\n  anchorScrolling: 'enabled',\r\n  scrollPositionRestoration: 'top',\r\n  enableTracing: true,\r\n  useHash: true\r\n};\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: 'ps',\r\n    // canActivate: [AuthGuard],\r\n    loadChildren: () =>\r\n      import('./home/<USER>').then((m) => m.HomeModule),\r\n    resolve: {\r\n      commonContent: contentResolver,\r\n    },\r\n    data: {\r\n      slug: 'common',\r\n    },\r\n  },\r\n  {\r\n    path: \"auth\",\r\n    loadChildren: () =>\r\n      import(\"./session/session.module\").then((mod) => mod.SessionModule),\r\n    resolve: {\r\n      commonContent: contentResolver,\r\n    },\r\n    data: {\r\n      slug: 'common',\r\n    },\r\n  },\r\n  { path: '', redirectTo: 'ps', pathMatch: 'full' },\r\n  { path: '**', redirectTo: 'ps' },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes, routerOptions)],\r\n  exports: [RouterModule],\r\n})\r\nexport class AppRoutingModule { }\r\n"], "mappings": "AACA,SAAuBA,YAAY,QAAgB,iBAAiB;AACpE,SAASC,eAAe,QAAQ,yBAAyB;;;AAEzD,MAAMC,aAAa,GAAiB;EAClCC,eAAe,EAAE,SAAS;EAC1BC,yBAAyB,EAAE,KAAK;EAChCC,aAAa,EAAE,IAAI;EACnBC,OAAO,EAAE;CACV;AAED,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,IAAI;EACV;EACAC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU,CAAC;EACxDC,OAAO,EAAE;IACPC,aAAa,EAAEb;GAChB;EACDc,IAAI,EAAE;IACJC,IAAI,EAAE;;CAET,EACD;EACER,IAAI,EAAE,MAAM;EACZC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEO,GAAG,IAAKA,GAAG,CAACC,aAAa,CAAC;EACrEL,OAAO,EAAE;IACPC,aAAa,EAAEb;GAChB;EACDc,IAAI,EAAE;IACJC,IAAI,EAAE;;CAET,EACD;EAAER,IAAI,EAAE,EAAE;EAAEW,UAAU,EAAE,IAAI;EAAEC,SAAS,EAAE;AAAM,CAAE,EACjD;EAAEZ,IAAI,EAAE,IAAI;EAAEW,UAAU,EAAE;AAAI,CAAE,CACjC;AAMD,OAAM,MAAOE,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBrB,YAAY,CAACsB,OAAO,CAACf,MAAM,EAAEL,aAAa,CAAC,EAC3CF,YAAY;IAAA;EAAA;;;2EAEXqB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAxB,YAAA;IAAAyB,OAAA,GAFjBzB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}