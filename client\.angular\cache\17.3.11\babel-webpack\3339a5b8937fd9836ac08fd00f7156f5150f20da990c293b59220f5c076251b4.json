{"ast": null, "code": "import _asyncToGenerator from \"D:/Code/ASAR/Azure/Public Service/SNJYA-PUBLIC-SERVICE/client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./auth.service\";\nexport class AuthGuard {\n  constructor(router, auth) {\n    this.router = router;\n    this.auth = auth;\n  }\n  canActivate(route, state) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      return yield _this.authenticate(route, state.url);\n    })();\n  }\n  canActivateChild(childRoute, state) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      return yield _this2.authenticate(childRoute, state.url);\n    })();\n  }\n  authenticate(route, url) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (_this3.auth.isLoggedIn) {\n        // If User already login and try to access auth pages.\n        if (url.startsWith(\"/auth\")) {\n          return _this3.router.parseUrl(\"/store\");\n        }\n        // const routeData: any = route?.data || null;\n        // const permission: any = routeData?.permission || null;\n        // let permissions = this.auth.getPermissions;\n        // if (!permissions.length) {\n        //   permissions = await this.auth.getUserPermissions();\n        //   if (!permissions.length) {\n        //     return this.router.parseUrl(\"/store\");\n        //   }\n        // }\n        // if (permission !== null && !permissions.includes(permission)) {\n        //   return this.router.parseUrl(\"/store\");\n        // }\n        return true;\n      } else {\n        if (!url.startsWith(\"/auth\")) {\n          return _this3.router.parseUrl(\"/auth/login\");\n        }\n      }\n      return true;\n    })();\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "router", "auth", "canActivate", "route", "state", "_this", "_asyncToGenerator", "authenticate", "url", "canActivateChild", "childRoute", "_this2", "_this3", "isLoggedIn", "startsWith", "parseUrl", "i0", "ɵɵinject", "i1", "Router", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\core\\authentication\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport {\r\n  ActivatedRouteSnapshot,\r\n  Router,\r\n  RouterStateSnapshot,\r\n  UrlTree,\r\n} from \"@angular/router\";\r\nimport { AuthService } from \"./auth.service\";\r\n\r\n@Injectable({\r\n  providedIn: \"root\",\r\n})\r\nexport class AuthGuard {\r\n  constructor(\r\n    private router: Router,\r\n    private auth: AuthService,\r\n  ) { }\r\n\r\n  async canActivate(\r\n    route: ActivatedRouteSnapshot,\r\n    state: RouterStateSnapshot\r\n  ): Promise<boolean | UrlTree | any> {\r\n    return await this.authenticate(route, state.url);\r\n  }\r\n\r\n  async canActivateChild(\r\n    childRoute: ActivatedRouteSnapshot,\r\n    state: RouterStateSnapshot\r\n  ): Promise<boolean | UrlTree | any> {\r\n    return await this.authenticate(childRoute, state.url);\r\n  }\r\n\r\n  private async authenticate(\r\n    route: ActivatedRouteSnapshot,\r\n    url: string,\r\n  ): Promise<boolean | UrlTree | any> {\r\n    if (this.auth.isLoggedIn) {\r\n      // If User already login and try to access auth pages.\r\n      if (url.startsWith(\"/auth\")) {\r\n        return this.router.parseUrl(\"/store\");\r\n      }\r\n      // const routeData: any = route?.data || null;\r\n      // const permission: any = routeData?.permission || null;\r\n      // let permissions = this.auth.getPermissions;\r\n      // if (!permissions.length) {\r\n      //   permissions = await this.auth.getUserPermissions();\r\n      //   if (!permissions.length) {\r\n      //     return this.router.parseUrl(\"/store\");\r\n      //   }\r\n      // }\r\n      // if (permission !== null && !permissions.includes(permission)) {\r\n      //   return this.router.parseUrl(\"/store\");\r\n      // }\r\n\r\n      return true;\r\n    } else {\r\n      if (!url.startsWith(\"/auth\")) {\r\n        return this.router.parseUrl(\"/auth/login\");\r\n      }\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n}\r\n"], "mappings": ";;;;AAYA,OAAM,MAAOA,SAAS;EACpBC,YACUC,MAAc,EACdC,IAAiB;IADjB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;EACV;EAEEC,WAAWA,CACfC,KAA6B,EAC7BC,KAA0B;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAE1B,aAAaD,KAAI,CAACE,YAAY,CAACJ,KAAK,EAAEC,KAAK,CAACI,GAAG,CAAC;IAAC;EACnD;EAEMC,gBAAgBA,CACpBC,UAAkC,EAClCN,KAA0B;IAAA,IAAAO,MAAA;IAAA,OAAAL,iBAAA;MAE1B,aAAaK,MAAI,CAACJ,YAAY,CAACG,UAAU,EAAEN,KAAK,CAACI,GAAG,CAAC;IAAC;EACxD;EAEcD,YAAYA,CACxBJ,KAA6B,EAC7BK,GAAW;IAAA,IAAAI,MAAA;IAAA,OAAAN,iBAAA;MAEX,IAAIM,MAAI,CAACX,IAAI,CAACY,UAAU,EAAE;QACxB;QACA,IAAIL,GAAG,CAACM,UAAU,CAAC,OAAO,CAAC,EAAE;UAC3B,OAAOF,MAAI,CAACZ,MAAM,CAACe,QAAQ,CAAC,QAAQ,CAAC;QACvC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,OAAO,IAAI;MACb,CAAC,MAAM;QACL,IAAI,CAACP,GAAG,CAACM,UAAU,CAAC,OAAO,CAAC,EAAE;UAC5B,OAAOF,MAAI,CAACZ,MAAM,CAACe,QAAQ,CAAC,aAAa,CAAC;QAC5C;MACF;MAEA,OAAO,IAAI;IAAC;EACd;;;uBAlDWjB,SAAS,EAAAkB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAATvB,SAAS;MAAAwB,OAAA,EAATxB,SAAS,CAAAyB,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}