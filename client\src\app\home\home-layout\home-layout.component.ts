import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, Renderer2 } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { PrimeNGConfig } from 'primeng/api';

@Component({
  selector: 'app-home-layout',
  templateUrl: './home-layout.component.html',
  styleUrl: './home-layout.component.scss'
})
export class HomeLayoutComponent implements OnInit, OnDestroy {

  commonContent!: any;

  constructor(
    private primengConfig: PrimeNGConfig,
    private renderer: Renderer2,
    private route: ActivatedRoute
  ) { }

  ngOnInit(): void {
    // Get common content from route data
    this.commonContent = this.route.snapshot.data['commonContent'];
    console.log('Common Content:', this.commonContent);

    // Inject theme 
    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';
    const link = this.renderer.createElement('link');
    this.renderer.setAttribute(link, 'id', 'theme-link');
    this.renderer.setAttribute(link, 'rel', 'stylesheet');
    this.renderer.setAttribute(link, 'type', 'text/css');
    this.renderer.setAttribute(link, 'href', href);

    // Append the link tag to the head of the document
    this.renderer.appendChild(document.head, link);

    this.primengConfig.ripple = true; //enables core ripple functionality
  }

  ngOnDestroy(): void {
    // Find and remove the link tag when the component is destroyed
    const link = document.getElementById('theme-link');
    if (link) {
      link.remove();
    }
  }
}
