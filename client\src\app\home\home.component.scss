// Home page specific styles
:host ::ng-deep {
    .banner-sec {
        margin: 8rem 0 0 0;
        height: calc(100vh - 8rem);
        background: url(../../assets/layout/images/banner-img.jpg) center center no-repeat;
        background-size: cover;

        .banner-box {
            margin: 120px 0 0 0;
        }
    }

    .services-sec {
        margin: -87px 0 0 0;

        .services-box.s-box-1 {
            background: #a8c1cd;
        }

        .services-box.s-box-2 {
            background: #e3cab0;
        }

        .services-box.s-box-3 {
            background: #c4a597;
        }

        .services-box.s-box-4 {
            background: #e8816e;
        }
    }

    .about-sec {
        &:before {
            position: absolute;
            content: "";
            right: 0;
            height: 100%;
            background: #030f5e !important;
            width: 20%;
        }
    }

    .city-members-sec {
        &:before {
            position: absolute;
            content: "";
            left: 0;
            height: 100%;
            background: var(--orange-50) !important;
            width: 20%;
        }
    }

    .city-members-left {
        width: 70%;

        .cm-time-box {
            flex: 0 0 40%;
        }

        .cm-info {
            flex: 0 0 60%;
            padding: 0 0 0 50px;
        }
    }

    .city-members-right {
        width: 30%;

        .cm-right-box {
            h3 {
                sup {
                    top: 7px;
                }
            }
        }
    }

    .quick-access-sec {
        background: url(../../assets/layout/images/pexels-vitor-gusmao.jpg) center right no-repeat;
        background-size: 38% auto;

        .quick-access-left {
            flex: 0 0 67%;
            width: 67%;

            .quick-access-list {
                grid-template-columns: repeat(auto-fill, minmax(33.333%, 1fr));
            }
        }

        .quick-access-right {
            flex: 0 0 33%;
            width: 33%;
        }
    }

    .news-sec {
        .news-list {
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }
    }

    .news-letter-sec {
        background: linear-gradient(180deg, #030f5e 50%, transparent 50%);

        .news-letter-box-list {
            .news-letter-img {
                height: 640px;
            }
        }
    }

    .what-happning-sec {
        .what-happning-body {
            .what-happning-img {
                height: 800px;
            }

            .what-happning-box {
                .wh-img-box {
                    width: 120px;
                    height: 120px;
                }

                .wh-cnt {
                    max-width: 70%;
                    width: 100%;
                }
            }
        }
    }
}