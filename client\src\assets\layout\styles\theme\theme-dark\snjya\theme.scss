$primaryColor: #2e7eff;
$primaryLightColor: scale-color($primaryColor, $lightness: 60%) !default;
$primaryDarkColor: scale-color($primaryColor, $lightness: -10%) !default;
$primaryDarkerColor: scale-color($primaryColor, $lightness: -20%) !default;
$primaryTextColor: #ffffff;
$primary500: #2e7eff !default;


$highlightBg: $primaryColor;
$highlightTextColor: $primaryTextColor;

@import '../_variables';
@import '../../theme-base/_components';
@import '../_extensions';
@import '../../extensions/_fullcalendar';


.layout-dark {
  --body-image: none !important;
  --exception-pages-image: none !important;
  --body-bg: #1f1e2e !important;
}

/*-----CUSTOM CSS-----*/
/* width */
::-webkit-scrollbar {
  width: 8px;
  height: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #ced9df;
}

.layout-content-wrapper {
  padding: 1rem !important;
  margin-left: 4.4rem !important;
  min-height: 100vh !important;
}

.layout-rightmenu-button {
  background: linear-gradient(271.89deg, rgb(70, 82, 104), rgb(115, 130, 161));
}

.layout-rightmenu-button:enabled:hover {
  background: linear-gradient(271.89deg, rgba(70, 82, 104, 0.5), rgba(115, 130, 161, 0.5));
}

.layout-topbar {
  margin-bottom: 1rem !important;
}

.layout-topbar .topbar-start .topbar-menubutton {
  width: fit-content !important;
  height: fit-content !important;
  background: none !important;
}

.layout-topbar .topbar-start .topbar-menubutton i {
  font-size: 1.25rem !important;
  color: var(--text-color) !important;
  transition: color var(--transition-duration) !important;
}

.layout-topbar .topbar-start .topbar-menubutton:hover i {
  color: var(--primary-color) !important;
}

.layout-topbar .topbar-menu {
  margin-right: 8px !important;
}

.layout-topbar .topbar-menu li.topbar-item ul {
  top: 20px !important;
  bottom: 0 !important;
  background: var(--surface-0) !important;
  height: fit-content;
}

.layout-topbar .topbar-menu li.topbar-item ul a {
  color: var(--text-color);
  line-height: 22px;
}

.layout-topbar .topbar-menu li.topbar-item ul .logout-btn {
  border-color: var(--surface-100) !important;
}

.layout-topbar .topbar-menu li.topbar-item ul .logout-btn a {
  color: var(--red-600) !important;
}

.layout-container.layout-static-inactive .layout-content-wrapper {
  margin-left: 0 !important;
}

/*--SIDEBAR--*/
.layout-sidebar {
  border-radius: 0px;
  box-shadow: 2px 0 2px rgba(74, 81, 175, 0.1803921569);
}

.layout-sidebar .sidebar-header {
  padding: 1rem 0.5rem 0.5rem 1.4rem !important;
}

.layout-container.layout-reveal.layout-sidebar-active .layout-sidebar .sidebar-header {
  padding: 0 1.4rem;
}

.layout-sidebar .sidebar-header .app-logo .app-logo-normal img {
  height: 3.2rem !important;
}

.layout-sidebar .sidebar-header .app-logo .app-logo-small {
  display: none;
}

.layout-sidebar .sidebar-header .app-logo .app-logo-small img {
  height: 2.5rem;
}

.layout-sidebar .layout-menu > li:first-child {
  margin-top: 0 !important;
}

.layout-sidebar .layout-menu li {
  margin-bottom: 4px;
}

.layout-sidebar .layout-menu ul a {
  padding: 0.7rem 1rem !important;
  line-height: 18px;
}

.layout-sidebar .layout-menu ul a.active-route {
  background-color: var(--surface-0) !important;
  font-weight: 600;
}

.layout-sidebar .layout-menu {
  padding: 0 0.5rem;
}

.layout-container.layout-reveal.layout-sidebar-active .layout-sidebar .layout-menu {
  padding: 0 1.4rem;
}

.layout-sidebar .portal-name {
  padding-left: 12px;
  padding-right: 12px;
}

.layout-sidebar .portal-name h5 {
  transition: all 0.3s ease-in-out;
}

.layout-sidebar-active .layout-sidebar .portal-name h5 {
  width: 100% !important;
}

/*--SIDEBAR--*/
/*-----CUSTOM CSS-----*/

@media screen and (min-width: 992px) {
  .layout-container.layout-reveal.layout-sidebar-active .layout-sidebar .sidebar-header .app-logo .app-logo-normal {
    display: flex;
    justify-content: start;
  }
  .layout-container.layout-reveal .layout-sidebar {
    padding-bottom: 0;
    border-radius: 0;
    box-shadow: 2px 0px 3px rgba(0, 0, 0, 0.1215686275);
    background: var(--body-bg) !important;
    gap: 8px;
  }
}

@media screen and (max-width: 991px) {
  .layout-container .layout-content-wrapper {
    margin-left: 0 !important;
    padding: 1rem;
  }
  .layout-sidebar {
    border-radius: 0px !important;
  }
  .content-breadcrumb {
    margin-bottom: 0;
    padding: 0;
  }
  .layout-breadcrumb .breadcrumb-menu {
    padding: 0 !important;
    margin-right: 0 !important;
  }
  .layout-topbar .topbar-start .app-logo-normal {
    display: none;
  }
  .layout-topbar .topbar-start {
    gap: 0 !important;
  }
}
