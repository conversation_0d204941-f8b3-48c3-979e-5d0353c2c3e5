import { Component, Renderer2 } from '@angular/core';
import { PrimeNGConfig } from 'primeng/api';

@Component({
  template: `<router-outlet></router-outlet>`,
})
export class SessionComponent {
  constructor(
    private primengConfig: PrimeNGConfig,
    private renderer: Renderer2
  ) {}

  ngOnInit(): void {
    // Inject theme 
    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';
    const link = this.renderer.createElement('link');
    this.renderer.setAttribute(link, 'id', 'theme-link');
    this.renderer.setAttribute(link, 'rel', 'stylesheet');
    this.renderer.setAttribute(link, 'type', 'text/css');
    this.renderer.setAttribute(link, 'href', href);

    // Append the link tag to the head of the document
    this.renderer.appendChild(document.head, link);

    this.primengConfig.ripple = true; //enables core ripple functionality
  }

  ngOnDestroy(): void {
    // Find and remove the link tag when the component is destroyed
    const link = document.getElementById('theme-link');
    if (link) {
      link.remove();
    }
  }
}
