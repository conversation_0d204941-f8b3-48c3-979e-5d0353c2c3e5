{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nlet HomeComponent = class HomeComponent {\n  constructor(route, CMSservice) {\n    this.route = route;\n    this.CMSservice = CMSservice;\n    this.bannerData = null;\n    this.servicesData = [];\n  }\n  ngOnInit() {\n    this.content = this.route.snapshot.data['content'];\n    console.log('Home Content:', this.content);\n    // Extract banner\n    const bannerComponent = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.banner\");\n    if (bannerComponent?.length) {\n      this.bannerData = bannerComponent[0];\n    }\n    // Extract services\n    const servicesComponents = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.services\");\n    if (servicesComponents?.length) {\n      this.servicesData = servicesComponents;\n    }\n  }\n};\nHomeComponent = __decorate([Component({\n  selector: 'app-home',\n  templateUrl: './home.component.html',\n  styleUrl: './home.component.scss'\n})], HomeComponent);\nexport { HomeComponent };", "map": {"version": 3, "names": ["Component", "HomeComponent", "constructor", "route", "CMSservice", "bannerData", "servicesData", "ngOnInit", "content", "snapshot", "data", "console", "log", "bannerComponent", "getDataByComponentName", "body", "length", "servicesComponents", "__decorate", "selector", "templateUrl", "styleUrl"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { ContentService } from '../core/services/content-vendor.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrl: './home.component.scss'\r\n})\r\nexport class HomeComponent {\r\n\r\n  content!: any;\r\n  bannerData: any = null;\r\n  servicesData: any[] = [];\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private CMSservice: ContentService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.content = this.route.snapshot.data['content'];\r\n    console.log('Home Content:', this.content);\r\n\r\n    // Extract banner\r\n    const bannerComponent = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.banner\");\r\n    if (bannerComponent?.length) {\r\n      this.bannerData = bannerComponent[0];\r\n    }\r\n\r\n    // Extract services\r\n    const servicesComponents = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.services\");\r\n    if (servicesComponents?.length) {\r\n      this.servicesData = servicesComponents;\r\n    }\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AASlC,IAAMC,aAAa,GAAnB,MAAMA,aAAa;EAMxBC,YACUC,KAAqB,EACrBC,UAA0B;IAD1B,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IALpB,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,YAAY,GAAU,EAAE;EAKpB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,OAAO,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;IAClDC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACJ,OAAO,CAAC;IAE1C;IACA,MAAMK,eAAe,GAAG,IAAI,CAACT,UAAU,CAACU,sBAAsB,CAAC,IAAI,CAACN,OAAO,CAACO,IAAI,EAAE,sBAAsB,CAAC;IACzG,IAAIF,eAAe,EAAEG,MAAM,EAAE;MAC3B,IAAI,CAACX,UAAU,GAAGQ,eAAe,CAAC,CAAC,CAAC;IACtC;IAEA;IACA,MAAMI,kBAAkB,GAAG,IAAI,CAACb,UAAU,CAACU,sBAAsB,CAAC,IAAI,CAACN,OAAO,CAACO,IAAI,EAAE,wBAAwB,CAAC;IAC9G,IAAIE,kBAAkB,EAAED,MAAM,EAAE;MAC9B,IAAI,CAACV,YAAY,GAAGW,kBAAkB;IACxC;EACF;CAED;AA5BYhB,aAAa,GAAAiB,UAAA,EALzBlB,SAAS,CAAC;EACTmB,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,uBAAuB;EACpCC,QAAQ,EAAE;CACX,CAAC,C,EACWpB,aAAa,CA4BzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}