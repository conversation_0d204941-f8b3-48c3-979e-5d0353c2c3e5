import { Component } from '@angular/core';
import { MessageService } from 'primeng/api';
// import { SessionsyncService } from './store/services/sessionsync.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent {
  public isSidebarCollapsed = true;

  constructor(
    private messageservice: MessageService,
    // private sessionsyncservice: SessionsyncService
  ) {
    // Display error message while user try to login with "Microsoft SSO" button.
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('error')) {
      const error: any = urlParams.get('error');
      setTimeout(() => {
        this.messageservice.add({
          severity: 'error',
          summary: 'Error',
          detail: error,
        });
      }, 100);
    }
  }

  toggleSidebar() {
    this.isSidebarCollapsed = !this.isSidebarCollapsed;
  }
}
