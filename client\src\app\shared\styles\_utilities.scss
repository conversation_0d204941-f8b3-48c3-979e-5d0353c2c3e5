// Shared utility classes that can be used across components

.max-w-1200 {
    max-width: 1200px;
}

.secondary-color {
    color: #030f5e !important;
}

.secondary-bg-color {
    background: #030f5e !important;
}

.font-extrabold {
    font-weight: 900 !important;
}

.d-grid {
    display: grid !important;
}

.object-fit-contain {
    object-fit: contain;
}

.object-fit-cover {
    object-fit: cover;
}

.h-3-3rem {
    height: 3.3rem !important;
}

.bg-blue-75 {
    background: rgb(227 243 255) !important;
}

// Line utility classes for decorative underlines
.line {
    &::before {
        position: absolute;
        content: "";
        left: 0;
        bottom: 0;
        height: 2px;
        background: #ffffff3d !important;
        width: 100%;
    }

    &::after {
        position: absolute;
        content: "";
        left: 0;
        bottom: 0;
        height: 2px;
        background: rgb(213 200 186) !important;
        width: 64px;
    }
}

.line-2 {
    &::before {
        position: absolute;
        content: "";
        left: 0;
        bottom: 0;
        height: 2px;
        background: rgb(0 0 0 / 7%) !important;
        width: 100%;
    }

    &::after {
        position: absolute;
        content: "";
        left: 0;
        bottom: 0;
        height: 2px;
        background: rgb(213 200 186) !important;
        width: 64px;
    }
}
