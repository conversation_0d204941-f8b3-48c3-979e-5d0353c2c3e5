{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { HomeComponent } from './home.component';\nimport { contentResolver } from '../core/content-resolver';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: HomeComponent,\n  resolve: {\n    content: contentResolver\n  },\n  data: {\n    slug: 'home'\n  }\n}];\nexport class HomeRoutingModule {\n  static {\n    this.ɵfac = function HomeRoutingModule_Factory(t) {\n      return new (t || HomeRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: HomeRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HomeRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "HomeComponent", "contentResolver", "routes", "path", "component", "resolve", "content", "data", "slug", "HomeRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { HomeComponent } from './home.component';\r\nimport { contentResolver } from '../core/content-resolver';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '', component: HomeComponent, resolve: {\r\n      content: contentResolver,\r\n    },\r\n    data: {\r\n      slug: 'home',\r\n    },\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class HomeRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,eAAe,QAAQ,0BAA0B;;;AAE1D,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEJ,aAAa;EAAEK,OAAO,EAAE;IAC3CC,OAAO,EAAEL;GACV;EACDM,IAAI,EAAE;IACJC,IAAI,EAAE;;CAET,CACF;AAMD,OAAM,MAAOC,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAHlBV,YAAY,CAACW,QAAQ,CAACR,MAAM,CAAC,EAC7BH,YAAY;IAAA;EAAA;;;2EAEXU,iBAAiB;IAAAE,OAAA,GAAAC,EAAA,CAAAb,YAAA;IAAAc,OAAA,GAFlBd,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}