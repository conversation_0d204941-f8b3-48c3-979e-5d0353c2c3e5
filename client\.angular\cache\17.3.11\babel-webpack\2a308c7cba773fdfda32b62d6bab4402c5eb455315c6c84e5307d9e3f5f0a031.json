{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@angular/router\";\n// import { SessionsyncService } from './store/services/sessionsync.service';\nexport class AppComponent {\n  constructor(messageservice) {\n    this.messageservice = messageservice;\n    this.isSidebarCollapsed = true;\n    // Display error message while user try to login with \"Microsoft SSO\" button.\n    const urlParams = new URLSearchParams(window.location.search);\n    if (urlParams.has('error')) {\n      const error = urlParams.get('error');\n      setTimeout(() => {\n        this.messageservice.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: error\n        });\n      }, 100);\n    }\n  }\n  toggleSidebar() {\n    this.isSidebarCollapsed = !this.isSidebarCollapsed;\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.MessageService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 1,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i2.RouterOutlet],\n      styles: [\"@import url(https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200);\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "messageservice", "isSidebarCollapsed", "urlParams", "URLSearchParams", "window", "location", "search", "has", "error", "get", "setTimeout", "add", "severity", "summary", "detail", "toggleSidebar", "i0", "ɵɵdirectiveInject", "i1", "MessageService", "selectors", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\app.component.ts", "D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { MessageService } from 'primeng/api';\r\n// import { SessionsyncService } from './store/services/sessionsync.service';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.scss'],\r\n})\r\nexport class AppComponent {\r\n  public isSidebarCollapsed = true;\r\n\r\n  constructor(\r\n    private messageservice: MessageService,\r\n    // private sessionsyncservice: SessionsyncService\r\n  ) {\r\n    // Display error message while user try to login with \"Microsoft SSO\" button.\r\n    const urlParams = new URLSearchParams(window.location.search);\r\n    if (urlParams.has('error')) {\r\n      const error: any = urlParams.get('error');\r\n      setTimeout(() => {\r\n        this.messageservice.add({\r\n          severity: 'error',\r\n          summary: 'Error',\r\n          detail: error,\r\n        });\r\n      }, 100);\r\n    }\r\n  }\r\n\r\n  toggleSidebar() {\r\n    this.isSidebarCollapsed = !this.isSidebarCollapsed;\r\n  }\r\n}\r\n", "<router-outlet></router-outlet>"], "mappings": ";;;AAEA;AAOA,OAAM,MAAOA,YAAY;EAGvBC,YACUC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IAHjB,KAAAC,kBAAkB,GAAG,IAAI;IAM9B;IACA,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;IAC7D,IAAIJ,SAAS,CAACK,GAAG,CAAC,OAAO,CAAC,EAAE;MAC1B,MAAMC,KAAK,GAAQN,SAAS,CAACO,GAAG,CAAC,OAAO,CAAC;MACzCC,UAAU,CAAC,MAAK;QACd,IAAI,CAACV,cAAc,CAACW,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,OAAO;UAChBC,MAAM,EAAEN;SACT,CAAC;MACJ,CAAC,EAAE,GAAG,CAAC;IACT;EACF;EAEAO,aAAaA,CAAA;IACX,IAAI,CAACd,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;EACpD;;;uBAvBWH,YAAY,EAAAkB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAZrB,YAAY;MAAAsB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTzBT,EAAA,CAAAW,SAAA,oBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}