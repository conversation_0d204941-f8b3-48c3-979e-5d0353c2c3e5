{"ast": null, "code": "import { style, animate, animation, useAnimation, transition, trigger } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { ObjectUtils, ZIndexUtils } from 'primeng/utils';\nconst _c0 = [\"overlay\"];\nconst _c1 = [\"content\"];\nconst _c2 = [\"*\"];\nconst _c3 = (a0, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10, a11, a12, a13) => ({\n  \"p-overlay p-component\": true,\n  \"p-overlay-modal p-component-overlay p-component-overlay-enter\": a0,\n  \"p-overlay-center\": a1,\n  \"p-overlay-top\": a2,\n  \"p-overlay-top-start\": a3,\n  \"p-overlay-top-end\": a4,\n  \"p-overlay-bottom\": a5,\n  \"p-overlay-bottom-start\": a6,\n  \"p-overlay-bottom-end\": a7,\n  \"p-overlay-left\": a8,\n  \"p-overlay-left-start\": a9,\n  \"p-overlay-left-end\": a10,\n  \"p-overlay-right\": a11,\n  \"p-overlay-right-start\": a12,\n  \"p-overlay-right-end\": a13\n});\nconst _c4 = (a0, a1, a2) => ({\n  showTransitionParams: a0,\n  hideTransitionParams: a1,\n  transform: a2\n});\nconst _c5 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nconst _c6 = a0 => ({\n  mode: a0\n});\nconst _c7 = a0 => ({\n  $implicit: a0\n});\nfunction Overlay_div_0_div_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Overlay_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3, 1);\n    i0.ɵɵlistener(\"click\", function Overlay_div_0_div_2_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onOverlayContentClick($event));\n    })(\"@overlayContentAnimation.start\", function Overlay_div_0_div_2_Template_div_animation_overlayContentAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onOverlayContentAnimationStart($event));\n    })(\"@overlayContentAnimation.done\", function Overlay_div_0_div_2_Template_div_animation_overlayContentAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onOverlayContentAnimationDone($event));\n    });\n    i0.ɵɵprojection(2);\n    i0.ɵɵtemplate(3, Overlay_div_0_div_2_ng_container_3_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.contentStyleClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.contentStyle)(\"ngClass\", \"p-overlay-content\")(\"@overlayContentAnimation\", i0.ɵɵpureFunction1(11, _c5, i0.ɵɵpureFunction3(7, _c4, ctx_r1.showTransitionOptions, ctx_r1.hideTransitionOptions, ctx_r1.transformOptions[ctx_r1.modal ? ctx_r1.overlayResponsiveDirection : \"default\"])));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(15, _c7, i0.ɵɵpureFunction1(13, _c6, ctx_r1.overlayMode)));\n  }\n}\nfunction Overlay_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3, 0);\n    i0.ɵɵlistener(\"click\", function Overlay_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayClick());\n    });\n    i0.ɵɵtemplate(2, Overlay_div_0_div_2_Template, 4, 17, \"div\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.styleClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.style)(\"ngClass\", i0.ɵɵpureFunctionV(5, _c3, [ctx_r1.modal, ctx_r1.modal && ctx_r1.overlayResponsiveDirection === \"center\", ctx_r1.modal && ctx_r1.overlayResponsiveDirection === \"top\", ctx_r1.modal && ctx_r1.overlayResponsiveDirection === \"top-start\", ctx_r1.modal && ctx_r1.overlayResponsiveDirection === \"top-end\", ctx_r1.modal && ctx_r1.overlayResponsiveDirection === \"bottom\", ctx_r1.modal && ctx_r1.overlayResponsiveDirection === \"bottom-start\", ctx_r1.modal && ctx_r1.overlayResponsiveDirection === \"bottom-end\", ctx_r1.modal && ctx_r1.overlayResponsiveDirection === \"left\", ctx_r1.modal && ctx_r1.overlayResponsiveDirection === \"left-start\", ctx_r1.modal && ctx_r1.overlayResponsiveDirection === \"left-end\", ctx_r1.modal && ctx_r1.overlayResponsiveDirection === \"right\", ctx_r1.modal && ctx_r1.overlayResponsiveDirection === \"right-start\", ctx_r1.modal && ctx_r1.overlayResponsiveDirection === \"right-end\"]));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.visible);\n  }\n}\nconst OVERLAY_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Overlay),\n  multi: true\n};\nconst showOverlayContentAnimation = animation([style({\n  transform: '{{transform}}',\n  opacity: 0\n}), animate('{{showTransitionParams}}')]);\nconst hideOverlayContentAnimation = animation([animate('{{hideTransitionParams}}', style({\n  transform: '{{transform}}',\n  opacity: 0\n}))]);\n/**\n * This API allows overlay components to be controlled from the PrimeNGConfig. In this way, all overlay components in the application can have the same behavior.\n * @group Components\n */\nclass Overlay {\n  document;\n  platformId;\n  el;\n  renderer;\n  config;\n  overlayService;\n  cd;\n  zone;\n  /**\n   * The visible property is an input that determines the visibility of the component.\n   * @defaultValue false\n   * @group Props\n   */\n  get visible() {\n    return this._visible;\n  }\n  set visible(value) {\n    this._visible = value;\n    if (this._visible && !this.modalVisible) {\n      this.modalVisible = true;\n    }\n  }\n  /**\n   * The mode property is an input that determines the overlay mode type or string.\n   * @defaultValue null\n   * @group Props\n   */\n  get mode() {\n    return this._mode || this.overlayOptions?.mode;\n  }\n  set mode(value) {\n    this._mode = value;\n  }\n  /**\n   * The style property is an input that determines the style object for the component.\n   * @defaultValue null\n   * @group Props\n   */\n  get style() {\n    return ObjectUtils.merge(this._style, this.modal ? this.overlayResponsiveOptions?.style : this.overlayOptions?.style);\n  }\n  set style(value) {\n    this._style = value;\n  }\n  /**\n   * The styleClass property is an input that determines the CSS class(es) for the component.\n   * @defaultValue null\n   * @group Props\n   */\n  get styleClass() {\n    return ObjectUtils.merge(this._styleClass, this.modal ? this.overlayResponsiveOptions?.styleClass : this.overlayOptions?.styleClass);\n  }\n  set styleClass(value) {\n    this._styleClass = value;\n  }\n  /**\n   * The contentStyle property is an input that determines the style object for the content of the component.\n   * @defaultValue null\n   * @group Props\n   */\n  get contentStyle() {\n    return ObjectUtils.merge(this._contentStyle, this.modal ? this.overlayResponsiveOptions?.contentStyle : this.overlayOptions?.contentStyle);\n  }\n  set contentStyle(value) {\n    this._contentStyle = value;\n  }\n  /**\n   * The contentStyleClass property is an input that determines the CSS class(es) for the content of the component.\n   * @defaultValue null\n   * @group Props\n   */\n  get contentStyleClass() {\n    return ObjectUtils.merge(this._contentStyleClass, this.modal ? this.overlayResponsiveOptions?.contentStyleClass : this.overlayOptions?.contentStyleClass);\n  }\n  set contentStyleClass(value) {\n    this._contentStyleClass = value;\n  }\n  /**\n   * The target property is an input that specifies the target element or selector for the component.\n   * @defaultValue null\n   * @group Props\n   */\n  get target() {\n    const value = this._target || this.overlayOptions?.target;\n    return value === undefined ? '@prev' : value;\n  }\n  set target(value) {\n    this._target = value;\n  }\n  /**\n   * Overlay can be mounted into its location, body or DOM element instance using this option.\n   * @defaultValue null\n   * @group Props\n   */\n  get appendTo() {\n    return this._appendTo || this.overlayOptions?.appendTo;\n  }\n  set appendTo(value) {\n    this._appendTo = value;\n  }\n  /**\n   * The autoZIndex determines whether to automatically manage layering. Its default value is 'false'.\n   * @defaultValue false\n   * @group Props\n   */\n  get autoZIndex() {\n    const value = this._autoZIndex || this.overlayOptions?.autoZIndex;\n    return value === undefined ? true : value;\n  }\n  set autoZIndex(value) {\n    this._autoZIndex = value;\n  }\n  /**\n   * The baseZIndex is base zIndex value to use in layering.\n   * @defaultValue null\n   * @group Props\n   */\n  get baseZIndex() {\n    const value = this._baseZIndex || this.overlayOptions?.baseZIndex;\n    return value === undefined ? 0 : value;\n  }\n  set baseZIndex(value) {\n    this._baseZIndex = value;\n  }\n  /**\n   * Transition options of the show or hide animation.\n   * @defaultValue .12s cubic-bezier(0, 0, 0.2, 1)\n   * @group Props\n   */\n  get showTransitionOptions() {\n    const value = this._showTransitionOptions || this.overlayOptions?.showTransitionOptions;\n    return value === undefined ? '.12s cubic-bezier(0, 0, 0.2, 1)' : value;\n  }\n  set showTransitionOptions(value) {\n    this._showTransitionOptions = value;\n  }\n  /**\n   * The hideTransitionOptions property is an input that determines the CSS transition options for hiding the component.\n   * @defaultValue .1s linear\n   * @group Props\n   */\n  get hideTransitionOptions() {\n    const value = this._hideTransitionOptions || this.overlayOptions?.hideTransitionOptions;\n    return value === undefined ? '.1s linear' : value;\n  }\n  set hideTransitionOptions(value) {\n    this._hideTransitionOptions = value;\n  }\n  /**\n   * The listener property is an input that specifies the listener object for the component.\n   * @defaultValue null\n   * @group Props\n   */\n  get listener() {\n    return this._listener || this.overlayOptions?.listener;\n  }\n  set listener(value) {\n    this._listener = value;\n  }\n  /**\n   * It is the option used to determine in which mode it should appear according to the given media or breakpoint.\n   * @defaultValue null\n   * @group Props\n   */\n  get responsive() {\n    return this._responsive || this.overlayOptions?.responsive;\n  }\n  set responsive(val) {\n    this._responsive = val;\n  }\n  /**\n   * The options property is an input that specifies the overlay options for the component.\n   * @defaultValue null\n   * @group Props\n   */\n  get options() {\n    return this._options;\n  }\n  set options(val) {\n    this._options = val;\n  }\n  /**\n   * This EventEmitter is used to notify changes in the visibility state of a component.\n   * @param {Boolean} boolean - Value of visibility as boolean.\n   * @group Emits\n   */\n  visibleChange = new EventEmitter();\n  /**\n   * Callback to invoke before the overlay is shown.\n   * @param {OverlayOnBeforeShowEvent} event - Custom overlay before show event.\n   * @group Emits\n   */\n  onBeforeShow = new EventEmitter();\n  /**\n   * Callback to invoke when the overlay is shown.\n   * @param {OverlayOnShowEvent} event - Custom overlay show event.\n   * @group Emits\n   */\n  onShow = new EventEmitter();\n  /**\n   * Callback to invoke before the overlay is hidden.\n   * @param {OverlayOnBeforeHideEvent} event - Custom overlay before hide event.\n   * @group Emits\n   */\n  onBeforeHide = new EventEmitter();\n  /**\n   * Callback to invoke when the overlay is hidden\n   * @param {OverlayOnHideEvent} event - Custom hide event.\n   * @group Emits\n   */\n  onHide = new EventEmitter();\n  /**\n   * Callback to invoke when the animation is started.\n   * @param {AnimationEvent} event - Animation event.\n   * @group Emits\n   */\n  onAnimationStart = new EventEmitter();\n  /**\n   * Callback to invoke when the animation is done.\n   * @param {AnimationEvent} event - Animation event.\n   * @group Emits\n   */\n  onAnimationDone = new EventEmitter();\n  templates;\n  overlayViewChild;\n  contentViewChild;\n  contentTemplate;\n  _visible = false;\n  _mode;\n  _style;\n  _styleClass;\n  _contentStyle;\n  _contentStyleClass;\n  _target;\n  _appendTo;\n  _autoZIndex;\n  _baseZIndex;\n  _showTransitionOptions;\n  _hideTransitionOptions;\n  _listener;\n  _responsive;\n  _options;\n  modalVisible = false;\n  isOverlayClicked = false;\n  isOverlayContentClicked = false;\n  scrollHandler;\n  documentClickListener;\n  documentResizeListener;\n  documentKeyboardListener;\n  window;\n  transformOptions = {\n    default: 'scaleY(0.8)',\n    center: 'scale(0.7)',\n    top: 'translate3d(0px, -100%, 0px)',\n    'top-start': 'translate3d(0px, -100%, 0px)',\n    'top-end': 'translate3d(0px, -100%, 0px)',\n    bottom: 'translate3d(0px, 100%, 0px)',\n    'bottom-start': 'translate3d(0px, 100%, 0px)',\n    'bottom-end': 'translate3d(0px, 100%, 0px)',\n    left: 'translate3d(-100%, 0px, 0px)',\n    'left-start': 'translate3d(-100%, 0px, 0px)',\n    'left-end': 'translate3d(-100%, 0px, 0px)',\n    right: 'translate3d(100%, 0px, 0px)',\n    'right-start': 'translate3d(100%, 0px, 0px)',\n    'right-end': 'translate3d(100%, 0px, 0px)'\n  };\n  get modal() {\n    if (isPlatformBrowser(this.platformId)) {\n      return this.mode === 'modal' || this.overlayResponsiveOptions && this.window?.matchMedia(this.overlayResponsiveOptions.media?.replace('@media', '') || `(max-width: ${this.overlayResponsiveOptions.breakpoint})`).matches;\n    }\n  }\n  get overlayMode() {\n    return this.mode || (this.modal ? 'modal' : 'overlay');\n  }\n  get overlayOptions() {\n    return {\n      ...this.config?.overlayOptions,\n      ...this.options\n    }; // TODO: Improve performance\n  }\n  get overlayResponsiveOptions() {\n    return {\n      ...this.overlayOptions?.responsive,\n      ...this.responsive\n    }; // TODO: Improve performance\n  }\n  get overlayResponsiveDirection() {\n    return this.overlayResponsiveOptions?.direction || 'center';\n  }\n  get overlayEl() {\n    return this.overlayViewChild?.nativeElement;\n  }\n  get contentEl() {\n    return this.contentViewChild?.nativeElement;\n  }\n  get targetEl() {\n    return DomHandler.getTargetElement(this.target, this.el?.nativeElement);\n  }\n  constructor(document, platformId, el, renderer, config, overlayService, cd, zone) {\n    this.document = document;\n    this.platformId = platformId;\n    this.el = el;\n    this.renderer = renderer;\n    this.config = config;\n    this.overlayService = overlayService;\n    this.cd = cd;\n    this.zone = zone;\n    this.window = this.document.defaultView;\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n        // TODO: new template types may be added.\n        default:\n          this.contentTemplate = item.template;\n          break;\n      }\n    });\n  }\n  show(overlay, isFocus = false) {\n    this.onVisibleChange(true);\n    this.handleEvents('onShow', {\n      overlay: overlay || this.overlayEl,\n      target: this.targetEl,\n      mode: this.overlayMode\n    });\n    isFocus && DomHandler.focus(this.targetEl);\n    this.modal && DomHandler.addClass(this.document?.body, 'p-overflow-hidden');\n  }\n  hide(overlay, isFocus = false) {\n    if (!this.visible) {\n      return;\n    } else {\n      this.onVisibleChange(false);\n      this.handleEvents('onHide', {\n        overlay: overlay || this.overlayEl,\n        target: this.targetEl,\n        mode: this.overlayMode\n      });\n      isFocus && DomHandler.focus(this.targetEl);\n      this.modal && DomHandler.removeClass(this.document?.body, 'p-overflow-hidden');\n    }\n  }\n  alignOverlay() {\n    !this.modal && DomHandler.alignOverlay(this.overlayEl, this.targetEl, this.appendTo);\n  }\n  onVisibleChange(visible) {\n    this._visible = visible;\n    this.visibleChange.emit(visible);\n  }\n  onOverlayClick() {\n    this.isOverlayClicked = true;\n  }\n  onOverlayContentClick(event) {\n    this.overlayService.add({\n      originalEvent: event,\n      target: this.targetEl\n    });\n    this.isOverlayContentClicked = true;\n  }\n  onOverlayContentAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        this.handleEvents('onBeforeShow', {\n          overlay: this.overlayEl,\n          target: this.targetEl,\n          mode: this.overlayMode\n        });\n        if (this.autoZIndex) {\n          ZIndexUtils.set(this.overlayMode, this.overlayEl, this.baseZIndex + this.config?.zIndex[this.overlayMode]);\n        }\n        DomHandler.appendOverlay(this.overlayEl, this.appendTo === 'body' ? this.document.body : this.appendTo, this.appendTo);\n        this.alignOverlay();\n        break;\n      case 'void':\n        this.handleEvents('onBeforeHide', {\n          overlay: this.overlayEl,\n          target: this.targetEl,\n          mode: this.overlayMode\n        });\n        this.modal && DomHandler.addClass(this.overlayEl, 'p-component-overlay-leave');\n        break;\n    }\n    this.handleEvents('onAnimationStart', event);\n  }\n  onOverlayContentAnimationDone(event) {\n    const container = this.overlayEl || event.element.parentElement;\n    switch (event.toState) {\n      case 'visible':\n        this.show(container, true);\n        this.bindListeners();\n        break;\n      case 'void':\n        this.hide(container, true);\n        this.unbindListeners();\n        DomHandler.appendOverlay(this.overlayEl, this.targetEl, this.appendTo);\n        ZIndexUtils.clear(container);\n        this.modalVisible = false;\n        this.cd.markForCheck();\n        break;\n    }\n    this.handleEvents('onAnimationDone', event);\n  }\n  handleEvents(name, params) {\n    this[name].emit(params);\n    this.options && this.options[name] && this.options[name](params);\n    this.config?.overlayOptions && (this.config?.overlayOptions)[name] && (this.config?.overlayOptions)[name](params);\n  }\n  bindListeners() {\n    this.bindScrollListener();\n    this.bindDocumentClickListener();\n    this.bindDocumentResizeListener();\n    this.bindDocumentKeyboardListener();\n  }\n  unbindListeners() {\n    this.unbindScrollListener();\n    this.unbindDocumentClickListener();\n    this.unbindDocumentResizeListener();\n    this.unbindDocumentKeyboardListener();\n  }\n  bindScrollListener() {\n    if (!this.scrollHandler) {\n      this.scrollHandler = new ConnectedOverlayScrollHandler(this.targetEl, event => {\n        const valid = this.listener ? this.listener(event, {\n          type: 'scroll',\n          mode: this.overlayMode,\n          valid: true\n        }) : true;\n        valid && this.hide(event, true);\n      });\n    }\n    this.scrollHandler.bindScrollListener();\n  }\n  unbindScrollListener() {\n    if (this.scrollHandler) {\n      this.scrollHandler.unbindScrollListener();\n    }\n  }\n  bindDocumentClickListener() {\n    if (!this.documentClickListener) {\n      this.documentClickListener = this.renderer.listen(this.document, 'click', event => {\n        const isTargetClicked = this.targetEl && (this.targetEl.isSameNode(event.target) || !this.isOverlayClicked && this.targetEl.contains(event.target));\n        const isOutsideClicked = !isTargetClicked && !this.isOverlayContentClicked;\n        const valid = this.listener ? this.listener(event, {\n          type: 'outside',\n          mode: this.overlayMode,\n          valid: event.which !== 3 && isOutsideClicked\n        }) : isOutsideClicked;\n        valid && this.hide(event);\n        this.isOverlayClicked = this.isOverlayContentClicked = false;\n      });\n    }\n  }\n  unbindDocumentClickListener() {\n    if (this.documentClickListener) {\n      this.documentClickListener();\n      this.documentClickListener = null;\n    }\n  }\n  bindDocumentResizeListener() {\n    if (!this.documentResizeListener) {\n      this.documentResizeListener = this.renderer.listen(this.window, 'resize', event => {\n        const valid = this.listener ? this.listener(event, {\n          type: 'resize',\n          mode: this.overlayMode,\n          valid: !DomHandler.isTouchDevice()\n        }) : !DomHandler.isTouchDevice();\n        valid && this.hide(event, true);\n      });\n    }\n  }\n  unbindDocumentResizeListener() {\n    if (this.documentResizeListener) {\n      this.documentResizeListener();\n      this.documentResizeListener = null;\n    }\n  }\n  bindDocumentKeyboardListener() {\n    if (this.documentKeyboardListener) {\n      return;\n    }\n    this.zone.runOutsideAngular(() => {\n      this.documentKeyboardListener = this.renderer.listen(this.window, 'keydown', event => {\n        if (this.overlayOptions.hideOnEscape === false || event.code !== 'Escape') {\n          return;\n        }\n        const valid = this.listener ? this.listener(event, {\n          type: 'keydown',\n          mode: this.overlayMode,\n          valid: !DomHandler.isTouchDevice()\n        }) : !DomHandler.isTouchDevice();\n        if (valid) {\n          this.zone.run(() => {\n            this.hide(event, true);\n          });\n        }\n      });\n    });\n  }\n  unbindDocumentKeyboardListener() {\n    if (this.documentKeyboardListener) {\n      this.documentKeyboardListener();\n      this.documentKeyboardListener = null;\n    }\n  }\n  ngOnDestroy() {\n    this.hide(this.overlayEl, true);\n    if (this.overlayEl) {\n      DomHandler.appendOverlay(this.overlayEl, this.targetEl, this.appendTo);\n      ZIndexUtils.clear(this.overlayEl);\n    }\n    if (this.scrollHandler) {\n      this.scrollHandler.destroy();\n      this.scrollHandler = null;\n    }\n    this.unbindListeners();\n  }\n  static ɵfac = function Overlay_Factory(t) {\n    return new (t || Overlay)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i1.OverlayService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Overlay,\n    selectors: [[\"p-overlay\"]],\n    contentQueries: function Overlay_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Overlay_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.overlayViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      visible: \"visible\",\n      mode: \"mode\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      contentStyle: \"contentStyle\",\n      contentStyleClass: \"contentStyleClass\",\n      target: \"target\",\n      appendTo: \"appendTo\",\n      autoZIndex: \"autoZIndex\",\n      baseZIndex: \"baseZIndex\",\n      showTransitionOptions: \"showTransitionOptions\",\n      hideTransitionOptions: \"hideTransitionOptions\",\n      listener: \"listener\",\n      responsive: \"responsive\",\n      options: \"options\"\n    },\n    outputs: {\n      visibleChange: \"visibleChange\",\n      onBeforeShow: \"onBeforeShow\",\n      onShow: \"onShow\",\n      onBeforeHide: \"onBeforeHide\",\n      onHide: \"onHide\",\n      onAnimationStart: \"onAnimationStart\",\n      onAnimationDone: \"onAnimationDone\"\n    },\n    features: [i0.ɵɵProvidersFeature([OVERLAY_VALUE_ACCESSOR])],\n    ngContentSelectors: _c2,\n    decls: 1,\n    vars: 1,\n    consts: [[\"overlay\", \"\"], [\"content\", \"\"], [3, \"ngStyle\", \"class\", \"ngClass\", \"click\", 4, \"ngIf\"], [3, \"click\", \"ngStyle\", \"ngClass\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n    template: function Overlay_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, Overlay_div_0_Template, 3, 20, \"div\", 2);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.modalVisible);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle],\n    styles: [\"@layer primeng{.p-overlay{position:absolute;top:0;left:0}.p-overlay-modal{display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100%;height:100%}.p-overlay-content{transform-origin:inherit}.p-overlay-modal>.p-overlay-content{z-index:1;width:90%}.p-overlay-top{align-items:flex-start}.p-overlay-top-start{align-items:flex-start;justify-content:flex-start}.p-overlay-top-end{align-items:flex-start;justify-content:flex-end}.p-overlay-bottom{align-items:flex-end}.p-overlay-bottom-start{align-items:flex-end;justify-content:flex-start}.p-overlay-bottom-end{align-items:flex-end;justify-content:flex-end}.p-overlay-left{justify-content:flex-start}.p-overlay-left-start{justify-content:flex-start;align-items:flex-start}.p-overlay-left-end{justify-content:flex-start;align-items:flex-end}.p-overlay-right{justify-content:flex-end}.p-overlay-right-start{justify-content:flex-end;align-items:flex-start}.p-overlay-right-end{justify-content:flex-end;align-items:flex-end}}\\n\"],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('overlayContentAnimation', [transition(':enter', [useAnimation(showOverlayContentAnimation)]), transition(':leave', [useAnimation(hideOverlayContentAnimation)])])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Overlay, [{\n    type: Component,\n    args: [{\n      selector: 'p-overlay',\n      template: `\n        <div\n            *ngIf=\"modalVisible\"\n            #overlay\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [ngClass]=\"{\n                'p-overlay p-component': true,\n                'p-overlay-modal p-component-overlay p-component-overlay-enter': modal,\n                'p-overlay-center': modal && overlayResponsiveDirection === 'center',\n                'p-overlay-top': modal && overlayResponsiveDirection === 'top',\n                'p-overlay-top-start': modal && overlayResponsiveDirection === 'top-start',\n                'p-overlay-top-end': modal && overlayResponsiveDirection === 'top-end',\n                'p-overlay-bottom': modal && overlayResponsiveDirection === 'bottom',\n                'p-overlay-bottom-start': modal && overlayResponsiveDirection === 'bottom-start',\n                'p-overlay-bottom-end': modal && overlayResponsiveDirection === 'bottom-end',\n                'p-overlay-left': modal && overlayResponsiveDirection === 'left',\n                'p-overlay-left-start': modal && overlayResponsiveDirection === 'left-start',\n                'p-overlay-left-end': modal && overlayResponsiveDirection === 'left-end',\n                'p-overlay-right': modal && overlayResponsiveDirection === 'right',\n                'p-overlay-right-start': modal && overlayResponsiveDirection === 'right-start',\n                'p-overlay-right-end': modal && overlayResponsiveDirection === 'right-end'\n            }\"\n            (click)=\"onOverlayClick()\"\n        >\n            <div\n                *ngIf=\"visible\"\n                #content\n                [ngStyle]=\"contentStyle\"\n                [class]=\"contentStyleClass\"\n                [ngClass]=\"'p-overlay-content'\"\n                (click)=\"onOverlayContentClick($event)\"\n                [@overlayContentAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions, transform: transformOptions[modal ? overlayResponsiveDirection : 'default'] } }\"\n                (@overlayContentAnimation.start)=\"onOverlayContentAnimationStart($event)\"\n                (@overlayContentAnimation.done)=\"onOverlayContentAnimationDone($event)\"\n            >\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: { mode: overlayMode } }\"></ng-container>\n            </div>\n        </div>\n    `,\n      animations: [trigger('overlayContentAnimation', [transition(':enter', [useAnimation(showOverlayContentAnimation)]), transition(':leave', [useAnimation(hideOverlayContentAnimation)])])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [OVERLAY_VALUE_ACCESSOR],\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-overlay{position:absolute;top:0;left:0}.p-overlay-modal{display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100%;height:100%}.p-overlay-content{transform-origin:inherit}.p-overlay-modal>.p-overlay-content{z-index:1;width:90%}.p-overlay-top{align-items:flex-start}.p-overlay-top-start{align-items:flex-start;justify-content:flex-start}.p-overlay-top-end{align-items:flex-start;justify-content:flex-end}.p-overlay-bottom{align-items:flex-end}.p-overlay-bottom-start{align-items:flex-end;justify-content:flex-start}.p-overlay-bottom-end{align-items:flex-end;justify-content:flex-end}.p-overlay-left{justify-content:flex-start}.p-overlay-left-start{justify-content:flex-start;align-items:flex-start}.p-overlay-left-end{justify-content:flex-start;align-items:flex-end}.p-overlay-right{justify-content:flex-end}.p-overlay-right-start{justify-content:flex-end;align-items:flex-start}.p-overlay-right-end{justify-content:flex-end;align-items:flex-end}}\\n\"]\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i1.PrimeNGConfig\n  }, {\n    type: i1.OverlayService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.NgZone\n  }], {\n    visible: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    contentStyle: [{\n      type: Input\n    }],\n    contentStyleClass: [{\n      type: Input\n    }],\n    target: [{\n      type: Input\n    }],\n    appendTo: [{\n      type: Input\n    }],\n    autoZIndex: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    listener: [{\n      type: Input\n    }],\n    responsive: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    visibleChange: [{\n      type: Output\n    }],\n    onBeforeShow: [{\n      type: Output\n    }],\n    onShow: [{\n      type: Output\n    }],\n    onBeforeHide: [{\n      type: Output\n    }],\n    onHide: [{\n      type: Output\n    }],\n    onAnimationStart: [{\n      type: Output\n    }],\n    onAnimationDone: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }],\n    overlayViewChild: [{\n      type: ViewChild,\n      args: ['overlay']\n    }],\n    contentViewChild: [{\n      type: ViewChild,\n      args: ['content']\n    }]\n  });\n})();\nclass OverlayModule {\n  static ɵfac = function OverlayModule_Factory(t) {\n    return new (t || OverlayModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: OverlayModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OverlayModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, SharedModule],\n      exports: [Overlay, SharedModule],\n      declarations: [Overlay]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { OVERLAY_VALUE_ACCESSOR, Overlay, OverlayModule };", "map": {"version": 3, "names": ["style", "animate", "animation", "useAnimation", "transition", "trigger", "i2", "isPlatformBrowser", "DOCUMENT", "CommonModule", "i0", "forwardRef", "EventEmitter", "PLATFORM_ID", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ContentChildren", "ViewChild", "NgModule", "NG_VALUE_ACCESSOR", "i1", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "ConnectedOverlayScrollHandler", "ObjectUtils", "ZIndexUtils", "_c0", "_c1", "_c2", "_c3", "a0", "a1", "a2", "a3", "a4", "a5", "a6", "a7", "a8", "a9", "a10", "a11", "a12", "a13", "_c4", "showTransitionParams", "hideTransitionParams", "transform", "_c5", "value", "params", "_c6", "mode", "_c7", "$implicit", "Overlay_div_0_div_2_ng_container_3_Template", "rf", "ctx", "ɵɵelementContainer", "Overlay_div_0_div_2_Template", "_r3", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "Overlay_div_0_div_2_Template_div_click_0_listener", "$event", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onOverlayContentClick", "Overlay_div_0_div_2_Template_div_animation_overlayContentAnimation_start_0_listener", "onOverlayContentAnimationStart", "Overlay_div_0_div_2_Template_div_animation_overlayContentAnimation_done_0_listener", "onOverlayContentAnimationDone", "ɵɵprojection", "ɵɵtemplate", "ɵɵelementEnd", "ɵɵclassMap", "contentStyleClass", "ɵɵproperty", "contentStyle", "ɵɵpureFunction1", "ɵɵpureFunction3", "showTransitionOptions", "hideTransitionOptions", "transformOptions", "modal", "overlayResponsiveDirection", "ɵɵadvance", "contentTemplate", "overlayMode", "Overlay_div_0_Template", "_r1", "Overlay_div_0_Template_div_click_0_listener", "onOverlayClick", "styleClass", "ɵɵpureFunctionV", "visible", "OVERLAY_VALUE_ACCESSOR", "provide", "useExisting", "Overlay", "multi", "showOverlayContentAnimation", "opacity", "hideOverlayContentAnimation", "document", "platformId", "el", "renderer", "config", "overlayService", "cd", "zone", "_visible", "modalVisible", "_mode", "overlayOptions", "merge", "_style", "overlayResponsiveOptions", "_styleClass", "_contentStyle", "_contentStyleClass", "target", "_target", "undefined", "appendTo", "_appendTo", "autoZIndex", "_autoZIndex", "baseZIndex", "_baseZIndex", "_showTransitionOptions", "_hideTransitionOptions", "listener", "_listener", "responsive", "_responsive", "val", "options", "_options", "visibleChange", "onBeforeShow", "onShow", "onBeforeHide", "onHide", "onAnimationStart", "onAnimationDone", "templates", "overlayViewChild", "contentViewChild", "isOverlayClicked", "isOverlayContentClicked", "<PERSON><PERSON><PERSON><PERSON>", "documentClickListener", "documentResizeListener", "documentKeyboardListener", "window", "default", "center", "top", "bottom", "left", "right", "matchMedia", "media", "replace", "breakpoint", "matches", "direction", "overlayEl", "nativeElement", "contentEl", "targetEl", "getTargetElement", "constructor", "defaultView", "ngAfterContentInit", "for<PERSON>ach", "item", "getType", "template", "show", "overlay", "isFocus", "onVisibleChange", "handleEvents", "focus", "addClass", "body", "hide", "removeClass", "alignOverlay", "emit", "event", "add", "originalEvent", "toState", "set", "zIndex", "appendOverlay", "container", "element", "parentElement", "bindListeners", "unbindListeners", "clear", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "bindScrollListener", "bindDocumentClickListener", "bindDocumentResizeListener", "bindDocumentKeyboardListener", "unbindScrollListener", "unbindDocumentClickListener", "unbindDocumentResizeListener", "unbindDocumentKeyboardListener", "valid", "type", "listen", "isTargetClicked", "isSameNode", "contains", "isOutsideClicked", "which", "isTouchDevice", "runOutsideAngular", "hideOnEscape", "code", "run", "ngOnDestroy", "destroy", "ɵfac", "Overlay_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "PrimeNGConfig", "OverlayService", "ChangeDetectorRef", "NgZone", "ɵcmp", "ɵɵdefineComponent", "selectors", "contentQueries", "Overlay_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "viewQuery", "Overlay_Query", "ɵɵviewQuery", "first", "hostAttrs", "inputs", "outputs", "features", "ɵɵProvidersFeature", "ngContentSelectors", "decls", "vars", "consts", "Overlay_Template", "ɵɵprojectionDef", "dependencies", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "styles", "encapsulation", "data", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "animations", "OnPush", "None", "providers", "host", "class", "Document", "decorators", "OverlayModule", "OverlayModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/Code/ASAR/Azure/Public Service/SNJYA-PUBLIC-SERVICE/client/node_modules/primeng/fesm2022/primeng-overlay.mjs"], "sourcesContent": ["import { style, animate, animation, useAnimation, transition, trigger } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport { ObjectUtils, ZIndexUtils } from 'primeng/utils';\n\nconst OVERLAY_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => Overlay),\n    multi: true\n};\nconst showOverlayContentAnimation = animation([style({ transform: '{{transform}}', opacity: 0 }), animate('{{showTransitionParams}}')]);\nconst hideOverlayContentAnimation = animation([animate('{{hideTransitionParams}}', style({ transform: '{{transform}}', opacity: 0 }))]);\n/**\n * This API allows overlay components to be controlled from the PrimeNGConfig. In this way, all overlay components in the application can have the same behavior.\n * @group Components\n */\nclass Overlay {\n    document;\n    platformId;\n    el;\n    renderer;\n    config;\n    overlayService;\n    cd;\n    zone;\n    /**\n     * The visible property is an input that determines the visibility of the component.\n     * @defaultValue false\n     * @group Props\n     */\n    get visible() {\n        return this._visible;\n    }\n    set visible(value) {\n        this._visible = value;\n        if (this._visible && !this.modalVisible) {\n            this.modalVisible = true;\n        }\n    }\n    /**\n     * The mode property is an input that determines the overlay mode type or string.\n     * @defaultValue null\n     * @group Props\n     */\n    get mode() {\n        return this._mode || this.overlayOptions?.mode;\n    }\n    set mode(value) {\n        this._mode = value;\n    }\n    /**\n     * The style property is an input that determines the style object for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get style() {\n        return ObjectUtils.merge(this._style, this.modal ? this.overlayResponsiveOptions?.style : this.overlayOptions?.style);\n    }\n    set style(value) {\n        this._style = value;\n    }\n    /**\n     * The styleClass property is an input that determines the CSS class(es) for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get styleClass() {\n        return ObjectUtils.merge(this._styleClass, this.modal ? this.overlayResponsiveOptions?.styleClass : this.overlayOptions?.styleClass);\n    }\n    set styleClass(value) {\n        this._styleClass = value;\n    }\n    /**\n     * The contentStyle property is an input that determines the style object for the content of the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get contentStyle() {\n        return ObjectUtils.merge(this._contentStyle, this.modal ? this.overlayResponsiveOptions?.contentStyle : this.overlayOptions?.contentStyle);\n    }\n    set contentStyle(value) {\n        this._contentStyle = value;\n    }\n    /**\n     * The contentStyleClass property is an input that determines the CSS class(es) for the content of the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get contentStyleClass() {\n        return ObjectUtils.merge(this._contentStyleClass, this.modal ? this.overlayResponsiveOptions?.contentStyleClass : this.overlayOptions?.contentStyleClass);\n    }\n    set contentStyleClass(value) {\n        this._contentStyleClass = value;\n    }\n    /**\n     * The target property is an input that specifies the target element or selector for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get target() {\n        const value = this._target || this.overlayOptions?.target;\n        return value === undefined ? '@prev' : value;\n    }\n    set target(value) {\n        this._target = value;\n    }\n    /**\n     * Overlay can be mounted into its location, body or DOM element instance using this option.\n     * @defaultValue null\n     * @group Props\n     */\n    get appendTo() {\n        return this._appendTo || this.overlayOptions?.appendTo;\n    }\n    set appendTo(value) {\n        this._appendTo = value;\n    }\n    /**\n     * The autoZIndex determines whether to automatically manage layering. Its default value is 'false'.\n     * @defaultValue false\n     * @group Props\n     */\n    get autoZIndex() {\n        const value = this._autoZIndex || this.overlayOptions?.autoZIndex;\n        return value === undefined ? true : value;\n    }\n    set autoZIndex(value) {\n        this._autoZIndex = value;\n    }\n    /**\n     * The baseZIndex is base zIndex value to use in layering.\n     * @defaultValue null\n     * @group Props\n     */\n    get baseZIndex() {\n        const value = this._baseZIndex || this.overlayOptions?.baseZIndex;\n        return value === undefined ? 0 : value;\n    }\n    set baseZIndex(value) {\n        this._baseZIndex = value;\n    }\n    /**\n     * Transition options of the show or hide animation.\n     * @defaultValue .12s cubic-bezier(0, 0, 0.2, 1)\n     * @group Props\n     */\n    get showTransitionOptions() {\n        const value = this._showTransitionOptions || this.overlayOptions?.showTransitionOptions;\n        return value === undefined ? '.12s cubic-bezier(0, 0, 0.2, 1)' : value;\n    }\n    set showTransitionOptions(value) {\n        this._showTransitionOptions = value;\n    }\n    /**\n     * The hideTransitionOptions property is an input that determines the CSS transition options for hiding the component.\n     * @defaultValue .1s linear\n     * @group Props\n     */\n    get hideTransitionOptions() {\n        const value = this._hideTransitionOptions || this.overlayOptions?.hideTransitionOptions;\n        return value === undefined ? '.1s linear' : value;\n    }\n    set hideTransitionOptions(value) {\n        this._hideTransitionOptions = value;\n    }\n    /**\n     * The listener property is an input that specifies the listener object for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get listener() {\n        return this._listener || this.overlayOptions?.listener;\n    }\n    set listener(value) {\n        this._listener = value;\n    }\n    /**\n     * It is the option used to determine in which mode it should appear according to the given media or breakpoint.\n     * @defaultValue null\n     * @group Props\n     */\n    get responsive() {\n        return this._responsive || this.overlayOptions?.responsive;\n    }\n    set responsive(val) {\n        this._responsive = val;\n    }\n    /**\n     * The options property is an input that specifies the overlay options for the component.\n     * @defaultValue null\n     * @group Props\n     */\n    get options() {\n        return this._options;\n    }\n    set options(val) {\n        this._options = val;\n    }\n    /**\n     * This EventEmitter is used to notify changes in the visibility state of a component.\n     * @param {Boolean} boolean - Value of visibility as boolean.\n     * @group Emits\n     */\n    visibleChange = new EventEmitter();\n    /**\n     * Callback to invoke before the overlay is shown.\n     * @param {OverlayOnBeforeShowEvent} event - Custom overlay before show event.\n     * @group Emits\n     */\n    onBeforeShow = new EventEmitter();\n    /**\n     * Callback to invoke when the overlay is shown.\n     * @param {OverlayOnShowEvent} event - Custom overlay show event.\n     * @group Emits\n     */\n    onShow = new EventEmitter();\n    /**\n     * Callback to invoke before the overlay is hidden.\n     * @param {OverlayOnBeforeHideEvent} event - Custom overlay before hide event.\n     * @group Emits\n     */\n    onBeforeHide = new EventEmitter();\n    /**\n     * Callback to invoke when the overlay is hidden\n     * @param {OverlayOnHideEvent} event - Custom hide event.\n     * @group Emits\n     */\n    onHide = new EventEmitter();\n    /**\n     * Callback to invoke when the animation is started.\n     * @param {AnimationEvent} event - Animation event.\n     * @group Emits\n     */\n    onAnimationStart = new EventEmitter();\n    /**\n     * Callback to invoke when the animation is done.\n     * @param {AnimationEvent} event - Animation event.\n     * @group Emits\n     */\n    onAnimationDone = new EventEmitter();\n    templates;\n    overlayViewChild;\n    contentViewChild;\n    contentTemplate;\n    _visible = false;\n    _mode;\n    _style;\n    _styleClass;\n    _contentStyle;\n    _contentStyleClass;\n    _target;\n    _appendTo;\n    _autoZIndex;\n    _baseZIndex;\n    _showTransitionOptions;\n    _hideTransitionOptions;\n    _listener;\n    _responsive;\n    _options;\n    modalVisible = false;\n    isOverlayClicked = false;\n    isOverlayContentClicked = false;\n    scrollHandler;\n    documentClickListener;\n    documentResizeListener;\n    documentKeyboardListener;\n    window;\n    transformOptions = {\n        default: 'scaleY(0.8)',\n        center: 'scale(0.7)',\n        top: 'translate3d(0px, -100%, 0px)',\n        'top-start': 'translate3d(0px, -100%, 0px)',\n        'top-end': 'translate3d(0px, -100%, 0px)',\n        bottom: 'translate3d(0px, 100%, 0px)',\n        'bottom-start': 'translate3d(0px, 100%, 0px)',\n        'bottom-end': 'translate3d(0px, 100%, 0px)',\n        left: 'translate3d(-100%, 0px, 0px)',\n        'left-start': 'translate3d(-100%, 0px, 0px)',\n        'left-end': 'translate3d(-100%, 0px, 0px)',\n        right: 'translate3d(100%, 0px, 0px)',\n        'right-start': 'translate3d(100%, 0px, 0px)',\n        'right-end': 'translate3d(100%, 0px, 0px)'\n    };\n    get modal() {\n        if (isPlatformBrowser(this.platformId)) {\n            return this.mode === 'modal' || (this.overlayResponsiveOptions && this.window?.matchMedia(this.overlayResponsiveOptions.media?.replace('@media', '') || `(max-width: ${this.overlayResponsiveOptions.breakpoint})`).matches);\n        }\n    }\n    get overlayMode() {\n        return this.mode || (this.modal ? 'modal' : 'overlay');\n    }\n    get overlayOptions() {\n        return { ...this.config?.overlayOptions, ...this.options }; // TODO: Improve performance\n    }\n    get overlayResponsiveOptions() {\n        return { ...this.overlayOptions?.responsive, ...this.responsive }; // TODO: Improve performance\n    }\n    get overlayResponsiveDirection() {\n        return this.overlayResponsiveOptions?.direction || 'center';\n    }\n    get overlayEl() {\n        return this.overlayViewChild?.nativeElement;\n    }\n    get contentEl() {\n        return this.contentViewChild?.nativeElement;\n    }\n    get targetEl() {\n        return DomHandler.getTargetElement(this.target, this.el?.nativeElement);\n    }\n    constructor(document, platformId, el, renderer, config, overlayService, cd, zone) {\n        this.document = document;\n        this.platformId = platformId;\n        this.el = el;\n        this.renderer = renderer;\n        this.config = config;\n        this.overlayService = overlayService;\n        this.cd = cd;\n        this.zone = zone;\n        this.window = this.document.defaultView;\n    }\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                // TODO: new template types may be added.\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    show(overlay, isFocus = false) {\n        this.onVisibleChange(true);\n        this.handleEvents('onShow', { overlay: overlay || this.overlayEl, target: this.targetEl, mode: this.overlayMode });\n        isFocus && DomHandler.focus(this.targetEl);\n        this.modal && DomHandler.addClass(this.document?.body, 'p-overflow-hidden');\n    }\n    hide(overlay, isFocus = false) {\n        if (!this.visible) {\n            return;\n        }\n        else {\n            this.onVisibleChange(false);\n            this.handleEvents('onHide', { overlay: overlay || this.overlayEl, target: this.targetEl, mode: this.overlayMode });\n            isFocus && DomHandler.focus(this.targetEl);\n            this.modal && DomHandler.removeClass(this.document?.body, 'p-overflow-hidden');\n        }\n    }\n    alignOverlay() {\n        !this.modal && DomHandler.alignOverlay(this.overlayEl, this.targetEl, this.appendTo);\n    }\n    onVisibleChange(visible) {\n        this._visible = visible;\n        this.visibleChange.emit(visible);\n    }\n    onOverlayClick() {\n        this.isOverlayClicked = true;\n    }\n    onOverlayContentClick(event) {\n        this.overlayService.add({\n            originalEvent: event,\n            target: this.targetEl\n        });\n        this.isOverlayContentClicked = true;\n    }\n    onOverlayContentAnimationStart(event) {\n        switch (event.toState) {\n            case 'visible':\n                this.handleEvents('onBeforeShow', { overlay: this.overlayEl, target: this.targetEl, mode: this.overlayMode });\n                if (this.autoZIndex) {\n                    ZIndexUtils.set(this.overlayMode, this.overlayEl, this.baseZIndex + this.config?.zIndex[this.overlayMode]);\n                }\n                DomHandler.appendOverlay(this.overlayEl, this.appendTo === 'body' ? this.document.body : this.appendTo, this.appendTo);\n                this.alignOverlay();\n                break;\n            case 'void':\n                this.handleEvents('onBeforeHide', { overlay: this.overlayEl, target: this.targetEl, mode: this.overlayMode });\n                this.modal && DomHandler.addClass(this.overlayEl, 'p-component-overlay-leave');\n                break;\n        }\n        this.handleEvents('onAnimationStart', event);\n    }\n    onOverlayContentAnimationDone(event) {\n        const container = this.overlayEl || event.element.parentElement;\n        switch (event.toState) {\n            case 'visible':\n                this.show(container, true);\n                this.bindListeners();\n                break;\n            case 'void':\n                this.hide(container, true);\n                this.unbindListeners();\n                DomHandler.appendOverlay(this.overlayEl, this.targetEl, this.appendTo);\n                ZIndexUtils.clear(container);\n                this.modalVisible = false;\n                this.cd.markForCheck();\n                break;\n        }\n        this.handleEvents('onAnimationDone', event);\n    }\n    handleEvents(name, params) {\n        this[name].emit(params);\n        this.options && this.options[name] && this.options[name](params);\n        this.config?.overlayOptions && (this.config?.overlayOptions)[name] && (this.config?.overlayOptions)[name](params);\n    }\n    bindListeners() {\n        this.bindScrollListener();\n        this.bindDocumentClickListener();\n        this.bindDocumentResizeListener();\n        this.bindDocumentKeyboardListener();\n    }\n    unbindListeners() {\n        this.unbindScrollListener();\n        this.unbindDocumentClickListener();\n        this.unbindDocumentResizeListener();\n        this.unbindDocumentKeyboardListener();\n    }\n    bindScrollListener() {\n        if (!this.scrollHandler) {\n            this.scrollHandler = new ConnectedOverlayScrollHandler(this.targetEl, (event) => {\n                const valid = this.listener ? this.listener(event, { type: 'scroll', mode: this.overlayMode, valid: true }) : true;\n                valid && this.hide(event, true);\n            });\n        }\n        this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n        if (this.scrollHandler) {\n            this.scrollHandler.unbindScrollListener();\n        }\n    }\n    bindDocumentClickListener() {\n        if (!this.documentClickListener) {\n            this.documentClickListener = this.renderer.listen(this.document, 'click', (event) => {\n                const isTargetClicked = this.targetEl && (this.targetEl.isSameNode(event.target) || (!this.isOverlayClicked && this.targetEl.contains(event.target)));\n                const isOutsideClicked = !isTargetClicked && !this.isOverlayContentClicked;\n                const valid = this.listener ? this.listener(event, { type: 'outside', mode: this.overlayMode, valid: event.which !== 3 && isOutsideClicked }) : isOutsideClicked;\n                valid && this.hide(event);\n                this.isOverlayClicked = this.isOverlayContentClicked = false;\n            });\n        }\n    }\n    unbindDocumentClickListener() {\n        if (this.documentClickListener) {\n            this.documentClickListener();\n            this.documentClickListener = null;\n        }\n    }\n    bindDocumentResizeListener() {\n        if (!this.documentResizeListener) {\n            this.documentResizeListener = this.renderer.listen(this.window, 'resize', (event) => {\n                const valid = this.listener ? this.listener(event, { type: 'resize', mode: this.overlayMode, valid: !DomHandler.isTouchDevice() }) : !DomHandler.isTouchDevice();\n                valid && this.hide(event, true);\n            });\n        }\n    }\n    unbindDocumentResizeListener() {\n        if (this.documentResizeListener) {\n            this.documentResizeListener();\n            this.documentResizeListener = null;\n        }\n    }\n    bindDocumentKeyboardListener() {\n        if (this.documentKeyboardListener) {\n            return;\n        }\n        this.zone.runOutsideAngular(() => {\n            this.documentKeyboardListener = this.renderer.listen(this.window, 'keydown', (event) => {\n                if (this.overlayOptions.hideOnEscape === false || event.code !== 'Escape') {\n                    return;\n                }\n                const valid = this.listener ? this.listener(event, { type: 'keydown', mode: this.overlayMode, valid: !DomHandler.isTouchDevice() }) : !DomHandler.isTouchDevice();\n                if (valid) {\n                    this.zone.run(() => {\n                        this.hide(event, true);\n                    });\n                }\n            });\n        });\n    }\n    unbindDocumentKeyboardListener() {\n        if (this.documentKeyboardListener) {\n            this.documentKeyboardListener();\n            this.documentKeyboardListener = null;\n        }\n    }\n    ngOnDestroy() {\n        this.hide(this.overlayEl, true);\n        if (this.overlayEl) {\n            DomHandler.appendOverlay(this.overlayEl, this.targetEl, this.appendTo);\n            ZIndexUtils.clear(this.overlayEl);\n        }\n        if (this.scrollHandler) {\n            this.scrollHandler.destroy();\n            this.scrollHandler = null;\n        }\n        this.unbindListeners();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Overlay, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i1.PrimeNGConfig }, { token: i1.OverlayService }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: Overlay, selector: \"p-overlay\", inputs: { visible: \"visible\", mode: \"mode\", style: \"style\", styleClass: \"styleClass\", contentStyle: \"contentStyle\", contentStyleClass: \"contentStyleClass\", target: \"target\", appendTo: \"appendTo\", autoZIndex: \"autoZIndex\", baseZIndex: \"baseZIndex\", showTransitionOptions: \"showTransitionOptions\", hideTransitionOptions: \"hideTransitionOptions\", listener: \"listener\", responsive: \"responsive\", options: \"options\" }, outputs: { visibleChange: \"visibleChange\", onBeforeShow: \"onBeforeShow\", onShow: \"onShow\", onBeforeHide: \"onBeforeHide\", onHide: \"onHide\", onAnimationStart: \"onAnimationStart\", onAnimationDone: \"onAnimationDone\" }, host: { classAttribute: \"p-element\" }, providers: [OVERLAY_VALUE_ACCESSOR], queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"overlayViewChild\", first: true, predicate: [\"overlay\"], descendants: true }, { propertyName: \"contentViewChild\", first: true, predicate: [\"content\"], descendants: true }], ngImport: i0, template: `\n        <div\n            *ngIf=\"modalVisible\"\n            #overlay\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [ngClass]=\"{\n                'p-overlay p-component': true,\n                'p-overlay-modal p-component-overlay p-component-overlay-enter': modal,\n                'p-overlay-center': modal && overlayResponsiveDirection === 'center',\n                'p-overlay-top': modal && overlayResponsiveDirection === 'top',\n                'p-overlay-top-start': modal && overlayResponsiveDirection === 'top-start',\n                'p-overlay-top-end': modal && overlayResponsiveDirection === 'top-end',\n                'p-overlay-bottom': modal && overlayResponsiveDirection === 'bottom',\n                'p-overlay-bottom-start': modal && overlayResponsiveDirection === 'bottom-start',\n                'p-overlay-bottom-end': modal && overlayResponsiveDirection === 'bottom-end',\n                'p-overlay-left': modal && overlayResponsiveDirection === 'left',\n                'p-overlay-left-start': modal && overlayResponsiveDirection === 'left-start',\n                'p-overlay-left-end': modal && overlayResponsiveDirection === 'left-end',\n                'p-overlay-right': modal && overlayResponsiveDirection === 'right',\n                'p-overlay-right-start': modal && overlayResponsiveDirection === 'right-start',\n                'p-overlay-right-end': modal && overlayResponsiveDirection === 'right-end'\n            }\"\n            (click)=\"onOverlayClick()\"\n        >\n            <div\n                *ngIf=\"visible\"\n                #content\n                [ngStyle]=\"contentStyle\"\n                [class]=\"contentStyleClass\"\n                [ngClass]=\"'p-overlay-content'\"\n                (click)=\"onOverlayContentClick($event)\"\n                [@overlayContentAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions, transform: transformOptions[modal ? overlayResponsiveDirection : 'default'] } }\"\n                (@overlayContentAnimation.start)=\"onOverlayContentAnimationStart($event)\"\n                (@overlayContentAnimation.done)=\"onOverlayContentAnimationDone($event)\"\n            >\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: { mode: overlayMode } }\"></ng-container>\n            </div>\n        </div>\n    `, isInline: true, styles: [\"@layer primeng{.p-overlay{position:absolute;top:0;left:0}.p-overlay-modal{display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100%;height:100%}.p-overlay-content{transform-origin:inherit}.p-overlay-modal>.p-overlay-content{z-index:1;width:90%}.p-overlay-top{align-items:flex-start}.p-overlay-top-start{align-items:flex-start;justify-content:flex-start}.p-overlay-top-end{align-items:flex-start;justify-content:flex-end}.p-overlay-bottom{align-items:flex-end}.p-overlay-bottom-start{align-items:flex-end;justify-content:flex-start}.p-overlay-bottom-end{align-items:flex-end;justify-content:flex-end}.p-overlay-left{justify-content:flex-start}.p-overlay-left-start{justify-content:flex-start;align-items:flex-start}.p-overlay-left-end{justify-content:flex-start;align-items:flex-end}.p-overlay-right{justify-content:flex-end}.p-overlay-right-start{justify-content:flex-end;align-items:flex-start}.p-overlay-right-end{justify-content:flex-end;align-items:flex-end}}\\n\"], dependencies: [{ kind: \"directive\", type: i2.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i2.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], animations: [trigger('overlayContentAnimation', [transition(':enter', [useAnimation(showOverlayContentAnimation)]), transition(':leave', [useAnimation(hideOverlayContentAnimation)])])], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Overlay, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-overlay', template: `\n        <div\n            *ngIf=\"modalVisible\"\n            #overlay\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            [ngClass]=\"{\n                'p-overlay p-component': true,\n                'p-overlay-modal p-component-overlay p-component-overlay-enter': modal,\n                'p-overlay-center': modal && overlayResponsiveDirection === 'center',\n                'p-overlay-top': modal && overlayResponsiveDirection === 'top',\n                'p-overlay-top-start': modal && overlayResponsiveDirection === 'top-start',\n                'p-overlay-top-end': modal && overlayResponsiveDirection === 'top-end',\n                'p-overlay-bottom': modal && overlayResponsiveDirection === 'bottom',\n                'p-overlay-bottom-start': modal && overlayResponsiveDirection === 'bottom-start',\n                'p-overlay-bottom-end': modal && overlayResponsiveDirection === 'bottom-end',\n                'p-overlay-left': modal && overlayResponsiveDirection === 'left',\n                'p-overlay-left-start': modal && overlayResponsiveDirection === 'left-start',\n                'p-overlay-left-end': modal && overlayResponsiveDirection === 'left-end',\n                'p-overlay-right': modal && overlayResponsiveDirection === 'right',\n                'p-overlay-right-start': modal && overlayResponsiveDirection === 'right-start',\n                'p-overlay-right-end': modal && overlayResponsiveDirection === 'right-end'\n            }\"\n            (click)=\"onOverlayClick()\"\n        >\n            <div\n                *ngIf=\"visible\"\n                #content\n                [ngStyle]=\"contentStyle\"\n                [class]=\"contentStyleClass\"\n                [ngClass]=\"'p-overlay-content'\"\n                (click)=\"onOverlayContentClick($event)\"\n                [@overlayContentAnimation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions, transform: transformOptions[modal ? overlayResponsiveDirection : 'default'] } }\"\n                (@overlayContentAnimation.start)=\"onOverlayContentAnimationStart($event)\"\n                (@overlayContentAnimation.done)=\"onOverlayContentAnimationDone($event)\"\n            >\n                <ng-content></ng-content>\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: { mode: overlayMode } }\"></ng-container>\n            </div>\n        </div>\n    `, animations: [trigger('overlayContentAnimation', [transition(':enter', [useAnimation(showOverlayContentAnimation)]), transition(':leave', [useAnimation(hideOverlayContentAnimation)])])], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, providers: [OVERLAY_VALUE_ACCESSOR], host: {\n                        class: 'p-element'\n                    }, styles: [\"@layer primeng{.p-overlay{position:absolute;top:0;left:0}.p-overlay-modal{display:flex;align-items:center;justify-content:center;position:fixed;top:0;left:0;width:100%;height:100%}.p-overlay-content{transform-origin:inherit}.p-overlay-modal>.p-overlay-content{z-index:1;width:90%}.p-overlay-top{align-items:flex-start}.p-overlay-top-start{align-items:flex-start;justify-content:flex-start}.p-overlay-top-end{align-items:flex-start;justify-content:flex-end}.p-overlay-bottom{align-items:flex-end}.p-overlay-bottom-start{align-items:flex-end;justify-content:flex-start}.p-overlay-bottom-end{align-items:flex-end;justify-content:flex-end}.p-overlay-left{justify-content:flex-start}.p-overlay-left-start{justify-content:flex-start;align-items:flex-start}.p-overlay-left-end{justify-content:flex-start;align-items:flex-end}.p-overlay-right{justify-content:flex-end}.p-overlay-right-start{justify-content:flex-end;align-items:flex-start}.p-overlay-right-end{justify-content:flex-end;align-items:flex-end}}\\n\"] }]\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i1.PrimeNGConfig }, { type: i1.OverlayService }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }], propDecorators: { visible: [{\n                type: Input\n            }], mode: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], contentStyle: [{\n                type: Input\n            }], contentStyleClass: [{\n                type: Input\n            }], target: [{\n                type: Input\n            }], appendTo: [{\n                type: Input\n            }], autoZIndex: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], showTransitionOptions: [{\n                type: Input\n            }], hideTransitionOptions: [{\n                type: Input\n            }], listener: [{\n                type: Input\n            }], responsive: [{\n                type: Input\n            }], options: [{\n                type: Input\n            }], visibleChange: [{\n                type: Output\n            }], onBeforeShow: [{\n                type: Output\n            }], onShow: [{\n                type: Output\n            }], onBeforeHide: [{\n                type: Output\n            }], onHide: [{\n                type: Output\n            }], onAnimationStart: [{\n                type: Output\n            }], onAnimationDone: [{\n                type: Output\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], overlayViewChild: [{\n                type: ViewChild,\n                args: ['overlay']\n            }], contentViewChild: [{\n                type: ViewChild,\n                args: ['content']\n            }] } });\nclass OverlayModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: OverlayModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: OverlayModule, declarations: [Overlay], imports: [CommonModule, SharedModule], exports: [Overlay, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: OverlayModule, imports: [CommonModule, SharedModule, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: OverlayModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, SharedModule],\n                    exports: [Overlay, SharedModule],\n                    declarations: [Overlay]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { OVERLAY_VALUE_ACCESSOR, Overlay, OverlayModule };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,OAAO,EAAEC,SAAS,EAAEC,YAAY,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAClG,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACzL,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,OAAO,KAAKC,EAAE,MAAM,aAAa;AACjC,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,SAASC,UAAU,EAAEC,6BAA6B,QAAQ,aAAa;AACvE,SAASC,WAAW,EAAEC,WAAW,QAAQ,eAAe;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA;EAAA,iEAAAb,EAAA;EAAA,oBAAAC,EAAA;EAAA,iBAAAC,EAAA;EAAA,uBAAAC,EAAA;EAAA,qBAAAC,EAAA;EAAA,oBAAAC,EAAA;EAAA,0BAAAC,EAAA;EAAA,wBAAAC,EAAA;EAAA,kBAAAC,EAAA;EAAA,wBAAAC,EAAA;EAAA,sBAAAC,GAAA;EAAA,mBAAAC,GAAA;EAAA,yBAAAC,GAAA;EAAA,uBAAAC;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAd,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAAAa,oBAAA,EAAAf,EAAA;EAAAgB,oBAAA,EAAAf,EAAA;EAAAgB,SAAA,EAAAf;AAAA;AAAA,MAAAgB,GAAA,GAAAlB,EAAA;EAAAmB,KAAA;EAAAC,MAAA,EAAApB;AAAA;AAAA,MAAAqB,GAAA,GAAArB,EAAA;EAAAsB,IAAA,EAAAtB;AAAA;AAAA,MAAAuB,GAAA,GAAAvB,EAAA;EAAAwB,SAAA,EAAAxB;AAAA;AAAA,SAAAyB,4CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAifoCnD,EAAE,CAAAqD,kBAAA,EAsCgC,CAAC;EAAA;AAAA;AAAA,SAAAC,6BAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAI,GAAA,GAtCnCvD,EAAE,CAAAwD,gBAAA;IAAFxD,EAAE,CAAAyD,cAAA,eAoCnF,CAAC;IApCgFzD,EAAE,CAAA0D,UAAA,mBAAAC,kDAAAC,MAAA;MAAF5D,EAAE,CAAA6D,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAF9D,EAAE,CAAA+D,aAAA;MAAA,OAAF/D,EAAE,CAAAgE,WAAA,CAgCtEF,MAAA,CAAAG,qBAAA,CAAAL,MAA4B,CAAC;IAAA,EAAC,4CAAAM,oFAAAN,MAAA;MAhCsC5D,EAAE,CAAA6D,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAF9D,EAAE,CAAA+D,aAAA;MAAA,OAAF/D,EAAE,CAAAgE,WAAA,CAkC7CF,MAAA,CAAAK,8BAAA,CAAAP,MAAqC,CAAC;IAAA,EAAC,2CAAAQ,mFAAAR,MAAA;MAlCI5D,EAAE,CAAA6D,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAF9D,EAAE,CAAA+D,aAAA;MAAA,OAAF/D,EAAE,CAAAgE,WAAA,CAmC9CF,MAAA,CAAAO,6BAAA,CAAAT,MAAoC,CAAC;IAAA,EAAC;IAnCM5D,EAAE,CAAAsE,YAAA,EAqCvD,CAAC;IArCoDtE,EAAE,CAAAuE,UAAA,IAAArB,2CAAA,yBAsCiB,CAAC;IAtCpBlD,EAAE,CAAAwE,YAAA,CAuC9E,CAAC;EAAA;EAAA,IAAArB,EAAA;IAAA,MAAAW,MAAA,GAvC2E9D,EAAE,CAAA+D,aAAA;IAAF/D,EAAE,CAAAyE,UAAA,CAAAX,MAAA,CAAAY,iBA8BrD,CAAC;IA9BkD1E,EAAE,CAAA2E,UAAA,YAAAb,MAAA,CAAAc,YA6BxD,CAAC,+BAEM,CAAC,6BA/B8C5E,EAAE,CAAA6E,eAAA,KAAAlC,GAAA,EAAF3C,EAAE,CAAA8E,eAAA,IAAAvC,GAAA,EAAAuB,MAAA,CAAAiB,qBAAA,EAAAjB,MAAA,CAAAkB,qBAAA,EAAAlB,MAAA,CAAAmB,gBAAA,CAAAnB,MAAA,CAAAoB,KAAA,GAAApB,MAAA,CAAAqB,0BAAA,eAiCoJ,CAAC;IAjCvJnF,EAAE,CAAAoF,SAAA,EAsC9B,CAAC;IAtC2BpF,EAAE,CAAA2E,UAAA,qBAAAb,MAAA,CAAAuB,eAsC9B,CAAC,4BAtC2BrF,EAAE,CAAA6E,eAAA,KAAA7B,GAAA,EAAFhD,EAAE,CAAA6E,eAAA,KAAA/B,GAAA,EAAAgB,MAAA,CAAAwB,WAAA,EAsCe,CAAC;EAAA;AAAA;AAAA,SAAAC,uBAAApC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAqC,GAAA,GAtClBxF,EAAE,CAAAwD,gBAAA;IAAFxD,EAAE,CAAAyD,cAAA,eAyBvF,CAAC;IAzBoFzD,EAAE,CAAA0D,UAAA,mBAAA+B,4CAAA;MAAFzF,EAAE,CAAA6D,aAAA,CAAA2B,GAAA;MAAA,MAAA1B,MAAA,GAAF9D,EAAE,CAAA+D,aAAA;MAAA,OAAF/D,EAAE,CAAAgE,WAAA,CAwB1EF,MAAA,CAAA4B,cAAA,CAAe,CAAC;IAAA,EAAC;IAxBuD1F,EAAE,CAAAuE,UAAA,IAAAjB,4BAAA,iBAoCnF,CAAC;IApCgFtD,EAAE,CAAAwE,YAAA,CAwClF,CAAC;EAAA;EAAA,IAAArB,EAAA;IAAA,MAAAW,MAAA,GAxC+E9D,EAAE,CAAA+D,aAAA;IAAF/D,EAAE,CAAAyE,UAAA,CAAAX,MAAA,CAAA6B,UAMhE,CAAC;IAN6D3F,EAAE,CAAA2E,UAAA,YAAAb,MAAA,CAAAxE,KAKnE,CAAC,YALgEU,EAAE,CAAA4F,eAAA,IAAApE,GAAA,GAAAsC,MAAA,CAAAoB,KAAA,EAAApB,MAAA,CAAAoB,KAAA,IAAApB,MAAA,CAAAqB,0BAAA,eAAArB,MAAA,CAAAoB,KAAA,IAAApB,MAAA,CAAAqB,0BAAA,YAAArB,MAAA,CAAAoB,KAAA,IAAApB,MAAA,CAAAqB,0BAAA,kBAAArB,MAAA,CAAAoB,KAAA,IAAApB,MAAA,CAAAqB,0BAAA,gBAAArB,MAAA,CAAAoB,KAAA,IAAApB,MAAA,CAAAqB,0BAAA,eAAArB,MAAA,CAAAoB,KAAA,IAAApB,MAAA,CAAAqB,0BAAA,qBAAArB,MAAA,CAAAoB,KAAA,IAAApB,MAAA,CAAAqB,0BAAA,mBAAArB,MAAA,CAAAoB,KAAA,IAAApB,MAAA,CAAAqB,0BAAA,aAAArB,MAAA,CAAAoB,KAAA,IAAApB,MAAA,CAAAqB,0BAAA,mBAAArB,MAAA,CAAAoB,KAAA,IAAApB,MAAA,CAAAqB,0BAAA,iBAAArB,MAAA,CAAAoB,KAAA,IAAApB,MAAA,CAAAqB,0BAAA,cAAArB,MAAA,CAAAoB,KAAA,IAAApB,MAAA,CAAAqB,0BAAA,oBAAArB,MAAA,CAAAoB,KAAA,IAAApB,MAAA,CAAAqB,0BAAA,kBAuBlF,CAAC;IAvB+EnF,EAAE,CAAAoF,SAAA,EA2BlE,CAAC;IA3B+DpF,EAAE,CAAA2E,UAAA,SAAAb,MAAA,CAAA+B,OA2BlE,CAAC;EAAA;AAAA;AA1gB9B,MAAMC,sBAAsB,GAAG;EAC3BC,OAAO,EAAElF,iBAAiB;EAC1BmF,WAAW,EAAE/F,UAAU,CAAC,MAAMgG,OAAO,CAAC;EACtCC,KAAK,EAAE;AACX,CAAC;AACD,MAAMC,2BAA2B,GAAG3G,SAAS,CAAC,CAACF,KAAK,CAAC;EAAEoD,SAAS,EAAE,eAAe;EAAE0D,OAAO,EAAE;AAAE,CAAC,CAAC,EAAE7G,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC;AACvI,MAAM8G,2BAA2B,GAAG7G,SAAS,CAAC,CAACD,OAAO,CAAC,0BAA0B,EAAED,KAAK,CAAC;EAAEoD,SAAS,EAAE,eAAe;EAAE0D,OAAO,EAAE;AAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACvI;AACA;AACA;AACA;AACA,MAAMH,OAAO,CAAC;EACVK,QAAQ;EACRC,UAAU;EACVC,EAAE;EACFC,QAAQ;EACRC,MAAM;EACNC,cAAc;EACdC,EAAE;EACFC,IAAI;EACJ;AACJ;AACA;AACA;AACA;EACI,IAAIhB,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACiB,QAAQ;EACxB;EACA,IAAIjB,OAAOA,CAACjD,KAAK,EAAE;IACf,IAAI,CAACkE,QAAQ,GAAGlE,KAAK;IACrB,IAAI,IAAI,CAACkE,QAAQ,IAAI,CAAC,IAAI,CAACC,YAAY,EAAE;MACrC,IAAI,CAACA,YAAY,GAAG,IAAI;IAC5B;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIhE,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACiE,KAAK,IAAI,IAAI,CAACC,cAAc,EAAElE,IAAI;EAClD;EACA,IAAIA,IAAIA,CAACH,KAAK,EAAE;IACZ,IAAI,CAACoE,KAAK,GAAGpE,KAAK;EACtB;EACA;AACJ;AACA;AACA;AACA;EACI,IAAItD,KAAKA,CAAA,EAAG;IACR,OAAO6B,WAAW,CAAC+F,KAAK,CAAC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACjC,KAAK,GAAG,IAAI,CAACkC,wBAAwB,EAAE9H,KAAK,GAAG,IAAI,CAAC2H,cAAc,EAAE3H,KAAK,CAAC;EACzH;EACA,IAAIA,KAAKA,CAACsD,KAAK,EAAE;IACb,IAAI,CAACuE,MAAM,GAAGvE,KAAK;EACvB;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI+C,UAAUA,CAAA,EAAG;IACb,OAAOxE,WAAW,CAAC+F,KAAK,CAAC,IAAI,CAACG,WAAW,EAAE,IAAI,CAACnC,KAAK,GAAG,IAAI,CAACkC,wBAAwB,EAAEzB,UAAU,GAAG,IAAI,CAACsB,cAAc,EAAEtB,UAAU,CAAC;EACxI;EACA,IAAIA,UAAUA,CAAC/C,KAAK,EAAE;IAClB,IAAI,CAACyE,WAAW,GAAGzE,KAAK;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIgC,YAAYA,CAAA,EAAG;IACf,OAAOzD,WAAW,CAAC+F,KAAK,CAAC,IAAI,CAACI,aAAa,EAAE,IAAI,CAACpC,KAAK,GAAG,IAAI,CAACkC,wBAAwB,EAAExC,YAAY,GAAG,IAAI,CAACqC,cAAc,EAAErC,YAAY,CAAC;EAC9I;EACA,IAAIA,YAAYA,CAAChC,KAAK,EAAE;IACpB,IAAI,CAAC0E,aAAa,GAAG1E,KAAK;EAC9B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI8B,iBAAiBA,CAAA,EAAG;IACpB,OAAOvD,WAAW,CAAC+F,KAAK,CAAC,IAAI,CAACK,kBAAkB,EAAE,IAAI,CAACrC,KAAK,GAAG,IAAI,CAACkC,wBAAwB,EAAE1C,iBAAiB,GAAG,IAAI,CAACuC,cAAc,EAAEvC,iBAAiB,CAAC;EAC7J;EACA,IAAIA,iBAAiBA,CAAC9B,KAAK,EAAE;IACzB,IAAI,CAAC2E,kBAAkB,GAAG3E,KAAK;EACnC;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI4E,MAAMA,CAAA,EAAG;IACT,MAAM5E,KAAK,GAAG,IAAI,CAAC6E,OAAO,IAAI,IAAI,CAACR,cAAc,EAAEO,MAAM;IACzD,OAAO5E,KAAK,KAAK8E,SAAS,GAAG,OAAO,GAAG9E,KAAK;EAChD;EACA,IAAI4E,MAAMA,CAAC5E,KAAK,EAAE;IACd,IAAI,CAAC6E,OAAO,GAAG7E,KAAK;EACxB;EACA;AACJ;AACA;AACA;AACA;EACI,IAAI+E,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAI,IAAI,CAACX,cAAc,EAAEU,QAAQ;EAC1D;EACA,IAAIA,QAAQA,CAAC/E,KAAK,EAAE;IAChB,IAAI,CAACgF,SAAS,GAAGhF,KAAK;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIiF,UAAUA,CAAA,EAAG;IACb,MAAMjF,KAAK,GAAG,IAAI,CAACkF,WAAW,IAAI,IAAI,CAACb,cAAc,EAAEY,UAAU;IACjE,OAAOjF,KAAK,KAAK8E,SAAS,GAAG,IAAI,GAAG9E,KAAK;EAC7C;EACA,IAAIiF,UAAUA,CAACjF,KAAK,EAAE;IAClB,IAAI,CAACkF,WAAW,GAAGlF,KAAK;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAImF,UAAUA,CAAA,EAAG;IACb,MAAMnF,KAAK,GAAG,IAAI,CAACoF,WAAW,IAAI,IAAI,CAACf,cAAc,EAAEc,UAAU;IACjE,OAAOnF,KAAK,KAAK8E,SAAS,GAAG,CAAC,GAAG9E,KAAK;EAC1C;EACA,IAAImF,UAAUA,CAACnF,KAAK,EAAE;IAClB,IAAI,CAACoF,WAAW,GAAGpF,KAAK;EAC5B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAImC,qBAAqBA,CAAA,EAAG;IACxB,MAAMnC,KAAK,GAAG,IAAI,CAACqF,sBAAsB,IAAI,IAAI,CAAChB,cAAc,EAAElC,qBAAqB;IACvF,OAAOnC,KAAK,KAAK8E,SAAS,GAAG,iCAAiC,GAAG9E,KAAK;EAC1E;EACA,IAAImC,qBAAqBA,CAACnC,KAAK,EAAE;IAC7B,IAAI,CAACqF,sBAAsB,GAAGrF,KAAK;EACvC;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIoC,qBAAqBA,CAAA,EAAG;IACxB,MAAMpC,KAAK,GAAG,IAAI,CAACsF,sBAAsB,IAAI,IAAI,CAACjB,cAAc,EAAEjC,qBAAqB;IACvF,OAAOpC,KAAK,KAAK8E,SAAS,GAAG,YAAY,GAAG9E,KAAK;EACrD;EACA,IAAIoC,qBAAqBA,CAACpC,KAAK,EAAE;IAC7B,IAAI,CAACsF,sBAAsB,GAAGtF,KAAK;EACvC;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIuF,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAI,IAAI,CAACnB,cAAc,EAAEkB,QAAQ;EAC1D;EACA,IAAIA,QAAQA,CAACvF,KAAK,EAAE;IAChB,IAAI,CAACwF,SAAS,GAAGxF,KAAK;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIyF,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW,IAAI,IAAI,CAACrB,cAAc,EAAEoB,UAAU;EAC9D;EACA,IAAIA,UAAUA,CAACE,GAAG,EAAE;IAChB,IAAI,CAACD,WAAW,GAAGC,GAAG;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACD,GAAG,EAAE;IACb,IAAI,CAACE,QAAQ,GAAGF,GAAG;EACvB;EACA;AACJ;AACA;AACA;AACA;EACIG,aAAa,GAAG,IAAIxI,YAAY,CAAC,CAAC;EAClC;AACJ;AACA;AACA;AACA;EACIyI,YAAY,GAAG,IAAIzI,YAAY,CAAC,CAAC;EACjC;AACJ;AACA;AACA;AACA;EACI0I,MAAM,GAAG,IAAI1I,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;AACA;EACI2I,YAAY,GAAG,IAAI3I,YAAY,CAAC,CAAC;EACjC;AACJ;AACA;AACA;AACA;EACI4I,MAAM,GAAG,IAAI5I,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;AACA;EACI6I,gBAAgB,GAAG,IAAI7I,YAAY,CAAC,CAAC;EACrC;AACJ;AACA;AACA;AACA;EACI8I,eAAe,GAAG,IAAI9I,YAAY,CAAC,CAAC;EACpC+I,SAAS;EACTC,gBAAgB;EAChBC,gBAAgB;EAChB9D,eAAe;EACfyB,QAAQ,GAAG,KAAK;EAChBE,KAAK;EACLG,MAAM;EACNE,WAAW;EACXC,aAAa;EACbC,kBAAkB;EAClBE,OAAO;EACPG,SAAS;EACTE,WAAW;EACXE,WAAW;EACXC,sBAAsB;EACtBC,sBAAsB;EACtBE,SAAS;EACTE,WAAW;EACXG,QAAQ;EACR1B,YAAY,GAAG,KAAK;EACpBqC,gBAAgB,GAAG,KAAK;EACxBC,uBAAuB,GAAG,KAAK;EAC/BC,aAAa;EACbC,qBAAqB;EACrBC,sBAAsB;EACtBC,wBAAwB;EACxBC,MAAM;EACNzE,gBAAgB,GAAG;IACf0E,OAAO,EAAE,aAAa;IACtBC,MAAM,EAAE,YAAY;IACpBC,GAAG,EAAE,8BAA8B;IACnC,WAAW,EAAE,8BAA8B;IAC3C,SAAS,EAAE,8BAA8B;IACzCC,MAAM,EAAE,6BAA6B;IACrC,cAAc,EAAE,6BAA6B;IAC7C,YAAY,EAAE,6BAA6B;IAC3CC,IAAI,EAAE,8BAA8B;IACpC,YAAY,EAAE,8BAA8B;IAC5C,UAAU,EAAE,8BAA8B;IAC1CC,KAAK,EAAE,6BAA6B;IACpC,aAAa,EAAE,6BAA6B;IAC5C,WAAW,EAAE;EACjB,CAAC;EACD,IAAI9E,KAAKA,CAAA,EAAG;IACR,IAAIrF,iBAAiB,CAAC,IAAI,CAAC0G,UAAU,CAAC,EAAE;MACpC,OAAO,IAAI,CAACxD,IAAI,KAAK,OAAO,IAAK,IAAI,CAACqE,wBAAwB,IAAI,IAAI,CAACsC,MAAM,EAAEO,UAAU,CAAC,IAAI,CAAC7C,wBAAwB,CAAC8C,KAAK,EAAEC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAK,eAAc,IAAI,CAAC/C,wBAAwB,CAACgD,UAAW,GAAE,CAAC,CAACC,OAAQ;IAChO;EACJ;EACA,IAAI/E,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACvC,IAAI,KAAK,IAAI,CAACmC,KAAK,GAAG,OAAO,GAAG,SAAS,CAAC;EAC1D;EACA,IAAI+B,cAAcA,CAAA,EAAG;IACjB,OAAO;MAAE,GAAG,IAAI,CAACP,MAAM,EAAEO,cAAc;MAAE,GAAG,IAAI,CAACuB;IAAQ,CAAC,CAAC,CAAC;EAChE;EACA,IAAIpB,wBAAwBA,CAAA,EAAG;IAC3B,OAAO;MAAE,GAAG,IAAI,CAACH,cAAc,EAAEoB,UAAU;MAAE,GAAG,IAAI,CAACA;IAAW,CAAC,CAAC,CAAC;EACvE;EACA,IAAIlD,0BAA0BA,CAAA,EAAG;IAC7B,OAAO,IAAI,CAACiC,wBAAwB,EAAEkD,SAAS,IAAI,QAAQ;EAC/D;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACrB,gBAAgB,EAAEsB,aAAa;EAC/C;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACtB,gBAAgB,EAAEqB,aAAa;EAC/C;EACA,IAAIE,QAAQA,CAAA,EAAG;IACX,OAAOzJ,UAAU,CAAC0J,gBAAgB,CAAC,IAAI,CAACnD,MAAM,EAAE,IAAI,CAAChB,EAAE,EAAEgE,aAAa,CAAC;EAC3E;EACAI,WAAWA,CAACtE,QAAQ,EAAEC,UAAU,EAAEC,EAAE,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,cAAc,EAAEC,EAAE,EAAEC,IAAI,EAAE;IAC9E,IAAI,CAACP,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC6C,MAAM,GAAG,IAAI,CAACpD,QAAQ,CAACuE,WAAW;EAC3C;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC7B,SAAS,EAAE8B,OAAO,CAAEC,IAAI,IAAK;MAC9B,QAAQA,IAAI,CAACC,OAAO,CAAC,CAAC;QAClB,KAAK,SAAS;UACV,IAAI,CAAC5F,eAAe,GAAG2F,IAAI,CAACE,QAAQ;UACpC;QACJ;QACA;UACI,IAAI,CAAC7F,eAAe,GAAG2F,IAAI,CAACE,QAAQ;UACpC;MACR;IACJ,CAAC,CAAC;EACN;EACAC,IAAIA,CAACC,OAAO,EAAEC,OAAO,GAAG,KAAK,EAAE;IAC3B,IAAI,CAACC,eAAe,CAAC,IAAI,CAAC;IAC1B,IAAI,CAACC,YAAY,CAAC,QAAQ,EAAE;MAAEH,OAAO,EAAEA,OAAO,IAAI,IAAI,CAACb,SAAS;MAAE/C,MAAM,EAAE,IAAI,CAACkD,QAAQ;MAAE3H,IAAI,EAAE,IAAI,CAACuC;IAAY,CAAC,CAAC;IAClH+F,OAAO,IAAIpK,UAAU,CAACuK,KAAK,CAAC,IAAI,CAACd,QAAQ,CAAC;IAC1C,IAAI,CAACxF,KAAK,IAAIjE,UAAU,CAACwK,QAAQ,CAAC,IAAI,CAACnF,QAAQ,EAAEoF,IAAI,EAAE,mBAAmB,CAAC;EAC/E;EACAC,IAAIA,CAACP,OAAO,EAAEC,OAAO,GAAG,KAAK,EAAE;IAC3B,IAAI,CAAC,IAAI,CAACxF,OAAO,EAAE;MACf;IACJ,CAAC,MACI;MACD,IAAI,CAACyF,eAAe,CAAC,KAAK,CAAC;MAC3B,IAAI,CAACC,YAAY,CAAC,QAAQ,EAAE;QAAEH,OAAO,EAAEA,OAAO,IAAI,IAAI,CAACb,SAAS;QAAE/C,MAAM,EAAE,IAAI,CAACkD,QAAQ;QAAE3H,IAAI,EAAE,IAAI,CAACuC;MAAY,CAAC,CAAC;MAClH+F,OAAO,IAAIpK,UAAU,CAACuK,KAAK,CAAC,IAAI,CAACd,QAAQ,CAAC;MAC1C,IAAI,CAACxF,KAAK,IAAIjE,UAAU,CAAC2K,WAAW,CAAC,IAAI,CAACtF,QAAQ,EAAEoF,IAAI,EAAE,mBAAmB,CAAC;IAClF;EACJ;EACAG,YAAYA,CAAA,EAAG;IACX,CAAC,IAAI,CAAC3G,KAAK,IAAIjE,UAAU,CAAC4K,YAAY,CAAC,IAAI,CAACtB,SAAS,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAAC/C,QAAQ,CAAC;EACxF;EACA2D,eAAeA,CAACzF,OAAO,EAAE;IACrB,IAAI,CAACiB,QAAQ,GAAGjB,OAAO;IACvB,IAAI,CAAC6C,aAAa,CAACoD,IAAI,CAACjG,OAAO,CAAC;EACpC;EACAH,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC0D,gBAAgB,GAAG,IAAI;EAChC;EACAnF,qBAAqBA,CAAC8H,KAAK,EAAE;IACzB,IAAI,CAACpF,cAAc,CAACqF,GAAG,CAAC;MACpBC,aAAa,EAAEF,KAAK;MACpBvE,MAAM,EAAE,IAAI,CAACkD;IACjB,CAAC,CAAC;IACF,IAAI,CAACrB,uBAAuB,GAAG,IAAI;EACvC;EACAlF,8BAA8BA,CAAC4H,KAAK,EAAE;IAClC,QAAQA,KAAK,CAACG,OAAO;MACjB,KAAK,SAAS;QACV,IAAI,CAACX,YAAY,CAAC,cAAc,EAAE;UAAEH,OAAO,EAAE,IAAI,CAACb,SAAS;UAAE/C,MAAM,EAAE,IAAI,CAACkD,QAAQ;UAAE3H,IAAI,EAAE,IAAI,CAACuC;QAAY,CAAC,CAAC;QAC7G,IAAI,IAAI,CAACuC,UAAU,EAAE;UACjBzG,WAAW,CAAC+K,GAAG,CAAC,IAAI,CAAC7G,WAAW,EAAE,IAAI,CAACiF,SAAS,EAAE,IAAI,CAACxC,UAAU,GAAG,IAAI,CAACrB,MAAM,EAAE0F,MAAM,CAAC,IAAI,CAAC9G,WAAW,CAAC,CAAC;QAC9G;QACArE,UAAU,CAACoL,aAAa,CAAC,IAAI,CAAC9B,SAAS,EAAE,IAAI,CAAC5C,QAAQ,KAAK,MAAM,GAAG,IAAI,CAACrB,QAAQ,CAACoF,IAAI,GAAG,IAAI,CAAC/D,QAAQ,EAAE,IAAI,CAACA,QAAQ,CAAC;QACtH,IAAI,CAACkE,YAAY,CAAC,CAAC;QACnB;MACJ,KAAK,MAAM;QACP,IAAI,CAACN,YAAY,CAAC,cAAc,EAAE;UAAEH,OAAO,EAAE,IAAI,CAACb,SAAS;UAAE/C,MAAM,EAAE,IAAI,CAACkD,QAAQ;UAAE3H,IAAI,EAAE,IAAI,CAACuC;QAAY,CAAC,CAAC;QAC7G,IAAI,CAACJ,KAAK,IAAIjE,UAAU,CAACwK,QAAQ,CAAC,IAAI,CAAClB,SAAS,EAAE,2BAA2B,CAAC;QAC9E;IACR;IACA,IAAI,CAACgB,YAAY,CAAC,kBAAkB,EAAEQ,KAAK,CAAC;EAChD;EACA1H,6BAA6BA,CAAC0H,KAAK,EAAE;IACjC,MAAMO,SAAS,GAAG,IAAI,CAAC/B,SAAS,IAAIwB,KAAK,CAACQ,OAAO,CAACC,aAAa;IAC/D,QAAQT,KAAK,CAACG,OAAO;MACjB,KAAK,SAAS;QACV,IAAI,CAACf,IAAI,CAACmB,SAAS,EAAE,IAAI,CAAC;QAC1B,IAAI,CAACG,aAAa,CAAC,CAAC;QACpB;MACJ,KAAK,MAAM;QACP,IAAI,CAACd,IAAI,CAACW,SAAS,EAAE,IAAI,CAAC;QAC1B,IAAI,CAACI,eAAe,CAAC,CAAC;QACtBzL,UAAU,CAACoL,aAAa,CAAC,IAAI,CAAC9B,SAAS,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAAC/C,QAAQ,CAAC;QACtEvG,WAAW,CAACuL,KAAK,CAACL,SAAS,CAAC;QAC5B,IAAI,CAACvF,YAAY,GAAG,KAAK;QACzB,IAAI,CAACH,EAAE,CAACgG,YAAY,CAAC,CAAC;QACtB;IACR;IACA,IAAI,CAACrB,YAAY,CAAC,iBAAiB,EAAEQ,KAAK,CAAC;EAC/C;EACAR,YAAYA,CAACsB,IAAI,EAAEhK,MAAM,EAAE;IACvB,IAAI,CAACgK,IAAI,CAAC,CAACf,IAAI,CAACjJ,MAAM,CAAC;IACvB,IAAI,CAAC2F,OAAO,IAAI,IAAI,CAACA,OAAO,CAACqE,IAAI,CAAC,IAAI,IAAI,CAACrE,OAAO,CAACqE,IAAI,CAAC,CAAChK,MAAM,CAAC;IAChE,IAAI,CAAC6D,MAAM,EAAEO,cAAc,IAAI,CAAC,IAAI,CAACP,MAAM,EAAEO,cAAc,EAAE4F,IAAI,CAAC,IAAI,CAAC,IAAI,CAACnG,MAAM,EAAEO,cAAc,EAAE4F,IAAI,CAAC,CAAChK,MAAM,CAAC;EACrH;EACA4J,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACK,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACC,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,4BAA4B,CAAC,CAAC;EACvC;EACAP,eAAeA,CAAA,EAAG;IACd,IAAI,CAACQ,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,2BAA2B,CAAC,CAAC;IAClC,IAAI,CAACC,4BAA4B,CAAC,CAAC;IACnC,IAAI,CAACC,8BAA8B,CAAC,CAAC;EACzC;EACAP,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACxD,aAAa,EAAE;MACrB,IAAI,CAACA,aAAa,GAAG,IAAIpI,6BAA6B,CAAC,IAAI,CAACwJ,QAAQ,EAAGqB,KAAK,IAAK;QAC7E,MAAMuB,KAAK,GAAG,IAAI,CAACnF,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC4D,KAAK,EAAE;UAAEwB,IAAI,EAAE,QAAQ;UAAExK,IAAI,EAAE,IAAI,CAACuC,WAAW;UAAEgI,KAAK,EAAE;QAAK,CAAC,CAAC,GAAG,IAAI;QAClHA,KAAK,IAAI,IAAI,CAAC3B,IAAI,CAACI,KAAK,EAAE,IAAI,CAAC;MACnC,CAAC,CAAC;IACN;IACA,IAAI,CAACzC,aAAa,CAACwD,kBAAkB,CAAC,CAAC;EAC3C;EACAI,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC5D,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAAC4D,oBAAoB,CAAC,CAAC;IAC7C;EACJ;EACAH,yBAAyBA,CAAA,EAAG;IACxB,IAAI,CAAC,IAAI,CAACxD,qBAAqB,EAAE;MAC7B,IAAI,CAACA,qBAAqB,GAAG,IAAI,CAAC9C,QAAQ,CAAC+G,MAAM,CAAC,IAAI,CAAClH,QAAQ,EAAE,OAAO,EAAGyF,KAAK,IAAK;QACjF,MAAM0B,eAAe,GAAG,IAAI,CAAC/C,QAAQ,KAAK,IAAI,CAACA,QAAQ,CAACgD,UAAU,CAAC3B,KAAK,CAACvE,MAAM,CAAC,IAAK,CAAC,IAAI,CAAC4B,gBAAgB,IAAI,IAAI,CAACsB,QAAQ,CAACiD,QAAQ,CAAC5B,KAAK,CAACvE,MAAM,CAAE,CAAC;QACrJ,MAAMoG,gBAAgB,GAAG,CAACH,eAAe,IAAI,CAAC,IAAI,CAACpE,uBAAuB;QAC1E,MAAMiE,KAAK,GAAG,IAAI,CAACnF,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC4D,KAAK,EAAE;UAAEwB,IAAI,EAAE,SAAS;UAAExK,IAAI,EAAE,IAAI,CAACuC,WAAW;UAAEgI,KAAK,EAAEvB,KAAK,CAAC8B,KAAK,KAAK,CAAC,IAAID;QAAiB,CAAC,CAAC,GAAGA,gBAAgB;QAChKN,KAAK,IAAI,IAAI,CAAC3B,IAAI,CAACI,KAAK,CAAC;QACzB,IAAI,CAAC3C,gBAAgB,GAAG,IAAI,CAACC,uBAAuB,GAAG,KAAK;MAChE,CAAC,CAAC;IACN;EACJ;EACA8D,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,IAAI,CAAC5D,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACA,qBAAqB,GAAG,IAAI;IACrC;EACJ;EACAyD,0BAA0BA,CAAA,EAAG;IACzB,IAAI,CAAC,IAAI,CAACxD,sBAAsB,EAAE;MAC9B,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAAC/C,QAAQ,CAAC+G,MAAM,CAAC,IAAI,CAAC9D,MAAM,EAAE,QAAQ,EAAGqC,KAAK,IAAK;QACjF,MAAMuB,KAAK,GAAG,IAAI,CAACnF,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC4D,KAAK,EAAE;UAAEwB,IAAI,EAAE,QAAQ;UAAExK,IAAI,EAAE,IAAI,CAACuC,WAAW;UAAEgI,KAAK,EAAE,CAACrM,UAAU,CAAC6M,aAAa,CAAC;QAAE,CAAC,CAAC,GAAG,CAAC7M,UAAU,CAAC6M,aAAa,CAAC,CAAC;QAChKR,KAAK,IAAI,IAAI,CAAC3B,IAAI,CAACI,KAAK,EAAE,IAAI,CAAC;MACnC,CAAC,CAAC;IACN;EACJ;EACAqB,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,IAAI,CAAC5D,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACA,sBAAsB,GAAG,IAAI;IACtC;EACJ;EACAyD,4BAA4BA,CAAA,EAAG;IAC3B,IAAI,IAAI,CAACxD,wBAAwB,EAAE;MAC/B;IACJ;IACA,IAAI,CAAC5C,IAAI,CAACkH,iBAAiB,CAAC,MAAM;MAC9B,IAAI,CAACtE,wBAAwB,GAAG,IAAI,CAAChD,QAAQ,CAAC+G,MAAM,CAAC,IAAI,CAAC9D,MAAM,EAAE,SAAS,EAAGqC,KAAK,IAAK;QACpF,IAAI,IAAI,CAAC9E,cAAc,CAAC+G,YAAY,KAAK,KAAK,IAAIjC,KAAK,CAACkC,IAAI,KAAK,QAAQ,EAAE;UACvE;QACJ;QACA,MAAMX,KAAK,GAAG,IAAI,CAACnF,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC4D,KAAK,EAAE;UAAEwB,IAAI,EAAE,SAAS;UAAExK,IAAI,EAAE,IAAI,CAACuC,WAAW;UAAEgI,KAAK,EAAE,CAACrM,UAAU,CAAC6M,aAAa,CAAC;QAAE,CAAC,CAAC,GAAG,CAAC7M,UAAU,CAAC6M,aAAa,CAAC,CAAC;QACjK,IAAIR,KAAK,EAAE;UACP,IAAI,CAACzG,IAAI,CAACqH,GAAG,CAAC,MAAM;YAChB,IAAI,CAACvC,IAAI,CAACI,KAAK,EAAE,IAAI,CAAC;UAC1B,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAsB,8BAA8BA,CAAA,EAAG;IAC7B,IAAI,IAAI,CAAC5D,wBAAwB,EAAE;MAC/B,IAAI,CAACA,wBAAwB,CAAC,CAAC;MAC/B,IAAI,CAACA,wBAAwB,GAAG,IAAI;IACxC;EACJ;EACA0E,WAAWA,CAAA,EAAG;IACV,IAAI,CAACxC,IAAI,CAAC,IAAI,CAACpB,SAAS,EAAE,IAAI,CAAC;IAC/B,IAAI,IAAI,CAACA,SAAS,EAAE;MAChBtJ,UAAU,CAACoL,aAAa,CAAC,IAAI,CAAC9B,SAAS,EAAE,IAAI,CAACG,QAAQ,EAAE,IAAI,CAAC/C,QAAQ,CAAC;MACtEvG,WAAW,CAACuL,KAAK,CAAC,IAAI,CAACpC,SAAS,CAAC;IACrC;IACA,IAAI,IAAI,CAACjB,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAAC8E,OAAO,CAAC,CAAC;MAC5B,IAAI,CAAC9E,aAAa,GAAG,IAAI;IAC7B;IACA,IAAI,CAACoD,eAAe,CAAC,CAAC;EAC1B;EACA,OAAO2B,IAAI,YAAAC,gBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFtI,OAAO,EAAjBjG,EAAE,CAAAwO,iBAAA,CAAiC1O,QAAQ,GAA3CE,EAAE,CAAAwO,iBAAA,CAAsDrO,WAAW,GAAnEH,EAAE,CAAAwO,iBAAA,CAA8ExO,EAAE,CAACyO,UAAU,GAA7FzO,EAAE,CAAAwO,iBAAA,CAAwGxO,EAAE,CAAC0O,SAAS,GAAtH1O,EAAE,CAAAwO,iBAAA,CAAiI1N,EAAE,CAAC6N,aAAa,GAAnJ3O,EAAE,CAAAwO,iBAAA,CAA8J1N,EAAE,CAAC8N,cAAc,GAAjL5O,EAAE,CAAAwO,iBAAA,CAA4LxO,EAAE,CAAC6O,iBAAiB,GAAlN7O,EAAE,CAAAwO,iBAAA,CAA6NxO,EAAE,CAAC8O,MAAM;EAAA;EACjU,OAAOC,IAAI,kBAD8E/O,EAAE,CAAAgP,iBAAA;IAAAzB,IAAA,EACJtH,OAAO;IAAAgJ,SAAA;IAAAC,cAAA,WAAAC,uBAAAhM,EAAA,EAAAC,GAAA,EAAAgM,QAAA;MAAA,IAAAjM,EAAA;QADLnD,EAAE,CAAAqP,cAAA,CAAAD,QAAA,EAC+wBrO,aAAa;MAAA;MAAA,IAAAoC,EAAA;QAAA,IAAAmM,EAAA;QAD9xBtP,EAAE,CAAAuP,cAAA,CAAAD,EAAA,GAAFtP,EAAE,CAAAwP,WAAA,QAAApM,GAAA,CAAA6F,SAAA,GAAAqG,EAAA;MAAA;IAAA;IAAAG,SAAA,WAAAC,cAAAvM,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFnD,EAAE,CAAA2P,WAAA,CAAAtO,GAAA;QAAFrB,EAAE,CAAA2P,WAAA,CAAArO,GAAA;MAAA;MAAA,IAAA6B,EAAA;QAAA,IAAAmM,EAAA;QAAFtP,EAAE,CAAAuP,cAAA,CAAAD,EAAA,GAAFtP,EAAE,CAAAwP,WAAA,QAAApM,GAAA,CAAA8F,gBAAA,GAAAoG,EAAA,CAAAM,KAAA;QAAF5P,EAAE,CAAAuP,cAAA,CAAAD,EAAA,GAAFtP,EAAE,CAAAwP,WAAA,QAAApM,GAAA,CAAA+F,gBAAA,GAAAmG,EAAA,CAAAM,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,MAAA;MAAAjK,OAAA;MAAA9C,IAAA;MAAAzD,KAAA;MAAAqG,UAAA;MAAAf,YAAA;MAAAF,iBAAA;MAAA8C,MAAA;MAAAG,QAAA;MAAAE,UAAA;MAAAE,UAAA;MAAAhD,qBAAA;MAAAC,qBAAA;MAAAmD,QAAA;MAAAE,UAAA;MAAAG,OAAA;IAAA;IAAAuH,OAAA;MAAArH,aAAA;MAAAC,YAAA;MAAAC,MAAA;MAAAC,YAAA;MAAAC,MAAA;MAAAC,gBAAA;MAAAC,eAAA;IAAA;IAAAgH,QAAA,GAAFhQ,EAAE,CAAAiQ,kBAAA,CACmsB,CAACnK,sBAAsB,CAAC;IAAAoK,kBAAA,EAAA3O,GAAA;IAAA4O,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAnF,QAAA,WAAAoF,iBAAAnN,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAD7tBnD,EAAE,CAAAuQ,eAAA;QAAFvQ,EAAE,CAAAuE,UAAA,IAAAgB,sBAAA,iBAyBvF,CAAC;MAAA;MAAA,IAAApC,EAAA;QAzBoFnD,EAAE,CAAA2E,UAAA,SAAAvB,GAAA,CAAA2D,YAGjE,CAAC;MAAA;IAAA;IAAAyJ,YAAA,GAsCyhC5Q,EAAE,CAAC6Q,OAAO,EAAoF7Q,EAAE,CAAC8Q,IAAI,EAA6F9Q,EAAE,CAAC+Q,gBAAgB,EAAoJ/Q,EAAE,CAACgR,OAAO;IAAAC,MAAA;IAAAC,aAAA;IAAAC,IAAA;MAAAvR,SAAA,EAA6D,CAACG,OAAO,CAAC,yBAAyB,EAAE,CAACD,UAAU,CAAC,QAAQ,EAAE,CAACD,YAAY,CAAC0G,2BAA2B,CAAC,CAAC,CAAC,EAAEzG,UAAU,CAAC,QAAQ,EAAE,CAACD,YAAY,CAAC4G,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC;IAAC;IAAA2K,eAAA;EAAA;AACppD;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3C6FjR,EAAE,CAAAkR,iBAAA,CA2CJjL,OAAO,EAAc,CAAC;IACrGsH,IAAI,EAAEnN,SAAS;IACf+Q,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,WAAW;MAAElG,QAAQ,EAAG;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAEmG,UAAU,EAAE,CAAC1R,OAAO,CAAC,yBAAyB,EAAE,CAACD,UAAU,CAAC,QAAQ,EAAE,CAACD,YAAY,CAAC0G,2BAA2B,CAAC,CAAC,CAAC,EAAEzG,UAAU,CAAC,QAAQ,EAAE,CAACD,YAAY,CAAC4G,2BAA2B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAAE2K,eAAe,EAAE3Q,uBAAuB,CAACiR,MAAM;MAAER,aAAa,EAAExQ,iBAAiB,CAACiR,IAAI;MAAEC,SAAS,EAAE,CAAC1L,sBAAsB,CAAC;MAAE2L,IAAI,EAAE;QAC5SC,KAAK,EAAE;MACX,CAAC;MAAEb,MAAM,EAAE,CAAC,2+BAA2+B;IAAE,CAAC;EACtgC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEtD,IAAI,EAAEoE,QAAQ;IAAEC,UAAU,EAAE,CAAC;MAC9CrE,IAAI,EAAEhN,MAAM;MACZ4Q,IAAI,EAAE,CAACrR,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEyN,IAAI,EAAE7F,SAAS;IAAEkK,UAAU,EAAE,CAAC;MAClCrE,IAAI,EAAEhN,MAAM;MACZ4Q,IAAI,EAAE,CAAChR,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEoN,IAAI,EAAEvN,EAAE,CAACyO;EAAW,CAAC,EAAE;IAAElB,IAAI,EAAEvN,EAAE,CAAC0O;EAAU,CAAC,EAAE;IAAEnB,IAAI,EAAEzM,EAAE,CAAC6N;EAAc,CAAC,EAAE;IAAEpB,IAAI,EAAEzM,EAAE,CAAC8N;EAAe,CAAC,EAAE;IAAErB,IAAI,EAAEvN,EAAE,CAAC6O;EAAkB,CAAC,EAAE;IAAEtB,IAAI,EAAEvN,EAAE,CAAC8O;EAAO,CAAC,CAAC,EAAkB;IAAEjJ,OAAO,EAAE,CAAC;MAClM0H,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEuC,IAAI,EAAE,CAAC;MACPwK,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAElB,KAAK,EAAE,CAAC;MACRiO,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEmF,UAAU,EAAE,CAAC;MACb4H,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEoE,YAAY,EAAE,CAAC;MACf2I,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEkE,iBAAiB,EAAE,CAAC;MACpB6I,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEgH,MAAM,EAAE,CAAC;MACT+F,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEmH,QAAQ,EAAE,CAAC;MACX4F,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEqH,UAAU,EAAE,CAAC;MACb0F,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEuH,UAAU,EAAE,CAAC;MACbwF,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEuE,qBAAqB,EAAE,CAAC;MACxBwI,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEwE,qBAAqB,EAAE,CAAC;MACxBuI,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAE2H,QAAQ,EAAE,CAAC;MACXoF,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAE6H,UAAU,EAAE,CAAC;MACbkF,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEgI,OAAO,EAAE,CAAC;MACV+E,IAAI,EAAE/M;IACV,CAAC,CAAC;IAAEkI,aAAa,EAAE,CAAC;MAChB6E,IAAI,EAAE9M;IACV,CAAC,CAAC;IAAEkI,YAAY,EAAE,CAAC;MACf4E,IAAI,EAAE9M;IACV,CAAC,CAAC;IAAEmI,MAAM,EAAE,CAAC;MACT2E,IAAI,EAAE9M;IACV,CAAC,CAAC;IAAEoI,YAAY,EAAE,CAAC;MACf0E,IAAI,EAAE9M;IACV,CAAC,CAAC;IAAEqI,MAAM,EAAE,CAAC;MACTyE,IAAI,EAAE9M;IACV,CAAC,CAAC;IAAEsI,gBAAgB,EAAE,CAAC;MACnBwE,IAAI,EAAE9M;IACV,CAAC,CAAC;IAAEuI,eAAe,EAAE,CAAC;MAClBuE,IAAI,EAAE9M;IACV,CAAC,CAAC;IAAEwI,SAAS,EAAE,CAAC;MACZsE,IAAI,EAAE7M,eAAe;MACrByQ,IAAI,EAAE,CAACpQ,aAAa;IACxB,CAAC,CAAC;IAAEmI,gBAAgB,EAAE,CAAC;MACnBqE,IAAI,EAAE5M,SAAS;MACfwQ,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEhI,gBAAgB,EAAE,CAAC;MACnBoE,IAAI,EAAE5M,SAAS;MACfwQ,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMU,aAAa,CAAC;EAChB,OAAOxD,IAAI,YAAAyD,sBAAAvD,CAAA;IAAA,YAAAA,CAAA,IAAwFsD,aAAa;EAAA;EAChH,OAAOE,IAAI,kBAtJ8E/R,EAAE,CAAAgS,gBAAA;IAAAzE,IAAA,EAsJSsE;EAAa;EACjH,OAAOI,IAAI,kBAvJ8EjS,EAAE,CAAAkS,gBAAA;IAAAC,OAAA,GAuJkCpS,YAAY,EAAEiB,YAAY,EAAEA,YAAY;EAAA;AACzK;AACA;EAAA,QAAAiQ,SAAA,oBAAAA,SAAA,KAzJ6FjR,EAAE,CAAAkR,iBAAA,CAyJJW,aAAa,EAAc,CAAC;IAC3GtE,IAAI,EAAE3M,QAAQ;IACduQ,IAAI,EAAE,CAAC;MACCgB,OAAO,EAAE,CAACpS,YAAY,EAAEiB,YAAY,CAAC;MACrCoR,OAAO,EAAE,CAACnM,OAAO,EAAEjF,YAAY,CAAC;MAChCqR,YAAY,EAAE,CAACpM,OAAO;IAC1B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASH,sBAAsB,EAAEG,OAAO,EAAE4L,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}