{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../core/services/content-vendor.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/inputtext\";\nfunction HomeComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99);\n    i0.ɵɵelement(1, \"img\", 100);\n    i0.ɵɵelementStart(2, \"h4\", 101);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 102);\n    i0.ɵɵelementStart(5, \"button\", 103);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementStart(7, \"span\", 18);\n    i0.ɵɵtext(8, \"arrow_right_alt\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const service_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    i0.ɵɵproperty(\"ngClass\", \"s-box-\" + (i_r2 + 1));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", service_r1.Icon == null ? null : service_r1.Icon.url, i0.ɵɵsanitizeUrl)(\"alt\", service_r1.Title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(service_r1.Title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", service_r1.Description, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", service_r1.Button_Title, \" \");\n  }\n}\nexport class HomeComponent {\n  constructor(route, CMSservice) {\n    this.route = route;\n    this.CMSservice = CMSservice;\n    this.bannerData = null;\n    this.servicesData = [];\n  }\n  ngOnInit() {\n    this.content = this.route.snapshot.data['content'];\n    console.log('Home Content:', this.content);\n    // Extract banner\n    const bannerComponent = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.banner\");\n    if (bannerComponent?.length) {\n      this.bannerData = bannerComponent[0];\n    }\n    // Extract services\n    const servicesComponents = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.services\");\n    if (servicesComponents?.length) {\n      this.servicesData = servicesComponents;\n    }\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ContentService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 254,\n      vars: 10,\n      consts: [[1, \"banner-sec\", \"relative\"], [1, \"banner-body\", \"h-full\", \"flex\", \"align-items-center\"], [1, \"banner-box\", \"relative\", \"flex-1\", \"p-8\"], [1, \"banner-box\", \"relative\", \"flex-1\", \"bg-white\", \"p-8\", \"flex\", \"flex-column\", \"justify-content-center\"], [1, \"mb-4\", \"text-yellow-400\"], [1, \"m-0\", \"mb-5\", \"text-8xl\", \"line-height-1\", \"font-extrabold\", \"text-color\"], [1, \"m-0\", \"text-lg\", \"font-medium\"], [1, \"services-sec\", \"w-full\"], [1, \"services-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\"], [\"class\", \"services-box p-5 flex-1 flex flex-column gap-2\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"about-sec\", \"relative\"], [1, \"about-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"flex-wrap\"], [1, \"about-box\", \"pr-8\", \"w-6\", \"secondary-bg-color\"], [\"src\", \"/assets/layout/images/director-img.jpg\", \"alt\", \"\", 1, \"w-full\"], [1, \"about-box\", \"p-8\", \"pr-0\", \"w-6\", \"secondary-bg-color\", \"flex\", \"flex-column\", \"justify-content-start\"], [1, \"line\", \"m-0\", \"mb-6\", \"relative\", \"pb-5\", \"text-6xl\", \"line-height-2\", \"font-bold\", \"text-white\"], [1, \"m-0\", \"text-lg\", \"text-white\", \"flex-1\"], [\"type\", \"button\", 1, \"px-5\", \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"h-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-white\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"material-symbols-rounded\"], [1, \"city-members-sec\", \"relative\"], [1, \"city-members-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"flex-wrap\"], [1, \"city-members-left\", \"pt-8\", \"pr-7\", \"pb-8\", \"pl-0\", \"relative\", \"flex\", \"bg-orange-50\"], [1, \"cm-time-box\"], [\"src\", \"/assets/layout/images/time-screen.png\", \"alt\", \"\", 1, \"w-full\"], [1, \"cm-info\"], [1, \"line\", \"m-0\", \"mb-6\", \"relative\", \"pb-5\", \"text-4xl\", \"line-height-2\", \"font-bold\", \"text-color\"], [1, \"m-0\", \"text-lg\", \"text-color\", \"flex-1\"], [1, \"city-members-right\", \"p-7\", \"pr-0\", \"relative\", \"flex\", \"flex-column\", \"gap-8\"], [1, \"cm-right-box\"], [1, \"flex\", \"align-items-start\", \"text-8xl\", \"font-extrabold\", \"line-height-1\"], [1, \"relative\", \"text-4xl\", \"font-bold\", \"inline-block\"], [1, \"uppercase\"], [1, \"text-color-secondary\"], [1, \"quick-access-sec\", \"relative\"], [1, \"quick-access-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"flex-wrap\"], [1, \"quick-access-left\", \"relative\", \"flex\", \"flex-column\", \"pt-8\", \"pb-8\"], [1, \"mb-4\", \"flex\", \"align-items-start\", \"text-6xl\", \"font-extrabold\", \"line-height-1\"], [1, \"line-2\", \"inline-flex\", \"w-fit\", \"m-0\", \"mb-6\", \"relative\", \"pb-5\", \"text-xl\", \"line-height-2\", \"font-bold\", \"text-yellow-400\"], [1, \"quick-access-list\", \"d-grid\", \"w-full\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-bottom-none\", \"border-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/001-toll.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"m-0\", \"mb-4\", \"uppercase\", \"flex-1\"], [1, \"text-sm\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-none\", \"border-top-1\", \"border-right-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/006-virus.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [\"src\", \"/assets/layout/images/003-trash-can.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/004-parking.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-none\", \"border-top-1\", \"border-right-1\", \"border-bottom-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/005-lecture.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [\"src\", \"/assets/layout/images/008-basketball.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"quick-access-right\"], [1, \"news-sec\", \"relative\", \"pt-7\", \"pb-8\", \"secondary-bg-color\"], [1, \"news-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\"], [1, \"news-title\"], [1, \"line\", \"m-0\", \"mb-5\", \"pb-5\", \"relative\", \"inline-flex\", \"w-fit\", \"text-yellow-400\"], [1, \"m-0\", \"mb-6\", \"relative\", \"text-6xl\", \"line-height-2\", \"font-bold\", \"text-white\"], [1, \"news-list\", \"d-grid\", \"w-full\", \"gap-6\"], [1, \"news-box\", \"w-full\", \"flex\", \"flex-column\"], [1, \"new-img\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/new-img-1.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"text-white\", \"text-xl\", \"flex-1\"], [1, \"mb-3\", \"flex\", \"gap-3\", \"align-items-center\"], [1, \"text\", \"flex\", \"align-items-center\", \"font-medium\", \"text-sm\", \"gap-1\", \"text-white\"], [1, \"material-symbols-rounded\", \"text-xl\"], [1, \"mb-5\", \"text-sm\", \"text-white\"], [\"type\", \"button\", 1, \"px-5\", \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"h-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-yellow-400\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"news-letter-sec\", \"relative\"], [1, \"news-letter-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\"], [1, \"news-letter-box-list\", \"flex\", \"bg-white\", \"shadow-1\"], [1, \"news-letter-box\", \"w-6\", \"p-7\", \"flex\", \"flex-column\", \"justify-content-center\"], [1, \"line-2\", \"relative\", \"mb-5\", \"pb-5\", \"inline-flex\", \"w-fit\", \"align-items-start\", \"text-6xl\", \"font-extrabold\", \"line-height-1\"], [1, \"news-letter-form\", \"mt-5\", \"relative\", \"flex\", \"align-items-center\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Your email address\", 1, \"h-3-3rem\", \"border-noround\", \"flex-1\"], [\"type\", \"button\", 1, \"px-6\", \"p-element\", \"p-ripple\", \"border-noround\", \"p-button\", \"p-component\", \"h-3-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-yellow-400\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"news-letter-img\", \"w-6\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/news-letter-img.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"what-happning-sec\", \"mt-8\", \"relative\", \"px-4\"], [1, \"what-happning-body\", \"relative\", \"w-full\", \"mx-auto\", \"flex\", \"bg-orange-50\"], [1, \"what-happning-img\", \"w-6\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/whats-happnening-mg.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"what-happning-right\", \"w-6\", \"p-8\"], [1, \"what-happning-title\"], [1, \"line-2\", \"m-0\", \"mb-5\", \"pb-5\", \"relative\", \"inline-flex\", \"w-fit\", \"text-yellow-400\"], [1, \"m-0\", \"mb-6\", \"relative\", \"text-6xl\", \"line-height-2\", \"font-bold\", \"text-color\"], [1, \"what-happning-list\"], [1, \"what-happning-box\", \"mb-4\", \"pb-4\", \"flex\", \"align-items-center\", \"gap-5\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"surface-border\"], [1, \"wh-img-box\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/arches-1.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"wh-cnt-sec\", \"flex-1\", \"flex\", \"align-items-center\", \"gap-6\"], [1, \"wh-cnt\", \"flex\", \"flex-column\"], [1, \"text-xl\", \"font-bold\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"flex-wrap\"], [1, \"flex\", \"w-fit\", \"align-items-center\", \"gap-2\", \"py-2\", \"p-3\", \"bg-orange-100\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"type\", \"button\", 1, \"px-4\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-noround\", \"p-button-outlined\", \"h-3-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"text-color\", \"font-semibold\"], [\"src\", \"/assets/layout/images/arches-2.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"what-happning-box\", \"flex\", \"align-items-center\", \"gap-5\"], [\"src\", \"/assets/layout/images/arches-3.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"services-box\", \"p-5\", \"flex-1\", \"flex\", \"flex-column\", \"gap-2\", 3, \"ngClass\"], [1, \"h-fit\", \"w-5rem\", 3, \"src\", \"alt\"], [1, \"font-bold\", \"text-white\"], [1, \"flex-grow-1\", 3, \"innerHTML\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"h-3rem\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-white\", \"text-color\", \"font-semibold\", \"border-none\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"div\", 2);\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"h4\", 4);\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"h1\", 5);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 6);\n          i0.ɵɵtext(9);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"section\", 7)(11, \"div\", 8);\n          i0.ɵɵtemplate(12, HomeComponent_div_12_Template, 9, 6, \"div\", 9);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"section\", 10)(14, \"div\", 11)(15, \"div\", 12);\n          i0.ɵɵelement(16, \"img\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 14)(18, \"h4\", 4);\n          i0.ɵɵtext(19, \"Far away from the every day!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"h2\", 15);\n          i0.ɵɵtext(21, \"A Message From the Director \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\", 16);\n          i0.ɵɵtext(23, \"Old Town of Riverside is a special place with limitless potential, where everyone deserves equal access to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in every neighborhood. It exists to serve you and be a resource for you, your family, and your community.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"button\", 17);\n          i0.ɵɵtext(25, \" Learn More \");\n          i0.ɵɵelementStart(26, \"span\", 18);\n          i0.ɵɵtext(27, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(28, \"section\", 19)(29, \"div\", 20)(30, \"div\", 21)(31, \"div\", 22);\n          i0.ɵɵelement(32, \"img\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 24)(34, \"h4\", 4);\n          i0.ɵɵtext(35, \"Our city in numbers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"h2\", 25);\n          i0.ɵɵtext(37, \"Programs to help launch, grow and expand any business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"p\", 26);\n          i0.ɵɵtext(39, \"Old Town of Riverside is a special place with limitless potential, where everyone deserves equal access to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in every neighborhood. It exists to serve you and be a resource for you, your family, and your community.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(40, \"div\", 27)(41, \"div\", 28)(42, \"h3\", 29);\n          i0.ɵɵtext(43, \"41 \");\n          i0.ɵɵelementStart(44, \"sup\", 30);\n          i0.ɵɵtext(45, \"k\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"h5\", 31);\n          i0.ɵɵtext(47, \"Highly-educated creative workforce\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"p\", 32);\n          i0.ɵɵtext(49, \"A vibrant destination where innovations in design and technology are born. A place for you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"div\", 28)(51, \"h3\", 29);\n          i0.ɵɵtext(52, \"8 \");\n          i0.ɵɵelementStart(53, \"sup\", 30);\n          i0.ɵɵtext(54, \"th\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"h5\", 31);\n          i0.ɵɵtext(56, \"Highest Per Capita Income in Virginia\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"p\", 32);\n          i0.ɵɵtext(58, \"A vibrant destination where innovations in design and technology are born. A place for you.\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(59, \"section\", 33)(60, \"div\", 34)(61, \"div\", 35)(62, \"h2\", 36);\n          i0.ɵɵtext(63, \"Quick Access\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"h4\", 37);\n          i0.ɵɵtext(65, \" What can the City help you with today?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"div\", 38)(67, \"div\", 39);\n          i0.ɵɵelement(68, \"img\", 40);\n          i0.ɵɵelementStart(69, \"h6\", 41);\n          i0.ɵɵtext(70, \"Road construction\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"p\", 42);\n          i0.ɵɵtext(72, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(73, \"div\", 43);\n          i0.ɵɵelement(74, \"img\", 44);\n          i0.ɵɵelementStart(75, \"h6\", 41);\n          i0.ɵɵtext(76, \"COVID-19 updates\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"p\", 42);\n          i0.ɵɵtext(78, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(79, \"div\", 43);\n          i0.ɵɵelement(80, \"img\", 45);\n          i0.ɵɵelementStart(81, \"h6\", 41);\n          i0.ɵɵtext(82, \"Garbage pickup\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"p\", 42);\n          i0.ɵɵtext(84, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"div\", 46);\n          i0.ɵɵelement(86, \"img\", 47);\n          i0.ɵɵelementStart(87, \"h6\", 41);\n          i0.ɵɵtext(88, \"Parking\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"p\", 42);\n          i0.ɵɵtext(90, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(91, \"div\", 48);\n          i0.ɵɵelement(92, \"img\", 49);\n          i0.ɵɵelementStart(93, \"h6\", 41);\n          i0.ɵɵtext(94, \"Council meetings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"p\", 42);\n          i0.ɵɵtext(96, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(97, \"div\", 48);\n          i0.ɵɵelement(98, \"img\", 50);\n          i0.ɵɵelementStart(99, \"h6\", 41);\n          i0.ɵɵtext(100, \"Recreation and sport programs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"p\", 42);\n          i0.ɵɵtext(102, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(103, \"div\", 51);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(104, \"section\", 52)(105, \"div\", 53)(106, \"div\", 54)(107, \"h5\", 55);\n          i0.ɵɵtext(108, \"Find out what\\u2019s going on & stay up to date.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"h2\", 56);\n          i0.ɵɵtext(110, \"Latest News\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(111, \"div\", 57)(112, \"div\", 58)(113, \"div\", 59);\n          i0.ɵɵelement(114, \"img\", 60);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"h3\", 61);\n          i0.ɵɵtext(116, \"Proposed Downtown District Ordinance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"div\", 62)(118, \"div\", 63)(119, \"span\", 64);\n          i0.ɵɵtext(120, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(121, \" September 16, 2018 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(122, \"div\", 63)(123, \"span\", 64);\n          i0.ɵɵtext(124, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(125, \" 240 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(126, \"p\", 65);\n          i0.ɵɵtext(127, \"The goal of the proposed ordinance is to create safe venues for customers and to discourage illegal activities. The governing officials of...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(128, \"button\", 66);\n          i0.ɵɵtext(129, \" Learn More \");\n          i0.ɵɵelementStart(130, \"span\", 18);\n          i0.ɵɵtext(131, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(132, \"div\", 58)(133, \"div\", 59);\n          i0.ɵɵelement(134, \"img\", 60);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(135, \"h3\", 61);\n          i0.ɵɵtext(136, \"Annual Water Quality Report (Gallery Post)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(137, \"div\", 62)(138, \"div\", 63)(139, \"span\", 64);\n          i0.ɵɵtext(140, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(141, \" September 16, 2018 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(142, \"div\", 63)(143, \"span\", 64);\n          i0.ɵɵtext(144, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(145, \" 240 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(146, \"p\", 65);\n          i0.ɵɵtext(147, \"The goal of the proposed ordinance is to create safe venues for customers and to discourage illegal activities. The governing officials of...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(148, \"button\", 66);\n          i0.ɵɵtext(149, \" Learn More \");\n          i0.ɵɵelementStart(150, \"span\", 18);\n          i0.ɵɵtext(151, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(152, \"div\", 58)(153, \"div\", 59);\n          i0.ɵɵelement(154, \"img\", 60);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(155, \"h3\", 61);\n          i0.ɵɵtext(156, \"Waste Industries Garbage Pick Up: Embeds\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(157, \"div\", 62)(158, \"div\", 63)(159, \"span\", 64);\n          i0.ɵɵtext(160, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(161, \" September 16, 2018 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(162, \"div\", 63)(163, \"span\", 64);\n          i0.ɵɵtext(164, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(165, \" 240 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(166, \"p\", 65);\n          i0.ɵɵtext(167, \"The goal of the proposed ordinance is to create safe venues for customers and to discourage illegal activities. The governing officials of...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(168, \"button\", 66);\n          i0.ɵɵtext(169, \" Learn More \");\n          i0.ɵɵelementStart(170, \"span\", 18);\n          i0.ɵɵtext(171, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(172, \"section\", 67)(173, \"div\", 68)(174, \"div\", 69)(175, \"div\", 70)(176, \"h2\", 71);\n          i0.ɵɵtext(177, \" Sign up for News & Alerts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(178, \"p\");\n          i0.ɵɵtext(179, \"Stay connected to the City of Riverside by signing up to receive official news and alerts by email! \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(180, \"div\", 72);\n          i0.ɵɵelement(181, \"input\", 73);\n          i0.ɵɵelementStart(182, \"button\", 74);\n          i0.ɵɵtext(183, \" Sign Up \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(184, \"div\", 75);\n          i0.ɵɵelement(185, \"img\", 76);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(186, \"section\", 77)(187, \"div\", 78)(188, \"div\", 79);\n          i0.ɵɵelement(189, \"img\", 80);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(190, \"div\", 81)(191, \"div\", 82)(192, \"h5\", 83);\n          i0.ɵɵtext(193, \"Join the fun in our city! \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(194, \"h2\", 84);\n          i0.ɵɵtext(195, \"What's Happening\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(196, \"div\", 85)(197, \"div\", 86)(198, \"div\", 87);\n          i0.ɵɵelement(199, \"img\", 88);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(200, \"div\", 89)(201, \"div\", 90)(202, \"h3\", 91);\n          i0.ɵɵtext(203, \"Heritage 10th Anniversary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(204, \"div\", 92)(205, \"div\", 93)(206, \"span\");\n          i0.ɵɵtext(207, \"June 9, 2027\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(208, \" - \");\n          i0.ɵɵelementStart(209, \"span\");\n          i0.ɵɵtext(210, \"June 18, 2027\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(211, \"div\", 94);\n          i0.ɵɵtext(212, \"All Day at \");\n          i0.ɵɵelementStart(213, \"b\");\n          i0.ɵɵtext(214, \"Baker Hall\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(215, \"button\", 95);\n          i0.ɵɵtext(216, \" Find out more \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(217, \"div\", 86)(218, \"div\", 87);\n          i0.ɵɵelement(219, \"img\", 96);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(220, \"div\", 89)(221, \"div\", 90)(222, \"h3\", 91);\n          i0.ɵɵtext(223, \"Along Pines Run\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(224, \"div\", 92)(225, \"div\", 93)(226, \"span\");\n          i0.ɵɵtext(227, \"July 22, 2027\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(228, \"div\", 94);\n          i0.ɵɵtext(229, \"12:00 am at \");\n          i0.ɵɵelementStart(230, \"b\");\n          i0.ɵɵtext(231, \"Boulder City Council\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(232, \"button\", 95);\n          i0.ɵɵtext(233, \" Find out more \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(234, \"div\", 97)(235, \"div\", 87);\n          i0.ɵɵelement(236, \"img\", 98);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(237, \"div\", 89)(238, \"div\", 90)(239, \"h3\", 91);\n          i0.ɵɵtext(240, \"Touch A Truck\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(241, \"div\", 92)(242, \"div\", 93)(243, \"span\");\n          i0.ɵɵtext(244, \"June 9, 2027\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(245, \" - \");\n          i0.ɵɵelementStart(246, \"span\");\n          i0.ɵɵtext(247, \"June 18, 2027\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(248, \"div\", 94);\n          i0.ɵɵtext(249, \"9:00 am - 5:00 pm at \");\n          i0.ɵɵelementStart(250, \"b\");\n          i0.ɵɵtext(251, \"Baker Hall\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(252, \"button\", 95);\n          i0.ɵɵtext(253, \" Find out more \");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleProp(\"background-image\", (ctx.bannerData == null ? null : ctx.bannerData.Image == null ? null : ctx.bannerData.Image.url) ? \"url(\" + ctx.bannerData.Image.url + \")\" : null)(\"background-size\", \"cover\")(\"background-position\", \"center\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate((ctx.bannerData == null ? null : ctx.bannerData.Annotation) || \"Far away from the every day!\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate((ctx.bannerData == null ? null : ctx.bannerData.Title) || \"Community of endless beauty & Calm\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate((ctx.bannerData == null ? null : ctx.bannerData.Description) || \"Drawn by clean air and mythical light, visitors come to experience traditions, fine art, great cuisine and natural beauty of the landscape.\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.servicesData);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i4.InputText],\n      styles: [\".max-w-1200 {\\n  max-width: 1200px;\\n}\\n  .secondary-color {\\n  color: #030f5e !important;\\n}\\n  .secondary-bg-color {\\n  background: #030f5e !important;\\n}\\n  .font-extrabold {\\n  font-weight: 900 !important;\\n}\\n  .d-grid {\\n  display: grid !important;\\n}\\n  .object-fit-contain {\\n  object-fit: contain;\\n}\\n  .object-fit-cover {\\n  object-fit: cover;\\n}\\n  .h-3-3rem {\\n  height: 3.3rem !important;\\n}\\n  .bg-blue-75 {\\n  background: rgb(227, 243, 255) !important;\\n}\\n  .banner-sec {\\n  margin: 8rem 0 0 0;\\n  height: calc(100vh - 8rem);\\n  background: url('banner-img.jpg') center center no-repeat;\\n  background-size: cover;\\n}\\n  .banner-sec .banner-box {\\n  margin: 120px 0 0 0;\\n}\\n  .services-sec {\\n  margin: -87px 0 0 0;\\n}\\n  .services-sec .services-box.s-box-1 {\\n  background: #a8c1cd;\\n}\\n  .services-sec .services-box.s-box-2 {\\n  background: #e3cab0;\\n}\\n  .services-sec .services-box.s-box-3 {\\n  background: #c4a597;\\n}\\n  .services-sec .services-box.s-box-4 {\\n  background: #e8816e;\\n}\\n  .about-sec:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  right: 0;\\n  height: 100%;\\n  background: #030f5e !important;\\n  width: 20%;\\n}\\n  .line::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.2392156863) !important;\\n  width: 100%;\\n}\\n  .line::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgb(213, 200, 186) !important;\\n  width: 64px;\\n}\\n  .line-2::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgba(0, 0, 0, 0.07) !important;\\n  width: 100%;\\n}\\n  .line-2::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgb(213, 200, 186) !important;\\n  width: 64px;\\n}\\n  .city-members-sec:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  height: 100%;\\n  background: var(--orange-50) !important;\\n  width: 20%;\\n}\\n  .city-members-left {\\n  width: 70%;\\n}\\n  .city-members-left .cm-time-box {\\n  flex: 0 0 40%;\\n}\\n  .city-members-left .cm-info {\\n  flex: 0 0 60%;\\n  padding: 0 0 0 50px;\\n}\\n  .city-members-right {\\n  width: 30%;\\n}\\n  .city-members-right .cm-right-box h3 sup {\\n  top: 7px;\\n}\\n  .quick-access-sec {\\n  background: url('pexels-vitor-gusmao.jpg') center right no-repeat;\\n  background-size: 38% auto;\\n}\\n  .quick-access-sec .quick-access-left {\\n  flex: 0 0 67%;\\n  width: 67%;\\n}\\n  .quick-access-sec .quick-access-left .quick-access-list {\\n  grid-template-columns: repeat(auto-fill, minmax(33.333%, 1fr));\\n}\\n  .quick-access-sec .quick-access-right {\\n  flex: 0 0 33%;\\n  width: 33%;\\n}\\n  .news-sec .news-list {\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n}\\n  .news-letter-sec {\\n  background: linear-gradient(180deg, #030f5e 50%, transparent 50%);\\n}\\n  .news-letter-sec .news-letter-box-list .news-letter-img {\\n  height: 640px;\\n}\\n  .what-happning-sec .what-happning-body .what-happning-img {\\n  height: 800px;\\n}\\n  .what-happning-sec .what-happning-body .what-happning-box .wh-img-box {\\n  width: 120px;\\n  height: 120px;\\n}\\n  .what-happning-sec .what-happning-body .what-happning-box .wh-cnt {\\n  max-width: 70%;\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvaG9tZS9ob21lLmNvbXBvbmVudC5zY3NzIiwid2VicGFjazovLy4vLi4vLi4vLi4vUHVibGljJTIwU2VydmljZS9TTkpZQS1QVUJMSUMtU0VSVklDRS9jbGllbnQvc3JjL2FwcC9ob21lL2hvbWUuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0k7RUFDSSxpQkFBQTtBQ0FSO0FER0k7RUFDSSx5QkFBQTtBQ0RSO0FESUk7RUFDSSw4QkFBQTtBQ0ZSO0FES0k7RUFDSSwyQkFBQTtBQ0hSO0FETUk7RUFDSSx3QkFBQTtBQ0pSO0FET0k7RUFDSSxtQkFBQTtBQ0xSO0FEUUk7RUFDSSxpQkFBQTtBQ05SO0FEU0k7RUFDSSx5QkFBQTtBQ1BSO0FEVUk7RUFDSSx5Q0FBQTtBQ1JSO0FEYUk7RUFDSSxrQkFBQTtFQUNBLDBCQUFBO0VBQ0EseURBQUE7RUFDQSxzQkFBQTtBQ1hSO0FEYVE7RUFDSSxtQkFBQTtBQ1haO0FEZUk7RUFDSSxtQkFBQTtBQ2JSO0FEZVE7RUFDSSxtQkFBQTtBQ2JaO0FEZ0JRO0VBQ0ksbUJBQUE7QUNkWjtBRGlCUTtFQUNJLG1CQUFBO0FDZlo7QURrQlE7RUFDSSxtQkFBQTtBQ2hCWjtBRHFCUTtFQUNJLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLFFBQUE7RUFDQSxZQUFBO0VBQ0EsOEJBQUE7RUFDQSxVQUFBO0FDbkJaO0FEd0JRO0VBQ0ksa0JBQUE7RUFDQSxXQUFBO0VBQ0EsT0FBQTtFQUNBLFNBQUE7RUFDQSxXQUFBO0VBQ0Esd0RBQUE7RUFDQSxXQUFBO0FDdEJaO0FEeUJRO0VBQ0ksa0JBQUE7RUFDQSxXQUFBO0VBQ0EsT0FBQTtFQUNBLFNBQUE7RUFDQSxXQUFBO0VBQ0EseUNBQUE7RUFDQSxXQUFBO0FDdkJaO0FENEJRO0VBQ0ksa0JBQUE7RUFDQSxXQUFBO0VBQ0EsT0FBQTtFQUNBLFNBQUE7RUFDQSxXQUFBO0VBQ0EsMENBQUE7RUFDQSxXQUFBO0FDMUJaO0FENkJRO0VBQ0ksa0JBQUE7RUFDQSxXQUFBO0VBQ0EsT0FBQTtFQUNBLFNBQUE7RUFDQSxXQUFBO0VBQ0EseUNBQUE7RUFDQSxXQUFBO0FDM0JaO0FEZ0NRO0VBQ0ksa0JBQUE7RUFDQSxXQUFBO0VBQ0EsT0FBQTtFQUNBLFlBQUE7RUFDQSx1Q0FBQTtFQUNBLFVBQUE7QUM5Qlo7QURrQ0k7RUFDSSxVQUFBO0FDaENSO0FEa0NRO0VBQ0ksYUFBQTtBQ2hDWjtBRG1DUTtFQUNJLGFBQUE7RUFDQSxtQkFBQTtBQ2pDWjtBRHFDSTtFQUNJLFVBQUE7QUNuQ1I7QUR1Q2dCO0VBQ0ksUUFBQTtBQ3JDcEI7QUQyQ0k7RUFDSSxpRUFBQTtFQUNBLHlCQUFBO0FDekNSO0FEMkNRO0VBQ0ksYUFBQTtFQUNBLFVBQUE7QUN6Q1o7QUQyQ1k7RUFDSSw4REFBQTtBQ3pDaEI7QUQ2Q1E7RUFDSSxhQUFBO0VBQ0EsVUFBQTtBQzNDWjtBRGdEUTtFQUNJLDREQUFBO0FDOUNaO0FEb0RJO0VBQ0ksaUVBQUE7QUNsRFI7QURxRFk7RUFDSSxhQUFBO0FDbkRoQjtBRDBEWTtFQUNJLGFBQUE7QUN4RGhCO0FENERnQjtFQUNJLFlBQUE7RUFDQSxhQUFBO0FDMURwQjtBRDZEZ0I7RUFDSSxjQUFBO0VBQ0EsV0FBQTtBQzNEcEIiLCJzb3VyY2VzQ29udGVudCI6WyI6Om5nLWRlZXAge1xyXG4gICAgLm1heC13LTEyMDAge1xyXG4gICAgICAgIG1heC13aWR0aDogMTIwMHB4O1xyXG4gICAgfVxyXG5cclxuICAgIC5zZWNvbmRhcnktY29sb3Ige1xyXG4gICAgICAgIGNvbG9yOiAjMDMwZjVlICFpbXBvcnRhbnQ7XHJcbiAgICB9XHJcblxyXG4gICAgLnNlY29uZGFyeS1iZy1jb2xvciB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogIzAzMGY1ZSAhaW1wb3J0YW50O1xyXG4gICAgfVxyXG5cclxuICAgIC5mb250LWV4dHJhYm9sZCB7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDkwMCAhaW1wb3J0YW50O1xyXG4gICAgfVxyXG5cclxuICAgIC5kLWdyaWQge1xyXG4gICAgICAgIGRpc3BsYXk6IGdyaWQgIWltcG9ydGFudDtcclxuICAgIH1cclxuXHJcbiAgICAub2JqZWN0LWZpdC1jb250YWluIHtcclxuICAgICAgICBvYmplY3QtZml0OiBjb250YWluO1xyXG4gICAgfVxyXG5cclxuICAgIC5vYmplY3QtZml0LWNvdmVyIHtcclxuICAgICAgICBvYmplY3QtZml0OiBjb3ZlcjtcclxuICAgIH1cclxuXHJcbiAgICAuaC0zLTNyZW0ge1xyXG4gICAgICAgIGhlaWdodDogMy4zcmVtICFpbXBvcnRhbnQ7XHJcbiAgICB9XHJcblxyXG4gICAgLmJnLWJsdWUtNzUge1xyXG4gICAgICAgIGJhY2tncm91bmQ6IHJnYigyMjcgMjQzIDI1NSkgIWltcG9ydGFudDtcclxuICAgIH1cclxuXHJcblxyXG5cclxuICAgIC5iYW5uZXItc2VjIHtcclxuICAgICAgICBtYXJnaW46IDhyZW0gMCAwIDA7XHJcbiAgICAgICAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gOHJlbSk7XHJcbiAgICAgICAgYmFja2dyb3VuZDogdXJsKC4uLy4uL2Fzc2V0cy9sYXlvdXQvaW1hZ2VzL2Jhbm5lci1pbWcuanBnKSBjZW50ZXIgY2VudGVyIG5vLXJlcGVhdDtcclxuICAgICAgICBiYWNrZ3JvdW5kLXNpemU6IGNvdmVyO1xyXG5cclxuICAgICAgICAuYmFubmVyLWJveCB7XHJcbiAgICAgICAgICAgIG1hcmdpbjogMTIwcHggMCAwIDA7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5zZXJ2aWNlcy1zZWMge1xyXG4gICAgICAgIG1hcmdpbjogLTg3cHggMCAwIDA7XHJcblxyXG4gICAgICAgIC5zZXJ2aWNlcy1ib3gucy1ib3gtMSB7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNhOGMxY2Q7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuc2VydmljZXMtYm94LnMtYm94LTIge1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAjZTNjYWIwO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLnNlcnZpY2VzLWJveC5zLWJveC0zIHtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogI2M0YTU5NztcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5zZXJ2aWNlcy1ib3gucy1ib3gtNCB7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNlODgxNmU7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5hYm91dC1zZWMge1xyXG4gICAgICAgICY6YmVmb3JlIHtcclxuICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgICAgICBjb250ZW50OiBcIlwiO1xyXG4gICAgICAgICAgICByaWdodDogMDtcclxuICAgICAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAjMDMwZjVlICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgIHdpZHRoOiAyMCU7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5saW5lIHtcclxuICAgICAgICAmOjpiZWZvcmUge1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICAgIGNvbnRlbnQ6IFwiXCI7XHJcbiAgICAgICAgICAgIGxlZnQ6IDA7XHJcbiAgICAgICAgICAgIGJvdHRvbTogMDtcclxuICAgICAgICAgICAgaGVpZ2h0OiAycHg7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICNmZmZmZmYzZCAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgICY6OmFmdGVyIHtcclxuICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgICAgICBjb250ZW50OiBcIlwiO1xyXG4gICAgICAgICAgICBsZWZ0OiAwO1xyXG4gICAgICAgICAgICBib3R0b206IDA7XHJcbiAgICAgICAgICAgIGhlaWdodDogMnB4O1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2IoMjEzIDIwMCAxODYpICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgIHdpZHRoOiA2NHB4O1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAubGluZS0yIHtcclxuICAgICAgICAmOjpiZWZvcmUge1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICAgIGNvbnRlbnQ6IFwiXCI7XHJcbiAgICAgICAgICAgIGxlZnQ6IDA7XHJcbiAgICAgICAgICAgIGJvdHRvbTogMDtcclxuICAgICAgICAgICAgaGVpZ2h0OiAycHg7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQ6IHJnYigwIDAgMCAvIDclKSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgICY6OmFmdGVyIHtcclxuICAgICAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgICAgICBjb250ZW50OiBcIlwiO1xyXG4gICAgICAgICAgICBsZWZ0OiAwO1xyXG4gICAgICAgICAgICBib3R0b206IDA7XHJcbiAgICAgICAgICAgIGhlaWdodDogMnB4O1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiByZ2IoMjEzIDIwMCAxODYpICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgIHdpZHRoOiA2NHB4O1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuY2l0eS1tZW1iZXJzLXNlYyB7XHJcbiAgICAgICAgJjpiZWZvcmUge1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICAgIGNvbnRlbnQ6IFwiXCI7XHJcbiAgICAgICAgICAgIGxlZnQ6IDA7XHJcbiAgICAgICAgICAgIGhlaWdodDogMTAwJTtcclxuICAgICAgICAgICAgYmFja2dyb3VuZDogdmFyKC0tb3JhbmdlLTUwKSAhaW1wb3J0YW50O1xyXG4gICAgICAgICAgICB3aWR0aDogMjAlO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuY2l0eS1tZW1iZXJzLWxlZnQge1xyXG4gICAgICAgIHdpZHRoOiA3MCU7XHJcblxyXG4gICAgICAgIC5jbS10aW1lLWJveCB7XHJcbiAgICAgICAgICAgIGZsZXg6IDAgMCA0MCU7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuY20taW5mbyB7XHJcbiAgICAgICAgICAgIGZsZXg6IDAgMCA2MCU7XHJcbiAgICAgICAgICAgIHBhZGRpbmc6IDAgMCAwIDUwcHg7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5jaXR5LW1lbWJlcnMtcmlnaHQge1xyXG4gICAgICAgIHdpZHRoOiAzMCU7XHJcblxyXG4gICAgICAgIC5jbS1yaWdodC1ib3gge1xyXG4gICAgICAgICAgICBoMyB7XHJcbiAgICAgICAgICAgICAgICBzdXAge1xyXG4gICAgICAgICAgICAgICAgICAgIHRvcDogN3B4O1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5xdWljay1hY2Nlc3Mtc2VjIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiB1cmwoLi4vLi4vYXNzZXRzL2xheW91dC9pbWFnZXMvcGV4ZWxzLXZpdG9yLWd1c21hby5qcGcpIGNlbnRlciByaWdodCBuby1yZXBlYXQ7XHJcbiAgICAgICAgYmFja2dyb3VuZC1zaXplOiAzOCUgYXV0bztcclxuXHJcbiAgICAgICAgLnF1aWNrLWFjY2Vzcy1sZWZ0IHtcclxuICAgICAgICAgICAgZmxleDogMCAwIDY3JTtcclxuICAgICAgICAgICAgd2lkdGg6IDY3JTtcclxuXHJcbiAgICAgICAgICAgIC5xdWljay1hY2Nlc3MtbGlzdCB7XHJcbiAgICAgICAgICAgICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpbGwsIG1pbm1heCgzMy4zMzMlLCAxZnIpKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLnF1aWNrLWFjY2Vzcy1yaWdodCB7XHJcbiAgICAgICAgICAgIGZsZXg6IDAgMCAzMyU7XHJcbiAgICAgICAgICAgIHdpZHRoOiAzMyU7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5uZXdzLXNlYyB7XHJcbiAgICAgICAgLm5ld3MtbGlzdCB7XHJcbiAgICAgICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZmlsbCwgbWlubWF4KDMwMHB4LCAxZnIpKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG5cclxuXHJcbiAgICAubmV3cy1sZXR0ZXItc2VjIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTgwZGVnLCAjMDMwZjVlIDUwJSwgdHJhbnNwYXJlbnQgNTAlKTtcclxuXHJcbiAgICAgICAgLm5ld3MtbGV0dGVyLWJveC1saXN0IHtcclxuICAgICAgICAgICAgLm5ld3MtbGV0dGVyLWltZyB7XHJcbiAgICAgICAgICAgICAgICBoZWlnaHQ6IDY0MHB4O1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC53aGF0LWhhcHBuaW5nLXNlYyB7XHJcbiAgICAgICAgLndoYXQtaGFwcG5pbmctYm9keSB7XHJcbiAgICAgICAgICAgIC53aGF0LWhhcHBuaW5nLWltZyB7XHJcbiAgICAgICAgICAgICAgICBoZWlnaHQ6IDgwMHB4O1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAud2hhdC1oYXBwbmluZy1ib3gge1xyXG4gICAgICAgICAgICAgICAgLndoLWltZy1ib3gge1xyXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMjBweDtcclxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IDEyMHB4O1xyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIC53aC1jbnQge1xyXG4gICAgICAgICAgICAgICAgICAgIG1heC13aWR0aDogNzAlO1xyXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuXHJcbn0iLCI6Om5nLWRlZXAgLm1heC13LTEyMDAge1xuICBtYXgtd2lkdGg6IDEyMDBweDtcbn1cbjo6bmctZGVlcCAuc2Vjb25kYXJ5LWNvbG9yIHtcbiAgY29sb3I6ICMwMzBmNWUgIWltcG9ydGFudDtcbn1cbjo6bmctZGVlcCAuc2Vjb25kYXJ5LWJnLWNvbG9yIHtcbiAgYmFja2dyb3VuZDogIzAzMGY1ZSAhaW1wb3J0YW50O1xufVxuOjpuZy1kZWVwIC5mb250LWV4dHJhYm9sZCB7XG4gIGZvbnQtd2VpZ2h0OiA5MDAgIWltcG9ydGFudDtcbn1cbjo6bmctZGVlcCAuZC1ncmlkIHtcbiAgZGlzcGxheTogZ3JpZCAhaW1wb3J0YW50O1xufVxuOjpuZy1kZWVwIC5vYmplY3QtZml0LWNvbnRhaW4ge1xuICBvYmplY3QtZml0OiBjb250YWluO1xufVxuOjpuZy1kZWVwIC5vYmplY3QtZml0LWNvdmVyIHtcbiAgb2JqZWN0LWZpdDogY292ZXI7XG59XG46Om5nLWRlZXAgLmgtMy0zcmVtIHtcbiAgaGVpZ2h0OiAzLjNyZW0gIWltcG9ydGFudDtcbn1cbjo6bmctZGVlcCAuYmctYmx1ZS03NSB7XG4gIGJhY2tncm91bmQ6IHJnYigyMjcsIDI0MywgMjU1KSAhaW1wb3J0YW50O1xufVxuOjpuZy1kZWVwIC5iYW5uZXItc2VjIHtcbiAgbWFyZ2luOiA4cmVtIDAgMCAwO1xuICBoZWlnaHQ6IGNhbGMoMTAwdmggLSA4cmVtKTtcbiAgYmFja2dyb3VuZDogdXJsKC4uLy4uL2Fzc2V0cy9sYXlvdXQvaW1hZ2VzL2Jhbm5lci1pbWcuanBnKSBjZW50ZXIgY2VudGVyIG5vLXJlcGVhdDtcbiAgYmFja2dyb3VuZC1zaXplOiBjb3Zlcjtcbn1cbjo6bmctZGVlcCAuYmFubmVyLXNlYyAuYmFubmVyLWJveCB7XG4gIG1hcmdpbjogMTIwcHggMCAwIDA7XG59XG46Om5nLWRlZXAgLnNlcnZpY2VzLXNlYyB7XG4gIG1hcmdpbjogLTg3cHggMCAwIDA7XG59XG46Om5nLWRlZXAgLnNlcnZpY2VzLXNlYyAuc2VydmljZXMtYm94LnMtYm94LTEge1xuICBiYWNrZ3JvdW5kOiAjYThjMWNkO1xufVxuOjpuZy1kZWVwIC5zZXJ2aWNlcy1zZWMgLnNlcnZpY2VzLWJveC5zLWJveC0yIHtcbiAgYmFja2dyb3VuZDogI2UzY2FiMDtcbn1cbjo6bmctZGVlcCAuc2VydmljZXMtc2VjIC5zZXJ2aWNlcy1ib3gucy1ib3gtMyB7XG4gIGJhY2tncm91bmQ6ICNjNGE1OTc7XG59XG46Om5nLWRlZXAgLnNlcnZpY2VzLXNlYyAuc2VydmljZXMtYm94LnMtYm94LTQge1xuICBiYWNrZ3JvdW5kOiAjZTg4MTZlO1xufVxuOjpuZy1kZWVwIC5hYm91dC1zZWM6YmVmb3JlIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICBjb250ZW50OiBcIlwiO1xuICByaWdodDogMDtcbiAgaGVpZ2h0OiAxMDAlO1xuICBiYWNrZ3JvdW5kOiAjMDMwZjVlICFpbXBvcnRhbnQ7XG4gIHdpZHRoOiAyMCU7XG59XG46Om5nLWRlZXAgLmxpbmU6OmJlZm9yZSB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgY29udGVudDogXCJcIjtcbiAgbGVmdDogMDtcbiAgYm90dG9tOiAwO1xuICBoZWlnaHQ6IDJweDtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIzOTIxNTY4NjMpICFpbXBvcnRhbnQ7XG4gIHdpZHRoOiAxMDAlO1xufVxuOjpuZy1kZWVwIC5saW5lOjphZnRlciB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgY29udGVudDogXCJcIjtcbiAgbGVmdDogMDtcbiAgYm90dG9tOiAwO1xuICBoZWlnaHQ6IDJweDtcbiAgYmFja2dyb3VuZDogcmdiKDIxMywgMjAwLCAxODYpICFpbXBvcnRhbnQ7XG4gIHdpZHRoOiA2NHB4O1xufVxuOjpuZy1kZWVwIC5saW5lLTI6OmJlZm9yZSB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgY29udGVudDogXCJcIjtcbiAgbGVmdDogMDtcbiAgYm90dG9tOiAwO1xuICBoZWlnaHQ6IDJweDtcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjA3KSAhaW1wb3J0YW50O1xuICB3aWR0aDogMTAwJTtcbn1cbjo6bmctZGVlcCAubGluZS0yOjphZnRlciB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgY29udGVudDogXCJcIjtcbiAgbGVmdDogMDtcbiAgYm90dG9tOiAwO1xuICBoZWlnaHQ6IDJweDtcbiAgYmFja2dyb3VuZDogcmdiKDIxMywgMjAwLCAxODYpICFpbXBvcnRhbnQ7XG4gIHdpZHRoOiA2NHB4O1xufVxuOjpuZy1kZWVwIC5jaXR5LW1lbWJlcnMtc2VjOmJlZm9yZSB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgY29udGVudDogXCJcIjtcbiAgbGVmdDogMDtcbiAgaGVpZ2h0OiAxMDAlO1xuICBiYWNrZ3JvdW5kOiB2YXIoLS1vcmFuZ2UtNTApICFpbXBvcnRhbnQ7XG4gIHdpZHRoOiAyMCU7XG59XG46Om5nLWRlZXAgLmNpdHktbWVtYmVycy1sZWZ0IHtcbiAgd2lkdGg6IDcwJTtcbn1cbjo6bmctZGVlcCAuY2l0eS1tZW1iZXJzLWxlZnQgLmNtLXRpbWUtYm94IHtcbiAgZmxleDogMCAwIDQwJTtcbn1cbjo6bmctZGVlcCAuY2l0eS1tZW1iZXJzLWxlZnQgLmNtLWluZm8ge1xuICBmbGV4OiAwIDAgNjAlO1xuICBwYWRkaW5nOiAwIDAgMCA1MHB4O1xufVxuOjpuZy1kZWVwIC5jaXR5LW1lbWJlcnMtcmlnaHQge1xuICB3aWR0aDogMzAlO1xufVxuOjpuZy1kZWVwIC5jaXR5LW1lbWJlcnMtcmlnaHQgLmNtLXJpZ2h0LWJveCBoMyBzdXAge1xuICB0b3A6IDdweDtcbn1cbjo6bmctZGVlcCAucXVpY2stYWNjZXNzLXNlYyB7XG4gIGJhY2tncm91bmQ6IHVybCguLi8uLi9hc3NldHMvbGF5b3V0L2ltYWdlcy9wZXhlbHMtdml0b3ItZ3VzbWFvLmpwZykgY2VudGVyIHJpZ2h0IG5vLXJlcGVhdDtcbiAgYmFja2dyb3VuZC1zaXplOiAzOCUgYXV0bztcbn1cbjo6bmctZGVlcCAucXVpY2stYWNjZXNzLXNlYyAucXVpY2stYWNjZXNzLWxlZnQge1xuICBmbGV4OiAwIDAgNjclO1xuICB3aWR0aDogNjclO1xufVxuOjpuZy1kZWVwIC5xdWljay1hY2Nlc3Mtc2VjIC5xdWljay1hY2Nlc3MtbGVmdCAucXVpY2stYWNjZXNzLWxpc3Qge1xuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpbGwsIG1pbm1heCgzMy4zMzMlLCAxZnIpKTtcbn1cbjo6bmctZGVlcCAucXVpY2stYWNjZXNzLXNlYyAucXVpY2stYWNjZXNzLXJpZ2h0IHtcbiAgZmxleDogMCAwIDMzJTtcbiAgd2lkdGg6IDMzJTtcbn1cbjo6bmctZGVlcCAubmV3cy1zZWMgLm5ld3MtbGlzdCB7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZmlsbCwgbWlubWF4KDMwMHB4LCAxZnIpKTtcbn1cbjo6bmctZGVlcCAubmV3cy1sZXR0ZXItc2VjIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDE4MGRlZywgIzAzMGY1ZSA1MCUsIHRyYW5zcGFyZW50IDUwJSk7XG59XG46Om5nLWRlZXAgLm5ld3MtbGV0dGVyLXNlYyAubmV3cy1sZXR0ZXItYm94LWxpc3QgLm5ld3MtbGV0dGVyLWltZyB7XG4gIGhlaWdodDogNjQwcHg7XG59XG46Om5nLWRlZXAgLndoYXQtaGFwcG5pbmctc2VjIC53aGF0LWhhcHBuaW5nLWJvZHkgLndoYXQtaGFwcG5pbmctaW1nIHtcbiAgaGVpZ2h0OiA4MDBweDtcbn1cbjo6bmctZGVlcCAud2hhdC1oYXBwbmluZy1zZWMgLndoYXQtaGFwcG5pbmctYm9keSAud2hhdC1oYXBwbmluZy1ib3ggLndoLWltZy1ib3gge1xuICB3aWR0aDogMTIwcHg7XG4gIGhlaWdodDogMTIwcHg7XG59XG46Om5nLWRlZXAgLndoYXQtaGFwcG5pbmctc2VjIC53aGF0LWhhcHBuaW5nLWJvZHkgLndoYXQtaGFwcG5pbmctYm94IC53aC1jbnQge1xuICBtYXgtd2lkdGg6IDcwJTtcbiAgd2lkdGg6IDEwMCU7XG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "i_r2", "ɵɵadvance", "service_r1", "Icon", "url", "ɵɵsanitizeUrl", "Title", "ɵɵtextInterpolate", "Description", "ɵɵsanitizeHtml", "ɵɵtextInterpolate1", "Button_Title", "HomeComponent", "constructor", "route", "CMSservice", "bannerData", "servicesData", "ngOnInit", "content", "snapshot", "data", "console", "log", "bannerComponent", "getDataByComponentName", "body", "length", "servicesComponents", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "ContentService", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵtemplate", "HomeComponent_div_12_Template", "ɵɵstyleProp", "Image", "Annotation"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home.component.ts", "D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { ContentService } from '../core/services/content-vendor.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrl: './home.component.scss'\r\n})\r\nexport class HomeComponent {\r\n\r\n  content!: any;\r\n  bannerData: any = null;\r\n  servicesData: any[] = [];\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private CMSservice: ContentService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.content = this.route.snapshot.data['content'];\r\n    console.log('Home Content:', this.content);\r\n\r\n    // Extract banner\r\n    const bannerComponent = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.banner\");\r\n    if (bannerComponent?.length) {\r\n      this.bannerData = bannerComponent[0];\r\n    }\r\n\r\n    // Extract services\r\n    const servicesComponents = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.services\");\r\n    if (servicesComponents?.length) {\r\n      this.servicesData = servicesComponents;\r\n    }\r\n  }\r\n\r\n}\r\n", "<!--BANNER SEC-->\r\n<section class=\"banner-sec relative\">\r\n    <div class=\"banner-body h-full flex align-items-center\">\r\n        <div class=\"banner-box relative flex-1 p-8\"\r\n             [style.background-image]=\"bannerData?.Image?.url ? 'url(' + bannerData.Image.url + ')' : null\"\r\n             [style.background-size]=\"'cover'\"\r\n             [style.background-position]=\"'center'\"></div>\r\n        <div class=\"banner-box relative flex-1 bg-white p-8 flex flex-column justify-content-center\">\r\n            <h4 class=\"mb-4 text-yellow-400\">{{ bannerData?.Annotation || 'Far away from the every day!' }}</h4>\r\n            <h1 class=\"m-0 mb-5 text-8xl line-height-1 font-extrabold text-color\">{{ bannerData?.Title || 'Community of endless beauty & Calm' }}</h1>\r\n            <p class=\"m-0 text-lg font-medium\">{{ bannerData?.Description || 'Drawn by clean air and mythical light, visitors come to experience traditions, fine art, great cuisine and natural beauty of the landscape.' }}</p>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--BANNER SEC-->\r\n\r\n<!--SERVICES SEC-->\r\n<section class=\"services-sec w-full\">\r\n    <div class=\"services-body relative max-w-1200 w-full mx-auto px-4 flex\">\r\n        <div class=\"services-box p-5 flex-1 flex flex-column gap-2\"\r\n             *ngFor=\"let service of servicesData; let i = index\"\r\n             [ngClass]=\"'s-box-' + (i + 1)\">\r\n            <img [src]=\"service.Icon?.url\" class=\"h-fit w-5rem\" [alt]=\"service.Title\" />\r\n            <h4 class=\"font-bold text-white\">{{ service.Title }}</h4>\r\n            <div [innerHTML]=\"service.Description\" class=\"flex-grow-1\"></div>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component h-3rem justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                {{ service.Button_Title }} <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--SERVICES SEC-->\r\n\r\n<!--ABOUT SEC-->\r\n<section class=\"about-sec relative\">\r\n    <div class=\"about-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap\">\r\n        <div class=\"about-box pr-8 w-6 secondary-bg-color\">\r\n            <img src=\"/assets/layout/images/director-img.jpg\" alt=\"\" class=\"w-full\" />\r\n        </div>\r\n        <div class=\"about-box p-8 pr-0 w-6 secondary-bg-color flex flex-column justify-content-start\">\r\n            <h4 class=\"mb-4 text-yellow-400\">Far away from the every day!</h4>\r\n            <h2 class=\"line m-0 mb-6 relative pb-5 text-6xl line-height-2 font-bold text-white\">A Message From the\r\n                Director\r\n            </h2>\r\n            <p class=\"m-0 text-lg text-white flex-1\">Old Town of Riverside is a special place with limitless potential,\r\n                where everyone deserves equal access\r\n                to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in every\r\n                neighborhood. It exists to serve you and be a resource for you, your family, and your community.</p>\r\n\r\n            <button type=\"button\"\r\n                class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--ABOUT SEC-->\r\n\r\n<!--CITY MEMBERS SEC-->\r\n<section class=\"city-members-sec relative\">\r\n    <div class=\"city-members-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap\">\r\n        <div class=\"city-members-left pt-8 pr-7 pb-8 pl-0 relative flex bg-orange-50\">\r\n            <div class=\"cm-time-box\">\r\n                <img src=\"/assets/layout/images/time-screen.png\" alt=\"\" class=\"w-full\" />\r\n            </div>\r\n            <div class=\"cm-info\">\r\n                <h4 class=\"mb-4 text-yellow-400\">Our city in numbers</h4>\r\n                <h2 class=\"line m-0 mb-6 relative pb-5 text-4xl line-height-2 font-bold text-color\">Programs to help\r\n                    launch,\r\n                    grow and expand any business</h2>\r\n                <p class=\"m-0 text-lg text-color flex-1\">Old Town of Riverside is a special place with limitless\r\n                    potential, where everyone deserves equal access\r\n                    to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in\r\n                    every\r\n                    neighborhood. It exists to serve you and be a resource for you, your family, and your community.</p>\r\n            </div>\r\n        </div>\r\n        <div class=\"city-members-right p-7 pr-0 relative flex flex-column gap-8\">\r\n            <div class=\"cm-right-box\">\r\n                <h3 class=\"flex align-items-start text-8xl font-extrabold line-height-1\">41 <sup\r\n                        class=\"relative text-4xl font-bold inline-block\">k</sup></h3>\r\n                <h5 class=\"uppercase\">Highly-educated creative workforce</h5>\r\n                <p class=\"text-color-secondary\">A vibrant destination where innovations in design and technology are\r\n                    born. A place for you.</p>\r\n            </div>\r\n            <div class=\"cm-right-box\">\r\n                <h3 class=\" flex align-items-start text-8xl font-extrabold line-height-1\">8 <sup\r\n                        class=\"relative text-4xl font-bold inline-block\">th</sup></h3>\r\n                <h5 class=\"uppercase\">Highest Per Capita Income in Virginia</h5>\r\n                <p class=\"text-color-secondary\">A vibrant destination where innovations in design and technology are\r\n                    born. A place for you.</p>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--CITY MEMBERS SEC-->\r\n\r\n<!--QUICK ACCESS SEC-->\r\n<section class=\"quick-access-sec relative\">\r\n    <div class=\"quick-access-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap\">\r\n        <div class=\"quick-access-left relative flex flex-column pt-8 pb-8\">\r\n            <h2 class=\"mb-4 flex align-items-start text-6xl font-extrabold line-height-1\">Quick Access</h2>\r\n            <h4 class=\"line-2 inline-flex w-fit m-0 mb-6 relative pb-5 text-xl line-height-2 font-bold text-yellow-400\">\r\n                What can the City help you with today?</h4>\r\n\r\n            <div class=\"quick-access-list d-grid w-full\">\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-bottom-none border-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/001-toll.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Road construction</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/006-virus.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">COVID-19 updates</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/003-trash-can.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Garbage pickup</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div class=\"quick-access-box p-4 w-full flex flex-column border-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/004-parking.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Parking</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-bottom-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/005-lecture.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Council meetings</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-bottom-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/008-basketball.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Recreation and sport programs</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"quick-access-right\"></div>\r\n    </div>\r\n</section>\r\n<!--QUICK ACCESS SEC-->\r\n\r\n<!--NEWS SEC-->\r\n<section class=\"news-sec relative pt-7 pb-8 secondary-bg-color\">\r\n    <div class=\"news-body relative max-w-1200 w-full mx-auto px-4\">\r\n        <div class=\"news-title\">\r\n            <h5 class=\"line m-0 mb-5 pb-5 relative inline-flex w-fit text-yellow-400\">Find out what’s going on & stay up\r\n                to date.</h5>\r\n            <h2 class=\"m-0 mb-6 relative text-6xl line-height-2 font-bold text-white\">Latest News</h2>\r\n        </div>\r\n        <div class=\"news-list d-grid w-full gap-6\">\r\n            <div class=\"news-box w-full flex flex-column\">\r\n                <div class=\"new-img flex align-items-center justify-content-center overflow-hidden\">\r\n                    <img src=\"/assets/layout/images/new-img-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                </div>\r\n                <h3 class=\"text-white text-xl flex-1\">Proposed Downtown District Ordinance</h3>\r\n                <div class=\"mb-3 flex gap-3 align-items-center\">\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">calendar_month</span> September 16, 2018\r\n                    </div>\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">visibility</span> 240\r\n                    </div>\r\n                </div>\r\n                <p class=\"mb-5 text-sm text-white\">The goal of the proposed ordinance is to create safe venues for\r\n                    customers\r\n                    and to discourage illegal activities. The governing officials of...</p>\r\n\r\n                <button type=\"button\"\r\n                    class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                    Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n                </button>\r\n            </div>\r\n            <div class=\"news-box w-full flex flex-column\">\r\n                <div class=\"new-img flex align-items-center justify-content-center overflow-hidden\">\r\n                    <img src=\"/assets/layout/images/new-img-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                </div>\r\n                <h3 class=\"text-white text-xl flex-1\">Annual Water Quality Report (Gallery Post)</h3>\r\n                <div class=\"mb-3 flex gap-3 align-items-center\">\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">calendar_month</span> September 16, 2018\r\n                    </div>\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">visibility</span> 240\r\n                    </div>\r\n                </div>\r\n                <p class=\"mb-5 text-sm text-white\">The goal of the proposed ordinance is to create safe venues for\r\n                    customers\r\n                    and to discourage illegal activities. The governing officials of...</p>\r\n\r\n                <button type=\"button\"\r\n                    class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                    Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n                </button>\r\n            </div>\r\n            <div class=\"news-box w-full flex flex-column\">\r\n                <div class=\"new-img flex align-items-center justify-content-center overflow-hidden\">\r\n                    <img src=\"/assets/layout/images/new-img-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                </div>\r\n                <h3 class=\"text-white text-xl flex-1\">Waste Industries Garbage Pick Up: Embeds</h3>\r\n                <div class=\"mb-3 flex gap-3 align-items-center\">\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">calendar_month</span> September 16, 2018\r\n                    </div>\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">visibility</span> 240\r\n                    </div>\r\n                </div>\r\n                <p class=\"mb-5 text-sm text-white\">The goal of the proposed ordinance is to create safe venues for\r\n                    customers\r\n                    and to discourage illegal activities. The governing officials of...</p>\r\n\r\n                <button type=\"button\"\r\n                    class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                    Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--NEWS SEC-->\r\n\r\n<!--NEWS LETTER SEC-->\r\n<section class=\"news-letter-sec relative\">\r\n    <div class=\"news-letter-body relative max-w-1200 w-full mx-auto px-4\">\r\n        <div class=\"news-letter-box-list flex bg-white shadow-1\">\r\n            <div class=\"news-letter-box w-6 p-7 flex flex-column justify-content-center\">\r\n                <h2\r\n                    class=\"line-2 relative mb-5 pb-5 inline-flex w-fit align-items-start text-6xl font-extrabold line-height-1\">\r\n                    Sign up for News & Alerts</h2>\r\n                <p>Stay connected to the City of Riverside by signing up to receive official news and alerts by email!\r\n                </p>\r\n                <div class=\"news-letter-form mt-5 relative flex align-items-center\">\r\n                    <input type=\"text\" pInputText class=\"h-3-3rem border-noround flex-1\"\r\n                        placeholder=\"Your email address\" />\r\n                    <button type=\"button\"\r\n                        class=\"px-6 p-element p-ripple border-noround p-button p-component h-3-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                        Sign Up\r\n                    </button>\r\n                </div>\r\n            </div>\r\n            <div class=\"news-letter-img w-6 flex align-items-center justify-content-center overflow-hidden\">\r\n                <img src=\"/assets/layout/images/news-letter-img.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--NEWS LETTER SEC-->\r\n\r\n<!--WHAT HAPPNENING SEC-->\r\n<section class=\"what-happning-sec mt-8 relative px-4\">\r\n    <div class=\"what-happning-body relative w-full mx-auto flex bg-orange-50\">\r\n        <div class=\"what-happning-img w-6 flex align-items-center justify-content-center overflow-hidden\">\r\n            <img src=\"/assets/layout/images/whats-happnening-mg.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n        </div>\r\n        <div class=\"what-happning-right w-6 p-8\">\r\n            <div class=\"what-happning-title\">\r\n                <h5 class=\"line-2 m-0 mb-5 pb-5 relative inline-flex w-fit text-yellow-400\">Join the fun in our city!\r\n                </h5>\r\n                <h2 class=\"m-0 mb-6 relative text-6xl line-height-2 font-bold text-color\">What's Happening</h2>\r\n            </div>\r\n            <div class=\"what-happning-list\">\r\n                <div\r\n                    class=\"what-happning-box mb-4 pb-4 flex align-items-center gap-5 border-none border-bottom-1 border-solid surface-border\">\r\n                    <div class=\"wh-img-box flex align-items-center justify-content-center overflow-hidden\">\r\n                        <img src=\"/assets/layout/images/arches-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                    </div>\r\n                    <div class=\"wh-cnt-sec flex-1 flex align-items-center gap-6\">\r\n                        <div class=\"wh-cnt flex flex-column\">\r\n                            <h3 class=\"text-xl font-bold\">Heritage 10th Anniversary</h3>\r\n                            <div class=\"flex align-items-center gap-3 flex-wrap\">\r\n                                <div class=\"flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100\">\r\n                                    <span>June 9, 2027</span> - <span>June 18, 2027</span>\r\n                                </div>\r\n                                <div class=\"flex align-items-center gap-2\">All Day at <b>Baker Hall</b></div>\r\n                            </div>\r\n                        </div>\r\n                        <button type=\"button\"\r\n                            class=\"px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold\">\r\n                            Find out more\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div\r\n                    class=\"what-happning-box mb-4 pb-4 flex align-items-center gap-5 border-none border-bottom-1 border-solid surface-border\">\r\n                    <div class=\"wh-img-box flex align-items-center justify-content-center overflow-hidden\">\r\n                        <img src=\"/assets/layout/images/arches-2.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                    </div>\r\n                    <div class=\"wh-cnt-sec flex-1 flex align-items-center gap-6\">\r\n                        <div class=\"wh-cnt flex flex-column\">\r\n                            <h3 class=\"text-xl font-bold\">Along Pines Run</h3>\r\n                            <div class=\"flex align-items-center gap-3 flex-wrap\">\r\n                                <div class=\"flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100\">\r\n                                    <span>July 22, 2027</span>\r\n                                </div>\r\n                                <div class=\"flex align-items-center gap-2\">12:00 am at <b>Boulder City Council</b></div>\r\n                            </div>\r\n                        </div>\r\n                        <button type=\"button\"\r\n                            class=\"px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold\">\r\n                            Find out more\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div class=\"what-happning-box flex align-items-center gap-5\">\r\n                    <div class=\"wh-img-box flex align-items-center justify-content-center overflow-hidden\">\r\n                        <img src=\"/assets/layout/images/arches-3.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                    </div>\r\n                    <div class=\"wh-cnt-sec flex-1 flex align-items-center gap-6\">\r\n                        <div class=\"wh-cnt flex flex-column\">\r\n                            <h3 class=\"text-xl font-bold\">Touch A Truck</h3>\r\n                            <div class=\"flex align-items-center gap-3 flex-wrap\">\r\n                                <div class=\"flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100\">\r\n                                    <span>June 9, 2027</span> - <span>June 18, 2027</span>\r\n                                </div>\r\n                                <div class=\"flex align-items-center gap-2\">9:00 am - 5:00 pm at <b>Baker Hall</b></div>\r\n                            </div>\r\n                        </div>\r\n                        <button type=\"button\"\r\n                            class=\"px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold\">\r\n                            Find out more\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--WHAT HAPPNENING SEC-->"], "mappings": ";;;;;;;ICmBQA,EAAA,CAAAC,cAAA,cAEoC;IAChCD,EAAA,CAAAE,SAAA,eAA4E;IAC5EF,EAAA,CAAAC,cAAA,cAAiC;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzDJ,EAAA,CAAAE,SAAA,eAAiE;IACjEF,EAAA,CAAAC,cAAA,kBACqK;IACjKD,EAAA,CAAAG,MAAA,GAA2B;IAAAH,EAAA,CAAAC,cAAA,eAAuC;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAEzFH,EAFyF,CAAAI,YAAA,EAAO,EACnF,EACP;;;;;IARDJ,EAAA,CAAAK,UAAA,wBAAAC,IAAA,MAA8B;IAC1BN,EAAA,CAAAO,SAAA,EAAyB;IAAsBP,EAA/C,CAAAK,UAAA,QAAAG,UAAA,CAAAC,IAAA,kBAAAD,UAAA,CAAAC,IAAA,CAAAC,GAAA,EAAAV,EAAA,CAAAW,aAAA,CAAyB,QAAAH,UAAA,CAAAI,KAAA,CAA2C;IACxCZ,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAa,iBAAA,CAAAL,UAAA,CAAAI,KAAA,CAAmB;IAC/CZ,EAAA,CAAAO,SAAA,EAAiC;IAAjCP,EAAA,CAAAK,UAAA,cAAAG,UAAA,CAAAM,WAAA,EAAAd,EAAA,CAAAe,cAAA,CAAiC;IAGlCf,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAgB,kBAAA,MAAAR,UAAA,CAAAS,YAAA,MAA2B;;;ADlB3C,OAAM,MAAOC,aAAa;EAMxBC,YACUC,KAAqB,EACrBC,UAA0B;IAD1B,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IALpB,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,YAAY,GAAU,EAAE;EAKpB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,OAAO,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;IAClDC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACJ,OAAO,CAAC;IAE1C;IACA,MAAMK,eAAe,GAAG,IAAI,CAACT,UAAU,CAACU,sBAAsB,CAAC,IAAI,CAACN,OAAO,CAACO,IAAI,EAAE,sBAAsB,CAAC;IACzG,IAAIF,eAAe,EAAEG,MAAM,EAAE;MAC3B,IAAI,CAACX,UAAU,GAAGQ,eAAe,CAAC,CAAC,CAAC;IACtC;IAEA;IACA,MAAMI,kBAAkB,GAAG,IAAI,CAACb,UAAU,CAACU,sBAAsB,CAAC,IAAI,CAACN,OAAO,CAACO,IAAI,EAAE,wBAAwB,CAAC;IAC9G,IAAIE,kBAAkB,EAAED,MAAM,EAAE;MAC9B,IAAI,CAACV,YAAY,GAAGW,kBAAkB;IACxC;EACF;;;uBA1BWhB,aAAa,EAAAlB,EAAA,CAAAmC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAArC,EAAA,CAAAmC,iBAAA,CAAAG,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAbrB,aAAa;MAAAsB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPtB9C,EADJ,CAAAC,cAAA,iBAAqC,aACuB;UACpDD,EAAA,CAAAE,SAAA,aAGkD;UAE9CF,EADJ,CAAAC,cAAA,aAA6F,YACxD;UAAAD,EAAA,CAAAG,MAAA,GAA8D;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACpGJ,EAAA,CAAAC,cAAA,YAAsE;UAAAD,EAAA,CAAAG,MAAA,GAA+D;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC1IJ,EAAA,CAAAC,cAAA,WAAmC;UAAAD,EAAA,CAAAG,MAAA,GAA8K;UAG7NH,EAH6N,CAAAI,YAAA,EAAI,EACnN,EACJ,EACA;UAKNJ,EADJ,CAAAC,cAAA,kBAAqC,cACuC;UACpED,EAAA,CAAAgD,UAAA,KAAAC,6BAAA,iBAEoC;UAU5CjD,EADI,CAAAI,YAAA,EAAM,EACA;UAMFJ,EAFR,CAAAC,cAAA,mBAAoC,eAC+C,eACxB;UAC/CD,EAAA,CAAAE,SAAA,eAA0E;UAC9EF,EAAA,CAAAI,YAAA,EAAM;UAEFJ,EADJ,CAAAC,cAAA,eAA8F,aACzD;UAAAD,EAAA,CAAAG,MAAA,oCAA4B;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClEJ,EAAA,CAAAC,cAAA,cAAoF;UAAAD,EAAA,CAAAG,MAAA,oCAEpF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAG,MAAA,sTAG2D;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAExGJ,EAAA,CAAAC,cAAA,kBACgL;UAC5KD,EAAA,CAAAG,MAAA,oBAAW;UAAAH,EAAA,CAAAC,cAAA,gBAAuC;UAAAD,EAAA,CAAAG,MAAA,uBAAe;UAIjFH,EAJiF,CAAAI,YAAA,EAAO,EACnE,EACP,EACJ,EACA;UAOEJ,EAHZ,CAAAC,cAAA,mBAA2C,eAC+C,eACJ,eACjD;UACrBD,EAAA,CAAAE,SAAA,eAAyE;UAC7EF,EAAA,CAAAI,YAAA,EAAM;UAEFJ,EADJ,CAAAC,cAAA,eAAqB,aACgB;UAAAD,EAAA,CAAAG,MAAA,2BAAmB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzDJ,EAAA,CAAAC,cAAA,cAAoF;UAAAD,EAAA,CAAAG,MAAA,6DAEpD;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACrCJ,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAG,MAAA,sTAI2D;UAE5GH,EAF4G,CAAAI,YAAA,EAAI,EACtG,EACJ;UAGEJ,EAFR,CAAAC,cAAA,eAAyE,eAC3C,cACmD;UAAAD,EAAA,CAAAG,MAAA,WAAG;UAAAH,EAAA,CAAAC,cAAA,eACnB;UAAAD,EAAA,CAAAG,MAAA,SAAC;UAAMH,EAAN,CAAAI,YAAA,EAAM,EAAK;UACrEJ,EAAA,CAAAC,cAAA,cAAsB;UAAAD,EAAA,CAAAG,MAAA,0CAAkC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC7DJ,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAG,MAAA,mGACN;UAC9BH,EAD8B,CAAAI,YAAA,EAAI,EAC5B;UAEFJ,EADJ,CAAAC,cAAA,eAA0B,cACoD;UAAAD,EAAA,CAAAG,MAAA,UAAE;UAAAH,EAAA,CAAAC,cAAA,eACnB;UAAAD,EAAA,CAAAG,MAAA,UAAE;UAAMH,EAAN,CAAAI,YAAA,EAAM,EAAK;UACtEJ,EAAA,CAAAC,cAAA,cAAsB;UAAAD,EAAA,CAAAG,MAAA,6CAAqC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChEJ,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAG,MAAA,mGACN;UAI1CH,EAJ0C,CAAAI,YAAA,EAAI,EAC5B,EACJ,EACJ,EACA;UAOEJ,EAHZ,CAAAC,cAAA,mBAA2C,eAC+C,eACf,cACe;UAAAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC/FJ,EAAA,CAAAC,cAAA,cAA4G;UACxGD,EAAA,CAAAG,MAAA,+CAAsC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAG3CJ,EADJ,CAAAC,cAAA,eAA6C,eAEwE;UAC7GD,EAAA,CAAAE,SAAA,eAA2E;UAC3EF,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAG,MAAA,yBAAiB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC5DJ,EAAA,CAAAC,cAAA,aAAmB;UAAAD,EAAA,CAAAG,MAAA,yGACI;UAC3BH,EAD2B,CAAAI,YAAA,EAAI,EACzB;UACNJ,EAAA,CAAAC,cAAA,eAC6H;UACzHD,EAAA,CAAAE,SAAA,eAA4E;UAC5EF,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAG,MAAA,wBAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC3DJ,EAAA,CAAAC,cAAA,aAAmB;UAAAD,EAAA,CAAAG,MAAA,yGACI;UAC3BH,EAD2B,CAAAI,YAAA,EAAI,EACzB;UACNJ,EAAA,CAAAC,cAAA,eAC6H;UACzHD,EAAA,CAAAE,SAAA,eAAgF;UAChFF,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAG,MAAA,sBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzDJ,EAAA,CAAAC,cAAA,aAAmB;UAAAD,EAAA,CAAAG,MAAA,yGACI;UAC3BH,EAD2B,CAAAI,YAAA,EAAI,EACzB;UACNJ,EAAA,CAAAC,cAAA,eAA+F;UAC3FD,EAAA,CAAAE,SAAA,eAA8E;UAC9EF,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClDJ,EAAA,CAAAC,cAAA,aAAmB;UAAAD,EAAA,CAAAG,MAAA,yGACI;UAC3BH,EAD2B,CAAAI,YAAA,EAAI,EACzB;UACNJ,EAAA,CAAAC,cAAA,eAC6I;UACzID,EAAA,CAAAE,SAAA,eAA8E;UAC9EF,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAG,MAAA,wBAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC3DJ,EAAA,CAAAC,cAAA,aAAmB;UAAAD,EAAA,CAAAG,MAAA,yGACI;UAC3BH,EAD2B,CAAAI,YAAA,EAAI,EACzB;UACNJ,EAAA,CAAAC,cAAA,eAC6I;UACzID,EAAA,CAAAE,SAAA,eAAiF;UACjFF,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAG,MAAA,sCAA6B;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACxEJ,EAAA,CAAAC,cAAA,cAAmB;UAAAD,EAAA,CAAAG,MAAA,0GACI;UAGnCH,EAHmC,CAAAI,YAAA,EAAI,EACzB,EACJ,EACJ;UACNJ,EAAA,CAAAE,SAAA,gBAAsC;UAE9CF,EADI,CAAAI,YAAA,EAAM,EACA;UAOEJ,EAHZ,CAAAC,cAAA,oBAAgE,gBACG,gBACnC,eACsD;UAAAD,EAAA,CAAAG,MAAA,yDAC9D;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACjBJ,EAAA,CAAAC,cAAA,eAA0E;UAAAD,EAAA,CAAAG,MAAA,oBAAW;UACzFH,EADyF,CAAAI,YAAA,EAAK,EACxF;UAGEJ,EAFR,CAAAC,cAAA,gBAA2C,gBACO,gBAC0C;UAChFD,EAAA,CAAAE,SAAA,gBAA6F;UACjGF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAG,MAAA,6CAAoC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGvEJ,EAFR,CAAAC,cAAA,gBAAgD,gBACmC,iBAC5B;UAAAD,EAAA,CAAAG,MAAA,uBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAACJ,EAAA,CAAAG,MAAA,6BACzE;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAEFJ,EADJ,CAAAC,cAAA,gBAA+E,iBAC5B;UAAAD,EAAA,CAAAG,MAAA,mBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAACJ,EAAA,CAAAG,MAAA,cACrE;UACJH,EADI,CAAAI,YAAA,EAAM,EACJ;UACNJ,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAG,MAAA,sJAEoC;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAE3EJ,EAAA,CAAAC,cAAA,mBACqL;UACjLD,EAAA,CAAAG,MAAA,qBAAW;UAAAH,EAAA,CAAAC,cAAA,iBAAuC;UAAAD,EAAA,CAAAG,MAAA,wBAAe;UAEzEH,EAFyE,CAAAI,YAAA,EAAO,EACnE,EACP;UAEFJ,EADJ,CAAAC,cAAA,gBAA8C,gBAC0C;UAChFD,EAAA,CAAAE,SAAA,gBAA6F;UACjGF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAG,MAAA,mDAA0C;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAG7EJ,EAFR,CAAAC,cAAA,gBAAgD,gBACmC,iBAC5B;UAAAD,EAAA,CAAAG,MAAA,uBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAACJ,EAAA,CAAAG,MAAA,6BACzE;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAEFJ,EADJ,CAAAC,cAAA,gBAA+E,iBAC5B;UAAAD,EAAA,CAAAG,MAAA,mBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAACJ,EAAA,CAAAG,MAAA,cACrE;UACJH,EADI,CAAAI,YAAA,EAAM,EACJ;UACNJ,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAG,MAAA,sJAEoC;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAE3EJ,EAAA,CAAAC,cAAA,mBACqL;UACjLD,EAAA,CAAAG,MAAA,qBAAW;UAAAH,EAAA,CAAAC,cAAA,iBAAuC;UAAAD,EAAA,CAAAG,MAAA,wBAAe;UAEzEH,EAFyE,CAAAI,YAAA,EAAO,EACnE,EACP;UAEFJ,EADJ,CAAAC,cAAA,gBAA8C,gBAC0C;UAChFD,EAAA,CAAAE,SAAA,gBAA6F;UACjGF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAG,MAAA,iDAAwC;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAG3EJ,EAFR,CAAAC,cAAA,gBAAgD,gBACmC,iBAC5B;UAAAD,EAAA,CAAAG,MAAA,uBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAACJ,EAAA,CAAAG,MAAA,6BACzE;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAEFJ,EADJ,CAAAC,cAAA,gBAA+E,iBAC5B;UAAAD,EAAA,CAAAG,MAAA,mBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAACJ,EAAA,CAAAG,MAAA,cACrE;UACJH,EADI,CAAAI,YAAA,EAAM,EACJ;UACNJ,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAG,MAAA,sJAEoC;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAE3EJ,EAAA,CAAAC,cAAA,mBACqL;UACjLD,EAAA,CAAAG,MAAA,qBAAW;UAAAH,EAAA,CAAAC,cAAA,iBAAuC;UAAAD,EAAA,CAAAG,MAAA,wBAAe;UAKrFH,EALqF,CAAAI,YAAA,EAAO,EACnE,EACP,EACJ,EACJ,EACA;UAQMJ,EAJhB,CAAAC,cAAA,oBAA0C,gBACgC,gBACT,gBACwB,eAEuC;UAC5GD,EAAA,CAAAG,MAAA,mCAAyB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClCJ,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAG,MAAA,6GACH;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAC,cAAA,gBAAoE;UAChED,EAAA,CAAAE,SAAA,kBACuC;UACvCF,EAAA,CAAAC,cAAA,mBACqL;UACjLD,EAAA,CAAAG,MAAA,kBACJ;UAERH,EAFQ,CAAAI,YAAA,EAAS,EACP,EACJ;UACNJ,EAAA,CAAAC,cAAA,gBAAgG;UAC5FD,EAAA,CAAAE,SAAA,gBAAmG;UAInHF,EAHY,CAAAI,YAAA,EAAM,EACJ,EACJ,EACA;UAMFJ,EAFR,CAAAC,cAAA,oBAAsD,gBACwB,gBAC4B;UAC9FD,EAAA,CAAAE,SAAA,gBAAuG;UAC3GF,EAAA,CAAAI,YAAA,EAAM;UAGEJ,EAFR,CAAAC,cAAA,gBAAyC,gBACJ,eAC+C;UAAAD,EAAA,CAAAG,MAAA,mCAC5E;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,eAA0E;UAAAD,EAAA,CAAAG,MAAA,yBAAgB;UAC9FH,EAD8F,CAAAI,YAAA,EAAK,EAC7F;UAIEJ,EAHR,CAAAC,cAAA,gBAAgC,gBAEkG,gBACnC;UACnFD,EAAA,CAAAE,SAAA,gBAA4F;UAChGF,EAAA,CAAAI,YAAA,EAAM;UAGEJ,EAFR,CAAAC,cAAA,gBAA6D,gBACpB,eACH;UAAAD,EAAA,CAAAG,MAAA,kCAAyB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGpDJ,EAFR,CAAAC,cAAA,gBAAqD,gBACuB,aAC9D;UAAAD,EAAA,CAAAG,MAAA,qBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAACJ,EAAA,CAAAG,MAAA,YAAE;UAAAH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAG,MAAA,sBAAa;UACnDH,EADmD,CAAAI,YAAA,EAAO,EACpD;UACNJ,EAAA,CAAAC,cAAA,gBAA2C;UAAAD,EAAA,CAAAG,MAAA,oBAAW;UAAAH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAG,MAAA,mBAAU;UAE3EH,EAF2E,CAAAI,YAAA,EAAI,EAAM,EAC3E,EACJ;UACNJ,EAAA,CAAAC,cAAA,mBAC6K;UACzKD,EAAA,CAAAG,MAAA,wBACJ;UAERH,EAFQ,CAAAI,YAAA,EAAS,EACP,EACJ;UAGFJ,EAFJ,CAAAC,cAAA,gBAC8H,gBACnC;UACnFD,EAAA,CAAAE,SAAA,gBAA4F;UAChGF,EAAA,CAAAI,YAAA,EAAM;UAGEJ,EAFR,CAAAC,cAAA,gBAA6D,gBACpB,eACH;UAAAD,EAAA,CAAAG,MAAA,wBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAG1CJ,EAFR,CAAAC,cAAA,gBAAqD,gBACuB,aAC9D;UAAAD,EAAA,CAAAG,MAAA,sBAAa;UACvBH,EADuB,CAAAI,YAAA,EAAO,EACxB;UACNJ,EAAA,CAAAC,cAAA,gBAA2C;UAAAD,EAAA,CAAAG,MAAA,qBAAY;UAAAH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAG,MAAA,6BAAoB;UAEtFH,EAFsF,CAAAI,YAAA,EAAI,EAAM,EACtF,EACJ;UACNJ,EAAA,CAAAC,cAAA,mBAC6K;UACzKD,EAAA,CAAAG,MAAA,wBACJ;UAERH,EAFQ,CAAAI,YAAA,EAAS,EACP,EACJ;UAEFJ,EADJ,CAAAC,cAAA,gBAA6D,gBAC8B;UACnFD,EAAA,CAAAE,SAAA,gBAA4F;UAChGF,EAAA,CAAAI,YAAA,EAAM;UAGEJ,EAFR,CAAAC,cAAA,gBAA6D,gBACpB,eACH;UAAAD,EAAA,CAAAG,MAAA,sBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGxCJ,EAFR,CAAAC,cAAA,gBAAqD,gBACuB,aAC9D;UAAAD,EAAA,CAAAG,MAAA,qBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAACJ,EAAA,CAAAG,MAAA,YAAE;UAAAH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAG,MAAA,sBAAa;UACnDH,EADmD,CAAAI,YAAA,EAAO,EACpD;UACNJ,EAAA,CAAAC,cAAA,gBAA2C;UAAAD,EAAA,CAAAG,MAAA,8BAAqB;UAAAH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAG,MAAA,mBAAU;UAErFH,EAFqF,CAAAI,YAAA,EAAI,EAAM,EACrF,EACJ;UACNJ,EAAA,CAAAC,cAAA,mBAC6K;UACzKD,EAAA,CAAAG,MAAA,wBACJ;UAMxBH,EANwB,CAAAI,YAAA,EAAS,EACP,EACJ,EACJ,EACJ,EACJ,EACA;;;UAhVGJ,EAAA,CAAAO,SAAA,GAA8F;UAE9FP,EAFA,CAAAkD,WAAA,sBAAAH,GAAA,CAAAzB,UAAA,kBAAAyB,GAAA,CAAAzB,UAAA,CAAA6B,KAAA,kBAAAJ,GAAA,CAAAzB,UAAA,CAAA6B,KAAA,CAAAzC,GAAA,aAAAqC,GAAA,CAAAzB,UAAA,CAAA6B,KAAA,CAAAzC,GAAA,cAA8F,4BAC7D,iCACK;UAENV,EAAA,CAAAO,SAAA,GAA8D;UAA9DP,EAAA,CAAAa,iBAAA,EAAAkC,GAAA,CAAAzB,UAAA,kBAAAyB,GAAA,CAAAzB,UAAA,CAAA8B,UAAA,oCAA8D;UACzBpD,EAAA,CAAAO,SAAA,GAA+D;UAA/DP,EAAA,CAAAa,iBAAA,EAAAkC,GAAA,CAAAzB,UAAA,kBAAAyB,GAAA,CAAAzB,UAAA,CAAAV,KAAA,0CAA+D;UAClGZ,EAAA,CAAAO,SAAA,GAA8K;UAA9KP,EAAA,CAAAa,iBAAA,EAAAkC,GAAA,CAAAzB,UAAA,kBAAAyB,GAAA,CAAAzB,UAAA,CAAAR,WAAA,mJAA8K;UAU5Ld,EAAA,CAAAO,SAAA,GAAiB;UAAjBP,EAAA,CAAAK,UAAA,YAAA0C,GAAA,CAAAxB,YAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}