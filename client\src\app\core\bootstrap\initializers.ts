import { APP_INITIALIZER } from '@angular/core';
import { jwtDecode } from 'jwt-decode';
import { AuthService } from '../authentication/auth.service';

export function initializeApp(service: AuthService) {
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.has('code')) {
    const token: any = urlParams.get('code');
    const decoded: any = jwtDecode(token);
    const user = { id: decoded.id, documentId: decoded.documentId, isAdmin: true };
    service.setAuth(token, user, false);
  }

  return () => service.checkAdminUser();
}

export const appInitializerProviders = [
  {
    provide: APP_INITIALIZER,
    useFactory: initializeApp,
    deps: [AuthService],
    multi: true,
  },
];
