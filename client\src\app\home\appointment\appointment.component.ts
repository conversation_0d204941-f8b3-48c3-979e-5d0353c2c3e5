import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ContentService } from '../../core/services/content-vendor.service';

@Component({
  selector: 'app-appointment',
  templateUrl: './appointment.component.html',
  styleUrl: './appointment.component.scss'
})
export class AppointmentComponent implements OnInit {

  serviceId: string | null = null;
  content!: any;
  serviceData: any = null;
  timeSlots: any[] = [];

  constructor(
    private route: ActivatedRoute,
    private CMSservice: ContentService
  ) {
    // Initialize time slots
    this.timeSlots = [
      { label: '9:00 AM', value: '09:00' },
      { label: '10:00 AM', value: '10:00' },
      { label: '11:00 AM', value: '11:00' },
      { label: '12:00 PM', value: '12:00' },
      { label: '1:00 PM', value: '13:00' },
      { label: '2:00 PM', value: '14:00' },
      { label: '3:00 PM', value: '15:00' },
      { label: '4:00 PM', value: '16:00' },
      { label: '5:00 PM', value: '17:00' }
    ];
  }

  ngOnInit(): void {
    // Get service ID from route parameters
    this.serviceId = this.route.snapshot.paramMap.get('id');
    
    // Get content from route data
    this.content = this.route.snapshot.data['content'];
    console.log('Appointment Content:', this.content);
    console.log('Service ID:', this.serviceId);

    // If we have a service ID, we could fetch specific service data
    // For now, we'll just store the ID for use in the component
  }

  onSubmitAppointment() {
    // Handle appointment submission logic here
    console.log('Submitting appointment for service:', this.serviceId);
    // Add your appointment booking logic here
  }
}
