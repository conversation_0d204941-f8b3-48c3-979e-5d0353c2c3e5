{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../core/services/content-vendor.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/inputtext\";\nfunction HomeComponent_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 137)(1, \"a\", 138);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 139);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const menuItem_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", menuItem_r1.Link || \"#\", i0.ɵɵsanitizeUrl)(\"target\", menuItem_r1.Target || \"_self\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", menuItem_r1.Title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(menuItem_r1.Sub_Title);\n  }\n}\nexport class HomeComponent {\n  constructor(primengConfig, renderer, route, CMSservice) {\n    this.primengConfig = primengConfig;\n    this.renderer = renderer;\n    this.route = route;\n    this.CMSservice = CMSservice;\n    this.logo = '';\n    this.menuItems = [];\n    this.bannerData = null;\n    this.servicesData = [];\n  }\n  ngOnInit() {\n    this.commonContent = this.route.snapshot.data['commonContent'];\n    console.log('Common Content:', this.commonContent);\n    // Extract logo\n    const logoComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.logo\");\n    if (logoComponent?.length) {\n      this.logo = logoComponent[0].Logo?.url || '';\n    }\n    // Extract menu\n    const menuComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.menu\");\n    if (menuComponent?.length) {\n      this.menuItems = menuComponent[0].Menu_Item || [];\n    }\n    this.content = this.route.snapshot.data['content'];\n    console.log('Home Content:', this.content);\n    // Extract banner\n    const bannerComponent = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.banner\");\n    if (bannerComponent?.length) {\n      this.bannerData = bannerComponent[0];\n    }\n    // Extract services\n    const servicesComponents = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.services\");\n    if (servicesComponents?.length) {\n      this.servicesData = servicesComponents;\n    }\n    console.log('Logo:', this.logo);\n    console.log('Menu Items:', this.menuItems);\n    console.log('Banner Data:', this.bannerData);\n    console.log('Services Data:', this.servicesData);\n    // Inject theme \n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\n    const link = this.renderer.createElement('link');\n    this.renderer.setAttribute(link, 'id', 'theme-link');\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\n    this.renderer.setAttribute(link, 'type', 'text/css');\n    this.renderer.setAttribute(link, 'href', href);\n    // Append the link tag to the head of the document\n    this.renderer.appendChild(document.head, link);\n    this.primengConfig.ripple = true; //enables core ripple functionality\n  }\n  ngOnDestroy() {\n    // Find and remove the link tag when the component is destroyed\n    const link = document.getElementById('theme-link');\n    if (link) {\n      link.remove();\n    }\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ContentService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 438,\n      vars: 2,\n      consts: [[1, \"main-header\", \"fixed\", \"top-0\", \"w-full\", \"bg-white\", \"z-5\"], [1, \"header-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"align-items-center\"], [1, \"header-logo\", \"relative\", \"pr-6\", \"flex\", \"align-items-center\", \"w-18rem\", \"h-8rem\", \"secondary-bg-color\"], [\"alt\", \"Logo\", 1, \"w-full\", \"h-fit\", 3, \"src\"], [1, \"header-menu-sec\", \"pl-5\", \"relative\", \"flex\", \"align-items-center\", \"justify-content-between\", \"flex-1\"], [1, \"menu-list\"], [1, \"p-0\", \"m-0\", \"flex\", \"align-items-center\", \"gap-5\"], [\"class\", \"flex\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-8rem\", \"h-3rem\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-bluegray-100\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"material-symbols-rounded\", \"text-xl\"], [1, \"banner-sec\", \"relative\"], [1, \"banner-body\", \"h-full\", \"flex\", \"align-items-center\"], [1, \"banner-box\", \"relative\", \"flex-1\", \"p-8\"], [1, \"banner-box\", \"relative\", \"flex-1\", \"bg-white\", \"p-8\", \"flex\", \"flex-column\", \"justify-content-center\"], [1, \"mb-4\", \"text-yellow-400\"], [1, \"m-0\", \"mb-5\", \"text-8xl\", \"line-height-1\", \"font-extrabold\", \"text-color\"], [1, \"m-0\", \"text-lg\", \"font-medium\"], [1, \"services-sec\", \"w-full\"], [1, \"services-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\"], [1, \"services-box\", \"s-box-1\", \"p-5\", \"flex-1\", \"flex\", \"flex-column\", \"gap-2\"], [\"src\", \"/assets/layout/images/id-services.png\", \"alt\", \"\", 1, \"h-fit\", \"w-5rem\"], [1, \"font-bold\", \"text-white\"], [1, \"m-0\", \"mb-5\", \"p-0\", \"flex\", \"flex-1\", \"flex-column\", \"gap-2\", \"list-none\"], [1, \"text-white\", \"flex\", \"align-items-center\", \"gap-2\"], [1, \"material-symbols-rounded\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"h-3rem\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-white\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"services-box\", \"s-box-2\", \"p-5\", \"flex-1\", \"flex\", \"flex-column\", \"gap-2\"], [\"src\", \"/assets/layout/images/car-services.png\", \"alt\", \"\", 1, \"h-fit\", \"w-5rem\"], [1, \"services-box\", \"s-box-3\", \"p-5\", \"flex-1\", \"flex\", \"flex-column\", \"gap-2\"], [\"src\", \"/assets/layout/images/business-services.png\", \"alt\", \"\", 1, \"h-fit\", \"w-5rem\"], [1, \"services-box\", \"s-box-4\", \"p-5\", \"flex-1\", \"flex\", \"flex-column\", \"gap-2\"], [\"src\", \"/assets/layout/images/senior-services.png\", \"alt\", \"\", 1, \"h-fit\", \"w-5rem\"], [1, \"about-sec\", \"relative\"], [1, \"about-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"flex-wrap\"], [1, \"about-box\", \"pr-8\", \"w-6\", \"secondary-bg-color\"], [\"src\", \"/assets/layout/images/director-img.jpg\", \"alt\", \"\", 1, \"w-full\"], [1, \"about-box\", \"p-8\", \"pr-0\", \"w-6\", \"secondary-bg-color\", \"flex\", \"flex-column\", \"justify-content-start\"], [1, \"line\", \"m-0\", \"mb-6\", \"relative\", \"pb-5\", \"text-6xl\", \"line-height-2\", \"font-bold\", \"text-white\"], [1, \"m-0\", \"text-lg\", \"text-white\", \"flex-1\"], [\"type\", \"button\", 1, \"px-5\", \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"h-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-white\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"city-members-sec\", \"relative\"], [1, \"city-members-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"flex-wrap\"], [1, \"city-members-left\", \"pt-8\", \"pr-7\", \"pb-8\", \"pl-0\", \"relative\", \"flex\", \"bg-orange-50\"], [1, \"cm-time-box\"], [\"src\", \"/assets/layout/images/time-screen.png\", \"alt\", \"\", 1, \"w-full\"], [1, \"cm-info\"], [1, \"line\", \"m-0\", \"mb-6\", \"relative\", \"pb-5\", \"text-4xl\", \"line-height-2\", \"font-bold\", \"text-color\"], [1, \"m-0\", \"text-lg\", \"text-color\", \"flex-1\"], [1, \"city-members-right\", \"p-7\", \"pr-0\", \"relative\", \"flex\", \"flex-column\", \"gap-8\"], [1, \"cm-right-box\"], [1, \"flex\", \"align-items-start\", \"text-8xl\", \"font-extrabold\", \"line-height-1\"], [1, \"relative\", \"text-4xl\", \"font-bold\", \"inline-block\"], [1, \"uppercase\"], [1, \"text-color-secondary\"], [1, \"quick-access-sec\", \"relative\"], [1, \"quick-access-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"flex-wrap\"], [1, \"quick-access-left\", \"relative\", \"flex\", \"flex-column\", \"pt-8\", \"pb-8\"], [1, \"mb-4\", \"flex\", \"align-items-start\", \"text-6xl\", \"font-extrabold\", \"line-height-1\"], [1, \"line-2\", \"inline-flex\", \"w-fit\", \"m-0\", \"mb-6\", \"relative\", \"pb-5\", \"text-xl\", \"line-height-2\", \"font-bold\", \"text-yellow-400\"], [1, \"quick-access-list\", \"d-grid\", \"w-full\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-bottom-none\", \"border-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/001-toll.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"m-0\", \"mb-4\", \"uppercase\", \"flex-1\"], [1, \"text-sm\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-none\", \"border-top-1\", \"border-right-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/006-virus.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [\"src\", \"/assets/layout/images/003-trash-can.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/004-parking.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-none\", \"border-top-1\", \"border-right-1\", \"border-bottom-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/005-lecture.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [\"src\", \"/assets/layout/images/008-basketball.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"quick-access-right\"], [1, \"news-sec\", \"relative\", \"pt-7\", \"pb-8\", \"secondary-bg-color\"], [1, \"news-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\"], [1, \"news-title\"], [1, \"line\", \"m-0\", \"mb-5\", \"pb-5\", \"relative\", \"inline-flex\", \"w-fit\", \"text-yellow-400\"], [1, \"m-0\", \"mb-6\", \"relative\", \"text-6xl\", \"line-height-2\", \"font-bold\", \"text-white\"], [1, \"news-list\", \"d-grid\", \"w-full\", \"gap-6\"], [1, \"news-box\", \"w-full\", \"flex\", \"flex-column\"], [1, \"new-img\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/new-img-1.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"text-white\", \"text-xl\", \"flex-1\"], [1, \"mb-3\", \"flex\", \"gap-3\", \"align-items-center\"], [1, \"text\", \"flex\", \"align-items-center\", \"font-medium\", \"text-sm\", \"gap-1\", \"text-white\"], [1, \"mb-5\", \"text-sm\", \"text-white\"], [\"type\", \"button\", 1, \"px-5\", \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"h-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-yellow-400\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"news-letter-sec\", \"relative\"], [1, \"news-letter-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\"], [1, \"news-letter-box-list\", \"flex\", \"bg-white\", \"shadow-1\"], [1, \"news-letter-box\", \"w-6\", \"p-7\", \"flex\", \"flex-column\", \"justify-content-center\"], [1, \"line-2\", \"relative\", \"mb-5\", \"pb-5\", \"inline-flex\", \"w-fit\", \"align-items-start\", \"text-6xl\", \"font-extrabold\", \"line-height-1\"], [1, \"news-letter-form\", \"mt-5\", \"relative\", \"flex\", \"align-items-center\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Your email address\", 1, \"h-3-3rem\", \"border-noround\", \"flex-1\"], [\"type\", \"button\", 1, \"px-6\", \"p-element\", \"p-ripple\", \"border-noround\", \"p-button\", \"p-component\", \"h-3-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-yellow-400\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"news-letter-img\", \"w-6\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/news-letter-img.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"what-happning-sec\", \"mt-8\", \"relative\", \"px-4\"], [1, \"what-happning-body\", \"relative\", \"w-full\", \"mx-auto\", \"flex\", \"bg-orange-50\"], [1, \"what-happning-img\", \"w-6\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/whats-happnening-mg.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"what-happning-right\", \"w-6\", \"p-8\"], [1, \"what-happning-title\"], [1, \"line-2\", \"m-0\", \"mb-5\", \"pb-5\", \"relative\", \"inline-flex\", \"w-fit\", \"text-yellow-400\"], [1, \"m-0\", \"mb-6\", \"relative\", \"text-6xl\", \"line-height-2\", \"font-bold\", \"text-color\"], [1, \"what-happning-list\"], [1, \"what-happning-box\", \"mb-4\", \"pb-4\", \"flex\", \"align-items-center\", \"gap-5\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"surface-border\"], [1, \"wh-img-box\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/arches-1.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"wh-cnt-sec\", \"flex-1\", \"flex\", \"align-items-center\", \"gap-6\"], [1, \"wh-cnt\", \"flex\", \"flex-column\"], [1, \"text-xl\", \"font-bold\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"flex-wrap\"], [1, \"flex\", \"w-fit\", \"align-items-center\", \"gap-2\", \"py-2\", \"p-3\", \"bg-orange-100\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"type\", \"button\", 1, \"px-4\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-noround\", \"p-button-outlined\", \"h-3-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"text-color\", \"font-semibold\"], [\"src\", \"/assets/layout/images/arches-2.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"what-happning-box\", \"flex\", \"align-items-center\", \"gap-5\"], [\"src\", \"/assets/layout/images/arches-3.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"footer-sec\", \"relative\", \"secondary-bg-color\"], [1, \"footer-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\"], [1, \"top-footer\", \"py-7\", \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-5\", \"md:col-5\"], [1, \"line\", \"flex\", \"mb-5\", \"pb-3\", \"relative\", \"text-white\", \"text-xl\"], [1, \"p-0\", \"m-0\", \"flex\", \"flex-column\", \"gap-4\"], [1, \"flex\", \"w-full\"], [\"href\", \"#\", 1, \"flex\", \"w-full\", \"text-white\"], [1, \"flex\", \"w-full\", \"text-white\"], [\"href\", \"\", 1, \"flex\", \"w-full\", \"text-white\"], [1, \"col-12\", \"lg:col-7\", \"md:col-7\", \"py-0\"], [1, \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\"], [1, \"middle-footer\", \"py-5\", \"w-full\", \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-100\"], [1, \"bottom-footer\", \"py-5\", \"w-full\", \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [1, \"m-0\", \"mb-3\", \"p-0\", \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-3\", \"list-none\"], [\"href\", \"#\", 1, \"inline-flex\", \"w-fit\", \"text-white\"], [1, \"m-0\", \"text-white\"], [1, \"flex\"], [1, \"flex\", \"flex-column\", \"gap-1\", \"text-lg\", \"font-semibold\", \"text-color\", \"line-height-1\", 3, \"href\", \"target\"], [1, \"text-sm\", \"font-normal\", \"text-color-secondary\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"header\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"img\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"ul\", 6);\n          i0.ɵɵtemplate(7, HomeComponent_li_7_Template, 5, 4, \"li\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"button\", 8)(9, \"span\", 9);\n          i0.ɵɵtext(10, \"login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11, \" Login \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"section\", 10)(13, \"div\", 11);\n          i0.ɵɵelement(14, \"div\", 12);\n          i0.ɵɵelementStart(15, \"div\", 13)(16, \"h4\", 14);\n          i0.ɵɵtext(17, \"Far away from the every day!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"h1\", 15);\n          i0.ɵɵtext(19, \"Community \");\n          i0.ɵɵelement(20, \"br\");\n          i0.ɵɵtext(21, \"of endless \");\n          i0.ɵɵelement(22, \"br\");\n          i0.ɵɵtext(23, \"beauty & Calm\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"p\", 16);\n          i0.ɵɵtext(25, \"Drawn by clean air and mythical light, visitors come to experience traditions, \");\n          i0.ɵɵelement(26, \"br\");\n          i0.ɵɵtext(27, \"fine art, great cuisine and natural beauty of the landscape.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(28, \"section\", 17)(29, \"div\", 18)(30, \"div\", 19);\n          i0.ɵɵelement(31, \"img\", 20);\n          i0.ɵɵelementStart(32, \"h4\", 21);\n          i0.ɵɵtext(33, \"Driver & ID Services\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"ul\", 22)(35, \"li\", 23)(36, \"i\", 24);\n          i0.ɵɵtext(37, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(38, \" Renew driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"li\", 23)(40, \"i\", 24);\n          i0.ɵɵtext(41, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(42, \" Replace driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"li\", 23)(44, \"i\", 24);\n          i0.ɵɵtext(45, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(46, \" Get a REAL ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"li\", 23)(48, \"i\", 24);\n          i0.ɵɵtext(49, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(50, \" First-time driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(51, \"button\", 25);\n          i0.ɵɵtext(52, \" Driver & ID Services \");\n          i0.ɵɵelementStart(53, \"span\", 24);\n          i0.ɵɵtext(54, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(55, \"div\", 26);\n          i0.ɵɵelement(56, \"img\", 27);\n          i0.ɵɵelementStart(57, \"h4\", 21);\n          i0.ɵɵtext(58, \"Vehicle & Plate Services\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"ul\", 22)(60, \"li\", 23)(61, \"i\", 24);\n          i0.ɵɵtext(62, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(63, \" Renew driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"li\", 23)(65, \"i\", 24);\n          i0.ɵɵtext(66, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(67, \" Replace driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"li\", 23)(69, \"i\", 24);\n          i0.ɵɵtext(70, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(71, \" Get a REAL ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"li\", 23)(73, \"i\", 24);\n          i0.ɵɵtext(74, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(75, \" First-time driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(76, \"button\", 25);\n          i0.ɵɵtext(77, \" Vehicle & Plate Services \");\n          i0.ɵɵelementStart(78, \"span\", 24);\n          i0.ɵɵtext(79, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(80, \"div\", 28);\n          i0.ɵɵelement(81, \"img\", 29);\n          i0.ɵɵelementStart(82, \"h4\", 21);\n          i0.ɵɵtext(83, \"Business Services\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"ul\", 22)(85, \"li\", 23)(86, \"i\", 24);\n          i0.ɵɵtext(87, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(88, \" Renew driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"li\", 23)(90, \"i\", 24);\n          i0.ɵɵtext(91, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(92, \" Replace driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"li\", 23)(94, \"i\", 24);\n          i0.ɵɵtext(95, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(96, \" Get a REAL ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"li\", 23)(98, \"i\", 24);\n          i0.ɵɵtext(99, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(100, \" First-time driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(101, \"button\", 25);\n          i0.ɵɵtext(102, \" Business Services \");\n          i0.ɵɵelementStart(103, \"span\", 24);\n          i0.ɵɵtext(104, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(105, \"div\", 30);\n          i0.ɵɵelement(106, \"img\", 31);\n          i0.ɵɵelementStart(107, \"h4\", 21);\n          i0.ɵɵtext(108, \"Senior Services\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"ul\", 22)(110, \"li\", 23)(111, \"i\", 24);\n          i0.ɵɵtext(112, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(113, \" Renew driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(114, \"li\", 23)(115, \"i\", 24);\n          i0.ɵɵtext(116, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(117, \" Replace driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(118, \"li\", 23)(119, \"i\", 24);\n          i0.ɵɵtext(120, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(121, \" Get a REAL ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(122, \"li\", 23)(123, \"i\", 24);\n          i0.ɵɵtext(124, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(125, \" First-time driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(126, \"button\", 25);\n          i0.ɵɵtext(127, \" Senior Services \");\n          i0.ɵɵelementStart(128, \"span\", 24);\n          i0.ɵɵtext(129, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(130, \"section\", 32)(131, \"div\", 33)(132, \"div\", 34);\n          i0.ɵɵelement(133, \"img\", 35);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(134, \"div\", 36)(135, \"h4\", 14);\n          i0.ɵɵtext(136, \"Far away from the every day!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(137, \"h2\", 37);\n          i0.ɵɵtext(138, \"A Message From the Director \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(139, \"p\", 38);\n          i0.ɵɵtext(140, \"Old Town of Riverside is a special place with limitless potential, where everyone deserves equal access to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in every neighborhood. It exists to serve you and be a resource for you, your family, and your community.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(141, \"button\", 39);\n          i0.ɵɵtext(142, \" Learn More \");\n          i0.ɵɵelementStart(143, \"span\", 24);\n          i0.ɵɵtext(144, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(145, \"section\", 40)(146, \"div\", 41)(147, \"div\", 42)(148, \"div\", 43);\n          i0.ɵɵelement(149, \"img\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(150, \"div\", 45)(151, \"h4\", 14);\n          i0.ɵɵtext(152, \"Our city in numbers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(153, \"h2\", 46);\n          i0.ɵɵtext(154, \"Programs to help launch, grow and expand any business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(155, \"p\", 47);\n          i0.ɵɵtext(156, \"Old Town of Riverside is a special place with limitless potential, where everyone deserves equal access to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in every neighborhood. It exists to serve you and be a resource for you, your family, and your community.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(157, \"div\", 48)(158, \"div\", 49)(159, \"h3\", 50);\n          i0.ɵɵtext(160, \"41 \");\n          i0.ɵɵelementStart(161, \"sup\", 51);\n          i0.ɵɵtext(162, \"k\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(163, \"h5\", 52);\n          i0.ɵɵtext(164, \"Highly-educated creative workforce\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(165, \"p\", 53);\n          i0.ɵɵtext(166, \"A vibrant destination where innovations in design and technology are born. A place for you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(167, \"div\", 49)(168, \"h3\", 50);\n          i0.ɵɵtext(169, \"8 \");\n          i0.ɵɵelementStart(170, \"sup\", 51);\n          i0.ɵɵtext(171, \"th\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(172, \"h5\", 52);\n          i0.ɵɵtext(173, \"Highest Per Capita Income in Virginia\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(174, \"p\", 53);\n          i0.ɵɵtext(175, \"A vibrant destination where innovations in design and technology are born. A place for you.\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(176, \"section\", 54)(177, \"div\", 55)(178, \"div\", 56)(179, \"h2\", 57);\n          i0.ɵɵtext(180, \"Quick Access\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(181, \"h4\", 58);\n          i0.ɵɵtext(182, \" What can the City help you with today?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(183, \"div\", 59)(184, \"div\", 60);\n          i0.ɵɵelement(185, \"img\", 61);\n          i0.ɵɵelementStart(186, \"h6\", 62);\n          i0.ɵɵtext(187, \"Road construction\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(188, \"p\", 63);\n          i0.ɵɵtext(189, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(190, \"div\", 64);\n          i0.ɵɵelement(191, \"img\", 65);\n          i0.ɵɵelementStart(192, \"h6\", 62);\n          i0.ɵɵtext(193, \"COVID-19 updates\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(194, \"p\", 63);\n          i0.ɵɵtext(195, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(196, \"div\", 64);\n          i0.ɵɵelement(197, \"img\", 66);\n          i0.ɵɵelementStart(198, \"h6\", 62);\n          i0.ɵɵtext(199, \"Garbage pickup\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(200, \"p\", 63);\n          i0.ɵɵtext(201, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(202, \"div\", 67);\n          i0.ɵɵelement(203, \"img\", 68);\n          i0.ɵɵelementStart(204, \"h6\", 62);\n          i0.ɵɵtext(205, \"Parking\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(206, \"p\", 63);\n          i0.ɵɵtext(207, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(208, \"div\", 69);\n          i0.ɵɵelement(209, \"img\", 70);\n          i0.ɵɵelementStart(210, \"h6\", 62);\n          i0.ɵɵtext(211, \"Council meetings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(212, \"p\", 63);\n          i0.ɵɵtext(213, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(214, \"div\", 69);\n          i0.ɵɵelement(215, \"img\", 71);\n          i0.ɵɵelementStart(216, \"h6\", 62);\n          i0.ɵɵtext(217, \"Recreation and sport programs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(218, \"p\", 63);\n          i0.ɵɵtext(219, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(220, \"div\", 72);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(221, \"section\", 73)(222, \"div\", 74)(223, \"div\", 75)(224, \"h5\", 76);\n          i0.ɵɵtext(225, \"Find out what\\u2019s going on & stay up to date.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(226, \"h2\", 77);\n          i0.ɵɵtext(227, \"Latest News\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(228, \"div\", 78)(229, \"div\", 79)(230, \"div\", 80);\n          i0.ɵɵelement(231, \"img\", 81);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(232, \"h3\", 82);\n          i0.ɵɵtext(233, \"Proposed Downtown District Ordinance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(234, \"div\", 83)(235, \"div\", 84)(236, \"span\", 9);\n          i0.ɵɵtext(237, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(238, \" September 16, 2018 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(239, \"div\", 84)(240, \"span\", 9);\n          i0.ɵɵtext(241, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(242, \" 240 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(243, \"p\", 85);\n          i0.ɵɵtext(244, \"The goal of the proposed ordinance is to create safe venues for customers and to discourage illegal activities. The governing officials of...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(245, \"button\", 86);\n          i0.ɵɵtext(246, \" Learn More \");\n          i0.ɵɵelementStart(247, \"span\", 24);\n          i0.ɵɵtext(248, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(249, \"div\", 79)(250, \"div\", 80);\n          i0.ɵɵelement(251, \"img\", 81);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(252, \"h3\", 82);\n          i0.ɵɵtext(253, \"Annual Water Quality Report (Gallery Post)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(254, \"div\", 83)(255, \"div\", 84)(256, \"span\", 9);\n          i0.ɵɵtext(257, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(258, \" September 16, 2018 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(259, \"div\", 84)(260, \"span\", 9);\n          i0.ɵɵtext(261, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(262, \" 240 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(263, \"p\", 85);\n          i0.ɵɵtext(264, \"The goal of the proposed ordinance is to create safe venues for customers and to discourage illegal activities. The governing officials of...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(265, \"button\", 86);\n          i0.ɵɵtext(266, \" Learn More \");\n          i0.ɵɵelementStart(267, \"span\", 24);\n          i0.ɵɵtext(268, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(269, \"div\", 79)(270, \"div\", 80);\n          i0.ɵɵelement(271, \"img\", 81);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(272, \"h3\", 82);\n          i0.ɵɵtext(273, \"Waste Industries Garbage Pick Up: Embeds\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(274, \"div\", 83)(275, \"div\", 84)(276, \"span\", 9);\n          i0.ɵɵtext(277, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(278, \" September 16, 2018 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(279, \"div\", 84)(280, \"span\", 9);\n          i0.ɵɵtext(281, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(282, \" 240 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(283, \"p\", 85);\n          i0.ɵɵtext(284, \"The goal of the proposed ordinance is to create safe venues for customers and to discourage illegal activities. The governing officials of...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(285, \"button\", 86);\n          i0.ɵɵtext(286, \" Learn More \");\n          i0.ɵɵelementStart(287, \"span\", 24);\n          i0.ɵɵtext(288, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(289, \"section\", 87)(290, \"div\", 88)(291, \"div\", 89)(292, \"div\", 90)(293, \"h2\", 91);\n          i0.ɵɵtext(294, \" Sign up for News & Alerts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(295, \"p\");\n          i0.ɵɵtext(296, \"Stay connected to the City of Riverside by signing up to receive official news and alerts by email! \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(297, \"div\", 92);\n          i0.ɵɵelement(298, \"input\", 93);\n          i0.ɵɵelementStart(299, \"button\", 94);\n          i0.ɵɵtext(300, \" Sign Up \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(301, \"div\", 95);\n          i0.ɵɵelement(302, \"img\", 96);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(303, \"section\", 97)(304, \"div\", 98)(305, \"div\", 99);\n          i0.ɵɵelement(306, \"img\", 100);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(307, \"div\", 101)(308, \"div\", 102)(309, \"h5\", 103);\n          i0.ɵɵtext(310, \"Join the fun in our city! \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(311, \"h2\", 104);\n          i0.ɵɵtext(312, \"What's Happening\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(313, \"div\", 105)(314, \"div\", 106)(315, \"div\", 107);\n          i0.ɵɵelement(316, \"img\", 108);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(317, \"div\", 109)(318, \"div\", 110)(319, \"h3\", 111);\n          i0.ɵɵtext(320, \"Heritage 10th Anniversary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(321, \"div\", 112)(322, \"div\", 113)(323, \"span\");\n          i0.ɵɵtext(324, \"June 9, 2027\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(325, \" - \");\n          i0.ɵɵelementStart(326, \"span\");\n          i0.ɵɵtext(327, \"June 18, 2027\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(328, \"div\", 114);\n          i0.ɵɵtext(329, \"All Day at \");\n          i0.ɵɵelementStart(330, \"b\");\n          i0.ɵɵtext(331, \"Baker Hall\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(332, \"button\", 115);\n          i0.ɵɵtext(333, \" Find out more \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(334, \"div\", 106)(335, \"div\", 107);\n          i0.ɵɵelement(336, \"img\", 116);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(337, \"div\", 109)(338, \"div\", 110)(339, \"h3\", 111);\n          i0.ɵɵtext(340, \"Along Pines Run\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(341, \"div\", 112)(342, \"div\", 113)(343, \"span\");\n          i0.ɵɵtext(344, \"July 22, 2027\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(345, \"div\", 114);\n          i0.ɵɵtext(346, \"12:00 am at \");\n          i0.ɵɵelementStart(347, \"b\");\n          i0.ɵɵtext(348, \"Boulder City Council\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(349, \"button\", 115);\n          i0.ɵɵtext(350, \" Find out more \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(351, \"div\", 117)(352, \"div\", 107);\n          i0.ɵɵelement(353, \"img\", 118);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(354, \"div\", 109)(355, \"div\", 110)(356, \"h3\", 111);\n          i0.ɵɵtext(357, \"Touch A Truck\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(358, \"div\", 112)(359, \"div\", 113)(360, \"span\");\n          i0.ɵɵtext(361, \"June 9, 2027\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(362, \" - \");\n          i0.ɵɵelementStart(363, \"span\");\n          i0.ɵɵtext(364, \"June 18, 2027\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(365, \"div\", 114);\n          i0.ɵɵtext(366, \"9:00 am - 5:00 pm at \");\n          i0.ɵɵelementStart(367, \"b\");\n          i0.ɵɵtext(368, \"Baker Hall\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(369, \"button\", 115);\n          i0.ɵɵtext(370, \" Find out more \");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(371, \"section\", 119)(372, \"div\", 120)(373, \"div\", 121)(374, \"div\", 122)(375, \"h3\", 123);\n          i0.ɵɵtext(376, \"Riverside City Hall\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(377, \"ul\", 124)(378, \"li\", 125)(379, \"a\", 126);\n          i0.ɵɵtext(380, \"8353 Sierra Avenue \\u2022 Riverside, CA 91335\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(381, \"li\", 125)(382, \"a\", 126);\n          i0.ɵɵtext(383, \"Phone: (*************\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(384, \"li\", 125)(385, \"a\", 127);\n          i0.ɵɵtext(386, \"Monday - Thursday, 8:00 am - 6:00 pm\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(387, \"li\", 125)(388, \"a\", 128);\n          i0.ɵɵtext(389, \"Email : <EMAIL>\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(390, \"div\", 129)(391, \"div\", 130)(392, \"div\", 131)(393, \"h3\", 123);\n          i0.ɵɵtext(394, \"Services\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(395, \"ul\", 124)(396, \"li\", 125)(397, \"a\", 126);\n          i0.ɵɵtext(398, \"Driver & ID Services\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(399, \"li\", 125)(400, \"a\", 126);\n          i0.ɵɵtext(401, \"Vehicle & Plate Services\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(402, \"li\", 125)(403, \"a\", 127);\n          i0.ɵɵtext(404, \"Business Services\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(405, \"li\", 125)(406, \"a\", 128);\n          i0.ɵɵtext(407, \"Senior Services\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(408, \"div\", 131)(409, \"h3\", 123);\n          i0.ɵɵtext(410, \"Useful Links\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(411, \"ul\", 124)(412, \"li\", 125)(413, \"a\", 126);\n          i0.ɵɵtext(414, \"Frequently Asked Questions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(415, \"li\", 125)(416, \"a\", 126);\n          i0.ɵɵtext(417, \"Latest News\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(418, \"li\", 125)(419, \"a\", 127);\n          i0.ɵɵtext(420, \"Community\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(421, \"li\", 125)(422, \"a\", 128);\n          i0.ɵɵtext(423, \"Help Center\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(424, \"li\", 125)(425, \"a\", 128);\n          i0.ɵɵtext(426, \"Careers\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelement(427, \"div\", 132);\n          i0.ɵɵelementStart(428, \"div\", 133)(429, \"ul\", 134)(430, \"li\")(431, \"a\", 135);\n          i0.ɵɵtext(432, \"Term & Conditions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(433, \"li\")(434, \"a\", 135);\n          i0.ɵɵtext(435, \"Privacy Policy\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(436, \"p\", 136);\n          i0.ɵɵtext(437, \"\\u00A9 2025 SNJYA. All rights reserved.\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"src\", ctx.logo || \"/assets/layout/images/snjya-public-services-logo.png\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.menuItems);\n        }\n      },\n      dependencies: [i4.NgForOf, i5.InputText],\n      styles: [\".max-w-1200 {\\n  max-width: 1200px;\\n}\\n  .secondary-color {\\n  color: #030f5e !important;\\n}\\n  .secondary-bg-color {\\n  background: #030f5e !important;\\n}\\n  .font-extrabold {\\n  font-weight: 900 !important;\\n}\\n  .d-grid {\\n  display: grid !important;\\n}\\n  .object-fit-contain {\\n  object-fit: contain;\\n}\\n  .object-fit-cover {\\n  object-fit: cover;\\n}\\n  .h-3-3rem {\\n  height: 3.3rem !important;\\n}\\n  .bg-blue-75 {\\n  background: rgb(227, 243, 255) !important;\\n}\\n  .main-header:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  height: 8rem;\\n  background: #030f5e !important;\\n  width: 20%;\\n}\\n  .banner-sec {\\n  margin: 8rem 0 0 0;\\n  height: calc(100vh - 8rem);\\n  background: url('banner-img.jpg') center center no-repeat;\\n  background-size: cover;\\n}\\n  .banner-sec .banner-box {\\n  margin: 120px 0 0 0;\\n}\\n  .services-sec {\\n  margin: -87px 0 0 0;\\n}\\n  .services-sec .services-box.s-box-1 {\\n  background: #a8c1cd;\\n}\\n  .services-sec .services-box.s-box-2 {\\n  background: #e3cab0;\\n}\\n  .services-sec .services-box.s-box-3 {\\n  background: #c4a597;\\n}\\n  .services-sec .services-box.s-box-4 {\\n  background: #e8816e;\\n}\\n  .about-sec:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  right: 0;\\n  height: 100%;\\n  background: #030f5e !important;\\n  width: 20%;\\n}\\n  .line::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.2392156863) !important;\\n  width: 100%;\\n}\\n  .line::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgb(213, 200, 186) !important;\\n  width: 64px;\\n}\\n  .line-2::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgba(0, 0, 0, 0.07) !important;\\n  width: 100%;\\n}\\n  .line-2::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgb(213, 200, 186) !important;\\n  width: 64px;\\n}\\n  .city-members-sec:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  height: 100%;\\n  background: var(--orange-50) !important;\\n  width: 20%;\\n}\\n  .city-members-left {\\n  width: 70%;\\n}\\n  .city-members-left .cm-time-box {\\n  flex: 0 0 40%;\\n}\\n  .city-members-left .cm-info {\\n  flex: 0 0 60%;\\n  padding: 0 0 0 50px;\\n}\\n  .city-members-right {\\n  width: 30%;\\n}\\n  .city-members-right .cm-right-box h3 sup {\\n  top: 7px;\\n}\\n  .quick-access-sec {\\n  background: url('pexels-vitor-gusmao.jpg') center right no-repeat;\\n  background-size: 38% auto;\\n}\\n  .quick-access-sec .quick-access-left {\\n  flex: 0 0 67%;\\n  width: 67%;\\n}\\n  .quick-access-sec .quick-access-left .quick-access-list {\\n  grid-template-columns: repeat(auto-fill, minmax(33.333%, 1fr));\\n}\\n  .quick-access-sec .quick-access-right {\\n  flex: 0 0 33%;\\n  width: 33%;\\n}\\n  .news-sec .news-list {\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n}\\n  .news-letter-sec {\\n  background: linear-gradient(180deg, #030f5e 50%, transparent 50%);\\n}\\n  .news-letter-sec .news-letter-box-list .news-letter-img {\\n  height: 640px;\\n}\\n  .what-happning-sec .what-happning-body .what-happning-img {\\n  height: 800px;\\n}\\n  .what-happning-sec .what-happning-body .what-happning-box .wh-img-box {\\n  width: 120px;\\n  height: 120px;\\n}\\n  .what-happning-sec .what-happning-body .what-happning-box .wh-cnt {\\n  max-width: 70%;\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************ */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "menuItem_r1", "Link", "ɵɵsanitizeUrl", "Target", "ɵɵtextInterpolate1", "Title", "ɵɵtextInterpolate", "Sub_Title", "HomeComponent", "constructor", "primengConfig", "renderer", "route", "CMSservice", "logo", "menuItems", "bannerData", "servicesData", "ngOnInit", "commonContent", "snapshot", "data", "console", "log", "logoComponent", "getDataByComponentName", "body", "length", "Logo", "url", "menuComponent", "<PERSON><PERSON>_<PERSON><PERSON>", "content", "bannerComponent", "servicesComponents", "href", "link", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "document", "head", "ripple", "ngOnDestroy", "getElementById", "remove", "ɵɵdirectiveInject", "i1", "PrimeNGConfig", "Renderer2", "i2", "ActivatedRoute", "i3", "ContentService", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "HomeComponent_li_7_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home.component.ts", "D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home.component.html"], "sourcesContent": ["import { Component, Renderer2 } from '@angular/core';\r\nimport { PrimeNGConfig } from 'primeng/api';\r\nimport { ContentService } from '../core/services/content-vendor.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrl: './home.component.scss'\r\n})\r\nexport class HomeComponent {\r\n\r\n  content!: any;\r\n  commonContent!: any;\r\n  logo: string = '';\r\n  menuItems: any[] = [];\r\n  bannerData: any = null;\r\n  servicesData: any[] = [];\r\n\r\n  constructor(\r\n    private primengConfig: PrimeNGConfig,\r\n    private renderer: Renderer2,\r\n    private route: ActivatedRoute,\r\n    private CMSservice: ContentService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.commonContent = this.route.snapshot.data['commonContent'];\r\n    console.log('Common Content:', this.commonContent);\r\n\r\n    // Extract logo\r\n    const logoComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.logo\");\r\n    if (logoComponent?.length) {\r\n      this.logo = logoComponent[0].Logo?.url || '';\r\n    }\r\n\r\n    // Extract menu\r\n    const menuComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.menu\");\r\n    if (menuComponent?.length) {\r\n      this.menuItems = menuComponent[0].Menu_Item || [];\r\n    }\r\n\r\n    this.content = this.route.snapshot.data['content'];\r\n    console.log('Home Content:', this.content);\r\n\r\n    // Extract banner\r\n    const bannerComponent = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.banner\");\r\n    if (bannerComponent?.length) {\r\n      this.bannerData = bannerComponent[0];\r\n    }\r\n\r\n    // Extract services\r\n    const servicesComponents = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.services\");\r\n    if (servicesComponents?.length) {\r\n      this.servicesData = servicesComponents;\r\n    }\r\n\r\n    console.log('Logo:', this.logo);\r\n    console.log('Menu Items:', this.menuItems);\r\n    console.log('Banner Data:', this.bannerData);\r\n    console.log('Services Data:', this.servicesData);\r\n\r\n    // Inject theme \r\n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\r\n    const link = this.renderer.createElement('link');\r\n    this.renderer.setAttribute(link, 'id', 'theme-link');\r\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\r\n    this.renderer.setAttribute(link, 'type', 'text/css');\r\n    this.renderer.setAttribute(link, 'href', href);\r\n\r\n    // Append the link tag to the head of the document\r\n    this.renderer.appendChild(document.head, link);\r\n\r\n    this.primengConfig.ripple = true; //enables core ripple functionality\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Find and remove the link tag when the component is destroyed\r\n    const link = document.getElementById('theme-link');\r\n    if (link) {\r\n      link.remove();\r\n    }\r\n  }\r\n\r\n}\r\n", "<!--HEADER SEC-->\r\n<header class=\"main-header fixed top-0 w-full bg-white z-5\">\r\n    <div class=\"header-body relative max-w-1200 w-full mx-auto px-4 flex align-items-center\">\r\n        <div class=\"header-logo relative pr-6 flex align-items-center w-18rem h-8rem secondary-bg-color\">\r\n            <img [src]=\"logo || '/assets/layout/images/snjya-public-services-logo.png'\" class=\"w-full h-fit\" alt=\"Logo\" />\r\n        </div>\r\n        <div class=\"header-menu-sec pl-5 relative flex align-items-center justify-content-between flex-1\">\r\n            <div class=\"menu-list\">\r\n                <ul class=\"p-0 m-0 flex align-items-center gap-5\">\r\n                    <li class=\"flex\" *ngFor=\"let menuItem of menuItems\">\r\n                        <a [href]=\"menuItem.Link || '#'\"\r\n                           [target]=\"menuItem.Target || '_self'\"\r\n                           class=\"flex flex-column gap-1 text-lg font-semibold text-color line-height-1\">\r\n                            {{ menuItem.Title }}\r\n                            <span class=\"text-sm font-normal text-color-secondary\">{{ menuItem.Sub_Title }}</span>\r\n                        </a>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component w-8rem h-3rem justify-content-center gap-2 line-height-2 bg-bluegray-100 text-color font-semibold border-none\">\r\n                <span class=\"material-symbols-rounded text-xl\">login</span> Login\r\n            </button>\r\n        </div>\r\n    </div>\r\n</header>\r\n<!--HEADER SEC-->\r\n\r\n<!--BANNER SEC-->\r\n<section class=\"banner-sec relative\">\r\n    <div class=\"banner-body h-full flex align-items-center\">\r\n        <div class=\"banner-box relative flex-1 p-8\"></div>\r\n        <div class=\"banner-box relative flex-1 bg-white p-8 flex flex-column justify-content-center\">\r\n            <h4 class=\"mb-4 text-yellow-400\">Far away from the every day!</h4>\r\n            <h1 class=\"m-0 mb-5 text-8xl line-height-1 font-extrabold text-color\">Community <br>of endless <br>beauty &\r\n                Calm</h1>\r\n            <p class=\"m-0 text-lg font-medium\">Drawn by clean air and mythical light, visitors come to experience\r\n                traditions, <br>fine art, great cuisine and natural beauty of the landscape.</p>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--BANNER SEC-->\r\n\r\n<!--SERVICES SEC-->\r\n<section class=\"services-sec w-full\">\r\n    <div class=\"services-body relative max-w-1200 w-full mx-auto px-4 flex\">\r\n        <div class=\"services-box s-box-1 p-5 flex-1 flex flex-column gap-2\">\r\n            <img src=\"/assets/layout/images/id-services.png\" class=\"h-fit w-5rem\" alt=\"\" />\r\n            <h4 class=\"font-bold text-white\">Driver & ID Services</h4>\r\n            <ul class=\"m-0 mb-5 p-0 flex flex-1 flex-column gap-2 list-none\">\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Renew driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Replace driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Get a REAL ID</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    First-time driver’s license/ID card</li>\r\n            </ul>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component h-3rem justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                Driver & ID Services <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n        <div class=\"services-box s-box-2 p-5 flex-1 flex flex-column gap-2\">\r\n            <img src=\"/assets/layout/images/car-services.png\" class=\"h-fit w-5rem\" alt=\"\" />\r\n            <h4 class=\"font-bold text-white\">Vehicle & Plate Services</h4>\r\n            <ul class=\"m-0 mb-5 p-0 flex flex-1 flex-column gap-2 list-none\">\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Renew driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Replace driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Get a REAL ID</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    First-time driver’s license/ID card</li>\r\n            </ul>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component h-3rem justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                Vehicle & Plate Services <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n        <div class=\"services-box s-box-3 p-5 flex-1 flex flex-column gap-2\">\r\n            <img src=\"/assets/layout/images/business-services.png\" class=\"h-fit w-5rem\" alt=\"\" />\r\n            <h4 class=\"font-bold text-white\">Business Services</h4>\r\n            <ul class=\"m-0 mb-5 p-0 flex flex-1 flex-column gap-2 list-none\">\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Renew driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Replace driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Get a REAL ID</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    First-time driver’s license/ID card</li>\r\n            </ul>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component h-3rem justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                Business Services <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n        <div class=\"services-box s-box-4 p-5 flex-1 flex flex-column gap-2\">\r\n            <img src=\"/assets/layout/images/senior-services.png\" class=\"h-fit w-5rem\" alt=\"\" />\r\n            <h4 class=\"font-bold text-white\">Senior Services</h4>\r\n            <ul class=\"m-0 mb-5 p-0 flex flex-1 flex-column gap-2 list-none\">\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Renew driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Replace driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Get a REAL ID</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    First-time driver’s license/ID card</li>\r\n            </ul>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component h-3rem justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                Senior Services <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--SERVICES SEC-->\r\n\r\n<!--ABOUT SEC-->\r\n<section class=\"about-sec relative\">\r\n    <div class=\"about-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap\">\r\n        <div class=\"about-box pr-8 w-6 secondary-bg-color\">\r\n            <img src=\"/assets/layout/images/director-img.jpg\" alt=\"\" class=\"w-full\" />\r\n        </div>\r\n        <div class=\"about-box p-8 pr-0 w-6 secondary-bg-color flex flex-column justify-content-start\">\r\n            <h4 class=\"mb-4 text-yellow-400\">Far away from the every day!</h4>\r\n            <h2 class=\"line m-0 mb-6 relative pb-5 text-6xl line-height-2 font-bold text-white\">A Message From the\r\n                Director\r\n            </h2>\r\n            <p class=\"m-0 text-lg text-white flex-1\">Old Town of Riverside is a special place with limitless potential,\r\n                where everyone deserves equal access\r\n                to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in every\r\n                neighborhood. It exists to serve you and be a resource for you, your family, and your community.</p>\r\n\r\n            <button type=\"button\"\r\n                class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--ABOUT SEC-->\r\n\r\n<!--CITY MEMBERS SEC-->\r\n<section class=\"city-members-sec relative\">\r\n    <div class=\"city-members-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap\">\r\n        <div class=\"city-members-left pt-8 pr-7 pb-8 pl-0 relative flex bg-orange-50\">\r\n            <div class=\"cm-time-box\">\r\n                <img src=\"/assets/layout/images/time-screen.png\" alt=\"\" class=\"w-full\" />\r\n            </div>\r\n            <div class=\"cm-info\">\r\n                <h4 class=\"mb-4 text-yellow-400\">Our city in numbers</h4>\r\n                <h2 class=\"line m-0 mb-6 relative pb-5 text-4xl line-height-2 font-bold text-color\">Programs to help\r\n                    launch,\r\n                    grow and expand any business</h2>\r\n                <p class=\"m-0 text-lg text-color flex-1\">Old Town of Riverside is a special place with limitless\r\n                    potential, where everyone deserves equal access\r\n                    to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in\r\n                    every\r\n                    neighborhood. It exists to serve you and be a resource for you, your family, and your community.</p>\r\n            </div>\r\n        </div>\r\n        <div class=\"city-members-right p-7 pr-0 relative flex flex-column gap-8\">\r\n            <div class=\"cm-right-box\">\r\n                <h3 class=\"flex align-items-start text-8xl font-extrabold line-height-1\">41 <sup\r\n                        class=\"relative text-4xl font-bold inline-block\">k</sup></h3>\r\n                <h5 class=\"uppercase\">Highly-educated creative workforce</h5>\r\n                <p class=\"text-color-secondary\">A vibrant destination where innovations in design and technology are\r\n                    born. A place for you.</p>\r\n            </div>\r\n            <div class=\"cm-right-box\">\r\n                <h3 class=\" flex align-items-start text-8xl font-extrabold line-height-1\">8 <sup\r\n                        class=\"relative text-4xl font-bold inline-block\">th</sup></h3>\r\n                <h5 class=\"uppercase\">Highest Per Capita Income in Virginia</h5>\r\n                <p class=\"text-color-secondary\">A vibrant destination where innovations in design and technology are\r\n                    born. A place for you.</p>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--CITY MEMBERS SEC-->\r\n\r\n<!--QUICK ACCESS SEC-->\r\n<section class=\"quick-access-sec relative\">\r\n    <div class=\"quick-access-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap\">\r\n        <div class=\"quick-access-left relative flex flex-column pt-8 pb-8\">\r\n            <h2 class=\"mb-4 flex align-items-start text-6xl font-extrabold line-height-1\">Quick Access</h2>\r\n            <h4 class=\"line-2 inline-flex w-fit m-0 mb-6 relative pb-5 text-xl line-height-2 font-bold text-yellow-400\">\r\n                What can the City help you with today?</h4>\r\n\r\n            <div class=\"quick-access-list d-grid w-full\">\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-bottom-none border-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/001-toll.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Road construction</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/006-virus.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">COVID-19 updates</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/003-trash-can.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Garbage pickup</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div class=\"quick-access-box p-4 w-full flex flex-column border-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/004-parking.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Parking</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-bottom-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/005-lecture.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Council meetings</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-bottom-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/008-basketball.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Recreation and sport programs</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"quick-access-right\"></div>\r\n    </div>\r\n</section>\r\n<!--QUICK ACCESS SEC-->\r\n\r\n<!--NEWS SEC-->\r\n<section class=\"news-sec relative pt-7 pb-8 secondary-bg-color\">\r\n    <div class=\"news-body relative max-w-1200 w-full mx-auto px-4\">\r\n        <div class=\"news-title\">\r\n            <h5 class=\"line m-0 mb-5 pb-5 relative inline-flex w-fit text-yellow-400\">Find out what’s going on & stay up\r\n                to date.</h5>\r\n            <h2 class=\"m-0 mb-6 relative text-6xl line-height-2 font-bold text-white\">Latest News</h2>\r\n        </div>\r\n        <div class=\"news-list d-grid w-full gap-6\">\r\n            <div class=\"news-box w-full flex flex-column\">\r\n                <div class=\"new-img flex align-items-center justify-content-center overflow-hidden\">\r\n                    <img src=\"/assets/layout/images/new-img-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                </div>\r\n                <h3 class=\"text-white text-xl flex-1\">Proposed Downtown District Ordinance</h3>\r\n                <div class=\"mb-3 flex gap-3 align-items-center\">\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">calendar_month</span> September 16, 2018\r\n                    </div>\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">visibility</span> 240\r\n                    </div>\r\n                </div>\r\n                <p class=\"mb-5 text-sm text-white\">The goal of the proposed ordinance is to create safe venues for\r\n                    customers\r\n                    and to discourage illegal activities. The governing officials of...</p>\r\n\r\n                <button type=\"button\"\r\n                    class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                    Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n                </button>\r\n            </div>\r\n            <div class=\"news-box w-full flex flex-column\">\r\n                <div class=\"new-img flex align-items-center justify-content-center overflow-hidden\">\r\n                    <img src=\"/assets/layout/images/new-img-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                </div>\r\n                <h3 class=\"text-white text-xl flex-1\">Annual Water Quality Report (Gallery Post)</h3>\r\n                <div class=\"mb-3 flex gap-3 align-items-center\">\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">calendar_month</span> September 16, 2018\r\n                    </div>\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">visibility</span> 240\r\n                    </div>\r\n                </div>\r\n                <p class=\"mb-5 text-sm text-white\">The goal of the proposed ordinance is to create safe venues for\r\n                    customers\r\n                    and to discourage illegal activities. The governing officials of...</p>\r\n\r\n                <button type=\"button\"\r\n                    class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                    Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n                </button>\r\n            </div>\r\n            <div class=\"news-box w-full flex flex-column\">\r\n                <div class=\"new-img flex align-items-center justify-content-center overflow-hidden\">\r\n                    <img src=\"/assets/layout/images/new-img-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                </div>\r\n                <h3 class=\"text-white text-xl flex-1\">Waste Industries Garbage Pick Up: Embeds</h3>\r\n                <div class=\"mb-3 flex gap-3 align-items-center\">\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">calendar_month</span> September 16, 2018\r\n                    </div>\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">visibility</span> 240\r\n                    </div>\r\n                </div>\r\n                <p class=\"mb-5 text-sm text-white\">The goal of the proposed ordinance is to create safe venues for\r\n                    customers\r\n                    and to discourage illegal activities. The governing officials of...</p>\r\n\r\n                <button type=\"button\"\r\n                    class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                    Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--NEWS SEC-->\r\n\r\n<!--NEWS LETTER SEC-->\r\n<section class=\"news-letter-sec relative\">\r\n    <div class=\"news-letter-body relative max-w-1200 w-full mx-auto px-4\">\r\n        <div class=\"news-letter-box-list flex bg-white shadow-1\">\r\n            <div class=\"news-letter-box w-6 p-7 flex flex-column justify-content-center\">\r\n                <h2\r\n                    class=\"line-2 relative mb-5 pb-5 inline-flex w-fit align-items-start text-6xl font-extrabold line-height-1\">\r\n                    Sign up for News & Alerts</h2>\r\n                <p>Stay connected to the City of Riverside by signing up to receive official news and alerts by email!\r\n                </p>\r\n                <div class=\"news-letter-form mt-5 relative flex align-items-center\">\r\n                    <input type=\"text\" pInputText class=\"h-3-3rem border-noround flex-1\"\r\n                        placeholder=\"Your email address\" />\r\n                    <button type=\"button\"\r\n                        class=\"px-6 p-element p-ripple border-noround p-button p-component h-3-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                        Sign Up\r\n                    </button>\r\n                </div>\r\n            </div>\r\n            <div class=\"news-letter-img w-6 flex align-items-center justify-content-center overflow-hidden\">\r\n                <img src=\"/assets/layout/images/news-letter-img.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--NEWS LETTER SEC-->\r\n\r\n<!--WHAT HAPPNENING SEC-->\r\n<section class=\"what-happning-sec mt-8 relative px-4\">\r\n    <div class=\"what-happning-body relative w-full mx-auto flex bg-orange-50\">\r\n        <div class=\"what-happning-img w-6 flex align-items-center justify-content-center overflow-hidden\">\r\n            <img src=\"/assets/layout/images/whats-happnening-mg.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n        </div>\r\n        <div class=\"what-happning-right w-6 p-8\">\r\n            <div class=\"what-happning-title\">\r\n                <h5 class=\"line-2 m-0 mb-5 pb-5 relative inline-flex w-fit text-yellow-400\">Join the fun in our city!\r\n                </h5>\r\n                <h2 class=\"m-0 mb-6 relative text-6xl line-height-2 font-bold text-color\">What's Happening</h2>\r\n            </div>\r\n            <div class=\"what-happning-list\">\r\n                <div\r\n                    class=\"what-happning-box mb-4 pb-4 flex align-items-center gap-5 border-none border-bottom-1 border-solid surface-border\">\r\n                    <div class=\"wh-img-box flex align-items-center justify-content-center overflow-hidden\">\r\n                        <img src=\"/assets/layout/images/arches-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                    </div>\r\n                    <div class=\"wh-cnt-sec flex-1 flex align-items-center gap-6\">\r\n                        <div class=\"wh-cnt flex flex-column\">\r\n                            <h3 class=\"text-xl font-bold\">Heritage 10th Anniversary</h3>\r\n                            <div class=\"flex align-items-center gap-3 flex-wrap\">\r\n                                <div class=\"flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100\">\r\n                                    <span>June 9, 2027</span> - <span>June 18, 2027</span>\r\n                                </div>\r\n                                <div class=\"flex align-items-center gap-2\">All Day at <b>Baker Hall</b></div>\r\n                            </div>\r\n                        </div>\r\n                        <button type=\"button\"\r\n                            class=\"px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold\">\r\n                            Find out more\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div\r\n                    class=\"what-happning-box mb-4 pb-4 flex align-items-center gap-5 border-none border-bottom-1 border-solid surface-border\">\r\n                    <div class=\"wh-img-box flex align-items-center justify-content-center overflow-hidden\">\r\n                        <img src=\"/assets/layout/images/arches-2.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                    </div>\r\n                    <div class=\"wh-cnt-sec flex-1 flex align-items-center gap-6\">\r\n                        <div class=\"wh-cnt flex flex-column\">\r\n                            <h3 class=\"text-xl font-bold\">Along Pines Run</h3>\r\n                            <div class=\"flex align-items-center gap-3 flex-wrap\">\r\n                                <div class=\"flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100\">\r\n                                    <span>July 22, 2027</span>\r\n                                </div>\r\n                                <div class=\"flex align-items-center gap-2\">12:00 am at <b>Boulder City Council</b></div>\r\n                            </div>\r\n                        </div>\r\n                        <button type=\"button\"\r\n                            class=\"px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold\">\r\n                            Find out more\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div class=\"what-happning-box flex align-items-center gap-5\">\r\n                    <div class=\"wh-img-box flex align-items-center justify-content-center overflow-hidden\">\r\n                        <img src=\"/assets/layout/images/arches-3.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                    </div>\r\n                    <div class=\"wh-cnt-sec flex-1 flex align-items-center gap-6\">\r\n                        <div class=\"wh-cnt flex flex-column\">\r\n                            <h3 class=\"text-xl font-bold\">Touch A Truck</h3>\r\n                            <div class=\"flex align-items-center gap-3 flex-wrap\">\r\n                                <div class=\"flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100\">\r\n                                    <span>June 9, 2027</span> - <span>June 18, 2027</span>\r\n                                </div>\r\n                                <div class=\"flex align-items-center gap-2\">9:00 am - 5:00 pm at <b>Baker Hall</b></div>\r\n                            </div>\r\n                        </div>\r\n                        <button type=\"button\"\r\n                            class=\"px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold\">\r\n                            Find out more\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--WHAT HAPPNENING SEC-->\r\n\r\n<!--FOOTER SEC-->\r\n<section class=\"footer-sec relative secondary-bg-color\">\r\n    <div class=\"footer-body relative max-w-1200 w-full mx-auto px-4\">\r\n        <div class=\"top-footer py-7 grid mt-0\">\r\n            <div class=\"col-12 lg:col-5 md:col-5\">\r\n                <h3 class=\"line flex mb-5 pb-3 relative text-white text-xl\">Riverside City Hall</h3>\r\n                <ul class=\"p-0 m-0 flex flex-column gap-4\">\r\n                    <li class=\"flex w-full\">\r\n                        <a href=\"#\" class=\"flex w-full text-white\">8353 Sierra Avenue • Riverside, CA 91335</a>\r\n                    </li>\r\n                    <li class=\"flex w-full\">\r\n                        <a href=\"#\" class=\"flex w-full text-white\">Phone: (*************</a>\r\n                    </li>\r\n                    <li class=\"flex w-full\">\r\n                        <a class=\"flex w-full text-white\">Monday - Thursday, 8:00 am - 6:00 pm</a>\r\n                    </li>\r\n                    <li class=\"flex w-full\">\r\n                        <a href=\"\" class=\"flex w-full text-white\">Email : info&#64;asardigital.com</a>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n            <div class=\"col-12 lg:col-7 md:col-7 py-0\">\r\n                <div class=\"grid mt-0\">\r\n                    <div class=\"col-12 lg:col-4 md:col-4\">\r\n                        <h3 class=\"line flex mb-5 pb-3 relative text-white text-xl\">Services</h3>\r\n                        <ul class=\"p-0 m-0 flex flex-column gap-4\">\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"#\" class=\"flex w-full text-white\">Driver & ID Services</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"#\" class=\"flex w-full text-white\">Vehicle & Plate Services</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a class=\"flex w-full text-white\">Business Services</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"\" class=\"flex w-full text-white\">Senior Services</a>\r\n                            </li>\r\n                        </ul>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-4 md:col-4\">\r\n                        <h3 class=\"line flex mb-5 pb-3 relative text-white text-xl\">Useful Links</h3>\r\n                        <ul class=\"p-0 m-0 flex flex-column gap-4\">\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"#\" class=\"flex w-full text-white\">Frequently Asked Questions</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"#\" class=\"flex w-full text-white\">Latest News</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a class=\"flex w-full text-white\">Community</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"\" class=\"flex w-full text-white\">Help Center</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"\" class=\"flex w-full text-white\">Careers</a>\r\n                            </li>\r\n                        </ul>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"middle-footer py-5 w-full flex align-items-center justify-content-center bg-blue-100\">\r\n\r\n        </div>\r\n        <div class=\"bottom-footer py-5 w-full flex flex-column align-items-center justify-content-center\">\r\n            <ul class=\"m-0 mb-3 p-0 flex align-items-center justify-content-center gap-3 list-none\">\r\n                <li><a href=\"#\" class=\"inline-flex w-fit text-white\">Term & Conditions</a></li>\r\n                <li><a href=\"#\" class=\"inline-flex w-fit text-white\">Privacy Policy</a></li>\r\n            </ul>\r\n            <p class=\"m-0 text-white\">© 2025 SNJYA. All rights reserved.</p>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--FOOTER SEC-->"], "mappings": ";;;;;;;;ICUwBA,EADJ,CAAAC,cAAA,cAAoD,aAGiC;IAC7ED,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,gBAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAEvFF,EAFuF,CAAAG,YAAA,EAAO,EACtF,EACH;;;;IANEH,EAAA,CAAAI,SAAA,EAA6B;IAC7BJ,EADA,CAAAK,UAAA,SAAAC,WAAA,CAAAC,IAAA,SAAAP,EAAA,CAAAQ,aAAA,CAA6B,WAAAF,WAAA,CAAAG,MAAA,YACQ;IAEpCT,EAAA,CAAAI,SAAA,EACA;IADAJ,EAAA,CAAAU,kBAAA,MAAAJ,WAAA,CAAAK,KAAA,MACA;IAAuDX,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAY,iBAAA,CAAAN,WAAA,CAAAO,SAAA,CAAwB;;;ADJ3G,OAAM,MAAOC,aAAa;EASxBC,YACUC,aAA4B,EAC5BC,QAAmB,EACnBC,KAAqB,EACrBC,UAA0B;IAH1B,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IATpB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,YAAY,GAAU,EAAE;EAOpB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACC,IAAI,CAAC,eAAe,CAAC;IAC9DC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACJ,aAAa,CAAC;IAElD;IACA,MAAMK,aAAa,GAAG,IAAI,CAACX,UAAU,CAACY,sBAAsB,CAAC,IAAI,CAACN,aAAa,CAACO,IAAI,EAAE,oBAAoB,CAAC;IAC3G,IAAIF,aAAa,EAAEG,MAAM,EAAE;MACzB,IAAI,CAACb,IAAI,GAAGU,aAAa,CAAC,CAAC,CAAC,CAACI,IAAI,EAAEC,GAAG,IAAI,EAAE;IAC9C;IAEA;IACA,MAAMC,aAAa,GAAG,IAAI,CAACjB,UAAU,CAACY,sBAAsB,CAAC,IAAI,CAACN,aAAa,CAACO,IAAI,EAAE,oBAAoB,CAAC;IAC3G,IAAII,aAAa,EAAEH,MAAM,EAAE;MACzB,IAAI,CAACZ,SAAS,GAAGe,aAAa,CAAC,CAAC,CAAC,CAACC,SAAS,IAAI,EAAE;IACnD;IAEA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACpB,KAAK,CAACQ,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;IAClDC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACS,OAAO,CAAC;IAE1C;IACA,MAAMC,eAAe,GAAG,IAAI,CAACpB,UAAU,CAACY,sBAAsB,CAAC,IAAI,CAACO,OAAO,CAACN,IAAI,EAAE,sBAAsB,CAAC;IACzG,IAAIO,eAAe,EAAEN,MAAM,EAAE;MAC3B,IAAI,CAACX,UAAU,GAAGiB,eAAe,CAAC,CAAC,CAAC;IACtC;IAEA;IACA,MAAMC,kBAAkB,GAAG,IAAI,CAACrB,UAAU,CAACY,sBAAsB,CAAC,IAAI,CAACO,OAAO,CAACN,IAAI,EAAE,wBAAwB,CAAC;IAC9G,IAAIQ,kBAAkB,EAAEP,MAAM,EAAE;MAC9B,IAAI,CAACV,YAAY,GAAGiB,kBAAkB;IACxC;IAEAZ,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,IAAI,CAACT,IAAI,CAAC;IAC/BQ,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACR,SAAS,CAAC;IAC1CO,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACP,UAAU,CAAC;IAC5CM,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACN,YAAY,CAAC;IAEhD;IACA,MAAMkB,IAAI,GAAG,wDAAwD;IACrE,MAAMC,IAAI,GAAG,IAAI,CAACzB,QAAQ,CAAC0B,aAAa,CAAC,MAAM,CAAC;IAChD,IAAI,CAAC1B,QAAQ,CAAC2B,YAAY,CAACF,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC;IACpD,IAAI,CAACzB,QAAQ,CAAC2B,YAAY,CAACF,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC;IACrD,IAAI,CAACzB,QAAQ,CAAC2B,YAAY,CAACF,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC;IACpD,IAAI,CAACzB,QAAQ,CAAC2B,YAAY,CAACF,IAAI,EAAE,MAAM,EAAED,IAAI,CAAC;IAE9C;IACA,IAAI,CAACxB,QAAQ,CAAC4B,WAAW,CAACC,QAAQ,CAACC,IAAI,EAAEL,IAAI,CAAC;IAE9C,IAAI,CAAC1B,aAAa,CAACgC,MAAM,GAAG,IAAI,CAAC,CAAC;EACpC;EAEAC,WAAWA,CAAA;IACT;IACA,MAAMP,IAAI,GAAGI,QAAQ,CAACI,cAAc,CAAC,YAAY,CAAC;IAClD,IAAIR,IAAI,EAAE;MACRA,IAAI,CAACS,MAAM,EAAE;IACf;EACF;;;uBAxEWrC,aAAa,EAAAd,EAAA,CAAAoD,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAtD,EAAA,CAAAoD,iBAAA,CAAApD,EAAA,CAAAuD,SAAA,GAAAvD,EAAA,CAAAoD,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAzD,EAAA,CAAAoD,iBAAA,CAAAM,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAb7C,aAAa;MAAA8C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPlBlE,EAFR,CAAAC,cAAA,gBAA4D,aACiC,aACY;UAC7FD,EAAA,CAAAoE,SAAA,aAA8G;UAClHpE,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,aAAkG,aACvE,YAC+B;UAC9CD,EAAA,CAAAqE,UAAA,IAAAC,2BAAA,gBAAoD;UAS5DtE,EADI,CAAAG,YAAA,EAAK,EACH;UAGFH,EAFJ,CAAAC,cAAA,gBACmL,cAChI;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,eAChE;UAGZF,EAHY,CAAAG,YAAA,EAAS,EACP,EACJ,EACD;UAKLH,EADJ,CAAAC,cAAA,mBAAqC,eACuB;UACpDD,EAAA,CAAAoE,SAAA,eAAkD;UAE9CpE,EADJ,CAAAC,cAAA,eAA6F,cACxD;UAAAD,EAAA,CAAAE,MAAA,oCAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClEH,EAAA,CAAAC,cAAA,cAAsE;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAoE,SAAA,UAAI;UAAApE,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAoE,SAAA,UAAI;UAAApE,EAAA,CAAAE,MAAA,qBAC3F;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACbH,EAAA,CAAAC,cAAA,aAAmC;UAAAD,EAAA,CAAAE,MAAA,uFACnB;UAAAF,EAAA,CAAAoE,SAAA,UAAI;UAAApE,EAAA,CAAAE,MAAA,oEAA4D;UAG5FF,EAH4F,CAAAG,YAAA,EAAI,EAClF,EACJ,EACA;UAMFH,EAFR,CAAAC,cAAA,mBAAqC,eACuC,eACA;UAChED,EAAA,CAAAoE,SAAA,eAA+E;UAC/EpE,EAAA,CAAAC,cAAA,cAAiC;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEDH,EADzD,CAAAC,cAAA,cAAiE,cACR,aAAoC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,MAAA,4CAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACcH,EAArD,CAAAC,cAAA,cAAqD,aAAoC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,MAAA,8CAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACYH,EAArD,CAAAC,cAAA,cAAqD,aAAoC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC+BH,EAArD,CAAAC,cAAA,cAAqD,aAAoC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,MAAA,iDAAmC;UAC3CF,EAD2C,CAAAG,YAAA,EAAK,EAC3C;UACLH,EAAA,CAAAC,cAAA,kBACqK;UACjKD,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAC,cAAA,gBAAuC;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAEnFF,EAFmF,CAAAG,YAAA,EAAO,EAC7E,EACP;UACNH,EAAA,CAAAC,cAAA,eAAoE;UAChED,EAAA,CAAAoE,SAAA,eAAgF;UAChFpE,EAAA,CAAAC,cAAA,cAAiC;UAAAD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAELH,EADzD,CAAAC,cAAA,cAAiE,cACR,aAAoC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,MAAA,4CAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACcH,EAArD,CAAAC,cAAA,cAAqD,aAAoC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,MAAA,8CAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACYH,EAArD,CAAAC,cAAA,cAAqD,aAAoC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC+BH,EAArD,CAAAC,cAAA,cAAqD,aAAoC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,MAAA,iDAAmC;UAC3CF,EAD2C,CAAAG,YAAA,EAAK,EAC3C;UACLH,EAAA,CAAAC,cAAA,kBACqK;UACjKD,EAAA,CAAAE,MAAA,kCAAyB;UAAAF,EAAA,CAAAC,cAAA,gBAAuC;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAEvFF,EAFuF,CAAAG,YAAA,EAAO,EACjF,EACP;UACNH,EAAA,CAAAC,cAAA,eAAoE;UAChED,EAAA,CAAAoE,SAAA,eAAqF;UACrFpE,EAAA,CAAAC,cAAA,cAAiC;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEEH,EADzD,CAAAC,cAAA,cAAiE,cACR,aAAoC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,MAAA,4CAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACcH,EAArD,CAAAC,cAAA,cAAqD,aAAoC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,MAAA,8CAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACYH,EAArD,CAAAC,cAAA,cAAqD,aAAoC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC+BH,EAArD,CAAAC,cAAA,cAAqD,aAAoC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,MAAA,kDAAmC;UAC3CF,EAD2C,CAAAG,YAAA,EAAK,EAC3C;UACLH,EAAA,CAAAC,cAAA,mBACqK;UACjKD,EAAA,CAAAE,MAAA,4BAAkB;UAAAF,EAAA,CAAAC,cAAA,iBAAuC;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAEhFF,EAFgF,CAAAG,YAAA,EAAO,EAC1E,EACP;UACNH,EAAA,CAAAC,cAAA,gBAAoE;UAChED,EAAA,CAAAoE,SAAA,gBAAmF;UACnFpE,EAAA,CAAAC,cAAA,eAAiC;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEIH,EADzD,CAAAC,cAAA,eAAiE,eACR,cAAoC;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,MAAA,6CAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACcH,EAArD,CAAAC,cAAA,eAAqD,cAAoC;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,MAAA,+CAAgC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACYH,EAArD,CAAAC,cAAA,eAAqD,cAAoC;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,MAAA,uBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC+BH,EAArD,CAAAC,cAAA,eAAqD,cAAoC;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpGH,EAAA,CAAAE,MAAA,kDAAmC;UAC3CF,EAD2C,CAAAG,YAAA,EAAK,EAC3C;UACLH,EAAA,CAAAC,cAAA,mBACqK;UACjKD,EAAA,CAAAE,MAAA,0BAAgB;UAAAF,EAAA,CAAAC,cAAA,iBAAuC;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAItFF,EAJsF,CAAAG,YAAA,EAAO,EACxE,EACP,EACJ,EACA;UAMFH,EAFR,CAAAC,cAAA,oBAAoC,gBAC+C,gBACxB;UAC/CD,EAAA,CAAAoE,SAAA,gBAA0E;UAC9EpE,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,gBAA8F,eACzD;UAAAD,EAAA,CAAAE,MAAA,qCAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClEH,EAAA,CAAAC,cAAA,eAAoF;UAAAD,EAAA,CAAAE,MAAA,qCAEpF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,cAAyC;UAAAD,EAAA,CAAAE,MAAA,uTAG2D;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAExGH,EAAA,CAAAC,cAAA,mBACgL;UAC5KD,EAAA,CAAAE,MAAA,qBAAW;UAAAF,EAAA,CAAAC,cAAA,iBAAuC;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAIjFF,EAJiF,CAAAG,YAAA,EAAO,EACnE,EACP,EACJ,EACA;UAOEH,EAHZ,CAAAC,cAAA,oBAA2C,gBAC+C,gBACJ,gBACjD;UACrBD,EAAA,CAAAoE,SAAA,gBAAyE;UAC7EpE,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,gBAAqB,eACgB;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzDH,EAAA,CAAAC,cAAA,eAAoF;UAAAD,EAAA,CAAAE,MAAA,8DAEpD;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,cAAyC;UAAAD,EAAA,CAAAE,MAAA,uTAI2D;UAE5GF,EAF4G,CAAAG,YAAA,EAAI,EACtG,EACJ;UAGEH,EAFR,CAAAC,cAAA,gBAAyE,gBAC3C,eACmD;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAC,cAAA,gBACnB;UAAAD,EAAA,CAAAE,MAAA,UAAC;UAAMF,EAAN,CAAAG,YAAA,EAAM,EAAK;UACrEH,EAAA,CAAAC,cAAA,eAAsB;UAAAD,EAAA,CAAAE,MAAA,2CAAkC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7DH,EAAA,CAAAC,cAAA,cAAgC;UAAAD,EAAA,CAAAE,MAAA,oGACN;UAC9BF,EAD8B,CAAAG,YAAA,EAAI,EAC5B;UAEFH,EADJ,CAAAC,cAAA,gBAA0B,eACoD;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAAF,EAAA,CAAAC,cAAA,gBACnB;UAAAD,EAAA,CAAAE,MAAA,WAAE;UAAMF,EAAN,CAAAG,YAAA,EAAM,EAAK;UACtEH,EAAA,CAAAC,cAAA,eAAsB;UAAAD,EAAA,CAAAE,MAAA,8CAAqC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChEH,EAAA,CAAAC,cAAA,cAAgC;UAAAD,EAAA,CAAAE,MAAA,oGACN;UAI1CF,EAJ0C,CAAAG,YAAA,EAAI,EAC5B,EACJ,EACJ,EACA;UAOEH,EAHZ,CAAAC,cAAA,oBAA2C,gBAC+C,gBACf,eACe;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/FH,EAAA,CAAAC,cAAA,eAA4G;UACxGD,EAAA,CAAAE,MAAA,gDAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG3CH,EADJ,CAAAC,cAAA,gBAA6C,gBAEwE;UAC7GD,EAAA,CAAAoE,SAAA,gBAA2E;UAC3EpE,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,0BAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5DH,EAAA,CAAAC,cAAA,cAAmB;UAAAD,EAAA,CAAAE,MAAA,0GACI;UAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;UACNH,EAAA,CAAAC,cAAA,gBAC6H;UACzHD,EAAA,CAAAoE,SAAA,gBAA4E;UAC5EpE,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,yBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3DH,EAAA,CAAAC,cAAA,cAAmB;UAAAD,EAAA,CAAAE,MAAA,0GACI;UAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;UACNH,EAAA,CAAAC,cAAA,gBAC6H;UACzHD,EAAA,CAAAoE,SAAA,gBAAgF;UAChFpE,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzDH,EAAA,CAAAC,cAAA,cAAmB;UAAAD,EAAA,CAAAE,MAAA,0GACI;UAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;UACNH,EAAA,CAAAC,cAAA,gBAA+F;UAC3FD,EAAA,CAAAoE,SAAA,gBAA8E;UAC9EpE,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,cAAmB;UAAAD,EAAA,CAAAE,MAAA,0GACI;UAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;UACNH,EAAA,CAAAC,cAAA,gBAC6I;UACzID,EAAA,CAAAoE,SAAA,gBAA8E;UAC9EpE,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,yBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3DH,EAAA,CAAAC,cAAA,cAAmB;UAAAD,EAAA,CAAAE,MAAA,0GACI;UAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;UACNH,EAAA,CAAAC,cAAA,gBAC6I;UACzID,EAAA,CAAAoE,SAAA,gBAAiF;UACjFpE,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,sCAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxEH,EAAA,CAAAC,cAAA,cAAmB;UAAAD,EAAA,CAAAE,MAAA,0GACI;UAGnCF,EAHmC,CAAAG,YAAA,EAAI,EACzB,EACJ,EACJ;UACNH,EAAA,CAAAoE,SAAA,gBAAsC;UAE9CpE,EADI,CAAAG,YAAA,EAAM,EACA;UAOEH,EAHZ,CAAAC,cAAA,oBAAgE,gBACG,gBACnC,eACsD;UAAAD,EAAA,CAAAE,MAAA,yDAC9D;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAC,cAAA,eAA0E;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UACzFF,EADyF,CAAAG,YAAA,EAAK,EACxF;UAGEH,EAFR,CAAAC,cAAA,gBAA2C,gBACO,gBAC0C;UAChFD,EAAA,CAAAoE,SAAA,gBAA6F;UACjGpE,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,6CAAoC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGvEH,EAFR,CAAAC,cAAA,gBAAgD,gBACmC,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,6BACzE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,gBAA+E,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,cACrE;UACJF,EADI,CAAAG,YAAA,EAAM,EACJ;UACNH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,sJAEoC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE3EH,EAAA,CAAAC,cAAA,mBACqL;UACjLD,EAAA,CAAAE,MAAA,qBAAW;UAAAF,EAAA,CAAAC,cAAA,iBAAuC;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAEzEF,EAFyE,CAAAG,YAAA,EAAO,EACnE,EACP;UAEFH,EADJ,CAAAC,cAAA,gBAA8C,gBAC0C;UAChFD,EAAA,CAAAoE,SAAA,gBAA6F;UACjGpE,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,mDAA0C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG7EH,EAFR,CAAAC,cAAA,gBAAgD,gBACmC,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,6BACzE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,gBAA+E,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,cACrE;UACJF,EADI,CAAAG,YAAA,EAAM,EACJ;UACNH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,sJAEoC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE3EH,EAAA,CAAAC,cAAA,mBACqL;UACjLD,EAAA,CAAAE,MAAA,qBAAW;UAAAF,EAAA,CAAAC,cAAA,iBAAuC;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAEzEF,EAFyE,CAAAG,YAAA,EAAO,EACnE,EACP;UAEFH,EADJ,CAAAC,cAAA,gBAA8C,gBAC0C;UAChFD,EAAA,CAAAoE,SAAA,gBAA6F;UACjGpE,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,iDAAwC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG3EH,EAFR,CAAAC,cAAA,gBAAgD,gBACmC,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,6BACzE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,gBAA+E,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,cACrE;UACJF,EADI,CAAAG,YAAA,EAAM,EACJ;UACNH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,sJAEoC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE3EH,EAAA,CAAAC,cAAA,mBACqL;UACjLD,EAAA,CAAAE,MAAA,qBAAW;UAAAF,EAAA,CAAAC,cAAA,iBAAuC;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAKrFF,EALqF,CAAAG,YAAA,EAAO,EACnE,EACP,EACJ,EACJ,EACA;UAQMH,EAJhB,CAAAC,cAAA,oBAA0C,gBACgC,gBACT,gBACwB,eAEuC;UAC5GD,EAAA,CAAAE,MAAA,mCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClCH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,6GACH;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,gBAAoE;UAChED,EAAA,CAAAoE,SAAA,kBACuC;UACvCpE,EAAA,CAAAC,cAAA,mBACqL;UACjLD,EAAA,CAAAE,MAAA,kBACJ;UAERF,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;UACNH,EAAA,CAAAC,cAAA,gBAAgG;UAC5FD,EAAA,CAAAoE,SAAA,gBAAmG;UAInHpE,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACA;UAMFH,EAFR,CAAAC,cAAA,oBAAsD,gBACwB,gBAC4B;UAC9FD,EAAA,CAAAoE,SAAA,iBAAuG;UAC3GpE,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,iBAAyC,iBACJ,gBAC+C;UAAAD,EAAA,CAAAE,MAAA,mCAC5E;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,gBAA0E;UAAAD,EAAA,CAAAE,MAAA,yBAAgB;UAC9FF,EAD8F,CAAAG,YAAA,EAAK,EAC7F;UAIEH,EAHR,CAAAC,cAAA,iBAAgC,iBAEkG,iBACnC;UACnFD,EAAA,CAAAoE,SAAA,iBAA4F;UAChGpE,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,iBAA6D,iBACpB,gBACH;UAAAD,EAAA,CAAAE,MAAA,kCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGpDH,EAFR,CAAAC,cAAA,iBAAqD,iBACuB,aAC9D;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,YAAE;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UACnDF,EADmD,CAAAG,YAAA,EAAO,EACpD;UACNH,EAAA,CAAAC,cAAA,iBAA2C;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAE3EF,EAF2E,CAAAG,YAAA,EAAI,EAAM,EAC3E,EACJ;UACNH,EAAA,CAAAC,cAAA,oBAC6K;UACzKD,EAAA,CAAAE,MAAA,wBACJ;UAERF,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;UAGFH,EAFJ,CAAAC,cAAA,iBAC8H,iBACnC;UACnFD,EAAA,CAAAoE,SAAA,iBAA4F;UAChGpE,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,iBAA6D,iBACpB,gBACH;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG1CH,EAFR,CAAAC,cAAA,iBAAqD,iBACuB,aAC9D;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UACvBF,EADuB,CAAAG,YAAA,EAAO,EACxB;UACNH,EAAA,CAAAC,cAAA,iBAA2C;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,6BAAoB;UAEtFF,EAFsF,CAAAG,YAAA,EAAI,EAAM,EACtF,EACJ;UACNH,EAAA,CAAAC,cAAA,oBAC6K;UACzKD,EAAA,CAAAE,MAAA,wBACJ;UAERF,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;UAEFH,EADJ,CAAAC,cAAA,iBAA6D,iBAC8B;UACnFD,EAAA,CAAAoE,SAAA,iBAA4F;UAChGpE,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,iBAA6D,iBACpB,gBACH;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGxCH,EAFR,CAAAC,cAAA,iBAAqD,iBACuB,aAC9D;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,YAAE;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UACnDF,EADmD,CAAAG,YAAA,EAAO,EACpD;UACNH,EAAA,CAAAC,cAAA,iBAA2C;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAErFF,EAFqF,CAAAG,YAAA,EAAI,EAAM,EACrF,EACJ;UACNH,EAAA,CAAAC,cAAA,oBAC6K;UACzKD,EAAA,CAAAE,MAAA,wBACJ;UAMxBF,EANwB,CAAAG,YAAA,EAAS,EACP,EACJ,EACJ,EACJ,EACJ,EACA;UAQMH,EAJhB,CAAAC,cAAA,qBAAwD,iBACa,iBACtB,iBACG,gBAC0B;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG5EH,EAFR,CAAAC,cAAA,gBAA2C,gBACf,eACuB;UAAAD,EAAA,CAAAE,MAAA,sDAAwC;UACvFF,EADuF,CAAAG,YAAA,EAAI,EACtF;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACuB;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UACpEF,EADoE,CAAAG,YAAA,EAAI,EACnE;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACc;UAAAD,EAAA,CAAAE,MAAA,6CAAoC;UAC1EF,EAD0E,CAAAG,YAAA,EAAI,EACzE;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACsB;UAAAD,EAAA,CAAAE,MAAA,qCAAgC;UAGtFF,EAHsF,CAAAG,YAAA,EAAI,EAC7E,EACJ,EACH;UAIMH,EAHZ,CAAAC,cAAA,iBAA2C,iBAChB,iBACmB,gBAC0B;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGjEH,EAFR,CAAAC,cAAA,gBAA2C,gBACf,eACuB;UAAAD,EAAA,CAAAE,MAAA,6BAAoB;UACnEF,EADmE,CAAAG,YAAA,EAAI,EAClE;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACuB;UAAAD,EAAA,CAAAE,MAAA,iCAAwB;UACvEF,EADuE,CAAAG,YAAA,EAAI,EACtE;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACc;UAAAD,EAAA,CAAAE,MAAA,0BAAiB;UACvDF,EADuD,CAAAG,YAAA,EAAI,EACtD;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACsB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAGrEF,EAHqE,CAAAG,YAAA,EAAI,EAC5D,EACJ,EACH;UAEFH,EADJ,CAAAC,cAAA,iBAAsC,gBAC0B;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGrEH,EAFR,CAAAC,cAAA,gBAA2C,gBACf,eACuB;UAAAD,EAAA,CAAAE,MAAA,mCAA0B;UACzEF,EADyE,CAAAG,YAAA,EAAI,EACxE;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACuB;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAC1DF,EAD0D,CAAAG,YAAA,EAAI,EACzD;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACc;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAC/CF,EAD+C,CAAAG,YAAA,EAAI,EAC9C;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACsB;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UACzDF,EADyD,CAAAG,YAAA,EAAI,EACxD;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACsB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAMzEF,EANyE,CAAAG,YAAA,EAAI,EACpD,EACJ,EACH,EACJ,EACJ,EACJ;UACNH,EAAA,CAAAoE,SAAA,iBAEM;UAGMpE,EAFZ,CAAAC,cAAA,iBAAkG,gBACN,WAChF,eAAiD;UAAAD,EAAA,CAAAE,MAAA,0BAAiB;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAK;UAC3EH,EAAJ,CAAAC,cAAA,WAAI,eAAiD;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UACvEF,EADuE,CAAAG,YAAA,EAAI,EAAK,EAC3E;UACLH,EAAA,CAAAC,cAAA,eAA0B;UAAAD,EAAA,CAAAE,MAAA,gDAAkC;UAGxEF,EAHwE,CAAAG,YAAA,EAAI,EAC9D,EACJ,EACA;;;UArfOH,EAAA,CAAAI,SAAA,GAAsE;UAAtEJ,EAAA,CAAAK,UAAA,QAAA8D,GAAA,CAAA/C,IAAA,4DAAApB,EAAA,CAAAQ,aAAA,CAAsE;UAK7BR,EAAA,CAAAI,SAAA,GAAY;UAAZJ,EAAA,CAAAK,UAAA,YAAA8D,GAAA,CAAA9C,SAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}