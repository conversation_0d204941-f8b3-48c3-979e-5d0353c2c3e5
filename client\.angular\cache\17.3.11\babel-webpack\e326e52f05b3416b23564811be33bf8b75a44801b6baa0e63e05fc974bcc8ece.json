{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { AppLayoutModule as StoreAppLayoutModule } from './store/layout/app.layout.module';\nimport { AppLayoutModule as BackofficeAppLayoutModule } from './backoffice/layout/app.layout.module';\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { AuthInterceptor } from './core/authentication/auth.intreceptor';\nimport { appInitializerProviders } from './core/bootstrap/initializers';\nimport { MessageService } from 'primeng/api';\nlet AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent],\n  imports: [BrowserModule, AppRoutingModule, StoreAppLayoutModule, BackofficeAppLayoutModule],\n  providers: [{\n    provide: HTTP_INTERCEPTORS,\n    useClass: AuthInterceptor,\n    multi: true\n  }, appInitializerProviders, MessageService],\n  bootstrap: [AppComponent]\n})], AppModule);\nexport { AppModule };", "map": {"version": 3, "names": ["NgModule", "BrowserModule", "AppRoutingModule", "AppComponent", "AppLayoutModule", "StoreAppLayoutModule", "BackofficeAppLayoutModule", "HTTP_INTERCEPTORS", "AuthInterceptor", "appInitializerProviders", "MessageService", "AppModule", "__decorate", "declarations", "imports", "providers", "provide", "useClass", "multi", "bootstrap"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\n\r\nimport { AppRoutingModule } from './app-routing.module';\r\nimport { AppComponent } from './app.component';\r\nimport { AppLayoutModule as StoreAppLayoutModule } from './store/layout/app.layout.module';\r\nimport { AppLayoutModule as BackofficeAppLayoutModule } from './backoffice/layout/app.layout.module';\r\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\r\nimport { AuthInterceptor } from './core/authentication/auth.intreceptor';\r\nimport { appInitializerProviders } from './core/bootstrap/initializers';\r\nimport { MessageService } from 'primeng/api';\r\n\r\n@NgModule({\r\n  declarations: [AppComponent],\r\n  imports: [\r\n    BrowserModule,\r\n    AppRoutingModule,\r\n    StoreAppLayoutModule,\r\n    BackofficeAppLayoutModule,\r\n  ],\r\n  providers: [\r\n    {\r\n      provide: HTTP_INTERCEPTORS,\r\n      useClass: AuthInterceptor,\r\n      multi: true,\r\n    },\r\n    appInitializerProviders,\r\n    MessageService,\r\n  ],\r\n  bootstrap: [AppComponent],\r\n})\r\nexport class AppModule {}\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,2BAA2B;AAEzD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,IAAIC,oBAAoB,QAAQ,kCAAkC;AAC1F,SAASD,eAAe,IAAIE,yBAAyB,QAAQ,uCAAuC;AACpG,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,eAAe,QAAQ,wCAAwC;AACxE,SAASC,uBAAuB,QAAQ,+BAA+B;AACvE,SAASC,cAAc,QAAQ,aAAa;AAqBrC,IAAMC,SAAS,GAAf,MAAMA,SAAS,GAAG;AAAZA,SAAS,GAAAC,UAAA,EAnBrBZ,QAAQ,CAAC;EACRa,YAAY,EAAE,CAACV,YAAY,CAAC;EAC5BW,OAAO,EAAE,CACPb,aAAa,EACbC,gBAAgB,EAChBG,oBAAoB,EACpBC,yBAAyB,CAC1B;EACDS,SAAS,EAAE,CACT;IACEC,OAAO,EAAET,iBAAiB;IAC1BU,QAAQ,EAAET,eAAe;IACzBU,KAAK,EAAE;GACR,EACDT,uBAAuB,EACvBC,cAAc,CACf;EACDS,SAAS,EAAE,CAAChB,YAAY;CACzB,CAAC,C,EACWQ,SAAS,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}