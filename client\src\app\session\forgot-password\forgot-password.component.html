<p-dialog [(visible)]="visible" header="Forgot Password" [modal]="true" [closable]="true" [resizable]="false"
    (onHide)="onDialogHide()" styleClass="bg-white w-30rem">
    <form [formGroup]="form">
        <div class="p-fluid p-formgrid grid required mb-6">
            <div class="field col-12 md:col-12 mb-0 pb-0">
                <label class="text-base font-medium text-gray-600 mb-2">Email</label>
                <input type="text" formControlName="email"
                    class="p-inputtext p-component p-element w-full bg-gray-50 mb-4 h-3rem"
                    placeholder="Enter your registerd email"
                    [ngClass]="{ 'is-invalid': submitted && f['email'].errors }" />
                <div *ngIf="submitted && f['email'].errors" class="invalid-feedback">
                    <div *ngIf="f['email'].errors['required']">Email is required</div>
                    <div *ngIf="f['email'].errors['email']">Email is invalid</div>
                </div>
                <span class="form-text hint">We will send reset instructions on your registered email</span>
            </div>
            <div class="field col-12 md:col-12 mb-0 pb-0">
                <label class="text-base font-medium text-gray-600">Security Question 1</label>
                <p-dropdown [options]="cities" [(ngModel)]="selectedCity" optionLabel="name" [showClear]="true"
                    [styleClass]="'p-inputtext p-component p-element w-full bg-gray-50'"></p-dropdown>
            </div>
            <div class="field col-12 md:col-12 mb-0 pb-0">
                <label class="text-base font-medium text-gray-600">Answer</label>
                <input type="text" class="p-inputtext p-component p-element w-full bg-gray-50 h-3rem" id="username" />
            </div>
            <div class="field col-12 md:col-12 mb-0 pb-0">
                <label class="text-base font-medium text-gray-600">Security Question 2</label>
                <p-dropdown [options]="cities" [(ngModel)]="selectedCity" optionLabel="name" [showClear]="true"
                    [styleClass]="'p-inputtext p-component p-element w-full bg-gray-50'"></p-dropdown>
            </div>
            <div class="field col-12 md:col-12 mb-0 pb-0">
                <label class="text-base font-medium text-gray-600">Answer</label>
                <input type="text" class="p-inputtext p-component p-element w-full bg-gray-50 h-3rem" id="username" />
            </div>
        </div>
        <div class="flex justify-content-between gap-3">
            <button type="submit"
                class="p-element p-ripple p-button-rounded p-button p-component w-full h-3rem justify-content-center"
                [disabled]="!!form.invalid || saving" (click)="onSubmit()">Reset
                Password</button>
            <button type="submit"
                class="p-element p-ripple p-button-outlined p-button-rounded p-button p-component w-full h-3rem justify-content-center"
                (click)="visible = false">
                Cancel
            </button>
        </div>
    </form>
</p-dialog>