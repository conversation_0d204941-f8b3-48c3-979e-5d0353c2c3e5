import { Injectable, NgZone } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { ApiConstant, AppConstant, CMS_APIContstant } from "src/app/constants/api.constants";
import {
  BehaviorSubject,
  catchError,
  fromEvent,
  lastValueFrom,
  map,
  Observable,
  of,
  switchMap,
  tap,
} from "rxjs";

@Injectable({
  providedIn: "root",
})
export class AuthService {
  public userSubject: BehaviorSubject<any>;
  public permissions: BehaviorSubject<any> = new BehaviorSubject<any>([]);
  private cmsTokenVal: BehaviorSubject<any> = new BehaviorSubject<any>("");
  private sessionChannel = new BroadcastChannel("session");
  private timer: any;
  private logoutTriggered = false;
  public TokenKey = 'jwtToken';
  public UserDetailsKey = 'userInfo';

  constructor(
    private http: HttpClient,
    private ngZone: NgZone
  ) {
    const user: any = this.getAuth();
    this.userSubject = new BehaviorSubject<any>(Object.keys(user).length ? user : "");
    this.bindUserActivityEvents();
  }

  checkAdminUser() {
    const user: any = this.getAuth();
    if (user && user[this.UserDetailsKey]?.documentId) {
      return this.getCartDetails(user[this.UserDetailsKey].documentId).pipe(
        tap((cartres: any) => {
          if (cartres) {
            this.updateAuth({
              userDetails: {
                address: cartres.address,
                email: cartres.email,
                firstname: cartres.firstname,
                lastname: cartres.lastname,
                username: cartres.username,
              }
            });
          }
        })
      )
    } else {
      return of(null);
    }
  }

  bindUserActivityEvents() {
    const events = ["click", "keydown"];
    for (let i = 0; i < events.length; i++) {
      const element = events[i];
      fromEvent(document, element).subscribe(() => {
        this.setInavtivityTimer();
        this.sessionChannel.postMessage({
          type: "activityFound",
        });
      });
    }
    this.sessionChannel.onmessage = (event) => {
      if (event?.data?.type == "activityFound") {
        this.ngZone.run(() => {
          this.setInavtivityTimer();
        });
      }
      if (event?.data?.type == "logout") {
        this.logoutTriggered = true;
        this.doLogout();
      }
    };
    this.setInavtivityTimer();
    this.sessionChannel.postMessage({
      type: "activityFound",
    });
  }

  setInavtivityTimer() {
    clearTimeout(this.timer);
    if (!this.isLoggedIn) {
      return;
    }
    this.timer = setTimeout(() => {
      this.doLogout();
    }, AppConstant.SESSION_TIMEOUT);
  }

  login(username: string, password: string, rememberMe: boolean) {
    return this.http
      .post<any>(CMS_APIContstant.SINGIN, {
        identifier: (username || "").toLowerCase(),
        password,
      })
      .pipe(
        tap((res) => {
          if (res) {
            this.setAuth(res.jwt, res.user, rememberMe);
          }
          return res;
        }),
        switchMap((res) => {
          if (res?.user) {
            return this.getCartDetails(res.user.documentId).pipe(
              map((data: any) => {
                if (data?.cart) {
                  res.user.cart = data.cart;
                  res.user.customer = data.cart.customer;
                }
                this.updateAuth(res.user);
                return res;
              })
            )
          }
          return of(null);
        }),
      );
  }

  getCartDetails(userId: string): any {
    return this.http.get(`${CMS_APIContstant.USER_DETAILS}/${userId}/me`);
  }

  getToken() {
    const val = this.userSubject.value;
    return val ? val[this.TokenKey] : null;
  }

  get partnerFunction() {
    const user = this.userSubject.value;
    if (user && user[this.UserDetailsKey]?.customer?.partner_functions?.length) {
      return user[this.UserDetailsKey].customer.partner_functions[0];
    }
    return {};
  }

  get userDetail() {
    const user = this.userSubject.value;
    return user ? user[this.UserDetailsKey] : null;
  }

  get isLoggedIn(): boolean {
    return !!this.userSubject.value;
  }

  updateAuth(user: any) {
    const auth: any = this.getAuth();
    if (user?.userDetails) {
      auth[this.UserDetailsKey] = {
        ...auth[this.UserDetailsKey],
        ...user?.userDetails
      };
    }
    if (user?.cart) {
      auth[this.UserDetailsKey].cart = user?.cart;
    }
    if (user?.customer) {
      auth[this.UserDetailsKey].customer = user?.customer;
    }
    this.setAuth(auth[this.TokenKey], auth[this.UserDetailsKey], this.isRememberMeSelected());
  }

  isRememberMeSelected(): boolean {
    return !!localStorage.getItem(this.TokenKey);
  }

  doLogout() {
    this.resetAuth();
  }

  resetAuth() {
    this.http.get(`${CMS_APIContstant.USER_DETAILS}/${this.userDetail.documentId}/logout`).subscribe(() => {
      this.removeAuthToken();
      !this.logoutTriggered &&
        this.sessionChannel.postMessage({
          type: "logout",
        });
      this.userSubject.next(null);
      window.location.href = "#/auth/login";
      window.location.reload();
    });
  }

  getAuth(): any {
    const authtoken: any = this.getAuthToken();
    const userDetails: any = this.getUserDetails();
    if (authtoken && this.isJsonString(userDetails)) {
      return {
        [this.UserDetailsKey]: JSON.parse(userDetails),
        [this.TokenKey]: JSON.parse(authtoken)
      }
    }
    return {};
  }

  setAuth(token: string, user: any, rememberMe: boolean) {
    if (rememberMe) {
      localStorage.setItem(this.TokenKey, JSON.stringify(token));
      localStorage.setItem(this.UserDetailsKey, JSON.stringify(user));
    } else {
      sessionStorage.setItem(this.TokenKey, JSON.stringify(token));
      sessionStorage.setItem(this.UserDetailsKey, JSON.stringify(user));
    }
    this.userSubject.next({
      [this.UserDetailsKey]: user,
      [this.TokenKey]: token
    });
  }

  getAuthToken() {
    return localStorage.getItem(this.TokenKey) || sessionStorage.getItem(this.TokenKey);
  }

  getUserDetails() {
    return localStorage.getItem(this.UserDetailsKey) || sessionStorage.getItem(this.UserDetailsKey);
  }

  getUserEmail(): string | null {
    const userData = sessionStorage.getItem('userInfo');

    if (userData) {
      try {
        const parsedUser = JSON.parse(userData);
        return parsedUser.email || null;
      } catch (error) {
        return null;
      }
    }
    return null;
  }

  removeAuthToken() {
    localStorage.removeItem(this.TokenKey);
    sessionStorage.removeItem(this.TokenKey);
    localStorage.removeItem(this.UserDetailsKey);
    sessionStorage.removeItem(this.UserDetailsKey);
  }

  isJsonString(str: any) {
    try {
      JSON.parse(str);
    } catch (e) {
      return false;
    }
    return true;
  }

  async getUserPermissions() {
    const userDetails = this.userDetail;
    return await lastValueFrom(this.getUserPermissionsDB(userDetails));
  }

  getUserPermissionsDB(userDetails: any) {
    return this.http
      .get<any>(`${CMS_APIContstant.USER_DETAILS}/${userDetails.documentId}/permissions/vendor`)
      .pipe(
        map((res) => {
          if (res?.data?.length) {
            const data = (res?.data || []);
            this.permissions.next(data);
            return data;
          }
          return [];
        })
      )
      .pipe(
        catchError((error) => {
          this.permissions.next([]);
          return error;
        })
      )
  }

  get getPermissions(): any[] {
    return this.permissions?.value || [];
  }

  getCMSToken(): Observable<string> {
    return this.http.get<{ token: string }>(ApiConstant.FETCH_TOKEN).pipe(
      map((response) => {
        this.cmsTokenVal.next(response.token);
        return response.token as string;
      }),
    )
  }

  get cmsToken(): Observable<string> {
    return of(this.cmsTokenVal?.value || "");
  }
}
