import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { LoginComponent } from "./login/login.component";
import { AuthGuard } from "src/app/core/authentication/auth.guard";
import { SessionComponent } from "./session.component";

const routes: Routes = [
  {
    path: '',
    component: SessionComponent,
    children: [
      {
        path: "login",
        canActivate: [AuthGuard],
        component: LoginComponent,
      },
      {
        path: "reset-password",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import("./reset-password/reset-password.module").then(
            (m) => m.ResetPasswordModule
          ),
      },
      {
        path: "signup",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import("./signup/signup.module").then(
            (m) => m.SignupModule
          ),
      },
      { path: "", redirectTo: "login", pathMatch: "full" },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SessionRoutingModule { }
