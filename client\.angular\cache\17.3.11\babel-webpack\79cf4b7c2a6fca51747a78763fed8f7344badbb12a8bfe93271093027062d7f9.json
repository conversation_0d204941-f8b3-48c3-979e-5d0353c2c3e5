{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\nimport { AuthInterceptor } from './core/authentication/auth.intreceptor';\nimport { appInitializerProviders } from './core/bootstrap/initializers';\nimport { MessageService } from 'primeng/api';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: HTTP_INTERCEPTORS,\n        useClass: AuthInterceptor,\n        multi: true\n      }, appInitializerProviders, MessageService],\n      imports: [BrowserModule, AppRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent],\n    imports: [BrowserModule, AppRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "AppRoutingModule", "AppComponent", "HTTP_INTERCEPTORS", "AuthInterceptor", "appInitializerProviders", "MessageService", "AppModule", "bootstrap", "provide", "useClass", "multi", "imports", "declarations"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\n\r\nimport { AppRoutingModule } from './app-routing.module';\r\nimport { AppComponent } from './app.component';\r\nimport { HTTP_INTERCEPTORS } from '@angular/common/http';\r\nimport { AuthInterceptor } from './core/authentication/auth.intreceptor';\r\nimport { appInitializerProviders } from './core/bootstrap/initializers';\r\nimport { MessageService } from 'primeng/api';\r\n\r\n@NgModule({\r\n  declarations: [AppComponent],\r\n  imports: [\r\n    BrowserModule,\r\n    AppRoutingModule,\r\n  ],\r\n  providers: [\r\n    {\r\n      provide: HTTP_INTERCEPTORS,\r\n      useClass: AuthInterceptor,\r\n      multi: true,\r\n    },\r\n    appInitializerProviders,\r\n    MessageService,\r\n  ],\r\n  bootstrap: [AppComponent],\r\n})\r\nexport class AppModule { }\r\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AAEzD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,eAAe,QAAQ,wCAAwC;AACxE,SAASC,uBAAuB,QAAQ,+BAA+B;AACvE,SAASC,cAAc,QAAQ,aAAa;;AAmB5C,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRN,YAAY;IAAA;EAAA;;;iBATb,CACT;QACEO,OAAO,EAAEN,iBAAiB;QAC1BO,QAAQ,EAAEN,eAAe;QACzBO,KAAK,EAAE;OACR,EACDN,uBAAuB,EACvBC,cAAc,CACf;MAAAM,OAAA,GAXCZ,aAAa,EACbC,gBAAgB;IAAA;EAAA;;;2EAaPM,SAAS;IAAAM,YAAA,GAhBLX,YAAY;IAAAU,OAAA,GAEzBZ,aAAa,EACbC,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}