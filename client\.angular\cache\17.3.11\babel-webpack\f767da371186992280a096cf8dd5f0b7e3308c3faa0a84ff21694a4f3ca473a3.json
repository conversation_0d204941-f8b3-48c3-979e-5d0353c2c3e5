{"ast": null, "code": "import { AppSidebarComponent } from './app.sidebar.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/store/layout/service/app.layout.service\";\nimport * as i2 from \"src/app/core/authentication/auth.service\";\nimport * as i3 from \"./app.menu.service\";\nimport * as i4 from \"primeng/button\";\nimport * as i5 from \"primeng/ripple\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"primeng/styleclass\";\nimport * as i8 from \"./app.sidebar.component\";\nconst _c0 = [\"menubutton\"];\nconst _c1 = [\"searchinput\"];\nconst _c2 = () => [\"/store/profile\"];\nexport class AppTopbarComponent {\n  constructor(layoutService, el, authService, menuService) {\n    this.layoutService = layoutService;\n    this.el = el;\n    this.authService = authService;\n    this.menuService = menuService;\n    this.searchActive = false;\n    this.activeMenu = '';\n  }\n  activateSearch() {\n    this.searchActive = true;\n    setTimeout(() => {\n      this.searchInput.nativeElement.focus();\n    }, 100);\n  }\n  deactivateSearch() {\n    this.searchActive = false;\n  }\n  onMenuButtonClick() {\n    this.layoutService.onMenuToggle();\n  }\n  onConfigButtonClick() {\n    this.layoutService.showConfigSidebar();\n  }\n  onSidebarButtonClick() {\n    this.layoutService.showSidebar();\n  }\n  logout() {\n    this.authService.doLogout();\n  }\n  ngOnInit() {\n    this.menuService.activeMenu$.subscribe(menuName => {\n      this.activeMenu = menuName;\n    });\n  }\n  static {\n    this.ɵfac = function AppTopbarComponent_Factory(t) {\n      return new (t || AppTopbarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.MenuService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppTopbarComponent,\n      selectors: [[\"app-topbar\"]],\n      viewQuery: function AppTopbarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(AppSidebarComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuButton = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.searchInput = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.appSidebar = _t.first);\n        }\n      },\n      decls: 28,\n      vars: 4,\n      consts: [[\"menubutton\", \"\"], [\"profile\", \"\"], [1, \"layout-topbar\"], [1, \"topbar-start\", \"gap-6\"], [1, \"app-logo-normal\", \"w-12rem\"], [3, \"src\"], [\"type\", \"button\", 1, \"topbar-menubutton\", \"p-link\", \"p-trigger\", 3, \"click\"], [1, \"pi\", \"pi-bars\"], [1, \"layout-topbar-menu-section\"], [1, \"topbar-end\"], [1, \"topbar-menu\"], [1, \"ml-3\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-cog\", 1, \"p-button-text\", \"p-button-secondary\", \"text-color-secondary\", \"p-button-rounded\", \"flex-shrink-0\", 3, \"click\"], [1, \"profile-item\", \"topbar-item\"], [\"pStyleClass\", \"@next\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", \"pRipple\", \"\", 1, \"cursor-pointer\", 3, \"hideOnOutsideClick\"], [1, \"pi\", \"pi-fw\", \"pi-user\"], [1, \"topbar-menu\", \"active-topbar-menu\", \"p-4\", \"w-15rem\", \"z-5\", \"ng-hidden\", \"border-round\", \"hadow-1\", \"border-1\", \"border-bluegray-100\"], [\"role\", \"menuitem\", 1, \"m-0\", \"mb-3\"], [\"href\", \"javascript: void(0)\", \"pStyleClass\", \"@grandparent\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", 1, \"flex\", \"align-items-center\", \"hover:text-primary-500\", \"transition-duration-200\", 3, \"routerLink\"], [1, \"pi\", \"pi-fw\", \"pi-user\", \"mr-2\"], [\"role\", \"menuitem\", 1, \"m-0\", \"mt-3\", \"pt-3\", \"border-top-1\", \"logout-btn\"], [\"pStyleClass\", \"@grandparent\", \"enterFromClass\", \"ng-hidden\", \"enterActiveClass\", \"px-scalein\", \"leaveToClass\", \"ng-hidden\", \"leaveActiveClass\", \"px-fadeout\", 1, \"flex\", \"align-items-center\", \"hover:text-primary-500\", \"transition-duration-200\", \"font-medium\", \"cursor-pointer\", 3, \"click\"], [1, \"pi\", \"pi-fw\", \"pi-sign-out\", \"mr-2\"]],\n      template: function AppTopbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n          i0.ɵɵelement(3, \"img\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"button\", 6, 0);\n          i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_4_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMenuButtonClick());\n          });\n          i0.ɵɵelement(6, \"i\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 8);\n          i0.ɵɵelement(8, \"app-sidebar\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 9)(10, \"ul\", 10)(11, \"li\", 11)(12, \"button\", 12);\n          i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_button_click_12_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onConfigButtonClick());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"li\", 13, 1)(15, \"a\", 14);\n          i0.ɵɵelement(16, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"ul\", 16)(18, \"li\", 17)(19, \"a\", 18);\n          i0.ɵɵelement(20, \"i\", 19);\n          i0.ɵɵelementStart(21, \"span\");\n          i0.ɵɵtext(22, \"Profile\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"li\", 20)(24, \"a\", 21);\n          i0.ɵɵlistener(\"click\", function AppTopbarComponent_Template_a_click_24_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.logout());\n          });\n          i0.ɵɵelement(25, \"i\", 22);\n          i0.ɵɵelementStart(26, \"span\");\n          i0.ɵɵtext(27, \"Logout\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"src\", \"assets/layout/images/chs-logo-\" + (ctx.layoutService.config().colorScheme === \"dark\" ? \"light\" : \"dark\") + \".svg\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"hideOnOutsideClick\", true);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(3, _c2));\n        }\n      },\n      dependencies: [i4.ButtonDirective, i5.Ripple, i6.RouterLink, i7.StyleClass, i8.AppSidebarComponent],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppSidebarComponent", "AppTopbarComponent", "constructor", "layoutService", "el", "authService", "menuService", "searchActive", "activeMenu", "activateSearch", "setTimeout", "searchInput", "nativeElement", "focus", "deactivateSearch", "onMenuButtonClick", "onMenuToggle", "onConfigButtonClick", "showConfigSidebar", "onSidebarButtonClick", "showSidebar", "logout", "doLogout", "ngOnInit", "activeMenu$", "subscribe", "menuName", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "ElementRef", "i2", "AuthService", "i3", "MenuService", "selectors", "viewQuery", "AppTopbarComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵlistener", "AppTopbarComponent_Template_button_click_4_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "AppTopbarComponent_Template_button_click_12_listener", "ɵɵtext", "AppTopbarComponent_Template_a_click_24_listener", "ɵɵadvance", "ɵɵproperty", "config", "colorScheme", "ɵɵsanitizeUrl", "ɵɵpureFunction0", "_c2"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\store\\layout\\app.topbar.component.ts", "D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\store\\layout\\app.topbar.component.html"], "sourcesContent": ["import { Component, ElementRef, ViewChild } from '@angular/core';\r\nimport { LayoutService } from 'src/app/store/layout/service/app.layout.service';\r\nimport { AppSidebarComponent } from './app.sidebar.component';\r\nimport { AuthService } from 'src/app/core/authentication/auth.service';\r\nimport { MenuService } from './app.menu.service';\r\n\r\n@Component({\r\n    selector: 'app-topbar',\r\n    templateUrl: './app.topbar.component.html'\r\n})\r\nexport class AppTopbarComponent {\r\n\r\n    @ViewChild('menubutton') menuButton!: ElementRef;\r\n    @ViewChild('searchinput') searchInput!: ElementRef;\r\n    @ViewChild(AppSidebarComponent) appSidebar!: AppSidebarComponent;\r\n    searchActive: boolean = false;\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        public el: ElementRef,\r\n        private authService: AuthService,\r\n        private menuService: MenuService\r\n    ) { }\r\n    activateSearch() {\r\n        this.searchActive = true;\r\n        setTimeout(() => {\r\n            this.searchInput.nativeElement.focus();\r\n        }, 100);\r\n    }\r\n\r\n    deactivateSearch() {\r\n        this.searchActive = false;\r\n    }\r\n    onMenuButtonClick() {\r\n        this.layoutService.onMenuToggle();\r\n    }\r\n\r\n    onConfigButtonClick() {\r\n        this.layoutService.showConfigSidebar();\r\n    }\r\n\r\n    onSidebarButtonClick() {\r\n        this.layoutService.showSidebar();\r\n    }\r\n\r\n    logout() {\r\n        this.authService.doLogout();\r\n    }\r\n\r\n    activeMenu: string = '';\r\n\r\n    ngOnInit(): void {\r\n        this.menuService.activeMenu$.subscribe((menuName) => {\r\n            this.activeMenu = menuName;\r\n        });\r\n    }\r\n}", "<div class=\"layout-topbar\">\r\n    <div class=\"topbar-start gap-6\">\r\n        <div class=\"app-logo-normal w-12rem\">\r\n            <img [src]=\"'assets/layout/images/chs-logo-'+ (layoutService.config().colorScheme === 'dark' ? 'light' : 'dark') + '.svg'\">\r\n        </div>\r\n\r\n        <button #menubutton type=\"button\" class=\"topbar-menubutton p-link p-trigger\" (click)=\"onMenuButtonClick()\">\r\n            <i class=\"pi pi-bars\"></i>\r\n        </button>\r\n\r\n        <!-- <app-breadcrumb class=\"topbar-breadcrumb\"></app-breadcrumb> -->\r\n         <!-- commented on 21-03-2025 because not needed on the top corner -->\r\n         <!-- <h1 class=\"header-title m-0 pl-3 text-3xl relative\">{{ activeMenu  }}</h1> -->\r\n         \r\n    </div>\r\n    <div class=\"layout-topbar-menu-section\">\r\n        <app-sidebar></app-sidebar>\r\n    </div>\r\n    <div class=\"topbar-end\">\r\n        <ul class=\"topbar-menu  \">\r\n            <!-- <li class=\"hidden lg:block\">\r\n                <div class=\"topbar-search\" [ngClass]=\"{'topbar-search-active': searchActive}\">\r\n                    <button pButton icon=\"pi pi-search\"\r\n                        class=\"topbar-searchbutton p-button-text p-button-secondary text-color-secondary p-button-rounded flex-shrink-0\"\r\n                        type=\"button\" (click)=\"activateSearch()\"></button>\r\n                    <div class=\"search-input-wrapper\">\r\n                        <span class=\"p-input-icon-right\">\r\n                            <input #searchinput type=\"text\" pInputText placeholder=\"Search\" (blur)=\"deactivateSearch()\"\r\n                                (keydown.escape)=\"deactivateSearch()\" />\r\n                            <i class=\"pi pi-search\"></i>\r\n                        </span>\r\n                    </div>\r\n                </div>\r\n            </li>\r\n\r\n            <li class=\"profile-item topbar-item \">\r\n                <button pButton type=\"button\" icon=\"pi pi-bell\"\r\n                    class=\"p-button-text p-button-secondary text-color-secondary p-button-rounded flex-shrink-0\"></button>\r\n            </li>\r\n\r\n            <li class=\"profile-item topbar-item \">\r\n                <button pButton type=\"button\" icon=\"pi pi-comment\"\r\n                    class=\"p-button-text p-button-secondary relative text-color-secondary p-button-rounded flex-shrink-0\"></button>\r\n            </li> -->\r\n\r\n            <li class=\"ml-3\">\r\n                <button pButton type=\"button\" icon=\"pi pi-cog\"\r\n                    class=\"p-button-text p-button-secondary text-color-secondary p-button-rounded flex-shrink-0\"\r\n                    (click)=\"onConfigButtonClick()\"></button>\r\n            </li>\r\n\r\n            <li #profile class=\"profile-item topbar-item \">\r\n                <a pStyleClass=\"@next\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\" leaveToClass=\"ng-hidden\"\r\n                    leaveActiveClass=\"px-fadeout\" [hideOnOutsideClick]=\"true\" pRipple class=\"cursor-pointer\">\r\n                    <i class=\"pi pi-fw pi-user\"></i>\r\n                </a>\r\n\r\n                <ul class=\"topbar-menu active-topbar-menu p-4 w-15rem z-5 ng-hidden border-round hadow-1 border-1 border-bluegray-100\">\r\n                    <li role=\"menuitem\" class=\"m-0 mb-3\">\r\n                        <a href=\"javascript: void(0)\" [routerLink]=\"['/store/profile']\"\r\n                            class=\"flex align-items-center hover:text-primary-500 transition-duration-200\"\r\n                            pStyleClass=\"@grandparent\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\"\r\n                            leaveToClass=\"ng-hidden\" leaveActiveClass=\"px-fadeout\">\r\n                            <i class=\"pi pi-fw pi-user mr-2\"></i>\r\n                            <span>Profile</span>\r\n                        </a>\r\n                    </li>\r\n                    <li role=\"menuitem\" class=\"m-0 mt-3 pt-3 border-top-1 logout-btn\">\r\n                        <a (click)=\"logout()\"\r\n                            class=\"flex align-items-center hover:text-primary-500 transition-duration-200 font-medium cursor-pointer\"\r\n                            pStyleClass=\"@grandparent\" enterFromClass=\"ng-hidden\" enterActiveClass=\"px-scalein\"\r\n                            leaveToClass=\"ng-hidden\" leaveActiveClass=\"px-fadeout\">\r\n                            <i class=\"pi pi-fw pi-sign-out mr-2\"></i>\r\n                            <span>Logout</span>\r\n                        </a>\r\n                    </li>\r\n                </ul>\r\n            </li>\r\n            <!-- <li class=\"right-panel-button relative hidden lg:block\">\r\n                <button pButton type=\"button\" label=\"Today\" style=\"width:5.7rem\" icon=\"pi pi-bookmark\"\r\n                    class=\"layout-rightmenu-button hidden md:block font-normal\" styleClass=\"font-normal\"\r\n                    (click)=\"onSidebarButtonClick()\"></button>\r\n                <button pButton type=\"button\" icon=\"pi pi-bookmark\" styleClass=\"font-normal\"\r\n                    class=\"layout-rightmenu-button block md:hidden\" (click)=\"onSidebarButtonClick()\"></button>\r\n            </li> -->\r\n        </ul>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,mBAAmB,QAAQ,yBAAyB;;;;;;;;;;;;;AAQ7D,OAAM,MAAOC,kBAAkB;EAM3BC,YACWC,aAA4B,EAC5BC,EAAc,EACbC,WAAwB,EACxBC,WAAwB;IAHzB,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,EAAE,GAAFA,EAAE;IACD,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IALvB,KAAAC,YAAY,GAAY,KAAK;IAiC7B,KAAAC,UAAU,GAAW,EAAE;EA3BnB;EACJC,cAAcA,CAAA;IACV,IAAI,CAACF,YAAY,GAAG,IAAI;IACxBG,UAAU,CAAC,MAAK;MACZ,IAAI,CAACC,WAAW,CAACC,aAAa,CAACC,KAAK,EAAE;IAC1C,CAAC,EAAE,GAAG,CAAC;EACX;EAEAC,gBAAgBA,CAAA;IACZ,IAAI,CAACP,YAAY,GAAG,KAAK;EAC7B;EACAQ,iBAAiBA,CAAA;IACb,IAAI,CAACZ,aAAa,CAACa,YAAY,EAAE;EACrC;EAEAC,mBAAmBA,CAAA;IACf,IAAI,CAACd,aAAa,CAACe,iBAAiB,EAAE;EAC1C;EAEAC,oBAAoBA,CAAA;IAChB,IAAI,CAAChB,aAAa,CAACiB,WAAW,EAAE;EACpC;EAEAC,MAAMA,CAAA;IACF,IAAI,CAAChB,WAAW,CAACiB,QAAQ,EAAE;EAC/B;EAIAC,QAAQA,CAAA;IACJ,IAAI,CAACjB,WAAW,CAACkB,WAAW,CAACC,SAAS,CAAEC,QAAQ,IAAI;MAChD,IAAI,CAAClB,UAAU,GAAGkB,QAAQ;IAC9B,CAAC,CAAC;EACN;;;uBA5CSzB,kBAAkB,EAAA0B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,UAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAlBlC,kBAAkB;MAAAmC,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;yBAIhBvC,mBAAmB;;;;;;;;;;;;;;;UCZ1B2B,EAFR,CAAAc,cAAA,aAA2B,aACS,aACS;UACjCd,EAAA,CAAAe,SAAA,aAA2H;UAC/Hf,EAAA,CAAAgB,YAAA,EAAM;UAENhB,EAAA,CAAAc,cAAA,mBAA2G;UAA9Bd,EAAA,CAAAiB,UAAA,mBAAAC,oDAAA;YAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;YAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASR,GAAA,CAAAzB,iBAAA,EAAmB;UAAA,EAAC;UACtGY,EAAA,CAAAe,SAAA,WAA0B;UAOlCf,EANI,CAAAgB,YAAA,EAAS,EAMP;UACNhB,EAAA,CAAAc,cAAA,aAAwC;UACpCd,EAAA,CAAAe,SAAA,kBAA2B;UAC/Bf,EAAA,CAAAgB,YAAA,EAAM;UA6BMhB,EA5BZ,CAAAc,cAAA,aAAwB,cACM,cA0BL,kBAGuB;UAAhCd,EAAA,CAAAiB,UAAA,mBAAAK,qDAAA;YAAAtB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;YAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASR,GAAA,CAAAvB,mBAAA,EAAqB;UAAA,EAAC;UACvCU,EADwC,CAAAgB,YAAA,EAAS,EAC5C;UAGDhB,EADJ,CAAAc,cAAA,iBAA+C,aAEkD;UACzFd,EAAA,CAAAe,SAAA,aAAgC;UACpCf,EAAA,CAAAgB,YAAA,EAAI;UAIIhB,EAFR,CAAAc,cAAA,cAAuH,cAC9E,aAI0B;UACvDd,EAAA,CAAAe,SAAA,aAAqC;UACrCf,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAuB,MAAA,eAAO;UAErBvB,EAFqB,CAAAgB,YAAA,EAAO,EACpB,EACH;UAEDhB,EADJ,CAAAc,cAAA,cAAkE,aAIH;UAHxDd,EAAA,CAAAiB,UAAA,mBAAAO,gDAAA;YAAAxB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;YAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASR,GAAA,CAAAnB,MAAA,EAAQ;UAAA,EAAC;UAIjBM,EAAA,CAAAe,SAAA,aAAyC;UACzCf,EAAA,CAAAc,cAAA,YAAM;UAAAd,EAAA,CAAAuB,MAAA,cAAM;UAcxCvB,EAdwC,CAAAgB,YAAA,EAAO,EACnB,EACH,EACJ,EACJ,EAQJ,EACH,EACJ;;;UApFWhB,EAAA,CAAAyB,SAAA,GAAqH;UAArHzB,EAAA,CAAA0B,UAAA,4CAAAb,GAAA,CAAArC,aAAA,CAAAmD,MAAA,GAAAC,WAAA,0CAAA5B,EAAA,CAAA6B,aAAA,CAAqH;UAkDpF7B,EAAA,CAAAyB,SAAA,IAA2B;UAA3BzB,EAAA,CAAA0B,UAAA,4BAA2B;UAMvB1B,EAAA,CAAAyB,SAAA,GAAiC;UAAjCzB,EAAA,CAAA0B,UAAA,eAAA1B,EAAA,CAAA8B,eAAA,IAAAC,GAAA,EAAiC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}