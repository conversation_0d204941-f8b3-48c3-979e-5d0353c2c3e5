{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../core/services/content-vendor.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/inputtext\";\nfunction HomeComponent_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 126)(1, \"a\", 127);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 128);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const menuItem_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", menuItem_r1.Link || \"#\", i0.ɵɵsanitizeUrl)(\"target\", menuItem_r1.Target || \"_self\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", menuItem_r1.Title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(menuItem_r1.Sub_Title);\n  }\n}\nfunction HomeComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 129);\n    i0.ɵɵelement(1, \"img\", 130);\n    i0.ɵɵelementStart(2, \"h4\", 131);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 132);\n    i0.ɵɵelementStart(5, \"button\", 133);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementStart(7, \"span\", 28);\n    i0.ɵɵtext(8, \"arrow_right_alt\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const service_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    i0.ɵɵproperty(\"ngClass\", \"s-box-\" + (i_r3 + 1));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", service_r2.Icon == null ? null : service_r2.Icon.url, i0.ɵɵsanitizeUrl)(\"alt\", service_r2.Title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(service_r2.Title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", service_r2.Description, i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", service_r2.Button_Title, \" \");\n  }\n}\nexport class HomeComponent {\n  constructor(primengConfig, renderer, route, CMSservice) {\n    this.primengConfig = primengConfig;\n    this.renderer = renderer;\n    this.route = route;\n    this.CMSservice = CMSservice;\n    this.logo = '';\n    this.menuItems = [];\n    this.bannerData = null;\n    this.servicesData = [];\n  }\n  ngOnInit() {\n    this.commonContent = this.route.snapshot.data['commonContent'];\n    console.log('Common Content:', this.commonContent);\n    // Extract logo\n    const logoComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.logo\");\n    if (logoComponent?.length) {\n      this.logo = logoComponent[0].Logo?.url || '';\n    }\n    // Extract menu\n    const menuComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.menu\");\n    if (menuComponent?.length) {\n      this.menuItems = menuComponent[0].Menu_Item || [];\n    }\n    this.content = this.route.snapshot.data['content'];\n    console.log('Home Content:', this.content);\n    // Extract banner\n    const bannerComponent = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.banner\");\n    if (bannerComponent?.length) {\n      this.bannerData = bannerComponent[0];\n    }\n    // Extract services\n    const servicesComponents = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.services\");\n    if (servicesComponents?.length) {\n      this.servicesData = servicesComponents;\n    }\n    // Inject theme \n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\n    const link = this.renderer.createElement('link');\n    this.renderer.setAttribute(link, 'id', 'theme-link');\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\n    this.renderer.setAttribute(link, 'type', 'text/css');\n    this.renderer.setAttribute(link, 'href', href);\n    // Append the link tag to the head of the document\n    this.renderer.appendChild(document.head, link);\n    this.primengConfig.ripple = true; //enables core ripple functionality\n  }\n  ngOnDestroy() {\n    // Find and remove the link tag when the component is destroyed\n    const link = document.getElementById('theme-link');\n    if (link) {\n      link.remove();\n    }\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ContentService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 333,\n      vars: 12,\n      consts: [[1, \"main-header\", \"fixed\", \"top-0\", \"w-full\", \"bg-white\", \"z-5\"], [1, \"header-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"align-items-center\"], [1, \"header-logo\", \"relative\", \"pr-6\", \"flex\", \"align-items-center\", \"w-18rem\", \"h-8rem\", \"secondary-bg-color\"], [\"alt\", \"Logo\", 1, \"w-full\", \"h-fit\", 3, \"src\"], [1, \"header-menu-sec\", \"pl-5\", \"relative\", \"flex\", \"align-items-center\", \"justify-content-between\", \"flex-1\"], [1, \"menu-list\"], [1, \"p-0\", \"m-0\", \"flex\", \"align-items-center\", \"gap-5\"], [\"class\", \"flex\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-8rem\", \"h-3rem\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-bluegray-100\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"material-symbols-rounded\", \"text-xl\"], [1, \"banner-sec\", \"relative\"], [1, \"banner-body\", \"h-full\", \"flex\", \"align-items-center\"], [1, \"banner-box\", \"relative\", \"flex-1\", \"p-8\"], [1, \"banner-box\", \"relative\", \"flex-1\", \"bg-white\", \"p-8\", \"flex\", \"flex-column\", \"justify-content-center\"], [1, \"mb-4\", \"text-yellow-400\"], [1, \"m-0\", \"mb-5\", \"text-8xl\", \"line-height-1\", \"font-extrabold\", \"text-color\"], [1, \"m-0\", \"text-lg\", \"font-medium\"], [1, \"services-sec\", \"w-full\"], [1, \"services-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\"], [\"class\", \"services-box p-5 flex-1 flex flex-column gap-2\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"about-sec\", \"relative\"], [1, \"about-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"flex-wrap\"], [1, \"about-box\", \"pr-8\", \"w-6\", \"secondary-bg-color\"], [\"src\", \"/assets/layout/images/director-img.jpg\", \"alt\", \"\", 1, \"w-full\"], [1, \"about-box\", \"p-8\", \"pr-0\", \"w-6\", \"secondary-bg-color\", \"flex\", \"flex-column\", \"justify-content-start\"], [1, \"line\", \"m-0\", \"mb-6\", \"relative\", \"pb-5\", \"text-6xl\", \"line-height-2\", \"font-bold\", \"text-white\"], [1, \"m-0\", \"text-lg\", \"text-white\", \"flex-1\"], [\"type\", \"button\", 1, \"px-5\", \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"h-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-white\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"material-symbols-rounded\"], [1, \"city-members-sec\", \"relative\"], [1, \"city-members-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"flex-wrap\"], [1, \"city-members-left\", \"pt-8\", \"pr-7\", \"pb-8\", \"pl-0\", \"relative\", \"flex\", \"bg-orange-50\"], [1, \"cm-time-box\"], [\"src\", \"/assets/layout/images/time-screen.png\", \"alt\", \"\", 1, \"w-full\"], [1, \"cm-info\"], [1, \"line\", \"m-0\", \"mb-6\", \"relative\", \"pb-5\", \"text-4xl\", \"line-height-2\", \"font-bold\", \"text-color\"], [1, \"m-0\", \"text-lg\", \"text-color\", \"flex-1\"], [1, \"city-members-right\", \"p-7\", \"pr-0\", \"relative\", \"flex\", \"flex-column\", \"gap-8\"], [1, \"cm-right-box\"], [1, \"flex\", \"align-items-start\", \"text-8xl\", \"font-extrabold\", \"line-height-1\"], [1, \"relative\", \"text-4xl\", \"font-bold\", \"inline-block\"], [1, \"uppercase\"], [1, \"text-color-secondary\"], [1, \"quick-access-sec\", \"relative\"], [1, \"quick-access-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"flex-wrap\"], [1, \"quick-access-left\", \"relative\", \"flex\", \"flex-column\", \"pt-8\", \"pb-8\"], [1, \"mb-4\", \"flex\", \"align-items-start\", \"text-6xl\", \"font-extrabold\", \"line-height-1\"], [1, \"line-2\", \"inline-flex\", \"w-fit\", \"m-0\", \"mb-6\", \"relative\", \"pb-5\", \"text-xl\", \"line-height-2\", \"font-bold\", \"text-yellow-400\"], [1, \"quick-access-list\", \"d-grid\", \"w-full\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-bottom-none\", \"border-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/001-toll.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"m-0\", \"mb-4\", \"uppercase\", \"flex-1\"], [1, \"text-sm\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-none\", \"border-top-1\", \"border-right-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/006-virus.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [\"src\", \"/assets/layout/images/003-trash-can.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/004-parking.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-none\", \"border-top-1\", \"border-right-1\", \"border-bottom-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/005-lecture.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [\"src\", \"/assets/layout/images/008-basketball.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"quick-access-right\"], [1, \"news-sec\", \"relative\", \"pt-7\", \"pb-8\", \"secondary-bg-color\"], [1, \"news-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\"], [1, \"news-title\"], [1, \"line\", \"m-0\", \"mb-5\", \"pb-5\", \"relative\", \"inline-flex\", \"w-fit\", \"text-yellow-400\"], [1, \"m-0\", \"mb-6\", \"relative\", \"text-6xl\", \"line-height-2\", \"font-bold\", \"text-white\"], [1, \"news-list\", \"d-grid\", \"w-full\", \"gap-6\"], [1, \"news-box\", \"w-full\", \"flex\", \"flex-column\"], [1, \"new-img\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/new-img-1.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"text-white\", \"text-xl\", \"flex-1\"], [1, \"mb-3\", \"flex\", \"gap-3\", \"align-items-center\"], [1, \"text\", \"flex\", \"align-items-center\", \"font-medium\", \"text-sm\", \"gap-1\", \"text-white\"], [1, \"mb-5\", \"text-sm\", \"text-white\"], [\"type\", \"button\", 1, \"px-5\", \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"h-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-yellow-400\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"news-letter-sec\", \"relative\"], [1, \"news-letter-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\"], [1, \"news-letter-box-list\", \"flex\", \"bg-white\", \"shadow-1\"], [1, \"news-letter-box\", \"w-6\", \"p-7\", \"flex\", \"flex-column\", \"justify-content-center\"], [1, \"line-2\", \"relative\", \"mb-5\", \"pb-5\", \"inline-flex\", \"w-fit\", \"align-items-start\", \"text-6xl\", \"font-extrabold\", \"line-height-1\"], [1, \"news-letter-form\", \"mt-5\", \"relative\", \"flex\", \"align-items-center\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Your email address\", 1, \"h-3-3rem\", \"border-noround\", \"flex-1\"], [\"type\", \"button\", 1, \"px-6\", \"p-element\", \"p-ripple\", \"border-noround\", \"p-button\", \"p-component\", \"h-3-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-yellow-400\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"news-letter-img\", \"w-6\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/news-letter-img.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"what-happning-sec\", \"mt-8\", \"relative\", \"px-4\"], [1, \"what-happning-body\", \"relative\", \"w-full\", \"mx-auto\", \"flex\", \"bg-orange-50\"], [1, \"what-happning-img\", \"w-6\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/whats-happnening-mg.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"what-happning-right\", \"w-6\", \"p-8\"], [1, \"what-happning-title\"], [1, \"line-2\", \"m-0\", \"mb-5\", \"pb-5\", \"relative\", \"inline-flex\", \"w-fit\", \"text-yellow-400\"], [1, \"m-0\", \"mb-6\", \"relative\", \"text-6xl\", \"line-height-2\", \"font-bold\", \"text-color\"], [1, \"what-happning-list\"], [1, \"what-happning-box\", \"mb-4\", \"pb-4\", \"flex\", \"align-items-center\", \"gap-5\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"surface-border\"], [1, \"wh-img-box\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/arches-1.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"wh-cnt-sec\", \"flex-1\", \"flex\", \"align-items-center\", \"gap-6\"], [1, \"wh-cnt\", \"flex\", \"flex-column\"], [1, \"text-xl\", \"font-bold\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"flex-wrap\"], [1, \"flex\", \"w-fit\", \"align-items-center\", \"gap-2\", \"py-2\", \"p-3\", \"bg-orange-100\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"type\", \"button\", 1, \"px-4\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-noround\", \"p-button-outlined\", \"h-3-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"text-color\", \"font-semibold\"], [\"src\", \"/assets/layout/images/arches-2.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"what-happning-box\", \"flex\", \"align-items-center\", \"gap-5\"], [\"src\", \"/assets/layout/images/arches-3.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"footer-sec\", \"relative\", \"secondary-bg-color\"], [1, \"footer-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\"], [1, \"top-footer\", \"py-7\", \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-5\", \"md:col-5\"], [1, \"line\", \"flex\", \"mb-5\", \"pb-3\", \"relative\", \"text-white\", \"text-xl\"], [1, \"p-0\", \"m-0\", \"flex\", \"flex-column\", \"gap-4\"], [1, \"flex\", \"w-full\"], [\"href\", \"#\", 1, \"flex\", \"w-full\", \"text-white\"], [1, \"flex\", \"w-full\", \"text-white\"], [\"href\", \"\", 1, \"flex\", \"w-full\", \"text-white\"], [1, \"col-12\", \"lg:col-7\", \"md:col-7\", \"py-0\"], [1, \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\"], [1, \"middle-footer\", \"py-5\", \"w-full\", \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-100\"], [1, \"bottom-footer\", \"py-5\", \"w-full\", \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [1, \"m-0\", \"mb-3\", \"p-0\", \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-3\", \"list-none\"], [\"href\", \"#\", 1, \"inline-flex\", \"w-fit\", \"text-white\"], [1, \"m-0\", \"text-white\"], [1, \"flex\"], [1, \"flex\", \"flex-column\", \"gap-1\", \"text-lg\", \"font-semibold\", \"text-color\", \"line-height-1\", 3, \"href\", \"target\"], [1, \"text-sm\", \"font-normal\", \"text-color-secondary\"], [1, \"services-box\", \"p-5\", \"flex-1\", \"flex\", \"flex-column\", \"gap-2\", 3, \"ngClass\"], [1, \"h-fit\", \"w-5rem\", 3, \"src\", \"alt\"], [1, \"font-bold\", \"text-white\"], [3, \"innerHTML\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"h-3rem\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-white\", \"text-color\", \"font-semibold\", \"border-none\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"header\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"img\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"ul\", 6);\n          i0.ɵɵtemplate(7, HomeComponent_li_7_Template, 5, 4, \"li\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"button\", 8)(9, \"span\", 9);\n          i0.ɵɵtext(10, \"login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11, \" Login \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(12, \"section\", 10)(13, \"div\", 11);\n          i0.ɵɵelement(14, \"div\", 12);\n          i0.ɵɵelementStart(15, \"div\", 13)(16, \"h4\", 14);\n          i0.ɵɵtext(17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"h1\", 15);\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"p\", 16);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(22, \"section\", 17)(23, \"div\", 18);\n          i0.ɵɵtemplate(24, HomeComponent_div_24_Template, 9, 6, \"div\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"section\", 20)(26, \"div\", 21)(27, \"div\", 22);\n          i0.ɵɵelement(28, \"img\", 23);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 24)(30, \"h4\", 14);\n          i0.ɵɵtext(31, \"Far away from the every day!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"h2\", 25);\n          i0.ɵɵtext(33, \"A Message From the Director \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"p\", 26);\n          i0.ɵɵtext(35, \"Old Town of Riverside is a special place with limitless potential, where everyone deserves equal access to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in every neighborhood. It exists to serve you and be a resource for you, your family, and your community.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"button\", 27);\n          i0.ɵɵtext(37, \" Learn More \");\n          i0.ɵɵelementStart(38, \"span\", 28);\n          i0.ɵɵtext(39, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(40, \"section\", 29)(41, \"div\", 30)(42, \"div\", 31)(43, \"div\", 32);\n          i0.ɵɵelement(44, \"img\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\", 34)(46, \"h4\", 14);\n          i0.ɵɵtext(47, \"Our city in numbers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"h2\", 35);\n          i0.ɵɵtext(49, \"Programs to help launch, grow and expand any business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"p\", 36);\n          i0.ɵɵtext(51, \"Old Town of Riverside is a special place with limitless potential, where everyone deserves equal access to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in every neighborhood. It exists to serve you and be a resource for you, your family, and your community.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(52, \"div\", 37)(53, \"div\", 38)(54, \"h3\", 39);\n          i0.ɵɵtext(55, \"41 \");\n          i0.ɵɵelementStart(56, \"sup\", 40);\n          i0.ɵɵtext(57, \"k\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"h5\", 41);\n          i0.ɵɵtext(59, \"Highly-educated creative workforce\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"p\", 42);\n          i0.ɵɵtext(61, \"A vibrant destination where innovations in design and technology are born. A place for you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"div\", 38)(63, \"h3\", 39);\n          i0.ɵɵtext(64, \"8 \");\n          i0.ɵɵelementStart(65, \"sup\", 40);\n          i0.ɵɵtext(66, \"th\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"h5\", 41);\n          i0.ɵɵtext(68, \"Highest Per Capita Income in Virginia\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"p\", 42);\n          i0.ɵɵtext(70, \"A vibrant destination where innovations in design and technology are born. A place for you.\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(71, \"section\", 43)(72, \"div\", 44)(73, \"div\", 45)(74, \"h2\", 46);\n          i0.ɵɵtext(75, \"Quick Access\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"h4\", 47);\n          i0.ɵɵtext(77, \" What can the City help you with today?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"div\", 48)(79, \"div\", 49);\n          i0.ɵɵelement(80, \"img\", 50);\n          i0.ɵɵelementStart(81, \"h6\", 51);\n          i0.ɵɵtext(82, \"Road construction\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"p\", 52);\n          i0.ɵɵtext(84, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(85, \"div\", 53);\n          i0.ɵɵelement(86, \"img\", 54);\n          i0.ɵɵelementStart(87, \"h6\", 51);\n          i0.ɵɵtext(88, \"COVID-19 updates\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"p\", 52);\n          i0.ɵɵtext(90, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(91, \"div\", 53);\n          i0.ɵɵelement(92, \"img\", 55);\n          i0.ɵɵelementStart(93, \"h6\", 51);\n          i0.ɵɵtext(94, \"Garbage pickup\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"p\", 52);\n          i0.ɵɵtext(96, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(97, \"div\", 56);\n          i0.ɵɵelement(98, \"img\", 57);\n          i0.ɵɵelementStart(99, \"h6\", 51);\n          i0.ɵɵtext(100, \"Parking\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"p\", 52);\n          i0.ɵɵtext(102, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(103, \"div\", 58);\n          i0.ɵɵelement(104, \"img\", 59);\n          i0.ɵɵelementStart(105, \"h6\", 51);\n          i0.ɵɵtext(106, \"Council meetings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"p\", 52);\n          i0.ɵɵtext(108, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(109, \"div\", 58);\n          i0.ɵɵelement(110, \"img\", 60);\n          i0.ɵɵelementStart(111, \"h6\", 51);\n          i0.ɵɵtext(112, \"Recreation and sport programs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(113, \"p\", 52);\n          i0.ɵɵtext(114, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(115, \"div\", 61);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(116, \"section\", 62)(117, \"div\", 63)(118, \"div\", 64)(119, \"h5\", 65);\n          i0.ɵɵtext(120, \"Find out what\\u2019s going on & stay up to date.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(121, \"h2\", 66);\n          i0.ɵɵtext(122, \"Latest News\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(123, \"div\", 67)(124, \"div\", 68)(125, \"div\", 69);\n          i0.ɵɵelement(126, \"img\", 70);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(127, \"h3\", 71);\n          i0.ɵɵtext(128, \"Proposed Downtown District Ordinance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(129, \"div\", 72)(130, \"div\", 73)(131, \"span\", 9);\n          i0.ɵɵtext(132, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(133, \" September 16, 2018 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(134, \"div\", 73)(135, \"span\", 9);\n          i0.ɵɵtext(136, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(137, \" 240 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(138, \"p\", 74);\n          i0.ɵɵtext(139, \"The goal of the proposed ordinance is to create safe venues for customers and to discourage illegal activities. The governing officials of...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(140, \"button\", 75);\n          i0.ɵɵtext(141, \" Learn More \");\n          i0.ɵɵelementStart(142, \"span\", 28);\n          i0.ɵɵtext(143, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(144, \"div\", 68)(145, \"div\", 69);\n          i0.ɵɵelement(146, \"img\", 70);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(147, \"h3\", 71);\n          i0.ɵɵtext(148, \"Annual Water Quality Report (Gallery Post)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(149, \"div\", 72)(150, \"div\", 73)(151, \"span\", 9);\n          i0.ɵɵtext(152, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(153, \" September 16, 2018 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(154, \"div\", 73)(155, \"span\", 9);\n          i0.ɵɵtext(156, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(157, \" 240 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(158, \"p\", 74);\n          i0.ɵɵtext(159, \"The goal of the proposed ordinance is to create safe venues for customers and to discourage illegal activities. The governing officials of...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(160, \"button\", 75);\n          i0.ɵɵtext(161, \" Learn More \");\n          i0.ɵɵelementStart(162, \"span\", 28);\n          i0.ɵɵtext(163, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(164, \"div\", 68)(165, \"div\", 69);\n          i0.ɵɵelement(166, \"img\", 70);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(167, \"h3\", 71);\n          i0.ɵɵtext(168, \"Waste Industries Garbage Pick Up: Embeds\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(169, \"div\", 72)(170, \"div\", 73)(171, \"span\", 9);\n          i0.ɵɵtext(172, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(173, \" September 16, 2018 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(174, \"div\", 73)(175, \"span\", 9);\n          i0.ɵɵtext(176, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(177, \" 240 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(178, \"p\", 74);\n          i0.ɵɵtext(179, \"The goal of the proposed ordinance is to create safe venues for customers and to discourage illegal activities. The governing officials of...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(180, \"button\", 75);\n          i0.ɵɵtext(181, \" Learn More \");\n          i0.ɵɵelementStart(182, \"span\", 28);\n          i0.ɵɵtext(183, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(184, \"section\", 76)(185, \"div\", 77)(186, \"div\", 78)(187, \"div\", 79)(188, \"h2\", 80);\n          i0.ɵɵtext(189, \" Sign up for News & Alerts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(190, \"p\");\n          i0.ɵɵtext(191, \"Stay connected to the City of Riverside by signing up to receive official news and alerts by email! \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(192, \"div\", 81);\n          i0.ɵɵelement(193, \"input\", 82);\n          i0.ɵɵelementStart(194, \"button\", 83);\n          i0.ɵɵtext(195, \" Sign Up \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(196, \"div\", 84);\n          i0.ɵɵelement(197, \"img\", 85);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(198, \"section\", 86)(199, \"div\", 87)(200, \"div\", 88);\n          i0.ɵɵelement(201, \"img\", 89);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(202, \"div\", 90)(203, \"div\", 91)(204, \"h5\", 92);\n          i0.ɵɵtext(205, \"Join the fun in our city! \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(206, \"h2\", 93);\n          i0.ɵɵtext(207, \"What's Happening\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(208, \"div\", 94)(209, \"div\", 95)(210, \"div\", 96);\n          i0.ɵɵelement(211, \"img\", 97);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(212, \"div\", 98)(213, \"div\", 99)(214, \"h3\", 100);\n          i0.ɵɵtext(215, \"Heritage 10th Anniversary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(216, \"div\", 101)(217, \"div\", 102)(218, \"span\");\n          i0.ɵɵtext(219, \"June 9, 2027\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(220, \" - \");\n          i0.ɵɵelementStart(221, \"span\");\n          i0.ɵɵtext(222, \"June 18, 2027\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(223, \"div\", 103);\n          i0.ɵɵtext(224, \"All Day at \");\n          i0.ɵɵelementStart(225, \"b\");\n          i0.ɵɵtext(226, \"Baker Hall\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(227, \"button\", 104);\n          i0.ɵɵtext(228, \" Find out more \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(229, \"div\", 95)(230, \"div\", 96);\n          i0.ɵɵelement(231, \"img\", 105);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(232, \"div\", 98)(233, \"div\", 99)(234, \"h3\", 100);\n          i0.ɵɵtext(235, \"Along Pines Run\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(236, \"div\", 101)(237, \"div\", 102)(238, \"span\");\n          i0.ɵɵtext(239, \"July 22, 2027\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(240, \"div\", 103);\n          i0.ɵɵtext(241, \"12:00 am at \");\n          i0.ɵɵelementStart(242, \"b\");\n          i0.ɵɵtext(243, \"Boulder City Council\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(244, \"button\", 104);\n          i0.ɵɵtext(245, \" Find out more \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(246, \"div\", 106)(247, \"div\", 96);\n          i0.ɵɵelement(248, \"img\", 107);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(249, \"div\", 98)(250, \"div\", 99)(251, \"h3\", 100);\n          i0.ɵɵtext(252, \"Touch A Truck\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(253, \"div\", 101)(254, \"div\", 102)(255, \"span\");\n          i0.ɵɵtext(256, \"June 9, 2027\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(257, \" - \");\n          i0.ɵɵelementStart(258, \"span\");\n          i0.ɵɵtext(259, \"June 18, 2027\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(260, \"div\", 103);\n          i0.ɵɵtext(261, \"9:00 am - 5:00 pm at \");\n          i0.ɵɵelementStart(262, \"b\");\n          i0.ɵɵtext(263, \"Baker Hall\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(264, \"button\", 104);\n          i0.ɵɵtext(265, \" Find out more \");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(266, \"section\", 108)(267, \"div\", 109)(268, \"div\", 110)(269, \"div\", 111)(270, \"h3\", 112);\n          i0.ɵɵtext(271, \"Riverside City Hall\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(272, \"ul\", 113)(273, \"li\", 114)(274, \"a\", 115);\n          i0.ɵɵtext(275, \"8353 Sierra Avenue \\u2022 Riverside, CA 91335\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(276, \"li\", 114)(277, \"a\", 115);\n          i0.ɵɵtext(278, \"Phone: (*************\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(279, \"li\", 114)(280, \"a\", 116);\n          i0.ɵɵtext(281, \"Monday - Thursday, 8:00 am - 6:00 pm\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(282, \"li\", 114)(283, \"a\", 117);\n          i0.ɵɵtext(284, \"Email : <EMAIL>\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(285, \"div\", 118)(286, \"div\", 119)(287, \"div\", 120)(288, \"h3\", 112);\n          i0.ɵɵtext(289, \"Services\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(290, \"ul\", 113)(291, \"li\", 114)(292, \"a\", 115);\n          i0.ɵɵtext(293, \"Driver & ID Services\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(294, \"li\", 114)(295, \"a\", 115);\n          i0.ɵɵtext(296, \"Vehicle & Plate Services\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(297, \"li\", 114)(298, \"a\", 116);\n          i0.ɵɵtext(299, \"Business Services\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(300, \"li\", 114)(301, \"a\", 117);\n          i0.ɵɵtext(302, \"Senior Services\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(303, \"div\", 120)(304, \"h3\", 112);\n          i0.ɵɵtext(305, \"Useful Links\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(306, \"ul\", 113)(307, \"li\", 114)(308, \"a\", 115);\n          i0.ɵɵtext(309, \"Frequently Asked Questions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(310, \"li\", 114)(311, \"a\", 115);\n          i0.ɵɵtext(312, \"Latest News\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(313, \"li\", 114)(314, \"a\", 116);\n          i0.ɵɵtext(315, \"Community\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(316, \"li\", 114)(317, \"a\", 117);\n          i0.ɵɵtext(318, \"Help Center\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(319, \"li\", 114)(320, \"a\", 117);\n          i0.ɵɵtext(321, \"Careers\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelement(322, \"div\", 121);\n          i0.ɵɵelementStart(323, \"div\", 122)(324, \"ul\", 123)(325, \"li\")(326, \"a\", 124);\n          i0.ɵɵtext(327, \"Term & Conditions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(328, \"li\")(329, \"a\", 124);\n          i0.ɵɵtext(330, \"Privacy Policy\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(331, \"p\", 125);\n          i0.ɵɵtext(332, \"\\u00A9 2025 SNJYA. All rights reserved.\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"src\", ctx.logo || \"/assets/layout/images/snjya-public-services-logo.png\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.menuItems);\n          i0.ɵɵadvance(7);\n          i0.ɵɵstyleProp(\"background-image\", (ctx.bannerData == null ? null : ctx.bannerData.Image == null ? null : ctx.bannerData.Image.url) ? \"url(\" + ctx.bannerData.Image.url + \")\" : null)(\"background-size\", \"cover\")(\"background-position\", \"center\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate((ctx.bannerData == null ? null : ctx.bannerData.Annotation) || \"Far away from the every day!\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate((ctx.bannerData == null ? null : ctx.bannerData.Title) || \"Community of endless beauty & Calm\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate((ctx.bannerData == null ? null : ctx.bannerData.Description) || \"Drawn by clean air and mythical light, visitors come to experience traditions, fine art, great cuisine and natural beauty of the landscape.\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.servicesData);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i5.InputText],\n      styles: [\".max-w-1200 {\\n  max-width: 1200px;\\n}\\n  .secondary-color {\\n  color: #030f5e !important;\\n}\\n  .secondary-bg-color {\\n  background: #030f5e !important;\\n}\\n  .font-extrabold {\\n  font-weight: 900 !important;\\n}\\n  .d-grid {\\n  display: grid !important;\\n}\\n  .object-fit-contain {\\n  object-fit: contain;\\n}\\n  .object-fit-cover {\\n  object-fit: cover;\\n}\\n  .h-3-3rem {\\n  height: 3.3rem !important;\\n}\\n  .bg-blue-75 {\\n  background: rgb(227, 243, 255) !important;\\n}\\n  .main-header:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  height: 8rem;\\n  background: #030f5e !important;\\n  width: 20%;\\n}\\n  .banner-sec {\\n  margin: 8rem 0 0 0;\\n  height: calc(100vh - 8rem);\\n  background: url('banner-img.jpg') center center no-repeat;\\n  background-size: cover;\\n}\\n  .banner-sec .banner-box {\\n  margin: 120px 0 0 0;\\n}\\n  .services-sec {\\n  margin: -87px 0 0 0;\\n}\\n  .services-sec .services-box.s-box-1 {\\n  background: #a8c1cd;\\n}\\n  .services-sec .services-box.s-box-2 {\\n  background: #e3cab0;\\n}\\n  .services-sec .services-box.s-box-3 {\\n  background: #c4a597;\\n}\\n  .services-sec .services-box.s-box-4 {\\n  background: #e8816e;\\n}\\n  .about-sec:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  right: 0;\\n  height: 100%;\\n  background: #030f5e !important;\\n  width: 20%;\\n}\\n  .line::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.2392156863) !important;\\n  width: 100%;\\n}\\n  .line::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgb(213, 200, 186) !important;\\n  width: 64px;\\n}\\n  .line-2::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgba(0, 0, 0, 0.07) !important;\\n  width: 100%;\\n}\\n  .line-2::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgb(213, 200, 186) !important;\\n  width: 64px;\\n}\\n  .city-members-sec:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  height: 100%;\\n  background: var(--orange-50) !important;\\n  width: 20%;\\n}\\n  .city-members-left {\\n  width: 70%;\\n}\\n  .city-members-left .cm-time-box {\\n  flex: 0 0 40%;\\n}\\n  .city-members-left .cm-info {\\n  flex: 0 0 60%;\\n  padding: 0 0 0 50px;\\n}\\n  .city-members-right {\\n  width: 30%;\\n}\\n  .city-members-right .cm-right-box h3 sup {\\n  top: 7px;\\n}\\n  .quick-access-sec {\\n  background: url('pexels-vitor-gusmao.jpg') center right no-repeat;\\n  background-size: 38% auto;\\n}\\n  .quick-access-sec .quick-access-left {\\n  flex: 0 0 67%;\\n  width: 67%;\\n}\\n  .quick-access-sec .quick-access-left .quick-access-list {\\n  grid-template-columns: repeat(auto-fill, minmax(33.333%, 1fr));\\n}\\n  .quick-access-sec .quick-access-right {\\n  flex: 0 0 33%;\\n  width: 33%;\\n}\\n  .news-sec .news-list {\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n}\\n  .news-letter-sec {\\n  background: linear-gradient(180deg, #030f5e 50%, transparent 50%);\\n}\\n  .news-letter-sec .news-letter-box-list .news-letter-img {\\n  height: 640px;\\n}\\n  .what-happning-sec .what-happning-body .what-happning-img {\\n  height: 800px;\\n}\\n  .what-happning-sec .what-happning-body .what-happning-box .wh-img-box {\\n  width: 120px;\\n  height: 120px;\\n}\\n  .what-happning-sec .what-happning-body .what-happning-box .wh-cnt {\\n  max-width: 70%;\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************ */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "menuItem_r1", "Link", "ɵɵsanitizeUrl", "Target", "ɵɵtextInterpolate1", "Title", "ɵɵtextInterpolate", "Sub_Title", "ɵɵelement", "i_r3", "service_r2", "Icon", "url", "Description", "ɵɵsanitizeHtml", "Button_Title", "HomeComponent", "constructor", "primengConfig", "renderer", "route", "CMSservice", "logo", "menuItems", "bannerData", "servicesData", "ngOnInit", "commonContent", "snapshot", "data", "console", "log", "logoComponent", "getDataByComponentName", "body", "length", "Logo", "menuComponent", "<PERSON><PERSON>_<PERSON><PERSON>", "content", "bannerComponent", "servicesComponents", "href", "link", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "document", "head", "ripple", "ngOnDestroy", "getElementById", "remove", "ɵɵdirectiveInject", "i1", "PrimeNGConfig", "Renderer2", "i2", "ActivatedRoute", "i3", "ContentService", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵtemplate", "HomeComponent_li_7_Template", "HomeComponent_div_24_Template", "ɵɵstyleProp", "Image", "Annotation"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home.component.ts", "D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home.component.html"], "sourcesContent": ["import { Component, Renderer2 } from '@angular/core';\r\nimport { PrimeNGConfig } from 'primeng/api';\r\nimport { ContentService } from '../core/services/content-vendor.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrl: './home.component.scss'\r\n})\r\nexport class HomeComponent {\r\n\r\n  content!: any;\r\n  commonContent!: any;\r\n  logo: string = '';\r\n  menuItems: any[] = [];\r\n  bannerData: any = null;\r\n  servicesData: any[] = [];\r\n\r\n  constructor(\r\n    private primengConfig: PrimeNGConfig,\r\n    private renderer: Renderer2,\r\n    private route: ActivatedRoute,\r\n    private CMSservice: ContentService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.commonContent = this.route.snapshot.data['commonContent'];\r\n    console.log('Common Content:', this.commonContent);\r\n\r\n    // Extract logo\r\n    const logoComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.logo\");\r\n    if (logoComponent?.length) {\r\n      this.logo = logoComponent[0].Logo?.url || '';\r\n    }\r\n\r\n    // Extract menu\r\n    const menuComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.menu\");\r\n    if (menuComponent?.length) {\r\n      this.menuItems = menuComponent[0].Menu_Item || [];\r\n    }\r\n\r\n    this.content = this.route.snapshot.data['content'];\r\n    console.log('Home Content:', this.content);\r\n\r\n    // Extract banner\r\n    const bannerComponent = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.banner\");\r\n    if (bannerComponent?.length) {\r\n      this.bannerData = bannerComponent[0];\r\n    }\r\n\r\n    // Extract services\r\n    const servicesComponents = this.CMSservice.getDataByComponentName(this.content.body, \"public-sector.services\");\r\n    if (servicesComponents?.length) {\r\n      this.servicesData = servicesComponents;\r\n    }\r\n\r\n    // Inject theme \r\n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\r\n    const link = this.renderer.createElement('link');\r\n    this.renderer.setAttribute(link, 'id', 'theme-link');\r\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\r\n    this.renderer.setAttribute(link, 'type', 'text/css');\r\n    this.renderer.setAttribute(link, 'href', href);\r\n\r\n    // Append the link tag to the head of the document\r\n    this.renderer.appendChild(document.head, link);\r\n\r\n    this.primengConfig.ripple = true; //enables core ripple functionality\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Find and remove the link tag when the component is destroyed\r\n    const link = document.getElementById('theme-link');\r\n    if (link) {\r\n      link.remove();\r\n    }\r\n  }\r\n\r\n}\r\n", "<!--HEADER SEC-->\r\n<header class=\"main-header fixed top-0 w-full bg-white z-5\">\r\n    <div class=\"header-body relative max-w-1200 w-full mx-auto px-4 flex align-items-center\">\r\n        <div class=\"header-logo relative pr-6 flex align-items-center w-18rem h-8rem secondary-bg-color\">\r\n            <img [src]=\"logo || '/assets/layout/images/snjya-public-services-logo.png'\" class=\"w-full h-fit\" alt=\"Logo\" />\r\n        </div>\r\n        <div class=\"header-menu-sec pl-5 relative flex align-items-center justify-content-between flex-1\">\r\n            <div class=\"menu-list\">\r\n                <ul class=\"p-0 m-0 flex align-items-center gap-5\">\r\n                    <li class=\"flex\" *ngFor=\"let menuItem of menuItems\">\r\n                        <a [href]=\"menuItem.Link || '#'\"\r\n                           [target]=\"menuItem.Target || '_self'\"\r\n                           class=\"flex flex-column gap-1 text-lg font-semibold text-color line-height-1\">\r\n                            {{ menuItem.Title }}\r\n                            <span class=\"text-sm font-normal text-color-secondary\">{{ menuItem.Sub_Title }}</span>\r\n                        </a>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component w-8rem h-3rem justify-content-center gap-2 line-height-2 bg-bluegray-100 text-color font-semibold border-none\">\r\n                <span class=\"material-symbols-rounded text-xl\">login</span> Login\r\n            </button>\r\n        </div>\r\n    </div>\r\n</header>\r\n<!--HEADER SEC-->\r\n\r\n<!--BANNER SEC-->\r\n<section class=\"banner-sec relative\">\r\n    <div class=\"banner-body h-full flex align-items-center\">\r\n        <div class=\"banner-box relative flex-1 p-8\"\r\n             [style.background-image]=\"bannerData?.Image?.url ? 'url(' + bannerData.Image.url + ')' : null\"\r\n             [style.background-size]=\"'cover'\"\r\n             [style.background-position]=\"'center'\"></div>\r\n        <div class=\"banner-box relative flex-1 bg-white p-8 flex flex-column justify-content-center\">\r\n            <h4 class=\"mb-4 text-yellow-400\">{{ bannerData?.Annotation || 'Far away from the every day!' }}</h4>\r\n            <h1 class=\"m-0 mb-5 text-8xl line-height-1 font-extrabold text-color\">{{ bannerData?.Title || 'Community of endless beauty & Calm' }}</h1>\r\n            <p class=\"m-0 text-lg font-medium\">{{ bannerData?.Description || 'Drawn by clean air and mythical light, visitors come to experience traditions, fine art, great cuisine and natural beauty of the landscape.' }}</p>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--BANNER SEC-->\r\n\r\n<!--SERVICES SEC-->\r\n<section class=\"services-sec w-full\">\r\n    <div class=\"services-body relative max-w-1200 w-full mx-auto px-4 flex\">\r\n        <div class=\"services-box p-5 flex-1 flex flex-column gap-2\"\r\n             *ngFor=\"let service of servicesData; let i = index\"\r\n             [ngClass]=\"'s-box-' + (i + 1)\">\r\n            <img [src]=\"service.Icon?.url\" class=\"h-fit w-5rem\" [alt]=\"service.Title\" />\r\n            <h4 class=\"font-bold text-white\">{{ service.Title }}</h4>\r\n            <div [innerHTML]=\"service.Description\"></div>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component h-3rem justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                {{ service.Button_Title }} <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--SERVICES SEC-->\r\n\r\n<!--ABOUT SEC-->\r\n<section class=\"about-sec relative\">\r\n    <div class=\"about-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap\">\r\n        <div class=\"about-box pr-8 w-6 secondary-bg-color\">\r\n            <img src=\"/assets/layout/images/director-img.jpg\" alt=\"\" class=\"w-full\" />\r\n        </div>\r\n        <div class=\"about-box p-8 pr-0 w-6 secondary-bg-color flex flex-column justify-content-start\">\r\n            <h4 class=\"mb-4 text-yellow-400\">Far away from the every day!</h4>\r\n            <h2 class=\"line m-0 mb-6 relative pb-5 text-6xl line-height-2 font-bold text-white\">A Message From the\r\n                Director\r\n            </h2>\r\n            <p class=\"m-0 text-lg text-white flex-1\">Old Town of Riverside is a special place with limitless potential,\r\n                where everyone deserves equal access\r\n                to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in every\r\n                neighborhood. It exists to serve you and be a resource for you, your family, and your community.</p>\r\n\r\n            <button type=\"button\"\r\n                class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--ABOUT SEC-->\r\n\r\n<!--CITY MEMBERS SEC-->\r\n<section class=\"city-members-sec relative\">\r\n    <div class=\"city-members-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap\">\r\n        <div class=\"city-members-left pt-8 pr-7 pb-8 pl-0 relative flex bg-orange-50\">\r\n            <div class=\"cm-time-box\">\r\n                <img src=\"/assets/layout/images/time-screen.png\" alt=\"\" class=\"w-full\" />\r\n            </div>\r\n            <div class=\"cm-info\">\r\n                <h4 class=\"mb-4 text-yellow-400\">Our city in numbers</h4>\r\n                <h2 class=\"line m-0 mb-6 relative pb-5 text-4xl line-height-2 font-bold text-color\">Programs to help\r\n                    launch,\r\n                    grow and expand any business</h2>\r\n                <p class=\"m-0 text-lg text-color flex-1\">Old Town of Riverside is a special place with limitless\r\n                    potential, where everyone deserves equal access\r\n                    to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in\r\n                    every\r\n                    neighborhood. It exists to serve you and be a resource for you, your family, and your community.</p>\r\n            </div>\r\n        </div>\r\n        <div class=\"city-members-right p-7 pr-0 relative flex flex-column gap-8\">\r\n            <div class=\"cm-right-box\">\r\n                <h3 class=\"flex align-items-start text-8xl font-extrabold line-height-1\">41 <sup\r\n                        class=\"relative text-4xl font-bold inline-block\">k</sup></h3>\r\n                <h5 class=\"uppercase\">Highly-educated creative workforce</h5>\r\n                <p class=\"text-color-secondary\">A vibrant destination where innovations in design and technology are\r\n                    born. A place for you.</p>\r\n            </div>\r\n            <div class=\"cm-right-box\">\r\n                <h3 class=\" flex align-items-start text-8xl font-extrabold line-height-1\">8 <sup\r\n                        class=\"relative text-4xl font-bold inline-block\">th</sup></h3>\r\n                <h5 class=\"uppercase\">Highest Per Capita Income in Virginia</h5>\r\n                <p class=\"text-color-secondary\">A vibrant destination where innovations in design and technology are\r\n                    born. A place for you.</p>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--CITY MEMBERS SEC-->\r\n\r\n<!--QUICK ACCESS SEC-->\r\n<section class=\"quick-access-sec relative\">\r\n    <div class=\"quick-access-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap\">\r\n        <div class=\"quick-access-left relative flex flex-column pt-8 pb-8\">\r\n            <h2 class=\"mb-4 flex align-items-start text-6xl font-extrabold line-height-1\">Quick Access</h2>\r\n            <h4 class=\"line-2 inline-flex w-fit m-0 mb-6 relative pb-5 text-xl line-height-2 font-bold text-yellow-400\">\r\n                What can the City help you with today?</h4>\r\n\r\n            <div class=\"quick-access-list d-grid w-full\">\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-bottom-none border-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/001-toll.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Road construction</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/006-virus.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">COVID-19 updates</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/003-trash-can.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Garbage pickup</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div class=\"quick-access-box p-4 w-full flex flex-column border-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/004-parking.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Parking</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-bottom-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/005-lecture.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Council meetings</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-bottom-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/008-basketball.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Recreation and sport programs</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"quick-access-right\"></div>\r\n    </div>\r\n</section>\r\n<!--QUICK ACCESS SEC-->\r\n\r\n<!--NEWS SEC-->\r\n<section class=\"news-sec relative pt-7 pb-8 secondary-bg-color\">\r\n    <div class=\"news-body relative max-w-1200 w-full mx-auto px-4\">\r\n        <div class=\"news-title\">\r\n            <h5 class=\"line m-0 mb-5 pb-5 relative inline-flex w-fit text-yellow-400\">Find out what’s going on & stay up\r\n                to date.</h5>\r\n            <h2 class=\"m-0 mb-6 relative text-6xl line-height-2 font-bold text-white\">Latest News</h2>\r\n        </div>\r\n        <div class=\"news-list d-grid w-full gap-6\">\r\n            <div class=\"news-box w-full flex flex-column\">\r\n                <div class=\"new-img flex align-items-center justify-content-center overflow-hidden\">\r\n                    <img src=\"/assets/layout/images/new-img-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                </div>\r\n                <h3 class=\"text-white text-xl flex-1\">Proposed Downtown District Ordinance</h3>\r\n                <div class=\"mb-3 flex gap-3 align-items-center\">\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">calendar_month</span> September 16, 2018\r\n                    </div>\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">visibility</span> 240\r\n                    </div>\r\n                </div>\r\n                <p class=\"mb-5 text-sm text-white\">The goal of the proposed ordinance is to create safe venues for\r\n                    customers\r\n                    and to discourage illegal activities. The governing officials of...</p>\r\n\r\n                <button type=\"button\"\r\n                    class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                    Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n                </button>\r\n            </div>\r\n            <div class=\"news-box w-full flex flex-column\">\r\n                <div class=\"new-img flex align-items-center justify-content-center overflow-hidden\">\r\n                    <img src=\"/assets/layout/images/new-img-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                </div>\r\n                <h3 class=\"text-white text-xl flex-1\">Annual Water Quality Report (Gallery Post)</h3>\r\n                <div class=\"mb-3 flex gap-3 align-items-center\">\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">calendar_month</span> September 16, 2018\r\n                    </div>\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">visibility</span> 240\r\n                    </div>\r\n                </div>\r\n                <p class=\"mb-5 text-sm text-white\">The goal of the proposed ordinance is to create safe venues for\r\n                    customers\r\n                    and to discourage illegal activities. The governing officials of...</p>\r\n\r\n                <button type=\"button\"\r\n                    class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                    Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n                </button>\r\n            </div>\r\n            <div class=\"news-box w-full flex flex-column\">\r\n                <div class=\"new-img flex align-items-center justify-content-center overflow-hidden\">\r\n                    <img src=\"/assets/layout/images/new-img-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                </div>\r\n                <h3 class=\"text-white text-xl flex-1\">Waste Industries Garbage Pick Up: Embeds</h3>\r\n                <div class=\"mb-3 flex gap-3 align-items-center\">\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">calendar_month</span> September 16, 2018\r\n                    </div>\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">visibility</span> 240\r\n                    </div>\r\n                </div>\r\n                <p class=\"mb-5 text-sm text-white\">The goal of the proposed ordinance is to create safe venues for\r\n                    customers\r\n                    and to discourage illegal activities. The governing officials of...</p>\r\n\r\n                <button type=\"button\"\r\n                    class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                    Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--NEWS SEC-->\r\n\r\n<!--NEWS LETTER SEC-->\r\n<section class=\"news-letter-sec relative\">\r\n    <div class=\"news-letter-body relative max-w-1200 w-full mx-auto px-4\">\r\n        <div class=\"news-letter-box-list flex bg-white shadow-1\">\r\n            <div class=\"news-letter-box w-6 p-7 flex flex-column justify-content-center\">\r\n                <h2\r\n                    class=\"line-2 relative mb-5 pb-5 inline-flex w-fit align-items-start text-6xl font-extrabold line-height-1\">\r\n                    Sign up for News & Alerts</h2>\r\n                <p>Stay connected to the City of Riverside by signing up to receive official news and alerts by email!\r\n                </p>\r\n                <div class=\"news-letter-form mt-5 relative flex align-items-center\">\r\n                    <input type=\"text\" pInputText class=\"h-3-3rem border-noround flex-1\"\r\n                        placeholder=\"Your email address\" />\r\n                    <button type=\"button\"\r\n                        class=\"px-6 p-element p-ripple border-noround p-button p-component h-3-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                        Sign Up\r\n                    </button>\r\n                </div>\r\n            </div>\r\n            <div class=\"news-letter-img w-6 flex align-items-center justify-content-center overflow-hidden\">\r\n                <img src=\"/assets/layout/images/news-letter-img.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--NEWS LETTER SEC-->\r\n\r\n<!--WHAT HAPPNENING SEC-->\r\n<section class=\"what-happning-sec mt-8 relative px-4\">\r\n    <div class=\"what-happning-body relative w-full mx-auto flex bg-orange-50\">\r\n        <div class=\"what-happning-img w-6 flex align-items-center justify-content-center overflow-hidden\">\r\n            <img src=\"/assets/layout/images/whats-happnening-mg.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n        </div>\r\n        <div class=\"what-happning-right w-6 p-8\">\r\n            <div class=\"what-happning-title\">\r\n                <h5 class=\"line-2 m-0 mb-5 pb-5 relative inline-flex w-fit text-yellow-400\">Join the fun in our city!\r\n                </h5>\r\n                <h2 class=\"m-0 mb-6 relative text-6xl line-height-2 font-bold text-color\">What's Happening</h2>\r\n            </div>\r\n            <div class=\"what-happning-list\">\r\n                <div\r\n                    class=\"what-happning-box mb-4 pb-4 flex align-items-center gap-5 border-none border-bottom-1 border-solid surface-border\">\r\n                    <div class=\"wh-img-box flex align-items-center justify-content-center overflow-hidden\">\r\n                        <img src=\"/assets/layout/images/arches-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                    </div>\r\n                    <div class=\"wh-cnt-sec flex-1 flex align-items-center gap-6\">\r\n                        <div class=\"wh-cnt flex flex-column\">\r\n                            <h3 class=\"text-xl font-bold\">Heritage 10th Anniversary</h3>\r\n                            <div class=\"flex align-items-center gap-3 flex-wrap\">\r\n                                <div class=\"flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100\">\r\n                                    <span>June 9, 2027</span> - <span>June 18, 2027</span>\r\n                                </div>\r\n                                <div class=\"flex align-items-center gap-2\">All Day at <b>Baker Hall</b></div>\r\n                            </div>\r\n                        </div>\r\n                        <button type=\"button\"\r\n                            class=\"px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold\">\r\n                            Find out more\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div\r\n                    class=\"what-happning-box mb-4 pb-4 flex align-items-center gap-5 border-none border-bottom-1 border-solid surface-border\">\r\n                    <div class=\"wh-img-box flex align-items-center justify-content-center overflow-hidden\">\r\n                        <img src=\"/assets/layout/images/arches-2.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                    </div>\r\n                    <div class=\"wh-cnt-sec flex-1 flex align-items-center gap-6\">\r\n                        <div class=\"wh-cnt flex flex-column\">\r\n                            <h3 class=\"text-xl font-bold\">Along Pines Run</h3>\r\n                            <div class=\"flex align-items-center gap-3 flex-wrap\">\r\n                                <div class=\"flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100\">\r\n                                    <span>July 22, 2027</span>\r\n                                </div>\r\n                                <div class=\"flex align-items-center gap-2\">12:00 am at <b>Boulder City Council</b></div>\r\n                            </div>\r\n                        </div>\r\n                        <button type=\"button\"\r\n                            class=\"px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold\">\r\n                            Find out more\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div class=\"what-happning-box flex align-items-center gap-5\">\r\n                    <div class=\"wh-img-box flex align-items-center justify-content-center overflow-hidden\">\r\n                        <img src=\"/assets/layout/images/arches-3.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                    </div>\r\n                    <div class=\"wh-cnt-sec flex-1 flex align-items-center gap-6\">\r\n                        <div class=\"wh-cnt flex flex-column\">\r\n                            <h3 class=\"text-xl font-bold\">Touch A Truck</h3>\r\n                            <div class=\"flex align-items-center gap-3 flex-wrap\">\r\n                                <div class=\"flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100\">\r\n                                    <span>June 9, 2027</span> - <span>June 18, 2027</span>\r\n                                </div>\r\n                                <div class=\"flex align-items-center gap-2\">9:00 am - 5:00 pm at <b>Baker Hall</b></div>\r\n                            </div>\r\n                        </div>\r\n                        <button type=\"button\"\r\n                            class=\"px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold\">\r\n                            Find out more\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--WHAT HAPPNENING SEC-->\r\n\r\n<!--FOOTER SEC-->\r\n<section class=\"footer-sec relative secondary-bg-color\">\r\n    <div class=\"footer-body relative max-w-1200 w-full mx-auto px-4\">\r\n        <div class=\"top-footer py-7 grid mt-0\">\r\n            <div class=\"col-12 lg:col-5 md:col-5\">\r\n                <h3 class=\"line flex mb-5 pb-3 relative text-white text-xl\">Riverside City Hall</h3>\r\n                <ul class=\"p-0 m-0 flex flex-column gap-4\">\r\n                    <li class=\"flex w-full\">\r\n                        <a href=\"#\" class=\"flex w-full text-white\">8353 Sierra Avenue • Riverside, CA 91335</a>\r\n                    </li>\r\n                    <li class=\"flex w-full\">\r\n                        <a href=\"#\" class=\"flex w-full text-white\">Phone: (*************</a>\r\n                    </li>\r\n                    <li class=\"flex w-full\">\r\n                        <a class=\"flex w-full text-white\">Monday - Thursday, 8:00 am - 6:00 pm</a>\r\n                    </li>\r\n                    <li class=\"flex w-full\">\r\n                        <a href=\"\" class=\"flex w-full text-white\">Email : info&#64;asardigital.com</a>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n            <div class=\"col-12 lg:col-7 md:col-7 py-0\">\r\n                <div class=\"grid mt-0\">\r\n                    <div class=\"col-12 lg:col-4 md:col-4\">\r\n                        <h3 class=\"line flex mb-5 pb-3 relative text-white text-xl\">Services</h3>\r\n                        <ul class=\"p-0 m-0 flex flex-column gap-4\">\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"#\" class=\"flex w-full text-white\">Driver & ID Services</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"#\" class=\"flex w-full text-white\">Vehicle & Plate Services</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a class=\"flex w-full text-white\">Business Services</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"\" class=\"flex w-full text-white\">Senior Services</a>\r\n                            </li>\r\n                        </ul>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-4 md:col-4\">\r\n                        <h3 class=\"line flex mb-5 pb-3 relative text-white text-xl\">Useful Links</h3>\r\n                        <ul class=\"p-0 m-0 flex flex-column gap-4\">\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"#\" class=\"flex w-full text-white\">Frequently Asked Questions</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"#\" class=\"flex w-full text-white\">Latest News</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a class=\"flex w-full text-white\">Community</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"\" class=\"flex w-full text-white\">Help Center</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"\" class=\"flex w-full text-white\">Careers</a>\r\n                            </li>\r\n                        </ul>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"middle-footer py-5 w-full flex align-items-center justify-content-center bg-blue-100\">\r\n\r\n        </div>\r\n        <div class=\"bottom-footer py-5 w-full flex flex-column align-items-center justify-content-center\">\r\n            <ul class=\"m-0 mb-3 p-0 flex align-items-center justify-content-center gap-3 list-none\">\r\n                <li><a href=\"#\" class=\"inline-flex w-fit text-white\">Term & Conditions</a></li>\r\n                <li><a href=\"#\" class=\"inline-flex w-fit text-white\">Privacy Policy</a></li>\r\n            </ul>\r\n            <p class=\"m-0 text-white\">© 2025 SNJYA. All rights reserved.</p>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--FOOTER SEC-->"], "mappings": ";;;;;;;;ICUwBA,EADJ,CAAAC,cAAA,cAAoD,aAGiC;IAC7ED,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,gBAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAEvFF,EAFuF,CAAAG,YAAA,EAAO,EACtF,EACH;;;;IANEH,EAAA,CAAAI,SAAA,EAA6B;IAC7BJ,EADA,CAAAK,UAAA,SAAAC,WAAA,CAAAC,IAAA,SAAAP,EAAA,CAAAQ,aAAA,CAA6B,WAAAF,WAAA,CAAAG,MAAA,YACQ;IAEpCT,EAAA,CAAAI,SAAA,EACA;IADAJ,EAAA,CAAAU,kBAAA,MAAAJ,WAAA,CAAAK,KAAA,MACA;IAAuDX,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAY,iBAAA,CAAAN,WAAA,CAAAO,SAAA,CAAwB;;;;;IAiCnGb,EAAA,CAAAC,cAAA,eAEoC;IAChCD,EAAA,CAAAc,SAAA,eAA4E;IAC5Ed,EAAA,CAAAC,cAAA,cAAiC;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzDH,EAAA,CAAAc,SAAA,eAA6C;IAC7Cd,EAAA,CAAAC,cAAA,kBACqK;IACjKD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAC,cAAA,eAAuC;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAEzFF,EAFyF,CAAAG,YAAA,EAAO,EACnF,EACP;;;;;IARDH,EAAA,CAAAK,UAAA,wBAAAU,IAAA,MAA8B;IAC1Bf,EAAA,CAAAI,SAAA,EAAyB;IAAsBJ,EAA/C,CAAAK,UAAA,QAAAW,UAAA,CAAAC,IAAA,kBAAAD,UAAA,CAAAC,IAAA,CAAAC,GAAA,EAAAlB,EAAA,CAAAQ,aAAA,CAAyB,QAAAQ,UAAA,CAAAL,KAAA,CAA2C;IACxCX,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAY,iBAAA,CAAAI,UAAA,CAAAL,KAAA,CAAmB;IAC/CX,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAK,UAAA,cAAAW,UAAA,CAAAG,WAAA,EAAAnB,EAAA,CAAAoB,cAAA,CAAiC;IAGlCpB,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAU,kBAAA,MAAAM,UAAA,CAAAK,YAAA,MAA2B;;;AD7C3C,OAAM,MAAOC,aAAa;EASxBC,YACUC,aAA4B,EAC5BC,QAAmB,EACnBC,KAAqB,EACrBC,UAA0B;IAH1B,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IATpB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,SAAS,GAAU,EAAE;IACrB,KAAAC,UAAU,GAAQ,IAAI;IACtB,KAAAC,YAAY,GAAU,EAAE;EAOpB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG,IAAI,CAACP,KAAK,CAACQ,QAAQ,CAACC,IAAI,CAAC,eAAe,CAAC;IAC9DC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACJ,aAAa,CAAC;IAElD;IACA,MAAMK,aAAa,GAAG,IAAI,CAACX,UAAU,CAACY,sBAAsB,CAAC,IAAI,CAACN,aAAa,CAACO,IAAI,EAAE,oBAAoB,CAAC;IAC3G,IAAIF,aAAa,EAAEG,MAAM,EAAE;MACzB,IAAI,CAACb,IAAI,GAAGU,aAAa,CAAC,CAAC,CAAC,CAACI,IAAI,EAAExB,GAAG,IAAI,EAAE;IAC9C;IAEA;IACA,MAAMyB,aAAa,GAAG,IAAI,CAAChB,UAAU,CAACY,sBAAsB,CAAC,IAAI,CAACN,aAAa,CAACO,IAAI,EAAE,oBAAoB,CAAC;IAC3G,IAAIG,aAAa,EAAEF,MAAM,EAAE;MACzB,IAAI,CAACZ,SAAS,GAAGc,aAAa,CAAC,CAAC,CAAC,CAACC,SAAS,IAAI,EAAE;IACnD;IAEA,IAAI,CAACC,OAAO,GAAG,IAAI,CAACnB,KAAK,CAACQ,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;IAClDC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACQ,OAAO,CAAC;IAE1C;IACA,MAAMC,eAAe,GAAG,IAAI,CAACnB,UAAU,CAACY,sBAAsB,CAAC,IAAI,CAACM,OAAO,CAACL,IAAI,EAAE,sBAAsB,CAAC;IACzG,IAAIM,eAAe,EAAEL,MAAM,EAAE;MAC3B,IAAI,CAACX,UAAU,GAAGgB,eAAe,CAAC,CAAC,CAAC;IACtC;IAEA;IACA,MAAMC,kBAAkB,GAAG,IAAI,CAACpB,UAAU,CAACY,sBAAsB,CAAC,IAAI,CAACM,OAAO,CAACL,IAAI,EAAE,wBAAwB,CAAC;IAC9G,IAAIO,kBAAkB,EAAEN,MAAM,EAAE;MAC9B,IAAI,CAACV,YAAY,GAAGgB,kBAAkB;IACxC;IAEA;IACA,MAAMC,IAAI,GAAG,wDAAwD;IACrE,MAAMC,IAAI,GAAG,IAAI,CAACxB,QAAQ,CAACyB,aAAa,CAAC,MAAM,CAAC;IAChD,IAAI,CAACzB,QAAQ,CAAC0B,YAAY,CAACF,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC;IACpD,IAAI,CAACxB,QAAQ,CAAC0B,YAAY,CAACF,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC;IACrD,IAAI,CAACxB,QAAQ,CAAC0B,YAAY,CAACF,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC;IACpD,IAAI,CAACxB,QAAQ,CAAC0B,YAAY,CAACF,IAAI,EAAE,MAAM,EAAED,IAAI,CAAC;IAE9C;IACA,IAAI,CAACvB,QAAQ,CAAC2B,WAAW,CAACC,QAAQ,CAACC,IAAI,EAAEL,IAAI,CAAC;IAE9C,IAAI,CAACzB,aAAa,CAAC+B,MAAM,GAAG,IAAI,CAAC,CAAC;EACpC;EAEAC,WAAWA,CAAA;IACT;IACA,MAAMP,IAAI,GAAGI,QAAQ,CAACI,cAAc,CAAC,YAAY,CAAC;IAClD,IAAIR,IAAI,EAAE;MACRA,IAAI,CAACS,MAAM,EAAE;IACf;EACF;;;uBAnEWpC,aAAa,EAAAtB,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA7D,EAAA,CAAA2D,iBAAA,CAAA3D,EAAA,CAAA8D,SAAA,GAAA9D,EAAA,CAAA2D,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAhE,EAAA,CAAA2D,iBAAA,CAAAM,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAb5C,aAAa;MAAA6C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPlBzE,EAFR,CAAAC,cAAA,gBAA4D,aACiC,aACY;UAC7FD,EAAA,CAAAc,SAAA,aAA8G;UAClHd,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,aAAkG,aACvE,YAC+B;UAC9CD,EAAA,CAAA2E,UAAA,IAAAC,2BAAA,gBAAoD;UAS5D5E,EADI,CAAAG,YAAA,EAAK,EACH;UAGFH,EAFJ,CAAAC,cAAA,gBACmL,cAChI;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,eAChE;UAGZF,EAHY,CAAAG,YAAA,EAAS,EACP,EACJ,EACD;UAKLH,EADJ,CAAAC,cAAA,mBAAqC,eACuB;UACpDD,EAAA,CAAAc,SAAA,eAGkD;UAE9Cd,EADJ,CAAAC,cAAA,eAA6F,cACxD;UAAAD,EAAA,CAAAE,MAAA,IAA8D;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpGH,EAAA,CAAAC,cAAA,cAAsE;UAAAD,EAAA,CAAAE,MAAA,IAA+D;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1IH,EAAA,CAAAC,cAAA,aAAmC;UAAAD,EAAA,CAAAE,MAAA,IAA8K;UAG7NF,EAH6N,CAAAG,YAAA,EAAI,EACnN,EACJ,EACA;UAKNH,EADJ,CAAAC,cAAA,mBAAqC,eACuC;UACpED,EAAA,CAAA2E,UAAA,KAAAE,6BAAA,kBAEoC;UAU5C7E,EADI,CAAAG,YAAA,EAAM,EACA;UAMFH,EAFR,CAAAC,cAAA,mBAAoC,eAC+C,eACxB;UAC/CD,EAAA,CAAAc,SAAA,eAA0E;UAC9Ed,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,eAA8F,cACzD;UAAAD,EAAA,CAAAE,MAAA,oCAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClEH,EAAA,CAAAC,cAAA,cAAoF;UAAAD,EAAA,CAAAE,MAAA,oCAEpF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAE,MAAA,sTAG2D;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAExGH,EAAA,CAAAC,cAAA,kBACgL;UAC5KD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAC,cAAA,gBAAuC;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAIjFF,EAJiF,CAAAG,YAAA,EAAO,EACnE,EACP,EACJ,EACA;UAOEH,EAHZ,CAAAC,cAAA,mBAA2C,eAC+C,eACJ,eACjD;UACrBD,EAAA,CAAAc,SAAA,eAAyE;UAC7Ed,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,eAAqB,cACgB;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzDH,EAAA,CAAAC,cAAA,cAAoF;UAAAD,EAAA,CAAAE,MAAA,6DAEpD;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrCH,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAE,MAAA,sTAI2D;UAE5GF,EAF4G,CAAAG,YAAA,EAAI,EACtG,EACJ;UAGEH,EAFR,CAAAC,cAAA,eAAyE,eAC3C,cACmD;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAC,cAAA,eACnB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAMF,EAAN,CAAAG,YAAA,EAAM,EAAK;UACrEH,EAAA,CAAAC,cAAA,cAAsB;UAAAD,EAAA,CAAAE,MAAA,0CAAkC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7DH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAE,MAAA,mGACN;UAC9BF,EAD8B,CAAAG,YAAA,EAAI,EAC5B;UAEFH,EADJ,CAAAC,cAAA,eAA0B,cACoD;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAC,cAAA,eACnB;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAMF,EAAN,CAAAG,YAAA,EAAM,EAAK;UACtEH,EAAA,CAAAC,cAAA,cAAsB;UAAAD,EAAA,CAAAE,MAAA,6CAAqC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChEH,EAAA,CAAAC,cAAA,aAAgC;UAAAD,EAAA,CAAAE,MAAA,mGACN;UAI1CF,EAJ0C,CAAAG,YAAA,EAAI,EAC5B,EACJ,EACJ,EACA;UAOEH,EAHZ,CAAAC,cAAA,mBAA2C,eAC+C,eACf,cACe;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/FH,EAAA,CAAAC,cAAA,cAA4G;UACxGD,EAAA,CAAAE,MAAA,+CAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG3CH,EADJ,CAAAC,cAAA,eAA6C,eAEwE;UAC7GD,EAAA,CAAAc,SAAA,eAA2E;UAC3Ed,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5DH,EAAA,CAAAC,cAAA,aAAmB;UAAAD,EAAA,CAAAE,MAAA,yGACI;UAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;UACNH,EAAA,CAAAC,cAAA,eAC6H;UACzHD,EAAA,CAAAc,SAAA,eAA4E;UAC5Ed,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3DH,EAAA,CAAAC,cAAA,aAAmB;UAAAD,EAAA,CAAAE,MAAA,yGACI;UAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;UACNH,EAAA,CAAAC,cAAA,eAC6H;UACzHD,EAAA,CAAAc,SAAA,eAAgF;UAChFd,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzDH,EAAA,CAAAC,cAAA,aAAmB;UAAAD,EAAA,CAAAE,MAAA,yGACI;UAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;UACNH,EAAA,CAAAC,cAAA,eAA+F;UAC3FD,EAAA,CAAAc,SAAA,eAA8E;UAC9Ed,EAAA,CAAAC,cAAA,cAAsC;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,cAAmB;UAAAD,EAAA,CAAAE,MAAA,0GACI;UAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;UACNH,EAAA,CAAAC,cAAA,gBAC6I;UACzID,EAAA,CAAAc,SAAA,gBAA8E;UAC9Ed,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,yBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3DH,EAAA,CAAAC,cAAA,cAAmB;UAAAD,EAAA,CAAAE,MAAA,0GACI;UAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;UACNH,EAAA,CAAAC,cAAA,gBAC6I;UACzID,EAAA,CAAAc,SAAA,gBAAiF;UACjFd,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,sCAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxEH,EAAA,CAAAC,cAAA,cAAmB;UAAAD,EAAA,CAAAE,MAAA,0GACI;UAGnCF,EAHmC,CAAAG,YAAA,EAAI,EACzB,EACJ,EACJ;UACNH,EAAA,CAAAc,SAAA,gBAAsC;UAE9Cd,EADI,CAAAG,YAAA,EAAM,EACA;UAOEH,EAHZ,CAAAC,cAAA,oBAAgE,gBACG,gBACnC,eACsD;UAAAD,EAAA,CAAAE,MAAA,yDAC9D;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAC,cAAA,eAA0E;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UACzFF,EADyF,CAAAG,YAAA,EAAK,EACxF;UAGEH,EAFR,CAAAC,cAAA,gBAA2C,gBACO,gBAC0C;UAChFD,EAAA,CAAAc,SAAA,gBAA6F;UACjGd,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,6CAAoC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGvEH,EAFR,CAAAC,cAAA,gBAAgD,gBACmC,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,6BACzE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,gBAA+E,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,cACrE;UACJF,EADI,CAAAG,YAAA,EAAM,EACJ;UACNH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,sJAEoC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE3EH,EAAA,CAAAC,cAAA,mBACqL;UACjLD,EAAA,CAAAE,MAAA,qBAAW;UAAAF,EAAA,CAAAC,cAAA,iBAAuC;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAEzEF,EAFyE,CAAAG,YAAA,EAAO,EACnE,EACP;UAEFH,EADJ,CAAAC,cAAA,gBAA8C,gBAC0C;UAChFD,EAAA,CAAAc,SAAA,gBAA6F;UACjGd,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,mDAA0C;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG7EH,EAFR,CAAAC,cAAA,gBAAgD,gBACmC,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,6BACzE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,gBAA+E,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,cACrE;UACJF,EADI,CAAAG,YAAA,EAAM,EACJ;UACNH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,sJAEoC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE3EH,EAAA,CAAAC,cAAA,mBACqL;UACjLD,EAAA,CAAAE,MAAA,qBAAW;UAAAF,EAAA,CAAAC,cAAA,iBAAuC;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAEzEF,EAFyE,CAAAG,YAAA,EAAO,EACnE,EACP;UAEFH,EADJ,CAAAC,cAAA,gBAA8C,gBAC0C;UAChFD,EAAA,CAAAc,SAAA,gBAA6F;UACjGd,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAsC;UAAAD,EAAA,CAAAE,MAAA,iDAAwC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG3EH,EAFR,CAAAC,cAAA,gBAAgD,gBACmC,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,6BACzE;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEFH,EADJ,CAAAC,cAAA,gBAA+E,gBAC5B;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,cACrE;UACJF,EADI,CAAAG,YAAA,EAAM,EACJ;UACNH,EAAA,CAAAC,cAAA,cAAmC;UAAAD,EAAA,CAAAE,MAAA,sJAEoC;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE3EH,EAAA,CAAAC,cAAA,mBACqL;UACjLD,EAAA,CAAAE,MAAA,qBAAW;UAAAF,EAAA,CAAAC,cAAA,iBAAuC;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAKrFF,EALqF,CAAAG,YAAA,EAAO,EACnE,EACP,EACJ,EACJ,EACA;UAQMH,EAJhB,CAAAC,cAAA,oBAA0C,gBACgC,gBACT,gBACwB,eAEuC;UAC5GD,EAAA,CAAAE,MAAA,mCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClCH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,6GACH;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,gBAAoE;UAChED,EAAA,CAAAc,SAAA,kBACuC;UACvCd,EAAA,CAAAC,cAAA,mBACqL;UACjLD,EAAA,CAAAE,MAAA,kBACJ;UAERF,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;UACNH,EAAA,CAAAC,cAAA,gBAAgG;UAC5FD,EAAA,CAAAc,SAAA,gBAAmG;UAInHd,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACA;UAMFH,EAFR,CAAAC,cAAA,oBAAsD,gBACwB,gBAC4B;UAC9FD,EAAA,CAAAc,SAAA,gBAAuG;UAC3Gd,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,gBAAyC,gBACJ,eAC+C;UAAAD,EAAA,CAAAE,MAAA,mCAC5E;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,eAA0E;UAAAD,EAAA,CAAAE,MAAA,yBAAgB;UAC9FF,EAD8F,CAAAG,YAAA,EAAK,EAC7F;UAIEH,EAHR,CAAAC,cAAA,gBAAgC,gBAEkG,gBACnC;UACnFD,EAAA,CAAAc,SAAA,gBAA4F;UAChGd,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,gBAA6D,gBACpB,gBACH;UAAAD,EAAA,CAAAE,MAAA,kCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGpDH,EAFR,CAAAC,cAAA,iBAAqD,iBACuB,aAC9D;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,YAAE;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UACnDF,EADmD,CAAAG,YAAA,EAAO,EACpD;UACNH,EAAA,CAAAC,cAAA,iBAA2C;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAE3EF,EAF2E,CAAAG,YAAA,EAAI,EAAM,EAC3E,EACJ;UACNH,EAAA,CAAAC,cAAA,oBAC6K;UACzKD,EAAA,CAAAE,MAAA,wBACJ;UAERF,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;UAGFH,EAFJ,CAAAC,cAAA,gBAC8H,gBACnC;UACnFD,EAAA,CAAAc,SAAA,iBAA4F;UAChGd,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,gBAA6D,gBACpB,gBACH;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG1CH,EAFR,CAAAC,cAAA,iBAAqD,iBACuB,aAC9D;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UACvBF,EADuB,CAAAG,YAAA,EAAO,EACxB;UACNH,EAAA,CAAAC,cAAA,iBAA2C;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,6BAAoB;UAEtFF,EAFsF,CAAAG,YAAA,EAAI,EAAM,EACtF,EACJ;UACNH,EAAA,CAAAC,cAAA,oBAC6K;UACzKD,EAAA,CAAAE,MAAA,wBACJ;UAERF,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;UAEFH,EADJ,CAAAC,cAAA,iBAA6D,gBAC8B;UACnFD,EAAA,CAAAc,SAAA,iBAA4F;UAChGd,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,gBAA6D,gBACpB,gBACH;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGxCH,EAFR,CAAAC,cAAA,iBAAqD,iBACuB,aAC9D;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,YAAE;UAAAF,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UACnDF,EADmD,CAAAG,YAAA,EAAO,EACpD;UACNH,EAAA,CAAAC,cAAA,iBAA2C;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAErFF,EAFqF,CAAAG,YAAA,EAAI,EAAM,EACrF,EACJ;UACNH,EAAA,CAAAC,cAAA,oBAC6K;UACzKD,EAAA,CAAAE,MAAA,wBACJ;UAMxBF,EANwB,CAAAG,YAAA,EAAS,EACP,EACJ,EACJ,EACJ,EACJ,EACA;UAQMH,EAJhB,CAAAC,cAAA,qBAAwD,iBACa,iBACtB,iBACG,gBAC0B;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAG5EH,EAFR,CAAAC,cAAA,gBAA2C,gBACf,eACuB;UAAAD,EAAA,CAAAE,MAAA,sDAAwC;UACvFF,EADuF,CAAAG,YAAA,EAAI,EACtF;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACuB;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UACpEF,EADoE,CAAAG,YAAA,EAAI,EACnE;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACc;UAAAD,EAAA,CAAAE,MAAA,6CAAoC;UAC1EF,EAD0E,CAAAG,YAAA,EAAI,EACzE;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACsB;UAAAD,EAAA,CAAAE,MAAA,qCAAgC;UAGtFF,EAHsF,CAAAG,YAAA,EAAI,EAC7E,EACJ,EACH;UAIMH,EAHZ,CAAAC,cAAA,iBAA2C,iBAChB,iBACmB,gBAC0B;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGjEH,EAFR,CAAAC,cAAA,gBAA2C,gBACf,eACuB;UAAAD,EAAA,CAAAE,MAAA,6BAAoB;UACnEF,EADmE,CAAAG,YAAA,EAAI,EAClE;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACuB;UAAAD,EAAA,CAAAE,MAAA,iCAAwB;UACvEF,EADuE,CAAAG,YAAA,EAAI,EACtE;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACc;UAAAD,EAAA,CAAAE,MAAA,0BAAiB;UACvDF,EADuD,CAAAG,YAAA,EAAI,EACtD;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACsB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAGrEF,EAHqE,CAAAG,YAAA,EAAI,EAC5D,EACJ,EACH;UAEFH,EADJ,CAAAC,cAAA,iBAAsC,gBAC0B;UAAAD,EAAA,CAAAE,MAAA,qBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGrEH,EAFR,CAAAC,cAAA,gBAA2C,gBACf,eACuB;UAAAD,EAAA,CAAAE,MAAA,mCAA0B;UACzEF,EADyE,CAAAG,YAAA,EAAI,EACxE;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACuB;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAC1DF,EAD0D,CAAAG,YAAA,EAAI,EACzD;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACc;UAAAD,EAAA,CAAAE,MAAA,kBAAS;UAC/CF,EAD+C,CAAAG,YAAA,EAAI,EAC9C;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACsB;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UACzDF,EADyD,CAAAG,YAAA,EAAI,EACxD;UAEDH,EADJ,CAAAC,cAAA,gBAAwB,eACsB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAMzEF,EANyE,CAAAG,YAAA,EAAI,EACpD,EACJ,EACH,EACJ,EACJ,EACJ;UACNH,EAAA,CAAAc,SAAA,iBAEM;UAGMd,EAFZ,CAAAC,cAAA,iBAAkG,gBACN,WAChF,eAAiD;UAAAD,EAAA,CAAAE,MAAA,0BAAiB;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAK;UAC3EH,EAAJ,CAAAC,cAAA,WAAI,eAAiD;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UACvEF,EADuE,CAAAG,YAAA,EAAI,EAAK,EAC3E;UACLH,EAAA,CAAAC,cAAA,eAA0B;UAAAD,EAAA,CAAAE,MAAA,gDAAkC;UAGxEF,EAHwE,CAAAG,YAAA,EAAI,EAC9D,EACJ,EACA;;;UAzbOH,EAAA,CAAAI,SAAA,GAAsE;UAAtEJ,EAAA,CAAAK,UAAA,QAAAqE,GAAA,CAAA9C,IAAA,4DAAA5B,EAAA,CAAAQ,aAAA,CAAsE;UAK7BR,EAAA,CAAAI,SAAA,GAAY;UAAZJ,EAAA,CAAAK,UAAA,YAAAqE,GAAA,CAAA7C,SAAA,CAAY;UAuBzD7B,EAAA,CAAAI,SAAA,GAA8F;UAE9FJ,EAFA,CAAA8E,WAAA,sBAAAJ,GAAA,CAAA5C,UAAA,kBAAA4C,GAAA,CAAA5C,UAAA,CAAAiD,KAAA,kBAAAL,GAAA,CAAA5C,UAAA,CAAAiD,KAAA,CAAA7D,GAAA,aAAAwD,GAAA,CAAA5C,UAAA,CAAAiD,KAAA,CAAA7D,GAAA,cAA8F,4BAC7D,iCACK;UAENlB,EAAA,CAAAI,SAAA,GAA8D;UAA9DJ,EAAA,CAAAY,iBAAA,EAAA8D,GAAA,CAAA5C,UAAA,kBAAA4C,GAAA,CAAA5C,UAAA,CAAAkD,UAAA,oCAA8D;UACzBhF,EAAA,CAAAI,SAAA,GAA+D;UAA/DJ,EAAA,CAAAY,iBAAA,EAAA8D,GAAA,CAAA5C,UAAA,kBAAA4C,GAAA,CAAA5C,UAAA,CAAAnB,KAAA,0CAA+D;UAClGX,EAAA,CAAAI,SAAA,GAA8K;UAA9KJ,EAAA,CAAAY,iBAAA,EAAA8D,GAAA,CAAA5C,UAAA,kBAAA4C,GAAA,CAAA5C,UAAA,CAAAX,WAAA,mJAA8K;UAU5LnB,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAK,UAAA,YAAAqE,GAAA,CAAA3C,YAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}