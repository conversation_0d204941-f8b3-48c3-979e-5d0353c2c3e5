import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup, Validators, FormBuilder, AbstractControl } from '@angular/forms';
import { ForgotPasswordService } from './forgot-password.service';

interface City {
  name: string,
  code: string
}

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss']
})
export class ForgotPasswordComponent {
  form: FormGroup = this.formBuilder.group(
    {
      email: ['', [Validators.required, Validators.email]],
    }
  );
  submitted = false;
  saving = false;

  @Input() visible = false;
  @Output() visibleChange = new EventEmitter<boolean>();

  onDialogHide() {
    this.visible = false;
    this.visibleChange.emit(this.visible);
  }

  constructor(
    private formBuilder: FormBuilder,
    private service: ForgotPasswordService
  ) {
    this.cities = [
      { name: "What's was your first car?", code: "NY" },
      { name: "What was your favorite school teacher's name?", code: "R<PERSON>" },
      { name: 'What is your date of birth?', code: 'LDN' },
      { name: 'What’s your favorite movie?', code: 'IST' },
      { name: 'What is your astrological sign?', code: 'PRS' }
    ];
  }

  ngOnInit(): void { }

  get f(): { [key: string]: AbstractControl } {
    return this.form.controls;
  }

  onSubmit(): void {
    this.submitted = true;

    if (this.form.invalid) {
      return;
    }

    this.saving = true;
    this.service.forgotPassword(this.form.value).subscribe({
      complete: () => {
        this.onReset();
        // this.activeModal.close();
        this.saving = false;
        // this._snackBar.open('Reset password link sent successfully!');
      },
      error: (err) => {
        this.saving = false;
        // this._snackBar.open(err?.error?.message || 'Error while processing your request.', { type: 'Error' });
      },
    })
  }

  onReset(): void {
    this.submitted = false;
    this.form.reset();
  }

  cities: City[];
  selectedCity: City | any;

}
