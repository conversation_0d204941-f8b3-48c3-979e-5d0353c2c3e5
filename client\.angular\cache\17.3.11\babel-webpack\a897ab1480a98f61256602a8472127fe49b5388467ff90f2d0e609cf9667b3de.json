{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./service/app.layout.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"primeng/sidebar\";\nimport * as i5 from \"primeng/calendar\";\nconst _c0 = a0 => ({\n  \"layout-rightmenu-active\": a0\n});\nexport class AppProfileSidebarComponent {\n  constructor(layoutService) {\n    this.layoutService = layoutService;\n    this.date = new Date();\n  }\n  get visible() {\n    return this.layoutService.state.rightMenuActive;\n  }\n  set visible(_val) {\n    this.layoutService.state.rightMenuActive = _val;\n  }\n  static {\n    this.ɵfac = function AppProfileSidebarComponent_Factory(t) {\n      return new (t || AppProfileSidebarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppProfileSidebarComponent,\n      selectors: [[\"app-profilemenu\"]],\n      decls: 51,\n      vars: 7,\n      consts: [[\"position\", \"right\", \"styleClass\", \"layout-profile-sidebar w-full sm:w-28rem\", 3, \"visibleChange\", \"visible\", \"transitionOptions\"], [1, \"layout-rightmenu\", \"h-full\", \"overflow-y-auto\", \"overflow-x-hidden\", 3, \"ngClass\"], [1, \"user-detail-wrapper\", \"text-center\", 2, \"padding\", \"4.5rem 0 2rem 0\"], [1, \"user-detail-content\", \"mb-4\"], [\"src\", \"assets/layout/images/avatar.png\", \"alt\", \"atlantis\", 1, \"user-image\"], [1, \"user-name\", \"text-2xl\", \"text-center\", \"block\", \"mt-4\", \"mb-1\"], [1, \"user-number\"], [1, \"user-tasks\", \"flex\", \"justify-content-between\", \"align-items-center\", \"py-4\", \"px-3\", \"border-bottom-1\", \"surface-border\"], [1, \"user-tasks-item\", \"in-progress\", \"font-medium\"], [1, \"task-number\", \"text-red-500\", \"flex\", \"justify-content-center\", \"align-items-center\", \"border-round\", 2, \"background\", \"rgba(255, 255, 255, 0.05)\", \"padding\", \"9px\", \"width\", \"50px\", \"height\", \"50px\", \"font-size\", \"30px\"], [1, \"task-name\", \"block\", \"mt-3\"], [1, \"user-tasks-item\", \"font-medium\"], [1, \"task-number\", \"flex\", \"justify-content-center\", \"align-items-center\", \"border-round\", 2, \"background\", \"rgba(255, 255, 255, 0.05)\", \"padding\", \"9px\", \"width\", \"50px\", \"height\", \"50px\", \"font-size\", \"30px\"], [\"styleClass\", \"w-full p-0\", 3, \"ngModelChange\", \"ngModel\", \"inline\"], [1, \"daily-plan-wrapper\", \"mt-5\"], [1, \"today-date\"], [1, \"list-none\", \"overflow-hidden\", \"p-0\", \"m-0\"], [1, \"mt-3\", \"border-round\", \"py-2\", \"px-3\", 2, \"background\", \"rgba(255, 255, 255, 0.05)\"], [1, \"event-time\", \"block\", \"font-semibold\", \"text-color-secondary\"], [1, \"event-topic\", \"block\", \"mt-2\"]],\n      template: function AppProfileSidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p-sidebar\", 0);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function AppProfileSidebarComponent_Template_p_sidebar_visibleChange_0_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n            return $event;\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementStart(5, \"span\", 5);\n          i0.ɵɵtext(6, \"Gene Russell\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"span\", 6);\n          i0.ɵɵtext(8, \"(406) 555-0120\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"a\", 9);\n          i0.ɵɵtext(12, \"23\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"span\", 10);\n          i0.ɵɵtext(14, \"Progress\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 11)(16, \"a\", 12);\n          i0.ɵɵtext(17, \"6\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"span\", 10);\n          i0.ɵɵtext(19, \"Overdue\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 11)(21, \"a\", 12);\n          i0.ɵɵtext(22, \"38\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"span\", 10);\n          i0.ɵɵtext(24, \"All deals\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\")(26, \"p-calendar\", 13);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function AppProfileSidebarComponent_Template_p_calendar_ngModelChange_26_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.date, $event) || (ctx.date = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 14)(28, \"span\", 15);\n          i0.ɵɵtext(29, \"14 Sunday, Jun 2020\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"ul\", 16)(31, \"li\", 17)(32, \"span\", 18);\n          i0.ɵɵtext(33, \"1:00 PM - 2:00 PM\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"span\", 19);\n          i0.ɵɵtext(35, \"Meeting with Alfredo Rhiel Madsen\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"li\", 17)(37, \"span\", 18);\n          i0.ɵɵtext(38, \"2:00 PM - 3:00 PM\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"span\", 19);\n          i0.ɵɵtext(40, \"Team Sync\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"li\", 17)(42, \"span\", 18);\n          i0.ɵɵtext(43, \"5:00 PM - 6:00 PM\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"span\", 19);\n          i0.ɵɵtext(45, \"Team Sync\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"li\", 17)(47, \"span\", 18);\n          i0.ɵɵtext(48, \"7:00 PM - 7:30 PM\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"span\", 19);\n          i0.ɵɵtext(50, \"Meeting with Engineering managers\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n          i0.ɵɵproperty(\"transitionOptions\", \".3s cubic-bezier(0, 0, 0.2, 1)\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, ctx.layoutService.state.rightMenuActive));\n          i0.ɵɵadvance(25);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.date);\n          i0.ɵɵproperty(\"inline\", true);\n        }\n      },\n      dependencies: [i2.NgClass, i3.NgControlStatus, i3.NgModel, i4.Sidebar, i5.Calendar],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppProfileSidebarComponent", "constructor", "layoutService", "date", "Date", "visible", "state", "rightMenuActive", "_val", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "selectors", "decls", "vars", "consts", "template", "AppProfileSidebarComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtwoWayListener", "AppProfileSidebarComponent_Template_p_sidebar_visibleChange_0_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "AppProfileSidebarComponent_Template_p_calendar_ngModelChange_26_listener", "ɵɵtwoWayProperty", "ɵɵproperty", "ɵɵadvance", "ɵɵpureFunction1", "_c0"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\store\\layout\\app.profilesidebar.component.ts", "D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\store\\layout\\app.profilesidebar.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { LayoutService } from './service/app.layout.service';\r\n\r\n@Component({\r\n    selector: 'app-profilemenu',\r\n    templateUrl: './app.profilesidebar.component.html'\r\n})\r\nexport class AppProfileSidebarComponent {\r\n    date: Date= new Date();\r\n    constructor(public layoutService: LayoutService) { }\r\n\r\n    get visible(): boolean {\r\n        return this.layoutService.state.rightMenuActive;\r\n    }\r\n\r\n    set visible(_val: boolean) {\r\n        this.layoutService.state.rightMenuActive = _val;\r\n    }\r\n}", "<p-sidebar [(visible)]=\"visible\" position=\"right\" [transitionOptions]=\"'.3s cubic-bezier(0, 0, 0.2, 1)'\"\r\n    styleClass=\"layout-profile-sidebar w-full sm:w-28rem\">\r\n\r\n    <div class=\"layout-rightmenu h-full overflow-y-auto overflow-x-hidden\"\r\n        [ngClass]=\"{'layout-rightmenu-active': layoutService.state.rightMenuActive}\">\r\n\r\n        <div class=\"user-detail-wrapper text-center\" style=\" padding: 4.5rem 0 2rem 0;\">\r\n            <div class=\"user-detail-content mb-4\">\r\n                <img src=\"assets/layout/images/avatar.png\" alt=\"atlantis\" class=\"user-image\">\r\n                <span class=\"user-name text-2xl text-center block mt-4 mb-1\"><PERSON></span>\r\n                <span class=\"user-number\">(406) 555-0120</span>\r\n            </div>\r\n            <div\r\n                class=\"user-tasks flex justify-content-between align-items-center py-4 px-3 border-bottom-1 surface-border\">\r\n                <div class=\"user-tasks-item in-progress font-medium \">\r\n                    <a class=\"task-number text-red-500 flex justify-content-center align-items-center  border-round\"\r\n                        style=\"background:rgba(255, 255, 255, 0.05);padding: 9px;width: 50px;\r\n                    height: 50px;\r\n                    font-size: 30px;\">23</a>\r\n                    <span class=\"task-name block mt-3\">Progress</span>\r\n                </div>\r\n                <div class=\"user-tasks-item  font-medium\">\r\n                    <a class=\"task-number flex justify-content-center align-items-center   border-round\" style=\"background:rgba(255, 255, 255, 0.05);padding: 9px;width: 50px;\r\n                    height: 50px;\r\n                    font-size: 30px;\">6</a>\r\n                    <span class=\"task-name block mt-3\">Overdue</span>\r\n                </div>\r\n                <div class=\"user-tasks-item  font-medium\">\r\n                    <a class=\"task-number flex justify-content-center align-items-center border-round\" style=\"background:rgba(255, 255, 255, 0.05);padding: 9px;width: 50px;\r\n                    height: 50px;\r\n                    font-size: 30px;\">38</a>\r\n                    <span class=\"task-name block mt-3\">All deals</span>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div>\r\n            <p-calendar [(ngModel)]=\"date\" [inline]=\"true\" styleClass=\"w-full p-0\"></p-calendar>\r\n        </div>\r\n        <div class=\"daily-plan-wrapper mt-5\">\r\n            <span class=\"today-date\">14 Sunday, Jun 2020</span>\r\n            <ul class=\"list-none overflow-hidden p-0 m-0\">\r\n                <li class=\"mt-3 border-round py-2 px-3\" style=\"background:rgba(255, 255, 255, 0.05);\">\r\n                    <span class=\"event-time block font-semibold text-color-secondary\">1:00 PM - 2:00 PM</span>\r\n                    <span class=\"event-topic block mt-2\">Meeting with Alfredo Rhiel Madsen</span>\r\n                </li>\r\n                <li class=\"mt-3 border-round py-2 px-3\" style=\"background:rgba(255, 255, 255, 0.05);\">\r\n                    <span class=\"event-time block font-semibold text-color-secondary\">2:00 PM - 3:00 PM</span>\r\n                    <span class=\"event-topic block mt-2\">Team Sync</span>\r\n                </li>\r\n                <li class=\"mt-3 border-round py-2 px-3\" style=\"background:rgba(255, 255, 255, 0.05);\">\r\n                    <span class=\"event-time block font-semibold text-color-secondary\">5:00 PM - 6:00 PM</span>\r\n                    <span class=\"event-topic block mt-2\">Team Sync</span>\r\n                </li>\r\n                <li class=\"mt-3 border-round py-2 px-3\" style=\"background:rgba(255, 255, 255, 0.05);\">\r\n                    <span class=\"event-time block font-semibold text-color-secondary\">7:00 PM - 7:30 PM</span>\r\n                    <span class=\"event-topic block mt-2\">Meeting with Engineering managers</span>\r\n                </li>\r\n            </ul>\r\n        </div>\r\n    </div>\r\n\r\n</p-sidebar>"], "mappings": ";;;;;;;;;AAOA,OAAM,MAAOA,0BAA0B;EAEnCC,YAAmBC,aAA4B;IAA5B,KAAAA,aAAa,GAAbA,aAAa;IADhC,KAAAC,IAAI,GAAQ,IAAIC,IAAI,EAAE;EAC6B;EAEnD,IAAIC,OAAOA,CAAA;IACP,OAAO,IAAI,CAACH,aAAa,CAACI,KAAK,CAACC,eAAe;EACnD;EAEA,IAAIF,OAAOA,CAACG,IAAa;IACrB,IAAI,CAACN,aAAa,CAACI,KAAK,CAACC,eAAe,GAAGC,IAAI;EACnD;;;uBAVSR,0BAA0B,EAAAS,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAA1BZ,0BAA0B;MAAAa,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPvCV,EAAA,CAAAY,cAAA,mBAC0D;UAD/CZ,EAAA,CAAAa,gBAAA,2BAAAC,uEAAAC,MAAA;YAAAf,EAAA,CAAAgB,kBAAA,CAAAL,GAAA,CAAAf,OAAA,EAAAmB,MAAA,MAAAJ,GAAA,CAAAf,OAAA,GAAAmB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAqB;UAOpBf,EAJR,CAAAY,cAAA,aACiF,aAEG,aACtC;UAClCZ,EAAA,CAAAiB,SAAA,aAA6E;UAC7EjB,EAAA,CAAAY,cAAA,cAA6D;UAAAZ,EAAA,CAAAkB,MAAA,mBAAY;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UAChFnB,EAAA,CAAAY,cAAA,cAA0B;UAAAZ,EAAA,CAAAkB,MAAA,qBAAc;UAC5ClB,EAD4C,CAAAmB,YAAA,EAAO,EAC7C;UAIEnB,EAHR,CAAAY,cAAA,aACgH,cACtD,YAIhC;UAAAZ,EAAA,CAAAkB,MAAA,UAAE;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UACxBnB,EAAA,CAAAY,cAAA,gBAAmC;UAAAZ,EAAA,CAAAkB,MAAA,gBAAQ;UAC/ClB,EAD+C,CAAAmB,YAAA,EAAO,EAChD;UAEFnB,EADJ,CAAAY,cAAA,eAA0C,aAGpB;UAAAZ,EAAA,CAAAkB,MAAA,SAAC;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UACvBnB,EAAA,CAAAY,cAAA,gBAAmC;UAAAZ,EAAA,CAAAkB,MAAA,eAAO;UAC9ClB,EAD8C,CAAAmB,YAAA,EAAO,EAC/C;UAEFnB,EADJ,CAAAY,cAAA,eAA0C,aAGpB;UAAAZ,EAAA,CAAAkB,MAAA,UAAE;UAAAlB,EAAA,CAAAmB,YAAA,EAAI;UACxBnB,EAAA,CAAAY,cAAA,gBAAmC;UAAAZ,EAAA,CAAAkB,MAAA,iBAAS;UAGxDlB,EAHwD,CAAAmB,YAAA,EAAO,EACjD,EACJ,EACJ;UAEFnB,EADJ,CAAAY,cAAA,WAAK,sBACsE;UAA3DZ,EAAA,CAAAa,gBAAA,2BAAAO,yEAAAL,MAAA;YAAAf,EAAA,CAAAgB,kBAAA,CAAAL,GAAA,CAAAjB,IAAA,EAAAqB,MAAA,MAAAJ,GAAA,CAAAjB,IAAA,GAAAqB,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAkB;UAClCf,EAD2E,CAAAmB,YAAA,EAAa,EAClF;UAEFnB,EADJ,CAAAY,cAAA,eAAqC,gBACR;UAAAZ,EAAA,CAAAkB,MAAA,2BAAmB;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UAG3CnB,EAFR,CAAAY,cAAA,cAA8C,cAC4C,gBAChB;UAAAZ,EAAA,CAAAkB,MAAA,yBAAiB;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UAC1FnB,EAAA,CAAAY,cAAA,gBAAqC;UAAAZ,EAAA,CAAAkB,MAAA,yCAAiC;UAC1ElB,EAD0E,CAAAmB,YAAA,EAAO,EAC5E;UAEDnB,EADJ,CAAAY,cAAA,cAAsF,gBAChB;UAAAZ,EAAA,CAAAkB,MAAA,yBAAiB;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UAC1FnB,EAAA,CAAAY,cAAA,gBAAqC;UAAAZ,EAAA,CAAAkB,MAAA,iBAAS;UAClDlB,EADkD,CAAAmB,YAAA,EAAO,EACpD;UAEDnB,EADJ,CAAAY,cAAA,cAAsF,gBAChB;UAAAZ,EAAA,CAAAkB,MAAA,yBAAiB;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UAC1FnB,EAAA,CAAAY,cAAA,gBAAqC;UAAAZ,EAAA,CAAAkB,MAAA,iBAAS;UAClDlB,EADkD,CAAAmB,YAAA,EAAO,EACpD;UAEDnB,EADJ,CAAAY,cAAA,cAAsF,gBAChB;UAAAZ,EAAA,CAAAkB,MAAA,yBAAiB;UAAAlB,EAAA,CAAAmB,YAAA,EAAO;UAC1FnB,EAAA,CAAAY,cAAA,gBAAqC;UAAAZ,EAAA,CAAAkB,MAAA,yCAAiC;UAM1FlB,EAN0F,CAAAmB,YAAA,EAAO,EAC5E,EACJ,EACH,EACJ,EAEE;;;UA7DDnB,EAAA,CAAAqB,gBAAA,YAAAV,GAAA,CAAAf,OAAA,CAAqB;UAAkBI,EAAA,CAAAsB,UAAA,uDAAsD;UAIhGtB,EAAA,CAAAuB,SAAA,EAA4E;UAA5EvB,EAAA,CAAAsB,UAAA,YAAAtB,EAAA,CAAAwB,eAAA,IAAAC,GAAA,EAAAd,GAAA,CAAAlB,aAAA,CAAAI,KAAA,CAAAC,eAAA,EAA4E;UAgC5DE,EAAA,CAAAuB,SAAA,IAAkB;UAAlBvB,EAAA,CAAAqB,gBAAA,YAAAV,GAAA,CAAAjB,IAAA,CAAkB;UAACM,EAAA,CAAAsB,UAAA,gBAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}