{"ast": null, "code": "import { style, animate, transition, trigger } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { WindowMaximizeIcon } from 'primeng/icons/windowmaximize';\nimport { WindowMinimizeIcon } from 'primeng/icons/windowminimize';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { ZIndexUtils, UniqueComponentId } from 'primeng/utils';\nimport * as i4 from 'primeng/focustrap';\nimport { FocusTrapModule } from 'primeng/focustrap';\nimport { platformBrowser } from '@angular/platform-browser';\n\n/**\n * Galleria is an advanced content gallery component.\n * @group Components\n */\nconst _c0 = [\"mask\"];\nconst _c1 = [\"container\"];\nconst _c2 = a0 => ({\n  \"p-galleria-mask p-component-overlay p-component-overlay-enter\": true,\n  \"p-galleria-visible\": a0\n});\nconst _c3 = (a0, a1) => ({\n  showTransitionParams: a0,\n  hideTransitionParams: a1\n});\nconst _c4 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nfunction Galleria_div_0_div_2_p_galleriaContent_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-galleriaContent\", 7);\n    i0.ɵɵlistener(\"@animation.start\", function Galleria_div_0_div_2_p_galleriaContent_2_Template_p_galleriaContent_animation_animation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onAnimationStart($event));\n    })(\"@animation.done\", function Galleria_div_0_div_2_p_galleriaContent_2_Template_p_galleriaContent_animation_animation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onAnimationEnd($event));\n    })(\"maskHide\", function Galleria_div_0_div_2_p_galleriaContent_2_Template_p_galleriaContent_maskHide_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onMaskHide());\n    })(\"activeItemChange\", function Galleria_div_0_div_2_p_galleriaContent_2_Template_p_galleriaContent_activeItemChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onActiveItemChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"@animation\", i0.ɵɵpureFunction1(8, _c4, i0.ɵɵpureFunction2(5, _c3, ctx_r1.showTransitionOptions, ctx_r1.hideTransitionOptions)))(\"value\", ctx_r1.value)(\"activeIndex\", ctx_r1.activeIndex)(\"numVisible\", ctx_r1.numVisibleLimit || ctx_r1.numVisible)(\"ngStyle\", ctx_r1.containerStyle);\n  }\n}\nfunction Galleria_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5, 2);\n    i0.ɵɵtemplate(2, Galleria_div_0_div_2_p_galleriaContent_2_Template, 1, 10, \"p-galleriaContent\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.maskClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c2, ctx_r1.visible));\n    i0.ɵɵattribute(\"role\", ctx_r1.fullScreen ? \"dialog\" : \"region\")(\"aria-modal\", ctx_r1.fullScreen ? \"true\" : undefined);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.visible);\n  }\n}\nfunction Galleria_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", null, 1);\n    i0.ɵɵtemplate(2, Galleria_div_0_div_2_Template, 3, 8, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.maskVisible);\n  }\n}\nfunction Galleria_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-galleriaContent\", 8);\n    i0.ɵɵlistener(\"activeItemChange\", function Galleria_ng_template_1_Template_p_galleriaContent_activeItemChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onActiveItemChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r1.value)(\"activeIndex\", ctx_r1.activeIndex)(\"numVisible\", ctx_r1.numVisibleLimit || ctx_r1.numVisible);\n  }\n}\nconst _c5 = [\"closeButton\"];\nconst _c6 = (a0, a1, a2) => ({\n  \"p-galleria p-component\": true,\n  \"p-galleria-fullscreen\": a0,\n  \"p-galleria-indicator-onitem\": a1,\n  \"p-galleria-item-nav-onhover\": a2\n});\nconst _c7 = () => ({});\nfunction GalleriaContent_div_0_button_1_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-galleria-close-icon\");\n  }\n}\nfunction GalleriaContent_div_0_button_1_2_ng_template_0_Template(rf, ctx) {}\nfunction GalleriaContent_div_0_button_1_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GalleriaContent_div_0_button_1_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction GalleriaContent_div_0_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function GalleriaContent_div_0_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.maskHide.emit());\n    });\n    i0.ɵɵtemplate(1, GalleriaContent_div_0_button_1_TimesIcon_1_Template, 1, 1, \"TimesIcon\", 9)(2, GalleriaContent_div_0_button_1_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.closeAriaLabel())(\"data-pc-section\", \"closebutton\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.galleria.closeIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.galleria.closeIconTemplate);\n  }\n}\nfunction GalleriaContent_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"p-galleriaItemSlot\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"templates\", ctx_r2.galleria.templates);\n  }\n}\nfunction GalleriaContent_div_0_p_galleriaThumbnails_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-galleriaThumbnails\", 14);\n    i0.ɵɵlistener(\"onActiveIndexChange\", function GalleriaContent_div_0_p_galleriaThumbnails_5_Template_p_galleriaThumbnails_onActiveIndexChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onActiveIndexChange($event));\n    })(\"stopSlideShow\", function GalleriaContent_div_0_p_galleriaThumbnails_5_Template_p_galleriaThumbnails_stopSlideShow_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.stopSlideShow());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"containerId\", ctx_r2.id)(\"value\", ctx_r2.value)(\"activeIndex\", ctx_r2.activeIndex)(\"templates\", ctx_r2.galleria.templates)(\"numVisible\", ctx_r2.numVisible)(\"responsiveOptions\", ctx_r2.galleria.responsiveOptions)(\"circular\", ctx_r2.galleria.circular)(\"isVertical\", ctx_r2.isVertical())(\"contentHeight\", ctx_r2.galleria.verticalThumbnailViewPortHeight)(\"showThumbnailNavigators\", ctx_r2.galleria.showThumbnailNavigators)(\"slideShowActive\", ctx_r2.slideShowActive);\n  }\n}\nfunction GalleriaContent_div_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵelement(1, \"p-galleriaItemSlot\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"templates\", ctx_r2.galleria.templates);\n  }\n}\nfunction GalleriaContent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, GalleriaContent_div_0_button_1_Template, 3, 4, \"button\", 2)(2, GalleriaContent_div_0_div_2_Template, 2, 1, \"div\", 3);\n    i0.ɵɵelementStart(3, \"div\", 4)(4, \"p-galleriaItem\", 5);\n    i0.ɵɵlistener(\"onActiveIndexChange\", function GalleriaContent_div_0_Template_p_galleriaItem_onActiveIndexChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onActiveIndexChange($event));\n    })(\"startSlideShow\", function GalleriaContent_div_0_Template_p_galleriaItem_startSlideShow_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.startSlideShow());\n    })(\"stopSlideShow\", function GalleriaContent_div_0_Template_p_galleriaItem_stopSlideShow_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.stopSlideShow());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, GalleriaContent_div_0_p_galleriaThumbnails_5_Template, 1, 11, \"p-galleriaThumbnails\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, GalleriaContent_div_0_div_6_Template, 2, 1, \"div\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r2.galleriaClass());\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(23, _c6, ctx_r2.galleria.fullScreen, ctx_r2.galleria.showIndicatorsOnItem, ctx_r2.galleria.showItemNavigatorsOnHover && !ctx_r2.galleria.fullScreen))(\"ngStyle\", !ctx_r2.galleria.fullScreen ? ctx_r2.galleria.containerStyle : i0.ɵɵpureFunction0(27, _c7));\n    i0.ɵɵattribute(\"id\", ctx_r2.id)(\"role\", \"region\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.galleria.fullScreen);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.galleria.templates && ctx_r2.galleria.headerFacet);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-live\", ctx_r2.galleria.autoPlay ? \"polite\" : \"off\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", ctx_r2.id)(\"value\", ctx_r2.value)(\"activeIndex\", ctx_r2.activeIndex)(\"circular\", ctx_r2.galleria.circular)(\"templates\", ctx_r2.galleria.templates)(\"showIndicators\", ctx_r2.galleria.showIndicators)(\"changeItemOnIndicatorHover\", ctx_r2.galleria.changeItemOnIndicatorHover)(\"indicatorFacet\", ctx_r2.galleria.indicatorFacet)(\"captionFacet\", ctx_r2.galleria.captionFacet)(\"showItemNavigators\", ctx_r2.galleria.showItemNavigators)(\"autoPlay\", ctx_r2.galleria.autoPlay)(\"slideShowActive\", ctx_r2.slideShowActive);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.galleria.showThumbnails);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.galleria.templates && ctx_r2.galleria.footerFacet);\n  }\n}\nfunction GalleriaItemSlot_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction GalleriaItemSlot_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, GalleriaItemSlot_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.contentTemplate)(\"ngTemplateOutletContext\", ctx_r0.context);\n  }\n}\nconst _c8 = a0 => ({\n  \"p-galleria-item-prev p-galleria-item-nav p-link\": true,\n  \"p-disabled\": a0\n});\nconst _c9 = a0 => ({\n  \"p-galleria-item-next p-galleria-item-nav p-link\": true,\n  \"p-disabled\": a0\n});\nconst _c10 = a0 => ({\n  \"p-galleria-indicator\": true,\n  \"p-highlight\": a0\n});\nfunction GalleriaItem_button_2_ChevronLeftIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronLeftIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-galleria-item-prev-icon\");\n  }\n}\nfunction GalleriaItem_button_2_2_ng_template_0_Template(rf, ctx) {}\nfunction GalleriaItem_button_2_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GalleriaItem_button_2_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction GalleriaItem_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function GalleriaItem_button_2_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navBackward($event));\n    });\n    i0.ɵɵtemplate(1, GalleriaItem_button_2_ChevronLeftIcon_1_Template, 1, 1, \"ChevronLeftIcon\", 9)(2, GalleriaItem_button_2_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c8, ctx_r1.isNavBackwardDisabled()))(\"disabled\", ctx_r1.isNavBackwardDisabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.galleria.itemPreviousIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.galleria.itemPreviousIconTemplate);\n  }\n}\nfunction GalleriaItem_button_5_ChevronRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-galleria-item-next-icon\");\n  }\n}\nfunction GalleriaItem_button_5_2_ng_template_0_Template(rf, ctx) {}\nfunction GalleriaItem_button_5_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GalleriaItem_button_5_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction GalleriaItem_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function GalleriaItem_button_5_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.navForward($event));\n    });\n    i0.ɵɵtemplate(1, GalleriaItem_button_5_ChevronRightIcon_1_Template, 1, 1, \"ChevronRightIcon\", 9)(2, GalleriaItem_button_5_2_Template, 1, 0, null, 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c9, ctx_r1.isNavForwardDisabled()))(\"disabled\", ctx_r1.isNavForwardDisabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.galleria.itemNextIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.galleria.itemNextIconTemplate);\n  }\n}\nfunction GalleriaItem_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵelement(1, \"p-galleriaItemSlot\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"item\", ctx_r1.activeItem)(\"templates\", ctx_r1.templates);\n  }\n}\nfunction GalleriaItem_ul_7_li_1_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"button\", 20);\n  }\n}\nfunction GalleriaItem_ul_7_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 17);\n    i0.ɵɵlistener(\"click\", function GalleriaItem_ul_7_li_1_Template_li_click_0_listener() {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onIndicatorClick(index_r5));\n    })(\"mouseenter\", function GalleriaItem_ul_7_li_1_Template_li_mouseenter_0_listener() {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onIndicatorMouseEnter(index_r5));\n    })(\"keydown\", function GalleriaItem_ul_7_li_1_Template_li_keydown_0_listener($event) {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onIndicatorKeyDown($event, index_r5));\n    });\n    i0.ɵɵtemplate(1, GalleriaItem_ul_7_li_1_button_1_Template, 1, 0, \"button\", 18);\n    i0.ɵɵelement(2, \"p-galleriaItemSlot\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const index_r5 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c10, ctx_r1.isIndicatorItemActive(index_r5)));\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.ariaPageLabel(index_r5 + 1))(\"aria-selected\", ctx_r1.activeIndex === index_r5)(\"aria-controls\", ctx_r1.id + \"_item_\" + index_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.indicatorFacet);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"index\", index_r5)(\"templates\", ctx_r1.templates);\n  }\n}\nfunction GalleriaItem_ul_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 15);\n    i0.ɵɵtemplate(1, GalleriaItem_ul_7_li_1_Template, 3, 9, \"li\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.value);\n  }\n}\nconst _c11 = [\"itemsContainer\"];\nconst _c12 = a0 => ({\n  height: a0\n});\nconst _c13 = a0 => ({\n  \"p-galleria-thumbnail-prev p-link\": true,\n  \"p-disabled\": a0\n});\nconst _c14 = (a0, a1, a2, a3) => ({\n  \"p-galleria-thumbnail-item\": true,\n  \"p-galleria-thumbnail-item-current\": a0,\n  \"p-galleria-thumbnail-item-active\": a1,\n  \"p-galleria-thumbnail-item-start\": a2,\n  \"p-galleria-thumbnail-item-end\": a3\n});\nconst _c15 = a0 => ({\n  \"p-galleria-thumbnail-next p-link\": true,\n  \"p-disabled\": a0\n});\nfunction GalleriaThumbnails_button_2_ng_container_1_ChevronLeftIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronLeftIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-galleria-thumbnail-prev-icon\");\n  }\n}\nfunction GalleriaThumbnails_button_2_ng_container_1_ChevronUpIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronUpIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-galleria-thumbnail-prev-icon\");\n  }\n}\nfunction GalleriaThumbnails_button_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, GalleriaThumbnails_button_2_ng_container_1_ChevronLeftIcon_1_Template, 1, 1, \"ChevronLeftIcon\", 10)(2, GalleriaThumbnails_button_2_ng_container_1_ChevronUpIcon_2_Template, 1, 1, \"ChevronUpIcon\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isVertical);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isVertical);\n  }\n}\nfunction GalleriaThumbnails_button_2_2_ng_template_0_Template(rf, ctx) {}\nfunction GalleriaThumbnails_button_2_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GalleriaThumbnails_button_2_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction GalleriaThumbnails_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function GalleriaThumbnails_button_2_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navBackward($event));\n    });\n    i0.ɵɵtemplate(1, GalleriaThumbnails_button_2_ng_container_1_Template, 3, 2, \"ng-container\", 8)(2, GalleriaThumbnails_button_2_2_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c13, ctx_r2.isNavBackwardDisabled()))(\"disabled\", ctx_r2.isNavBackwardDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.ariaPrevButtonLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.galleria.previousThumbnailIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.galleria.previousThumbnailIconTemplate);\n  }\n}\nfunction GalleriaThumbnails_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵlistener(\"keydown\", function GalleriaThumbnails_div_6_Template_div_keydown_0_listener($event) {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onThumbnailKeydown($event, index_r5));\n    });\n    i0.ɵɵelementStart(1, \"div\", 13);\n    i0.ɵɵlistener(\"click\", function GalleriaThumbnails_div_6_Template_div_click_1_listener() {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemClick(index_r5));\n    })(\"touchend\", function GalleriaThumbnails_div_6_Template_div_touchend_1_listener() {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemClick(index_r5));\n    })(\"keydown.enter\", function GalleriaThumbnails_div_6_Template_div_keydown_enter_1_listener() {\n      const index_r5 = i0.ɵɵrestoreView(_r4).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onItemClick(index_r5));\n    });\n    i0.ɵɵelement(2, \"p-galleriaItemSlot\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    const index_r5 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(10, _c14, ctx_r2.activeIndex === index_r5, ctx_r2.isItemActive(index_r5), ctx_r2.firstItemAciveIndex() === index_r5, ctx_r2.lastItemActiveIndex() === index_r5));\n    i0.ɵɵattribute(\"aria-selected\", ctx_r2.activeIndex === index_r5)(\"aria-controls\", ctx_r2.containerId + \"_item_\" + index_r5)(\"data-pc-section\", \"thumbnailitem\")(\"data-p-active\", ctx_r2.activeIndex === index_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"tabindex\", ctx_r2.activeIndex === index_r5 ? 0 : -1)(\"aria-current\", ctx_r2.activeIndex === index_r5 ? \"page\" : undefined)(\"aria-label\", ctx_r2.ariaPageLabel(index_r5 + 1));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"item\", item_r6)(\"templates\", ctx_r2.templates);\n  }\n}\nfunction GalleriaThumbnails_button_7_ng_container_1_ChevronRightIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 16);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngClass\", \"p-galleria-thumbnail-next-icon\");\n  }\n}\nfunction GalleriaThumbnails_button_7_ng_container_1_ChevronDownIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 16);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"ngClass\", \"p-galleria-thumbnail-next-icon\");\n  }\n}\nfunction GalleriaThumbnails_button_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, GalleriaThumbnails_button_7_ng_container_1_ChevronRightIcon_1_Template, 1, 1, \"ChevronRightIcon\", 15)(2, GalleriaThumbnails_button_7_ng_container_1_ChevronDownIcon_2_Template, 1, 1, \"ChevronDownIcon\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isVertical);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isVertical);\n  }\n}\nfunction GalleriaThumbnails_button_7_2_ng_template_0_Template(rf, ctx) {}\nfunction GalleriaThumbnails_button_7_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, GalleriaThumbnails_button_7_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction GalleriaThumbnails_button_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function GalleriaThumbnails_button_7_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navForward($event));\n    });\n    i0.ɵɵtemplate(1, GalleriaThumbnails_button_7_ng_container_1_Template, 3, 2, \"ng-container\", 8)(2, GalleriaThumbnails_button_7_2_Template, 1, 0, null, 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c15, ctx_r2.isNavForwardDisabled()))(\"disabled\", ctx_r2.isNavForwardDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r2.ariaNextButtonLabel());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.galleria.nextThumbnailIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.galleria.nextThumbnailIconTemplate);\n  }\n}\nclass Galleria {\n  document;\n  platformId;\n  element;\n  cd;\n  config;\n  /**\n   * Index of the first item.\n   * @group Props\n   */\n  get activeIndex() {\n    return this._activeIndex;\n  }\n  set activeIndex(activeIndex) {\n    this._activeIndex = activeIndex;\n  }\n  /**\n   * Whether to display the component on fullscreen.\n   * @group Props\n   */\n  fullScreen = false;\n  /**\n   * Unique identifier of the element.\n   * @group Props\n   */\n  id;\n  /**\n   * An array of objects to display.\n   * @group Props\n   */\n  value;\n  /**\n   * Number of items per page.\n   * @group Props\n   */\n  numVisible = 3;\n  /**\n   * An array of options for responsive design.\n   * @see {GalleriaResponsiveOptions}\n   * @group Props\n   */\n  responsiveOptions;\n  /**\n   * Whether to display navigation buttons in item section.\n   * @group Props\n   */\n  showItemNavigators = false;\n  /**\n   * Whether to display navigation buttons in thumbnail container.\n   * @group Props\n   */\n  showThumbnailNavigators = true;\n  /**\n   * Whether to display navigation buttons on item hover.\n   * @group Props\n   */\n  showItemNavigatorsOnHover = false;\n  /**\n   * When enabled, item is changed on indicator hover.\n   * @group Props\n   */\n  changeItemOnIndicatorHover = false;\n  /**\n   * Defines if scrolling would be infinite.\n   * @group Props\n   */\n  circular = false;\n  /**\n   * Items are displayed with a slideshow in autoPlay mode.\n   * @group Props\n   */\n  autoPlay = false;\n  /**\n   * When enabled, autorun should stop by click.\n   * @group Props\n   */\n  shouldStopAutoplayByClick = true;\n  /**\n   * Time in milliseconds to scroll items.\n   * @group Props\n   */\n  transitionInterval = 4000;\n  /**\n   * Whether to display thumbnail container.\n   * @group Props\n   */\n  showThumbnails = true;\n  /**\n   * Position of thumbnails.\n   * @group Props\n   */\n  thumbnailsPosition = 'bottom';\n  /**\n   * Height of the viewport in vertical thumbnail.\n   * @group Props\n   */\n  verticalThumbnailViewPortHeight = '300px';\n  /**\n   * Whether to display indicator container.\n   * @group Props\n   */\n  showIndicators = false;\n  /**\n   * When enabled, indicator container is displayed on item container.\n   * @group Props\n   */\n  showIndicatorsOnItem = false;\n  /**\n   * Position of indicators.\n   * @group Props\n   */\n  indicatorsPosition = 'bottom';\n  /**\n   * Base zIndex value to use in layering.\n   * @group Props\n   */\n  baseZIndex = 0;\n  /**\n   * Style class of the mask on fullscreen mode.\n   * @group Props\n   */\n  maskClass;\n  /**\n   * Style class of the component on fullscreen mode. Otherwise, the 'class' property can be used.\n   * @group Props\n   */\n  containerClass;\n  /**\n   * Inline style of the component on fullscreen mode. Otherwise, the 'style' property can be used.\n   * @group Props\n   */\n  containerStyle;\n  /**\n   * Transition options of the show animation.\n   * @group Props\n   */\n  showTransitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n  /**\n   * Transition options of the hide animation.\n   * @group Props\n   */\n  hideTransitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n  /**\n   * Specifies the visibility of the mask on fullscreen mode.\n   * @group Props\n   */\n  get visible() {\n    return this._visible;\n  }\n  set visible(visible) {\n    this._visible = visible;\n    if (this._visible && !this.maskVisible) {\n      this.maskVisible = true;\n    }\n  }\n  /**\n   * Callback to invoke on active index change.\n   * @param {number} number - Active index.\n   * @group Emits\n   */\n  activeIndexChange = new EventEmitter();\n  /**\n   * Callback to invoke on visiblity change.\n   * @param {boolean} boolean - Visible value.\n   * @group Emits\n   */\n  visibleChange = new EventEmitter();\n  mask;\n  container;\n  templates;\n  _visible = false;\n  _activeIndex = 0;\n  headerFacet;\n  footerFacet;\n  indicatorFacet;\n  captionFacet;\n  closeIconTemplate;\n  previousThumbnailIconTemplate;\n  nextThumbnailIconTemplate;\n  itemPreviousIconTemplate;\n  itemNextIconTemplate;\n  maskVisible = false;\n  numVisibleLimit = 0;\n  constructor(document, platformId, element, cd, config) {\n    this.document = document;\n    this.platformId = platformId;\n    this.element = element;\n    this.cd = cd;\n    this.config = config;\n  }\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      switch (item.getType()) {\n        case 'header':\n          this.headerFacet = item.template;\n          break;\n        case 'footer':\n          this.footerFacet = item.template;\n          break;\n        case 'indicator':\n          this.indicatorFacet = item.template;\n          break;\n        case 'closeicon':\n          this.closeIconTemplate = item.template;\n          break;\n        case 'itemnexticon':\n          this.itemNextIconTemplate = item.template;\n          break;\n        case 'itempreviousicon':\n          this.itemPreviousIconTemplate = item.template;\n          break;\n        case 'previousthumbnailicon':\n          this.previousThumbnailIconTemplate = item.template;\n          break;\n        case 'nextthumbnailicon':\n          this.nextThumbnailIconTemplate = item.template;\n          break;\n        case 'caption':\n          this.captionFacet = item.template;\n          break;\n      }\n    });\n  }\n  ngOnChanges(simpleChanges) {\n    if (simpleChanges.value && simpleChanges.value.currentValue?.length < this.numVisible) {\n      this.numVisibleLimit = simpleChanges.value.currentValue.length;\n    } else {\n      this.numVisibleLimit = 0;\n    }\n  }\n  onMaskHide() {\n    this.visible = false;\n    this.visibleChange.emit(false);\n  }\n  onActiveItemChange(index) {\n    if (this.activeIndex !== index) {\n      this.activeIndex = index;\n      this.activeIndexChange.emit(index);\n    }\n  }\n  onAnimationStart(event) {\n    switch (event.toState) {\n      case 'visible':\n        this.enableModality();\n        setTimeout(() => {\n          DomHandler.focus(DomHandler.findSingle(this.container.nativeElement, '[data-pc-section=\"closebutton\"]'));\n        }, 25);\n        break;\n      case 'void':\n        DomHandler.addClass(this.mask?.nativeElement, 'p-component-overlay-leave');\n        break;\n    }\n  }\n  onAnimationEnd(event) {\n    switch (event.toState) {\n      case 'void':\n        this.disableModality();\n        break;\n    }\n  }\n  enableModality() {\n    DomHandler.blockBodyScroll();\n    this.cd.markForCheck();\n    if (this.mask) {\n      ZIndexUtils.set('modal', this.mask.nativeElement, this.baseZIndex || this.config.zIndex.modal);\n    }\n  }\n  disableModality() {\n    DomHandler.unblockBodyScroll();\n    this.maskVisible = false;\n    this.cd.markForCheck();\n    if (this.mask) {\n      ZIndexUtils.clear(this.mask.nativeElement);\n    }\n  }\n  ngOnDestroy() {\n    if (this.fullScreen) {\n      DomHandler.removeClass(this.document.body, 'p-overflow-hidden');\n    }\n    if (this.mask) {\n      this.disableModality();\n    }\n  }\n  static ɵfac = function Galleria_Factory(t) {\n    return new (t || Galleria)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Galleria,\n    selectors: [[\"p-galleria\"]],\n    contentQueries: function Galleria_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Galleria_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.mask = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.container = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      activeIndex: \"activeIndex\",\n      fullScreen: \"fullScreen\",\n      id: \"id\",\n      value: \"value\",\n      numVisible: \"numVisible\",\n      responsiveOptions: \"responsiveOptions\",\n      showItemNavigators: \"showItemNavigators\",\n      showThumbnailNavigators: \"showThumbnailNavigators\",\n      showItemNavigatorsOnHover: \"showItemNavigatorsOnHover\",\n      changeItemOnIndicatorHover: \"changeItemOnIndicatorHover\",\n      circular: \"circular\",\n      autoPlay: \"autoPlay\",\n      shouldStopAutoplayByClick: \"shouldStopAutoplayByClick\",\n      transitionInterval: \"transitionInterval\",\n      showThumbnails: \"showThumbnails\",\n      thumbnailsPosition: \"thumbnailsPosition\",\n      verticalThumbnailViewPortHeight: \"verticalThumbnailViewPortHeight\",\n      showIndicators: \"showIndicators\",\n      showIndicatorsOnItem: \"showIndicatorsOnItem\",\n      indicatorsPosition: \"indicatorsPosition\",\n      baseZIndex: \"baseZIndex\",\n      maskClass: \"maskClass\",\n      containerClass: \"containerClass\",\n      containerStyle: \"containerStyle\",\n      showTransitionOptions: \"showTransitionOptions\",\n      hideTransitionOptions: \"hideTransitionOptions\",\n      visible: \"visible\"\n    },\n    outputs: {\n      activeIndexChange: \"activeIndexChange\",\n      visibleChange: \"visibleChange\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 3,\n    vars: 2,\n    consts: [[\"windowed\", \"\"], [\"container\", \"\"], [\"mask\", \"\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"ngClass\", \"class\", 4, \"ngIf\"], [3, \"ngClass\"], [3, \"value\", \"activeIndex\", \"numVisible\", \"ngStyle\", \"maskHide\", \"activeItemChange\", 4, \"ngIf\"], [3, \"maskHide\", \"activeItemChange\", \"value\", \"activeIndex\", \"numVisible\", \"ngStyle\"], [3, \"activeItemChange\", \"value\", \"activeIndex\", \"numVisible\"]],\n    template: function Galleria_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, Galleria_div_0_Template, 3, 1, \"div\", 3)(1, Galleria_ng_template_1_Template, 1, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        const windowed_r4 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.fullScreen)(\"ngIfElse\", windowed_r4);\n      }\n    },\n    dependencies: () => [i2.NgClass, i2.NgIf, i2.NgStyle, GalleriaContent],\n    styles: [\"@layer primeng{.p-galleria-content{display:flex;flex-direction:column}.p-galleria-item-wrapper{display:flex;flex-direction:column;position:relative}.p-galleria-item-container{position:relative;display:flex;height:100%}.p-galleria-item-nav{position:absolute;top:50%;margin-top:-.5rem;display:inline-flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-item-prev{left:0;border-top-left-radius:0;border-bottom-left-radius:0}.p-galleria-item-next{right:0;border-top-right-radius:0;border-bottom-right-radius:0}.p-galleria-item{display:flex;justify-content:center;align-items:center;height:100%;width:100%}.p-galleria-item-nav-onhover .p-galleria-item-nav{pointer-events:none;opacity:0;transition:opacity .2s ease-in-out}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav{pointer-events:all;opacity:1}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav.p-disabled{pointer-events:none}.p-galleria-caption{position:absolute;bottom:0;left:0;width:100%}.p-galleria-thumbnail-wrapper{display:flex;flex-direction:column;overflow:auto;flex-shrink:0}.p-galleria-thumbnail-prev,.p-galleria-thumbnail-next{align-self:center;flex:0 0 auto;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-galleria-thumbnail-prev span,.p-galleria-thumbnail-next span{display:flex;justify-content:center;align-items:center}.p-galleria-thumbnail-container{display:flex;flex-direction:row}.p-galleria-thumbnail-items-container{overflow:hidden;width:100%}.p-galleria-thumbnail-items{display:flex}.p-galleria-thumbnail-item{overflow:auto;display:flex;align-items:center;justify-content:center;cursor:pointer;opacity:.5}.p-galleria-thumbnail-item:hover{opacity:1;transition:opacity .3s}.p-galleria-thumbnail-item-current{opacity:1}.p-galleria-thumbnails-left .p-galleria-content,.p-galleria-thumbnails-right .p-galleria-content,.p-galleria-thumbnails-left .p-galleria-item-wrapper,.p-galleria-thumbnails-right .p-galleria-item-wrapper{flex-direction:row}.p-galleria-thumbnails-left p-galleriaitem,.p-galleria-thumbnails-top p-galleriaitem{order:2}.p-galleria-thumbnails-left p-galleriathumbnails,.p-galleria-thumbnails-top p-galleriathumbnails{order:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-container,.p-galleria-thumbnails-right .p-galleria-thumbnail-container{flex-direction:column;flex-grow:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-items,.p-galleria-thumbnails-right .p-galleria-thumbnail-items{flex-direction:column;height:100%}.p-galleria-thumbnails-left .p-galleria-thumbnail-wrapper,.p-galleria-thumbnails-right .p-galleria-thumbnail-wrapper{height:100%}.p-galleria-indicators{display:flex;align-items:center;justify-content:center}.p-galleria-indicator>button{display:inline-flex;align-items:center}.p-galleria-indicators-left .p-galleria-item-wrapper,.p-galleria-indicators-right .p-galleria-item-wrapper{flex-direction:row;align-items:center}.p-galleria-indicators-left .p-galleria-item-container,.p-galleria-indicators-top .p-galleria-item-container{order:2}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-top .p-galleria-indicators{order:1}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-right .p-galleria-indicators{flex-direction:column}.p-galleria-indicator-onitem .p-galleria-indicators{position:absolute;display:flex;z-index:1}.p-galleria-indicator-onitem.p-galleria-indicators-top .p-galleria-indicators{top:0;left:0;width:100%;align-items:flex-start}.p-galleria-indicator-onitem.p-galleria-indicators-right .p-galleria-indicators{right:0;top:0;height:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-bottom .p-galleria-indicators{bottom:0;left:0;width:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-left .p-galleria-indicators{left:0;top:0;height:100%;align-items:flex-start}.p-galleria-mask{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:transparent;transition-property:background-color}.p-galleria-close{position:absolute;top:0;right:0;display:flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-mask .p-galleria-item-nav{position:fixed;top:50%;margin-top:-.5rem}.p-galleria-mask.p-galleria-mask-leave{background-color:transparent}.p-items-hidden .p-galleria-thumbnail-item{visibility:hidden}.p-items-hidden .p-galleria-thumbnail-item.p-galleria-thumbnail-item-active{visibility:visible}}\\n\"],\n    encapsulation: 2,\n    data: {\n      animation: [trigger('animation', [transition('void => visible', [style({\n        transform: 'scale(0.7)',\n        opacity: 0\n      }), animate('{{showTransitionParams}}')]), transition('visible => void', [animate('{{hideTransitionParams}}', style({\n        transform: 'scale(0.7)',\n        opacity: 0\n      }))])])]\n    },\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Galleria, [{\n    type: Component,\n    args: [{\n      selector: 'p-galleria',\n      template: `\n        <div *ngIf=\"fullScreen; else windowed\" #container>\n            <div\n                *ngIf=\"maskVisible\"\n                #mask\n                [ngClass]=\"{ 'p-galleria-mask p-component-overlay p-component-overlay-enter': true, 'p-galleria-visible': this.visible }\"\n                [class]=\"maskClass\"\n                [attr.role]=\"fullScreen ? 'dialog' : 'region'\"\n                [attr.aria-modal]=\"fullScreen ? 'true' : undefined\"\n            >\n                <p-galleriaContent\n                    *ngIf=\"visible\"\n                    [@animation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n                    (@animation.start)=\"onAnimationStart($event)\"\n                    (@animation.done)=\"onAnimationEnd($event)\"\n                    [value]=\"value\"\n                    [activeIndex]=\"activeIndex\"\n                    [numVisible]=\"numVisibleLimit || numVisible\"\n                    (maskHide)=\"onMaskHide()\"\n                    (activeItemChange)=\"onActiveItemChange($event)\"\n                    [ngStyle]=\"containerStyle\"\n                ></p-galleriaContent>\n            </div>\n        </div>\n\n        <ng-template #windowed>\n            <p-galleriaContent [value]=\"value\" [activeIndex]=\"activeIndex\" [numVisible]=\"numVisibleLimit || numVisible\" (activeItemChange)=\"onActiveItemChange($event)\"></p-galleriaContent>\n        </ng-template>\n    `,\n      animations: [trigger('animation', [transition('void => visible', [style({\n        transform: 'scale(0.7)',\n        opacity: 0\n      }), animate('{{showTransitionParams}}')]), transition('visible => void', [animate('{{hideTransitionParams}}', style({\n        transform: 'scale(0.7)',\n        opacity: 0\n      }))])])],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-galleria-content{display:flex;flex-direction:column}.p-galleria-item-wrapper{display:flex;flex-direction:column;position:relative}.p-galleria-item-container{position:relative;display:flex;height:100%}.p-galleria-item-nav{position:absolute;top:50%;margin-top:-.5rem;display:inline-flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-item-prev{left:0;border-top-left-radius:0;border-bottom-left-radius:0}.p-galleria-item-next{right:0;border-top-right-radius:0;border-bottom-right-radius:0}.p-galleria-item{display:flex;justify-content:center;align-items:center;height:100%;width:100%}.p-galleria-item-nav-onhover .p-galleria-item-nav{pointer-events:none;opacity:0;transition:opacity .2s ease-in-out}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav{pointer-events:all;opacity:1}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav.p-disabled{pointer-events:none}.p-galleria-caption{position:absolute;bottom:0;left:0;width:100%}.p-galleria-thumbnail-wrapper{display:flex;flex-direction:column;overflow:auto;flex-shrink:0}.p-galleria-thumbnail-prev,.p-galleria-thumbnail-next{align-self:center;flex:0 0 auto;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-galleria-thumbnail-prev span,.p-galleria-thumbnail-next span{display:flex;justify-content:center;align-items:center}.p-galleria-thumbnail-container{display:flex;flex-direction:row}.p-galleria-thumbnail-items-container{overflow:hidden;width:100%}.p-galleria-thumbnail-items{display:flex}.p-galleria-thumbnail-item{overflow:auto;display:flex;align-items:center;justify-content:center;cursor:pointer;opacity:.5}.p-galleria-thumbnail-item:hover{opacity:1;transition:opacity .3s}.p-galleria-thumbnail-item-current{opacity:1}.p-galleria-thumbnails-left .p-galleria-content,.p-galleria-thumbnails-right .p-galleria-content,.p-galleria-thumbnails-left .p-galleria-item-wrapper,.p-galleria-thumbnails-right .p-galleria-item-wrapper{flex-direction:row}.p-galleria-thumbnails-left p-galleriaitem,.p-galleria-thumbnails-top p-galleriaitem{order:2}.p-galleria-thumbnails-left p-galleriathumbnails,.p-galleria-thumbnails-top p-galleriathumbnails{order:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-container,.p-galleria-thumbnails-right .p-galleria-thumbnail-container{flex-direction:column;flex-grow:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-items,.p-galleria-thumbnails-right .p-galleria-thumbnail-items{flex-direction:column;height:100%}.p-galleria-thumbnails-left .p-galleria-thumbnail-wrapper,.p-galleria-thumbnails-right .p-galleria-thumbnail-wrapper{height:100%}.p-galleria-indicators{display:flex;align-items:center;justify-content:center}.p-galleria-indicator>button{display:inline-flex;align-items:center}.p-galleria-indicators-left .p-galleria-item-wrapper,.p-galleria-indicators-right .p-galleria-item-wrapper{flex-direction:row;align-items:center}.p-galleria-indicators-left .p-galleria-item-container,.p-galleria-indicators-top .p-galleria-item-container{order:2}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-top .p-galleria-indicators{order:1}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-right .p-galleria-indicators{flex-direction:column}.p-galleria-indicator-onitem .p-galleria-indicators{position:absolute;display:flex;z-index:1}.p-galleria-indicator-onitem.p-galleria-indicators-top .p-galleria-indicators{top:0;left:0;width:100%;align-items:flex-start}.p-galleria-indicator-onitem.p-galleria-indicators-right .p-galleria-indicators{right:0;top:0;height:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-bottom .p-galleria-indicators{bottom:0;left:0;width:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-left .p-galleria-indicators{left:0;top:0;height:100%;align-items:flex-start}.p-galleria-mask{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:transparent;transition-property:background-color}.p-galleria-close{position:absolute;top:0;right:0;display:flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-mask .p-galleria-item-nav{position:fixed;top:50%;margin-top:-.5rem}.p-galleria-mask.p-galleria-mask-leave{background-color:transparent}.p-items-hidden .p-galleria-thumbnail-item{visibility:hidden}.p-items-hidden .p-galleria-thumbnail-item.p-galleria-thumbnail-item-active{visibility:visible}}\\n\"]\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.PrimeNGConfig\n  }], {\n    activeIndex: [{\n      type: Input\n    }],\n    fullScreen: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    numVisible: [{\n      type: Input\n    }],\n    responsiveOptions: [{\n      type: Input\n    }],\n    showItemNavigators: [{\n      type: Input\n    }],\n    showThumbnailNavigators: [{\n      type: Input\n    }],\n    showItemNavigatorsOnHover: [{\n      type: Input\n    }],\n    changeItemOnIndicatorHover: [{\n      type: Input\n    }],\n    circular: [{\n      type: Input\n    }],\n    autoPlay: [{\n      type: Input\n    }],\n    shouldStopAutoplayByClick: [{\n      type: Input\n    }],\n    transitionInterval: [{\n      type: Input\n    }],\n    showThumbnails: [{\n      type: Input\n    }],\n    thumbnailsPosition: [{\n      type: Input\n    }],\n    verticalThumbnailViewPortHeight: [{\n      type: Input\n    }],\n    showIndicators: [{\n      type: Input\n    }],\n    showIndicatorsOnItem: [{\n      type: Input\n    }],\n    indicatorsPosition: [{\n      type: Input\n    }],\n    baseZIndex: [{\n      type: Input\n    }],\n    maskClass: [{\n      type: Input\n    }],\n    containerClass: [{\n      type: Input\n    }],\n    containerStyle: [{\n      type: Input\n    }],\n    showTransitionOptions: [{\n      type: Input\n    }],\n    hideTransitionOptions: [{\n      type: Input\n    }],\n    visible: [{\n      type: Input\n    }],\n    activeIndexChange: [{\n      type: Output\n    }],\n    visibleChange: [{\n      type: Output\n    }],\n    mask: [{\n      type: ViewChild,\n      args: ['mask']\n    }],\n    container: [{\n      type: ViewChild,\n      args: ['container']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass GalleriaContent {\n  galleria;\n  cd;\n  differs;\n  config;\n  get activeIndex() {\n    return this._activeIndex;\n  }\n  set activeIndex(activeIndex) {\n    this._activeIndex = activeIndex;\n  }\n  value = [];\n  numVisible;\n  maskHide = new EventEmitter();\n  activeItemChange = new EventEmitter();\n  closeButton;\n  id;\n  _activeIndex = 0;\n  slideShowActive = true;\n  interval;\n  styleClass;\n  differ;\n  constructor(galleria, cd, differs, config) {\n    this.galleria = galleria;\n    this.cd = cd;\n    this.differs = differs;\n    this.config = config;\n    this.id = this.galleria.id || UniqueComponentId();\n    this.differ = this.differs.find(this.galleria).create();\n  }\n  ngDoCheck() {\n    if (isPlatformBrowser(this.galleria.platformId)) {\n      const changes = this.differ.diff(this.galleria);\n      if (changes && changes.forEachItem.length > 0) {\n        // Because we change the properties of the parent component,\n        // and the children take our entity from the injector.\n        // We can tell the children to redraw themselves when we change the properties of the parent component.\n        // Since we have an onPush strategy\n        this.cd.markForCheck();\n      }\n    }\n  }\n  galleriaClass() {\n    const thumbnailsPosClass = this.galleria.showThumbnails && this.getPositionClass('p-galleria-thumbnails', this.galleria.thumbnailsPosition);\n    const indicatorPosClass = this.galleria.showIndicators && this.getPositionClass('p-galleria-indicators', this.galleria.indicatorsPosition);\n    return (this.galleria.containerClass ? this.galleria.containerClass + ' ' : '') + (thumbnailsPosClass ? thumbnailsPosClass + ' ' : '') + (indicatorPosClass ? indicatorPosClass + ' ' : '');\n  }\n  startSlideShow() {\n    if (isPlatformBrowser(this.galleria.platformId)) {\n      this.interval = setInterval(() => {\n        let activeIndex = this.galleria.circular && this.value.length - 1 === this.activeIndex ? 0 : this.activeIndex + 1;\n        this.onActiveIndexChange(activeIndex);\n        this.activeIndex = activeIndex;\n      }, this.galleria.transitionInterval);\n      this.slideShowActive = true;\n    }\n  }\n  stopSlideShow() {\n    if (this.galleria.autoPlay && !this.galleria.shouldStopAutoplayByClick) {\n      return;\n    }\n    if (this.interval) {\n      clearInterval(this.interval);\n    }\n    this.slideShowActive = false;\n  }\n  getPositionClass(preClassName, position) {\n    const positions = ['top', 'left', 'bottom', 'right'];\n    const pos = positions.find(item => item === position);\n    return pos ? `${preClassName}-${pos}` : '';\n  }\n  isVertical() {\n    return this.galleria.thumbnailsPosition === 'left' || this.galleria.thumbnailsPosition === 'right';\n  }\n  onActiveIndexChange(index) {\n    if (this.activeIndex !== index) {\n      this.activeIndex = index;\n      this.activeItemChange.emit(this.activeIndex);\n    }\n  }\n  closeAriaLabel() {\n    return this.config.translation.aria ? this.config.translation.aria.close : undefined;\n  }\n  static ɵfac = function GalleriaContent_Factory(t) {\n    return new (t || GalleriaContent)(i0.ɵɵdirectiveInject(Galleria), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.KeyValueDiffers), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: GalleriaContent,\n    selectors: [[\"p-galleriaContent\"]],\n    viewQuery: function GalleriaContent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c5, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.closeButton = _t.first);\n      }\n    },\n    inputs: {\n      activeIndex: \"activeIndex\",\n      value: \"value\",\n      numVisible: \"numVisible\"\n    },\n    outputs: {\n      maskHide: \"maskHide\",\n      activeItemChange: \"activeItemChange\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[\"pFocusTrap\", \"\", 3, \"ngClass\", \"ngStyle\", \"class\", 4, \"ngIf\"], [\"pFocusTrap\", \"\", 3, \"ngClass\", \"ngStyle\"], [\"type\", \"button\", \"class\", \"p-galleria-close p-link\", \"pRipple\", \"\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"p-galleria-header\", 4, \"ngIf\"], [1, \"p-galleria-content\"], [3, \"onActiveIndexChange\", \"startSlideShow\", \"stopSlideShow\", \"id\", \"value\", \"activeIndex\", \"circular\", \"templates\", \"showIndicators\", \"changeItemOnIndicatorHover\", \"indicatorFacet\", \"captionFacet\", \"showItemNavigators\", \"autoPlay\", \"slideShowActive\"], [3, \"containerId\", \"value\", \"activeIndex\", \"templates\", \"numVisible\", \"responsiveOptions\", \"circular\", \"isVertical\", \"contentHeight\", \"showThumbnailNavigators\", \"slideShowActive\", \"onActiveIndexChange\", \"stopSlideShow\", 4, \"ngIf\"], [\"class\", \"p-galleria-footer\", 4, \"ngIf\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-galleria-close\", \"p-link\", 3, \"click\"], [3, \"styleClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\"], [1, \"p-galleria-header\"], [\"type\", \"header\", 3, \"templates\"], [3, \"onActiveIndexChange\", \"stopSlideShow\", \"containerId\", \"value\", \"activeIndex\", \"templates\", \"numVisible\", \"responsiveOptions\", \"circular\", \"isVertical\", \"contentHeight\", \"showThumbnailNavigators\", \"slideShowActive\"], [1, \"p-galleria-footer\"], [\"type\", \"footer\", 3, \"templates\"]],\n    template: function GalleriaContent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, GalleriaContent_div_0_Template, 7, 28, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.value && ctx.value.length > 0);\n      }\n    },\n    dependencies: () => [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Ripple, TimesIcon, i4.FocusTrap, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GalleriaContent, [{\n    type: Component,\n    args: [{\n      selector: 'p-galleriaContent',\n      template: `\n        <div\n            [attr.id]=\"id\"\n            [attr.role]=\"'region'\"\n            *ngIf=\"value && value.length > 0\"\n            [ngClass]=\"{\n                'p-galleria p-component': true,\n                'p-galleria-fullscreen': this.galleria.fullScreen,\n                'p-galleria-indicator-onitem': this.galleria.showIndicatorsOnItem,\n                'p-galleria-item-nav-onhover': this.galleria.showItemNavigatorsOnHover && !this.galleria.fullScreen\n            }\"\n            [ngStyle]=\"!galleria.fullScreen ? galleria.containerStyle : {}\"\n            [class]=\"galleriaClass()\"\n            pFocusTrap\n        >\n            <button *ngIf=\"galleria.fullScreen\" type=\"button\" class=\"p-galleria-close p-link\" (click)=\"maskHide.emit()\" pRipple [attr.aria-label]=\"closeAriaLabel()\" [attr.data-pc-section]=\"'closebutton'\">\n                <TimesIcon *ngIf=\"!galleria.closeIconTemplate\" [styleClass]=\"'p-galleria-close-icon'\" />\n                <ng-template *ngTemplateOutlet=\"galleria.closeIconTemplate\"></ng-template>\n            </button>\n            <div *ngIf=\"galleria.templates && galleria.headerFacet\" class=\"p-galleria-header\">\n                <p-galleriaItemSlot type=\"header\" [templates]=\"galleria.templates\"></p-galleriaItemSlot>\n            </div>\n            <div class=\"p-galleria-content\" [attr.aria-live]=\"galleria.autoPlay ? 'polite' : 'off'\">\n                <p-galleriaItem\n                    [id]=\"id\"\n                    [value]=\"value\"\n                    [activeIndex]=\"activeIndex\"\n                    [circular]=\"galleria.circular\"\n                    [templates]=\"galleria.templates\"\n                    (onActiveIndexChange)=\"onActiveIndexChange($event)\"\n                    [showIndicators]=\"galleria.showIndicators\"\n                    [changeItemOnIndicatorHover]=\"galleria.changeItemOnIndicatorHover\"\n                    [indicatorFacet]=\"galleria.indicatorFacet\"\n                    [captionFacet]=\"galleria.captionFacet\"\n                    [showItemNavigators]=\"galleria.showItemNavigators\"\n                    [autoPlay]=\"galleria.autoPlay\"\n                    [slideShowActive]=\"slideShowActive\"\n                    (startSlideShow)=\"startSlideShow()\"\n                    (stopSlideShow)=\"stopSlideShow()\"\n                ></p-galleriaItem>\n\n                <p-galleriaThumbnails\n                    *ngIf=\"galleria.showThumbnails\"\n                    [containerId]=\"id\"\n                    [value]=\"value\"\n                    (onActiveIndexChange)=\"onActiveIndexChange($event)\"\n                    [activeIndex]=\"activeIndex\"\n                    [templates]=\"galleria.templates\"\n                    [numVisible]=\"numVisible\"\n                    [responsiveOptions]=\"galleria.responsiveOptions\"\n                    [circular]=\"galleria.circular\"\n                    [isVertical]=\"isVertical()\"\n                    [contentHeight]=\"galleria.verticalThumbnailViewPortHeight\"\n                    [showThumbnailNavigators]=\"galleria.showThumbnailNavigators\"\n                    [slideShowActive]=\"slideShowActive\"\n                    (stopSlideShow)=\"stopSlideShow()\"\n                ></p-galleriaThumbnails>\n            </div>\n            <div *ngIf=\"galleria.templates && galleria.footerFacet\" class=\"p-galleria-footer\">\n                <p-galleriaItemSlot type=\"footer\" [templates]=\"galleria.templates\"></p-galleriaItemSlot>\n            </div>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], () => [{\n    type: Galleria\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.KeyValueDiffers\n  }, {\n    type: i1.PrimeNGConfig\n  }], {\n    activeIndex: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    numVisible: [{\n      type: Input\n    }],\n    maskHide: [{\n      type: Output\n    }],\n    activeItemChange: [{\n      type: Output\n    }],\n    closeButton: [{\n      type: ViewChild,\n      args: ['closeButton']\n    }]\n  });\n})();\nclass GalleriaItemSlot {\n  templates;\n  index;\n  get item() {\n    return this._item;\n  }\n  set item(item) {\n    this._item = item;\n    if (this.templates) {\n      this.templates.forEach(item => {\n        if (item.getType() === this.type) {\n          switch (this.type) {\n            case 'item':\n            case 'caption':\n            case 'thumbnail':\n              this.context = {\n                $implicit: this.item\n              };\n              this.contentTemplate = item.template;\n              break;\n          }\n        }\n      });\n    }\n  }\n  type;\n  contentTemplate;\n  context;\n  _item;\n  ngAfterContentInit() {\n    this.templates?.forEach(item => {\n      if (item.getType() === this.type) {\n        switch (this.type) {\n          case 'item':\n          case 'caption':\n          case 'thumbnail':\n            this.context = {\n              $implicit: this.item\n            };\n            this.contentTemplate = item.template;\n            break;\n          case 'indicator':\n            this.context = {\n              $implicit: this.index\n            };\n            this.contentTemplate = item.template;\n            break;\n          default:\n            this.context = {};\n            this.contentTemplate = item.template;\n            break;\n        }\n      }\n    });\n  }\n  static ɵfac = function GalleriaItemSlot_Factory(t) {\n    return new (t || GalleriaItemSlot)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: GalleriaItemSlot,\n    selectors: [[\"p-galleriaItemSlot\"]],\n    inputs: {\n      templates: \"templates\",\n      index: \"index\",\n      item: \"item\",\n      type: \"type\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"]],\n    template: function GalleriaItemSlot_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, GalleriaItemSlot_ng_container_0_Template, 2, 2, \"ng-container\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.contentTemplate);\n      }\n    },\n    dependencies: [i2.NgIf, i2.NgTemplateOutlet],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GalleriaItemSlot, [{\n    type: Component,\n    args: [{\n      selector: 'p-galleriaItemSlot',\n      template: `\n        <ng-container *ngIf=\"contentTemplate\">\n            <ng-container *ngTemplateOutlet=\"contentTemplate; context: context\"></ng-container>\n        </ng-container>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], null, {\n    templates: [{\n      type: Input\n    }],\n    index: [{\n      type: Input\n    }],\n    item: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }]\n  });\n})();\nclass GalleriaItem {\n  galleria;\n  id;\n  circular = false;\n  value;\n  showItemNavigators = false;\n  showIndicators = true;\n  slideShowActive = true;\n  changeItemOnIndicatorHover = true;\n  autoPlay = false;\n  templates;\n  indicatorFacet;\n  captionFacet;\n  startSlideShow = new EventEmitter();\n  stopSlideShow = new EventEmitter();\n  onActiveIndexChange = new EventEmitter();\n  get activeIndex() {\n    return this._activeIndex;\n  }\n  set activeIndex(activeIndex) {\n    this._activeIndex = activeIndex;\n  }\n  get activeItem() {\n    return this.value && this.value[this._activeIndex];\n  }\n  _activeIndex = 0;\n  constructor(galleria) {\n    this.galleria = galleria;\n  }\n  ngOnChanges({\n    autoPlay\n  }) {\n    if (autoPlay?.currentValue) {\n      this.startSlideShow.emit();\n    }\n    if (autoPlay && autoPlay.currentValue === false) {\n      this.stopTheSlideShow();\n    }\n  }\n  next() {\n    let nextItemIndex = this.activeIndex + 1;\n    let activeIndex = this.circular && this.value.length - 1 === this.activeIndex ? 0 : nextItemIndex;\n    this.onActiveIndexChange.emit(activeIndex);\n  }\n  prev() {\n    let prevItemIndex = this.activeIndex !== 0 ? this.activeIndex - 1 : 0;\n    let activeIndex = this.circular && this.activeIndex === 0 ? this.value.length - 1 : prevItemIndex;\n    this.onActiveIndexChange.emit(activeIndex);\n  }\n  stopTheSlideShow() {\n    if (this.slideShowActive && this.stopSlideShow) {\n      this.stopSlideShow.emit();\n    }\n  }\n  navForward(e) {\n    this.stopTheSlideShow();\n    this.next();\n    if (e && e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  navBackward(e) {\n    this.stopTheSlideShow();\n    this.prev();\n    if (e && e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  onIndicatorClick(index) {\n    this.stopTheSlideShow();\n    this.onActiveIndexChange.emit(index);\n  }\n  onIndicatorMouseEnter(index) {\n    if (this.changeItemOnIndicatorHover) {\n      this.stopTheSlideShow();\n      this.onActiveIndexChange.emit(index);\n    }\n  }\n  onIndicatorKeyDown(event, index) {\n    switch (event.code) {\n      case 'Enter':\n      case 'Space':\n        this.stopTheSlideShow();\n        this.onActiveIndexChange.emit(index);\n        event.preventDefault();\n        break;\n      case 'ArrowDown':\n      case 'ArrowUp':\n        event.preventDefault();\n        break;\n      default:\n        break;\n    }\n  }\n  isNavForwardDisabled() {\n    return !this.circular && this.activeIndex === this.value.length - 1;\n  }\n  isNavBackwardDisabled() {\n    return !this.circular && this.activeIndex === 0;\n  }\n  isIndicatorItemActive(index) {\n    return this.activeIndex === index;\n  }\n  ariaSlideLabel() {\n    return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.slide : undefined;\n  }\n  ariaSlideNumber(value) {\n    return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.slideNumber.replace(/{slideNumber}/g, value) : undefined;\n  }\n  ariaPageLabel(value) {\n    return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.pageLabel.replace(/{page}/g, value) : undefined;\n  }\n  static ɵfac = function GalleriaItem_Factory(t) {\n    return new (t || GalleriaItem)(i0.ɵɵdirectiveInject(Galleria));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: GalleriaItem,\n    selectors: [[\"p-galleriaItem\"]],\n    inputs: {\n      id: \"id\",\n      circular: \"circular\",\n      value: \"value\",\n      showItemNavigators: \"showItemNavigators\",\n      showIndicators: \"showIndicators\",\n      slideShowActive: \"slideShowActive\",\n      changeItemOnIndicatorHover: \"changeItemOnIndicatorHover\",\n      autoPlay: \"autoPlay\",\n      templates: \"templates\",\n      indicatorFacet: \"indicatorFacet\",\n      captionFacet: \"captionFacet\",\n      activeIndex: \"activeIndex\"\n    },\n    outputs: {\n      startSlideShow: \"startSlideShow\",\n      stopSlideShow: \"stopSlideShow\",\n      onActiveIndexChange: \"onActiveIndexChange\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 8,\n    vars: 11,\n    consts: [[1, \"p-galleria-item-wrapper\"], [1, \"p-galleria-item-container\"], [\"type\", \"button\", \"role\", \"navigation\", \"pRipple\", \"\", 3, \"ngClass\", \"disabled\", \"click\", 4, \"ngIf\"], [\"role\", \"group\", 3, \"id\"], [\"type\", \"item\", 1, \"p-galleria-item\", 3, \"item\", \"templates\"], [\"type\", \"button\", \"pRipple\", \"\", \"role\", \"navigation\", 3, \"ngClass\", \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"p-galleria-caption\", 4, \"ngIf\"], [\"class\", \"p-galleria-indicators p-reset\", 4, \"ngIf\"], [\"type\", \"button\", \"role\", \"navigation\", \"pRipple\", \"\", 3, \"click\", \"ngClass\", \"disabled\"], [3, \"styleClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\"], [\"type\", \"button\", \"pRipple\", \"\", \"role\", \"navigation\", 3, \"click\", \"ngClass\", \"disabled\"], [1, \"p-galleria-caption\"], [\"type\", \"caption\", 3, \"item\", \"templates\"], [1, \"p-galleria-indicators\", \"p-reset\"], [\"tabindex\", \"0\", 3, \"ngClass\", \"click\", \"mouseenter\", \"keydown\", 4, \"ngFor\", \"ngForOf\"], [\"tabindex\", \"0\", 3, \"click\", \"mouseenter\", \"keydown\", \"ngClass\"], [\"type\", \"button\", \"tabIndex\", \"-1\", \"class\", \"p-link\", 4, \"ngIf\"], [\"type\", \"indicator\", 3, \"index\", \"templates\"], [\"type\", \"button\", \"tabIndex\", \"-1\", 1, \"p-link\"]],\n    template: function GalleriaItem_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵtemplate(2, GalleriaItem_button_2_Template, 3, 6, \"button\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3);\n        i0.ɵɵelement(4, \"p-galleriaItemSlot\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, GalleriaItem_button_5_Template, 3, 6, \"button\", 5)(6, GalleriaItem_div_6_Template, 2, 2, \"div\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(7, GalleriaItem_ul_7_Template, 2, 1, \"ul\", 7);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.showItemNavigators);\n        i0.ɵɵadvance();\n        i0.ɵɵstyleProp(\"width\", \"100%\");\n        i0.ɵɵproperty(\"id\", ctx.id + \"_item_\" + ctx.activeIndex);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaSlideNumber(ctx.activeIndex + 1))(\"aria-roledescription\", ctx.ariaSlideLabel());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"item\", ctx.activeItem)(\"templates\", ctx.templates);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showItemNavigators);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.captionFacet);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showIndicators);\n      }\n    },\n    dependencies: () => [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i3.Ripple, ChevronRightIcon, ChevronLeftIcon, GalleriaItemSlot],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GalleriaItem, [{\n    type: Component,\n    args: [{\n      selector: 'p-galleriaItem',\n      template: `\n        <div class=\"p-galleria-item-wrapper\">\n            <div class=\"p-galleria-item-container\">\n                <button\n                    *ngIf=\"showItemNavigators\"\n                    type=\"button\"\n                    role=\"navigation\"\n                    [ngClass]=\"{ 'p-galleria-item-prev p-galleria-item-nav p-link': true, 'p-disabled': this.isNavBackwardDisabled() }\"\n                    (click)=\"navBackward($event)\"\n                    [disabled]=\"isNavBackwardDisabled()\"\n                    pRipple\n                >\n                    <ChevronLeftIcon *ngIf=\"!galleria.itemPreviousIconTemplate\" [styleClass]=\"'p-galleria-item-prev-icon'\" />\n                    <ng-template *ngTemplateOutlet=\"galleria.itemPreviousIconTemplate\"></ng-template>\n                </button>\n                <div [id]=\"id + '_item_' + activeIndex\" role=\"group\" [attr.aria-label]=\"ariaSlideNumber(activeIndex + 1)\" [attr.aria-roledescription]=\"ariaSlideLabel()\" [style.width]=\"'100%'\">\n                    <p-galleriaItemSlot type=\"item\" [item]=\"activeItem\" [templates]=\"templates\" class=\"p-galleria-item\"></p-galleriaItemSlot>\n                </div>\n                <button\n                    *ngIf=\"showItemNavigators\"\n                    type=\"button\"\n                    [ngClass]=\"{ 'p-galleria-item-next p-galleria-item-nav p-link': true, 'p-disabled': this.isNavForwardDisabled() }\"\n                    (click)=\"navForward($event)\"\n                    [disabled]=\"isNavForwardDisabled()\"\n                    pRipple\n                    role=\"navigation\"\n                >\n                    <ChevronRightIcon *ngIf=\"!galleria.itemNextIconTemplate\" [styleClass]=\"'p-galleria-item-next-icon'\" />\n                    <ng-template *ngTemplateOutlet=\"galleria.itemNextIconTemplate\"></ng-template>\n                </button>\n                <div class=\"p-galleria-caption\" *ngIf=\"captionFacet\">\n                    <p-galleriaItemSlot type=\"caption\" [item]=\"activeItem\" [templates]=\"templates\"></p-galleriaItemSlot>\n                </div>\n            </div>\n            <ul *ngIf=\"showIndicators\" class=\"p-galleria-indicators p-reset\">\n                <li\n                    *ngFor=\"let item of value; let index = index\"\n                    tabindex=\"0\"\n                    (click)=\"onIndicatorClick(index)\"\n                    (mouseenter)=\"onIndicatorMouseEnter(index)\"\n                    (keydown)=\"onIndicatorKeyDown($event, index)\"\n                    [ngClass]=\"{ 'p-galleria-indicator': true, 'p-highlight': isIndicatorItemActive(index) }\"\n                    [attr.aria-label]=\"ariaPageLabel(index + 1)\"\n                    [attr.aria-selected]=\"activeIndex === index\"\n                    [attr.aria-controls]=\"id + '_item_' + index\"\n                >\n                    <button type=\"button\" tabIndex=\"-1\" class=\"p-link\" *ngIf=\"!indicatorFacet\"></button>\n                    <p-galleriaItemSlot type=\"indicator\" [index]=\"index\" [templates]=\"templates\"></p-galleriaItemSlot>\n                </li>\n            </ul>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], () => [{\n    type: Galleria\n  }], {\n    id: [{\n      type: Input\n    }],\n    circular: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    showItemNavigators: [{\n      type: Input\n    }],\n    showIndicators: [{\n      type: Input\n    }],\n    slideShowActive: [{\n      type: Input\n    }],\n    changeItemOnIndicatorHover: [{\n      type: Input\n    }],\n    autoPlay: [{\n      type: Input\n    }],\n    templates: [{\n      type: Input\n    }],\n    indicatorFacet: [{\n      type: Input\n    }],\n    captionFacet: [{\n      type: Input\n    }],\n    startSlideShow: [{\n      type: Output\n    }],\n    stopSlideShow: [{\n      type: Output\n    }],\n    onActiveIndexChange: [{\n      type: Output\n    }],\n    activeIndex: [{\n      type: Input\n    }]\n  });\n})();\nclass GalleriaThumbnails {\n  galleria;\n  document;\n  platformId;\n  renderer;\n  cd;\n  containerId;\n  value;\n  isVertical = false;\n  slideShowActive = false;\n  circular = false;\n  responsiveOptions;\n  contentHeight = '300px';\n  showThumbnailNavigators = true;\n  templates;\n  onActiveIndexChange = new EventEmitter();\n  stopSlideShow = new EventEmitter();\n  itemsContainer;\n  get numVisible() {\n    return this._numVisible;\n  }\n  set numVisible(numVisible) {\n    this._numVisible = numVisible;\n    this._oldNumVisible = this.d_numVisible;\n    this.d_numVisible = numVisible;\n  }\n  get activeIndex() {\n    return this._activeIndex;\n  }\n  set activeIndex(activeIndex) {\n    this._oldactiveIndex = this._activeIndex;\n    this._activeIndex = activeIndex;\n  }\n  index;\n  startPos = null;\n  thumbnailsStyle = null;\n  sortedResponsiveOptions = null;\n  totalShiftedItems = 0;\n  page = 0;\n  documentResizeListener;\n  _numVisible = 0;\n  d_numVisible = 0;\n  _oldNumVisible = 0;\n  _activeIndex = 0;\n  _oldactiveIndex = 0;\n  constructor(galleria, document, platformId, renderer, cd) {\n    this.galleria = galleria;\n    this.document = document;\n    this.platformId = platformId;\n    this.renderer = renderer;\n    this.cd = cd;\n  }\n  ngOnInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.createStyle();\n      if (this.responsiveOptions) {\n        this.bindDocumentListeners();\n      }\n    }\n  }\n  ngAfterContentChecked() {\n    let totalShiftedItems = this.totalShiftedItems;\n    if ((this._oldNumVisible !== this.d_numVisible || this._oldactiveIndex !== this._activeIndex) && this.itemsContainer) {\n      if (this._activeIndex <= this.getMedianItemIndex()) {\n        totalShiftedItems = 0;\n      } else if (this.value.length - this.d_numVisible + this.getMedianItemIndex() < this._activeIndex) {\n        totalShiftedItems = this.d_numVisible - this.value.length;\n      } else if (this.value.length - this.d_numVisible < this._activeIndex && this.d_numVisible % 2 === 0) {\n        totalShiftedItems = this._activeIndex * -1 + this.getMedianItemIndex() + 1;\n      } else {\n        totalShiftedItems = this._activeIndex * -1 + this.getMedianItemIndex();\n      }\n      if (totalShiftedItems !== this.totalShiftedItems) {\n        this.totalShiftedItems = totalShiftedItems;\n      }\n      if (this.itemsContainer && this.itemsContainer.nativeElement) {\n        this.itemsContainer.nativeElement.style.transform = this.isVertical ? `translate3d(0, ${totalShiftedItems * (100 / this.d_numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this.d_numVisible)}%, 0, 0)`;\n      }\n      if (this._oldactiveIndex !== this._activeIndex) {\n        DomHandler.removeClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n        this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n      }\n      this._oldactiveIndex = this._activeIndex;\n      this._oldNumVisible = this.d_numVisible;\n    }\n  }\n  ngAfterViewInit() {\n    if (platformBrowser(this.platformId)) {\n      this.calculatePosition();\n    }\n  }\n  createStyle() {\n    if (!this.thumbnailsStyle) {\n      this.thumbnailsStyle = this.document.createElement('style');\n      this.document.body.appendChild(this.thumbnailsStyle);\n    }\n    let innerHTML = `\n            #${this.containerId} .p-galleria-thumbnail-item {\n                flex: 1 0 ${100 / this.d_numVisible}%\n            }\n        `;\n    if (this.responsiveOptions) {\n      this.sortedResponsiveOptions = [...this.responsiveOptions];\n      this.sortedResponsiveOptions.sort((data1, data2) => {\n        const value1 = data1.breakpoint;\n        const value2 = data2.breakpoint;\n        let result = null;\n        if (value1 == null && value2 != null) result = -1;else if (value1 != null && value2 == null) result = 1;else if (value1 == null && value2 == null) result = 0;else if (typeof value1 === 'string' && typeof value2 === 'string') result = value1.localeCompare(value2, undefined, {\n          numeric: true\n        });else result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n        return -1 * result;\n      });\n      for (let i = 0; i < this.sortedResponsiveOptions.length; i++) {\n        let res = this.sortedResponsiveOptions[i];\n        innerHTML += `\n                    @media screen and (max-width: ${res.breakpoint}) {\n                        #${this.containerId} .p-galleria-thumbnail-item {\n                            flex: 1 0 ${100 / res.numVisible}%\n                        }\n                    }\n                `;\n      }\n    }\n    this.thumbnailsStyle.innerHTML = innerHTML;\n  }\n  calculatePosition() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (this.itemsContainer && this.sortedResponsiveOptions) {\n        let windowWidth = window.innerWidth;\n        let matchedResponsiveData = {\n          numVisible: this._numVisible\n        };\n        for (let i = 0; i < this.sortedResponsiveOptions.length; i++) {\n          let res = this.sortedResponsiveOptions[i];\n          if (parseInt(res.breakpoint, 10) >= windowWidth) {\n            matchedResponsiveData = res;\n          }\n        }\n        if (this.d_numVisible !== matchedResponsiveData.numVisible) {\n          this.d_numVisible = matchedResponsiveData.numVisible;\n          this.cd.markForCheck();\n        }\n      }\n    }\n  }\n  getTabIndex(index) {\n    return this.isItemActive(index) ? 0 : null;\n  }\n  navForward(e) {\n    this.stopTheSlideShow();\n    let nextItemIndex = this._activeIndex + 1;\n    if (nextItemIndex + this.totalShiftedItems > this.getMedianItemIndex() && (-1 * this.totalShiftedItems < this.getTotalPageNumber() - 1 || this.circular)) {\n      this.step(-1);\n    }\n    let activeIndex = this.circular && this.value.length - 1 === this._activeIndex ? 0 : nextItemIndex;\n    this.onActiveIndexChange.emit(activeIndex);\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  navBackward(e) {\n    this.stopTheSlideShow();\n    let prevItemIndex = this._activeIndex !== 0 ? this._activeIndex - 1 : 0;\n    let diff = prevItemIndex + this.totalShiftedItems;\n    if (this.d_numVisible - diff - 1 > this.getMedianItemIndex() && (-1 * this.totalShiftedItems !== 0 || this.circular)) {\n      this.step(1);\n    }\n    let activeIndex = this.circular && this._activeIndex === 0 ? this.value.length - 1 : prevItemIndex;\n    this.onActiveIndexChange.emit(activeIndex);\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  onItemClick(index) {\n    this.stopTheSlideShow();\n    let selectedItemIndex = index;\n    if (selectedItemIndex !== this._activeIndex) {\n      const diff = selectedItemIndex + this.totalShiftedItems;\n      let dir = 0;\n      if (selectedItemIndex < this._activeIndex) {\n        dir = this.d_numVisible - diff - 1 - this.getMedianItemIndex();\n        if (dir > 0 && -1 * this.totalShiftedItems !== 0) {\n          this.step(dir);\n        }\n      } else {\n        dir = this.getMedianItemIndex() - diff;\n        if (dir < 0 && -1 * this.totalShiftedItems < this.getTotalPageNumber() - 1) {\n          this.step(dir);\n        }\n      }\n      this.activeIndex = selectedItemIndex;\n      this.onActiveIndexChange.emit(this.activeIndex);\n    }\n  }\n  onThumbnailKeydown(event, index) {\n    if (event.code === 'Enter' || event.code === 'Space') {\n      this.onItemClick(index);\n      event.preventDefault();\n    }\n    switch (event.code) {\n      case 'ArrowRight':\n        this.onRightKey();\n        break;\n      case 'ArrowLeft':\n        this.onLeftKey();\n        break;\n      case 'Home':\n        this.onHomeKey();\n        event.preventDefault();\n        break;\n      case 'End':\n        this.onEndKey();\n        event.preventDefault();\n        break;\n      case 'ArrowUp':\n      case 'ArrowDown':\n        event.preventDefault();\n        break;\n      case 'Tab':\n        this.onTabKey();\n        break;\n      default:\n        break;\n    }\n  }\n  onRightKey() {\n    const indicators = DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]');\n    const activeIndex = this.findFocusedIndicatorIndex();\n    this.changedFocusedIndicator(activeIndex, activeIndex + 1 === indicators.length ? indicators.length - 1 : activeIndex + 1);\n  }\n  onLeftKey() {\n    const activeIndex = this.findFocusedIndicatorIndex();\n    this.changedFocusedIndicator(activeIndex, activeIndex - 1 <= 0 ? 0 : activeIndex - 1);\n  }\n  onHomeKey() {\n    const activeIndex = this.findFocusedIndicatorIndex();\n    this.changedFocusedIndicator(activeIndex, 0);\n  }\n  onEndKey() {\n    const indicators = DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]');\n    const activeIndex = this.findFocusedIndicatorIndex();\n    this.changedFocusedIndicator(activeIndex, indicators.length - 1);\n  }\n  onTabKey() {\n    const indicators = [...DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]')];\n    const highlightedIndex = indicators.findIndex(ind => DomHandler.getAttribute(ind, 'data-p-active') === true);\n    const activeIndicator = DomHandler.findSingle(this.itemsContainer.nativeElement, '[tabindex=\"0\"]');\n    const activeIndex = indicators.findIndex(ind => ind === activeIndicator.parentElement);\n    indicators[activeIndex].children[0].tabIndex = '-1';\n    indicators[highlightedIndex].children[0].tabIndex = '0';\n  }\n  findFocusedIndicatorIndex() {\n    const indicators = [...DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]')];\n    const activeIndicator = DomHandler.findSingle(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"] > [tabindex=\"0\"]');\n    return indicators.findIndex(ind => ind === activeIndicator.parentElement);\n  }\n  changedFocusedIndicator(prevInd, nextInd) {\n    const indicators = DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]');\n    indicators[prevInd].children[0].tabIndex = '-1';\n    indicators[nextInd].children[0].tabIndex = '0';\n    indicators[nextInd].children[0].focus();\n  }\n  step(dir) {\n    let totalShiftedItems = this.totalShiftedItems + dir;\n    if (dir < 0 && -1 * totalShiftedItems + this.d_numVisible > this.value.length - 1) {\n      totalShiftedItems = this.d_numVisible - this.value.length;\n    } else if (dir > 0 && totalShiftedItems > 0) {\n      totalShiftedItems = 0;\n    }\n    if (this.circular) {\n      if (dir < 0 && this.value.length - 1 === this._activeIndex) {\n        totalShiftedItems = 0;\n      } else if (dir > 0 && this._activeIndex === 0) {\n        totalShiftedItems = this.d_numVisible - this.value.length;\n      }\n    }\n    if (this.itemsContainer) {\n      DomHandler.removeClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n      this.itemsContainer.nativeElement.style.transform = this.isVertical ? `translate3d(0, ${totalShiftedItems * (100 / this.d_numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this.d_numVisible)}%, 0, 0)`;\n      this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n    }\n    this.totalShiftedItems = totalShiftedItems;\n  }\n  stopTheSlideShow() {\n    if (this.slideShowActive && this.stopSlideShow) {\n      this.stopSlideShow.emit();\n    }\n  }\n  changePageOnTouch(e, diff) {\n    if (diff < 0) {\n      // left\n      this.navForward(e);\n    } else {\n      // right\n      this.navBackward(e);\n    }\n  }\n  getTotalPageNumber() {\n    return this.value.length > this.d_numVisible ? this.value.length - this.d_numVisible + 1 : 0;\n  }\n  getMedianItemIndex() {\n    let index = Math.floor(this.d_numVisible / 2);\n    return this.d_numVisible % 2 ? index : index - 1;\n  }\n  onTransitionEnd() {\n    if (this.itemsContainer && this.itemsContainer.nativeElement) {\n      DomHandler.addClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n      this.itemsContainer.nativeElement.style.transition = '';\n    }\n  }\n  onTouchEnd(e) {\n    let touchobj = e.changedTouches[0];\n    if (this.isVertical) {\n      this.changePageOnTouch(e, touchobj.pageY - this.startPos.y);\n    } else {\n      this.changePageOnTouch(e, touchobj.pageX - this.startPos.x);\n    }\n  }\n  onTouchMove(e) {\n    if (e.cancelable) {\n      e.preventDefault();\n    }\n  }\n  onTouchStart(e) {\n    let touchobj = e.changedTouches[0];\n    this.startPos = {\n      x: touchobj.pageX,\n      y: touchobj.pageY\n    };\n  }\n  isNavBackwardDisabled() {\n    return !this.circular && this._activeIndex === 0 || this.value.length <= this.d_numVisible;\n  }\n  isNavForwardDisabled() {\n    return !this.circular && this._activeIndex === this.value.length - 1 || this.value.length <= this.d_numVisible;\n  }\n  firstItemAciveIndex() {\n    return this.totalShiftedItems * -1;\n  }\n  lastItemActiveIndex() {\n    return this.firstItemAciveIndex() + this.d_numVisible - 1;\n  }\n  isItemActive(index) {\n    return this.firstItemAciveIndex() <= index && this.lastItemActiveIndex() >= index;\n  }\n  bindDocumentListeners() {\n    if (isPlatformBrowser(this.platformId)) {\n      const window = this.document.defaultView || 'window';\n      this.documentResizeListener = this.renderer.listen(window, 'resize', () => {\n        this.calculatePosition();\n      });\n    }\n  }\n  unbindDocumentListeners() {\n    if (this.documentResizeListener) {\n      this.documentResizeListener();\n      this.documentResizeListener = null;\n    }\n  }\n  ngOnDestroy() {\n    if (this.responsiveOptions) {\n      this.unbindDocumentListeners();\n    }\n    if (this.thumbnailsStyle) {\n      this.thumbnailsStyle.parentNode?.removeChild(this.thumbnailsStyle);\n    }\n  }\n  ariaPrevButtonLabel() {\n    return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.prevPageLabel : undefined;\n  }\n  ariaNextButtonLabel() {\n    return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.nextPageLabel : undefined;\n  }\n  ariaPageLabel(value) {\n    return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.pageLabel.replace(/{page}/g, value) : undefined;\n  }\n  static ɵfac = function GalleriaThumbnails_Factory(t) {\n    return new (t || GalleriaThumbnails)(i0.ɵɵdirectiveInject(Galleria), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: GalleriaThumbnails,\n    selectors: [[\"p-galleriaThumbnails\"]],\n    viewQuery: function GalleriaThumbnails_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c11, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.itemsContainer = _t.first);\n      }\n    },\n    inputs: {\n      containerId: \"containerId\",\n      value: \"value\",\n      isVertical: \"isVertical\",\n      slideShowActive: \"slideShowActive\",\n      circular: \"circular\",\n      responsiveOptions: \"responsiveOptions\",\n      contentHeight: \"contentHeight\",\n      showThumbnailNavigators: \"showThumbnailNavigators\",\n      templates: \"templates\",\n      numVisible: \"numVisible\",\n      activeIndex: \"activeIndex\"\n    },\n    outputs: {\n      onActiveIndexChange: \"onActiveIndexChange\",\n      stopSlideShow: \"stopSlideShow\"\n    },\n    decls: 8,\n    vars: 6,\n    consts: [[\"itemsContainer\", \"\"], [1, \"p-galleria-thumbnail-wrapper\"], [1, \"p-galleria-thumbnail-container\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"ngClass\", \"disabled\", \"click\", 4, \"ngIf\"], [1, \"p-galleria-thumbnail-items-container\", 3, \"ngStyle\"], [\"role\", \"tablist\", 1, \"p-galleria-thumbnail-items\", 3, \"transitionend\", \"touchstart\", \"touchmove\"], [3, \"ngClass\", \"keydown\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"pRipple\", \"\", 3, \"click\", \"ngClass\", \"disabled\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [3, \"keydown\", \"ngClass\"], [1, \"p-galleria-thumbnail-item-content\", 3, \"click\", \"touchend\", \"keydown.enter\"], [\"type\", \"thumbnail\", 3, \"item\", \"templates\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"]],\n    template: function GalleriaThumbnails_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n        i0.ɵɵtemplate(2, GalleriaThumbnails_button_2_Template, 3, 7, \"button\", 3);\n        i0.ɵɵelementStart(3, \"div\", 4)(4, \"div\", 5, 0);\n        i0.ɵɵlistener(\"transitionend\", function GalleriaThumbnails_Template_div_transitionend_4_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onTransitionEnd());\n        })(\"touchstart\", function GalleriaThumbnails_Template_div_touchstart_4_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onTouchStart($event));\n        })(\"touchmove\", function GalleriaThumbnails_Template_div_touchmove_4_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onTouchMove($event));\n        });\n        i0.ɵɵtemplate(6, GalleriaThumbnails_div_6_Template, 3, 15, \"div\", 6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(7, GalleriaThumbnails_button_7_Template, 3, 7, \"button\", 3);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.showThumbnailNavigators);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(4, _c12, ctx.isVertical ? ctx.contentHeight : \"\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.value);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showThumbnailNavigators);\n      }\n    },\n    dependencies: () => [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.Ripple, ChevronRightIcon, ChevronLeftIcon, GalleriaItemSlot],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GalleriaThumbnails, [{\n    type: Component,\n    args: [{\n      selector: 'p-galleriaThumbnails',\n      template: `\n        <div class=\"p-galleria-thumbnail-wrapper\">\n            <div class=\"p-galleria-thumbnail-container\">\n                <button\n                    *ngIf=\"showThumbnailNavigators\"\n                    type=\"button\"\n                    [ngClass]=\"{ 'p-galleria-thumbnail-prev p-link': true, 'p-disabled': this.isNavBackwardDisabled() }\"\n                    (click)=\"navBackward($event)\"\n                    [disabled]=\"isNavBackwardDisabled()\"\n                    pRipple\n                    [attr.aria-label]=\"ariaPrevButtonLabel()\"\n                >\n                    <ng-container *ngIf=\"!galleria.previousThumbnailIconTemplate\">\n                        <ChevronLeftIcon *ngIf=\"!isVertical\" [styleClass]=\"'p-galleria-thumbnail-prev-icon'\" />\n                        <ChevronUpIcon *ngIf=\"isVertical\" [styleClass]=\"'p-galleria-thumbnail-prev-icon'\" />\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"galleria.previousThumbnailIconTemplate\"></ng-template>\n                </button>\n                <div class=\"p-galleria-thumbnail-items-container\" [ngStyle]=\"{ height: isVertical ? contentHeight : '' }\">\n                    <div #itemsContainer class=\"p-galleria-thumbnail-items\" (transitionend)=\"onTransitionEnd()\" (touchstart)=\"onTouchStart($event)\" (touchmove)=\"onTouchMove($event)\" role=\"tablist\">\n                        <div\n                            *ngFor=\"let item of value; let index = index\"\n                            [ngClass]=\"{\n                                'p-galleria-thumbnail-item': true,\n                                'p-galleria-thumbnail-item-current': activeIndex === index,\n                                'p-galleria-thumbnail-item-active': isItemActive(index),\n                                'p-galleria-thumbnail-item-start': firstItemAciveIndex() === index,\n                                'p-galleria-thumbnail-item-end': lastItemActiveIndex() === index\n                            }\"\n                            [attr.aria-selected]=\"activeIndex === index\"\n                            [attr.aria-controls]=\"containerId + '_item_' + index\"\n                            [attr.data-pc-section]=\"'thumbnailitem'\"\n                            [attr.data-p-active]=\"activeIndex === index\"\n                            (keydown)=\"onThumbnailKeydown($event, index)\"\n                        >\n                            <div\n                                class=\"p-galleria-thumbnail-item-content\"\n                                [attr.tabindex]=\"activeIndex === index ? 0 : -1\"\n                                [attr.aria-current]=\"activeIndex === index ? 'page' : undefined\"\n                                [attr.aria-label]=\"ariaPageLabel(index + 1)\"\n                                (click)=\"onItemClick(index)\"\n                                (touchend)=\"onItemClick(index)\"\n                                (keydown.enter)=\"onItemClick(index)\"\n                            >\n                                <p-galleriaItemSlot type=\"thumbnail\" [item]=\"item\" [templates]=\"templates\"></p-galleriaItemSlot>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                <button\n                    *ngIf=\"showThumbnailNavigators\"\n                    type=\"button\"\n                    [ngClass]=\"{ 'p-galleria-thumbnail-next p-link': true, 'p-disabled': this.isNavForwardDisabled() }\"\n                    (click)=\"navForward($event)\"\n                    [disabled]=\"isNavForwardDisabled()\"\n                    pRipple\n                    [attr.aria-label]=\"ariaNextButtonLabel()\"\n                >\n                    <ng-container *ngIf=\"!galleria.nextThumbnailIconTemplate\">\n                        <ChevronRightIcon *ngIf=\"!isVertical\" [ngClass]=\"'p-galleria-thumbnail-next-icon'\" />\n                        <ChevronDownIcon *ngIf=\"isVertical\" [ngClass]=\"'p-galleria-thumbnail-next-icon'\" />\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"galleria.nextThumbnailIconTemplate\"></ng-template>\n                </button>\n            </div>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush\n    }]\n  }], () => [{\n    type: Galleria\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    containerId: [{\n      type: Input\n    }],\n    value: [{\n      type: Input\n    }],\n    isVertical: [{\n      type: Input\n    }],\n    slideShowActive: [{\n      type: Input\n    }],\n    circular: [{\n      type: Input\n    }],\n    responsiveOptions: [{\n      type: Input\n    }],\n    contentHeight: [{\n      type: Input\n    }],\n    showThumbnailNavigators: [{\n      type: Input\n    }],\n    templates: [{\n      type: Input\n    }],\n    onActiveIndexChange: [{\n      type: Output\n    }],\n    stopSlideShow: [{\n      type: Output\n    }],\n    itemsContainer: [{\n      type: ViewChild,\n      args: ['itemsContainer']\n    }],\n    numVisible: [{\n      type: Input\n    }],\n    activeIndex: [{\n      type: Input\n    }]\n  });\n})();\nclass GalleriaModule {\n  static ɵfac = function GalleriaModule_Factory(t) {\n    return new (t || GalleriaModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: GalleriaModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule, RippleModule, TimesIcon, ChevronRightIcon, ChevronLeftIcon, WindowMaximizeIcon, WindowMinimizeIcon, FocusTrapModule, CommonModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(GalleriaModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, SharedModule, RippleModule, TimesIcon, ChevronRightIcon, ChevronLeftIcon, WindowMaximizeIcon, WindowMinimizeIcon, FocusTrapModule],\n      exports: [CommonModule, Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails, SharedModule],\n      declarations: [Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Galleria, GalleriaContent, GalleriaItem, GalleriaItemSlot, GalleriaModule, GalleriaThumbnails };", "map": {"version": 3, "names": ["style", "animate", "transition", "trigger", "i2", "DOCUMENT", "isPlatformBrowser", "CommonModule", "i0", "EventEmitter", "PLATFORM_ID", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ViewChild", "ContentChildren", "NgModule", "i1", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "ChevronLeftIcon", "ChevronRightIcon", "TimesIcon", "WindowMaximizeIcon", "WindowMinimizeIcon", "i3", "RippleModule", "ZIndexUtils", "UniqueComponentId", "i4", "FocusTrapModule", "platformBrowser", "_c0", "_c1", "_c2", "a0", "_c3", "a1", "showTransitionParams", "hideTransitionParams", "_c4", "value", "params", "Galleria_div_0_div_2_p_galleriaContent_2_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "Galleria_div_0_div_2_p_galleriaContent_2_Template_p_galleriaContent_animation_animation_start_0_listener", "$event", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onAnimationStart", "Galleria_div_0_div_2_p_galleriaContent_2_Template_p_galleriaContent_animation_animation_done_0_listener", "onAnimationEnd", "Galleria_div_0_div_2_p_galleriaContent_2_Template_p_galleriaContent_maskHide_0_listener", "onMaskHide", "Galleria_div_0_div_2_p_galleriaContent_2_Template_p_galleriaContent_activeItemChange_0_listener", "onActiveItemChange", "ɵɵelementEnd", "ɵɵproperty", "ɵɵpureFunction1", "ɵɵpureFunction2", "showTransitionOptions", "hideTransitionOptions", "activeIndex", "numVisibleLimit", "numVisible", "containerStyle", "Galleria_div_0_div_2_Template", "ɵɵtemplate", "ɵɵclassMap", "maskClass", "visible", "ɵɵattribute", "fullScreen", "undefined", "ɵɵadvance", "Galleria_div_0_Template", "maskVisible", "Galleria_ng_template_1_Template", "_r3", "Galleria_ng_template_1_Template_p_galleriaContent_activeItemChange_0_listener", "_c5", "_c6", "a2", "_c7", "GalleriaContent_div_0_button_1_TimesIcon_1_Template", "ɵɵelement", "GalleriaContent_div_0_button_1_2_ng_template_0_Template", "GalleriaContent_div_0_button_1_2_Template", "GalleriaContent_div_0_button_1_Template", "_r2", "GalleriaContent_div_0_button_1_Template_button_click_0_listener", "ctx_r2", "maskHide", "emit", "closeAriaLabel", "galleria", "closeIconTemplate", "GalleriaContent_div_0_div_2_Template", "templates", "GalleriaContent_div_0_p_galleriaThumbnails_5_Template", "_r4", "GalleriaContent_div_0_p_galleriaThumbnails_5_Template_p_galleriaThumbnails_onActiveIndexChange_0_listener", "onActiveIndexChange", "GalleriaContent_div_0_p_galleriaThumbnails_5_Template_p_galleriaThumbnails_stopSlideShow_0_listener", "stopSlideShow", "id", "responsiveOptions", "circular", "isVertical", "verticalThumbnailViewPortHeight", "showThumbnailNavigators", "slideShowActive", "GalleriaContent_div_0_div_6_Template", "GalleriaContent_div_0_Template", "GalleriaContent_div_0_Template_p_galleriaItem_onActiveIndexChange_4_listener", "GalleriaContent_div_0_Template_p_galleriaItem_startSlideShow_4_listener", "startSlideShow", "GalleriaContent_div_0_Template_p_galleriaItem_stopSlideShow_4_listener", "galleriaClass", "ɵɵpureFunction3", "showIndicatorsOnItem", "showItemNavigatorsOnHover", "ɵɵpureFunction0", "headerFacet", "autoPlay", "showIndicators", "changeItemOnIndicatorHover", "indicatorFacet", "captionFacet", "showItemNavigators", "showThumbnails", "footer<PERSON><PERSON><PERSON>", "GalleriaItemSlot_ng_container_0_ng_container_1_Template", "ɵɵelementContainer", "GalleriaItemSlot_ng_container_0_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ctx_r0", "contentTemplate", "context", "_c8", "_c9", "_c10", "GalleriaItem_button_2_ChevronLeftIcon_1_Template", "GalleriaItem_button_2_2_ng_template_0_Template", "GalleriaItem_button_2_2_Template", "GalleriaItem_button_2_Template", "GalleriaItem_button_2_Template_button_click_0_listener", "navBackward", "isNavBackwardDisabled", "itemPreviousIconTemplate", "GalleriaItem_button_5_ChevronRightIcon_1_Template", "GalleriaItem_button_5_2_ng_template_0_Template", "GalleriaItem_button_5_2_Template", "GalleriaItem_button_5_Template", "GalleriaItem_button_5_Template_button_click_0_listener", "navForward", "isNavForwardDisabled", "itemNextIconTemplate", "GalleriaItem_div_6_Template", "activeItem", "GalleriaItem_ul_7_li_1_button_1_Template", "GalleriaItem_ul_7_li_1_Template", "GalleriaItem_ul_7_li_1_Template_li_click_0_listener", "index_r5", "index", "onIndicatorClick", "GalleriaItem_ul_7_li_1_Template_li_mouseenter_0_listener", "onIndicatorMouseEnter", "GalleriaItem_ul_7_li_1_Template_li_keydown_0_listener", "onIndicatorKeyDown", "isIndicatorItemActive", "ariaPageLabel", "GalleriaItem_ul_7_Template", "_c11", "_c12", "height", "_c13", "_c14", "a3", "_c15", "GalleriaThumbnails_button_2_ng_container_1_ChevronLeftIcon_1_Template", "GalleriaThumbnails_button_2_ng_container_1_ChevronUpIcon_2_Template", "GalleriaThumbnails_button_2_ng_container_1_Template", "GalleriaThumbnails_button_2_2_ng_template_0_Template", "GalleriaThumbnails_button_2_2_Template", "GalleriaThumbnails_button_2_Template", "GalleriaThumbnails_button_2_Template_button_click_0_listener", "ariaPrevButtonLabel", "previousThumbnailIconTemplate", "GalleriaThumbnails_div_6_Template", "GalleriaThumbnails_div_6_Template_div_keydown_0_listener", "onThumbnailKeydown", "GalleriaThumbnails_div_6_Template_div_click_1_listener", "onItemClick", "GalleriaThumbnails_div_6_Template_div_touchend_1_listener", "GalleriaThumbnails_div_6_Template_div_keydown_enter_1_listener", "item_r6", "$implicit", "ɵɵpureFunction4", "isItemActive", "firstItemAciveIndex", "lastItemActiveIndex", "containerId", "GalleriaThumbnails_button_7_ng_container_1_ChevronRightIcon_1_Template", "GalleriaThumbnails_button_7_ng_container_1_ChevronDownIcon_2_Template", "GalleriaThumbnails_button_7_ng_container_1_Template", "GalleriaThumbnails_button_7_2_ng_template_0_Template", "GalleriaThumbnails_button_7_2_Template", "GalleriaThumbnails_button_7_Template", "_r7", "GalleriaThumbnails_button_7_Template_button_click_0_listener", "ariaNextButtonLabel", "nextThumbnailIconTemplate", "Galleria", "document", "platformId", "element", "cd", "config", "_activeIndex", "shouldStopAutoplayByClick", "transitionInterval", "thumbnailsPosition", "indicatorsPosition", "baseZIndex", "containerClass", "_visible", "activeIndexChange", "visibleChange", "mask", "container", "constructor", "ngAfterContentInit", "for<PERSON>ach", "item", "getType", "template", "ngOnChanges", "simpleChanges", "currentValue", "length", "event", "toState", "enableModality", "setTimeout", "focus", "findSingle", "nativeElement", "addClass", "disableModality", "blockBodyScroll", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "set", "zIndex", "modal", "unblockBodyScroll", "clear", "ngOnDestroy", "removeClass", "body", "ɵfac", "Galleria_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "ChangeDetectorRef", "PrimeNGConfig", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "Galleria_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "viewQuery", "Galleria_Query", "ɵɵviewQuery", "first", "hostAttrs", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "Galleria_Template", "ɵɵtemplateRefExtractor", "windowed_r4", "ɵɵreference", "dependencies", "Ng<PERSON><PERSON>", "NgIf", "NgStyle", "GalleriaContent", "styles", "encapsulation", "data", "animation", "transform", "opacity", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "animations", "OnPush", "None", "host", "class", "Document", "decorators", "differs", "activeItemChange", "closeButton", "interval", "styleClass", "differ", "find", "create", "ngDoCheck", "changes", "diff", "forEachItem", "thumbnailsPosClass", "getPositionClass", "indicatorPosClass", "setInterval", "clearInterval", "preClassName", "position", "positions", "pos", "translation", "aria", "close", "GalleriaContent_Factory", "KeyValueDiffers", "GalleriaContent_Query", "GalleriaContent_Template", "NgTemplateOutlet", "<PERSON><PERSON><PERSON>", "FocusTrap", "GalleriaItemSlot", "GalleriaItem", "GalleriaThumbnails", "_item", "GalleriaItemSlot_Factory", "GalleriaItemSlot_Template", "stopTheSlideShow", "next", "nextItemIndex", "prev", "prevItemIndex", "e", "cancelable", "preventDefault", "code", "ariaSlideLabel", "slide", "ariaSlideNumber", "slideNumber", "replace", "pageLabel", "GalleriaItem_Factory", "GalleriaItem_Template", "ɵɵstyleProp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "renderer", "contentHeight", "itemsContainer", "_numVisible", "_oldNumVisible", "d_numVisible", "_oldactiveIndex", "startPos", "thumbnailsStyle", "sortedResponsiveOptions", "totalShiftedItems", "page", "documentResizeListener", "ngOnInit", "createStyle", "bindDocumentListeners", "ngAfterContentChecked", "getMedianItemIndex", "ngAfterViewInit", "calculatePosition", "createElement", "append<PERSON><PERSON><PERSON>", "innerHTML", "sort", "data1", "data2", "value1", "breakpoint", "value2", "result", "localeCompare", "numeric", "i", "res", "windowWidth", "window", "innerWidth", "matchedResponsiveData", "parseInt", "getTabIndex", "getTotalPageNumber", "step", "selectedItemIndex", "dir", "onRightKey", "onLeftKey", "onHomeKey", "onEndKey", "onTabKey", "indicators", "findFocusedIndicatorIndex", "changedFocusedIndicator", "highlightedIndex", "findIndex", "ind", "getAttribute", "activeIndicator", "parentElement", "children", "tabIndex", "prevInd", "nextInd", "changePageOnTouch", "Math", "floor", "onTransitionEnd", "onTouchEnd", "<PERSON><PERSON><PERSON>", "changedTouches", "pageY", "y", "pageX", "x", "onTouchMove", "onTouchStart", "defaultView", "listen", "unbindDocumentListeners", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "prevPageLabel", "nextPageLabel", "GalleriaThumbnails_Factory", "Renderer2", "GalleriaThumbnails_Query", "GalleriaThumbnails_Template", "GalleriaThumbnails_Template_div_transitionend_4_listener", "GalleriaThumbnails_Template_div_touchstart_4_listener", "GalleriaThumbnails_Template_div_touchmove_4_listener", "GalleriaModule", "GalleriaModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/Code/ASAR/Azure/Public Service/SNJYA-PUBLIC-SERVICE/client/node_modules/primeng/fesm2022/primeng-galleria.mjs"], "sourcesContent": ["import { style, animate, transition, trigger } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, isPlatformBrowser, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { WindowMaximizeIcon } from 'primeng/icons/windowmaximize';\nimport { WindowMinimizeIcon } from 'primeng/icons/windowminimize';\nimport * as i3 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { ZIndexUtils, UniqueComponentId } from 'primeng/utils';\nimport * as i4 from 'primeng/focustrap';\nimport { FocusTrapModule } from 'primeng/focustrap';\nimport { platformBrowser } from '@angular/platform-browser';\n\n/**\n * Galleria is an advanced content gallery component.\n * @group Components\n */\nclass Galleria {\n    document;\n    platformId;\n    element;\n    cd;\n    config;\n    /**\n     * Index of the first item.\n     * @group Props\n     */\n    get activeIndex() {\n        return this._activeIndex;\n    }\n    set activeIndex(activeIndex) {\n        this._activeIndex = activeIndex;\n    }\n    /**\n     * Whether to display the component on fullscreen.\n     * @group Props\n     */\n    fullScreen = false;\n    /**\n     * Unique identifier of the element.\n     * @group Props\n     */\n    id;\n    /**\n     * An array of objects to display.\n     * @group Props\n     */\n    value;\n    /**\n     * Number of items per page.\n     * @group Props\n     */\n    numVisible = 3;\n    /**\n     * An array of options for responsive design.\n     * @see {GalleriaResponsiveOptions}\n     * @group Props\n     */\n    responsiveOptions;\n    /**\n     * Whether to display navigation buttons in item section.\n     * @group Props\n     */\n    showItemNavigators = false;\n    /**\n     * Whether to display navigation buttons in thumbnail container.\n     * @group Props\n     */\n    showThumbnailNavigators = true;\n    /**\n     * Whether to display navigation buttons on item hover.\n     * @group Props\n     */\n    showItemNavigatorsOnHover = false;\n    /**\n     * When enabled, item is changed on indicator hover.\n     * @group Props\n     */\n    changeItemOnIndicatorHover = false;\n    /**\n     * Defines if scrolling would be infinite.\n     * @group Props\n     */\n    circular = false;\n    /**\n     * Items are displayed with a slideshow in autoPlay mode.\n     * @group Props\n     */\n    autoPlay = false;\n    /**\n     * When enabled, autorun should stop by click.\n     * @group Props\n     */\n    shouldStopAutoplayByClick = true;\n    /**\n     * Time in milliseconds to scroll items.\n     * @group Props\n     */\n    transitionInterval = 4000;\n    /**\n     * Whether to display thumbnail container.\n     * @group Props\n     */\n    showThumbnails = true;\n    /**\n     * Position of thumbnails.\n     * @group Props\n     */\n    thumbnailsPosition = 'bottom';\n    /**\n     * Height of the viewport in vertical thumbnail.\n     * @group Props\n     */\n    verticalThumbnailViewPortHeight = '300px';\n    /**\n     * Whether to display indicator container.\n     * @group Props\n     */\n    showIndicators = false;\n    /**\n     * When enabled, indicator container is displayed on item container.\n     * @group Props\n     */\n    showIndicatorsOnItem = false;\n    /**\n     * Position of indicators.\n     * @group Props\n     */\n    indicatorsPosition = 'bottom';\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * Style class of the mask on fullscreen mode.\n     * @group Props\n     */\n    maskClass;\n    /**\n     * Style class of the component on fullscreen mode. Otherwise, the 'class' property can be used.\n     * @group Props\n     */\n    containerClass;\n    /**\n     * Inline style of the component on fullscreen mode. Otherwise, the 'style' property can be used.\n     * @group Props\n     */\n    containerStyle;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    showTransitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    hideTransitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Specifies the visibility of the mask on fullscreen mode.\n     * @group Props\n     */\n    get visible() {\n        return this._visible;\n    }\n    set visible(visible) {\n        this._visible = visible;\n        if (this._visible && !this.maskVisible) {\n            this.maskVisible = true;\n        }\n    }\n    /**\n     * Callback to invoke on active index change.\n     * @param {number} number - Active index.\n     * @group Emits\n     */\n    activeIndexChange = new EventEmitter();\n    /**\n     * Callback to invoke on visiblity change.\n     * @param {boolean} boolean - Visible value.\n     * @group Emits\n     */\n    visibleChange = new EventEmitter();\n    mask;\n    container;\n    templates;\n    _visible = false;\n    _activeIndex = 0;\n    headerFacet;\n    footerFacet;\n    indicatorFacet;\n    captionFacet;\n    closeIconTemplate;\n    previousThumbnailIconTemplate;\n    nextThumbnailIconTemplate;\n    itemPreviousIconTemplate;\n    itemNextIconTemplate;\n    maskVisible = false;\n    numVisibleLimit = 0;\n    constructor(document, platformId, element, cd, config) {\n        this.document = document;\n        this.platformId = platformId;\n        this.element = element;\n        this.cd = cd;\n        this.config = config;\n    }\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            switch (item.getType()) {\n                case 'header':\n                    this.headerFacet = item.template;\n                    break;\n                case 'footer':\n                    this.footerFacet = item.template;\n                    break;\n                case 'indicator':\n                    this.indicatorFacet = item.template;\n                    break;\n                case 'closeicon':\n                    this.closeIconTemplate = item.template;\n                    break;\n                case 'itemnexticon':\n                    this.itemNextIconTemplate = item.template;\n                    break;\n                case 'itempreviousicon':\n                    this.itemPreviousIconTemplate = item.template;\n                    break;\n                case 'previousthumbnailicon':\n                    this.previousThumbnailIconTemplate = item.template;\n                    break;\n                case 'nextthumbnailicon':\n                    this.nextThumbnailIconTemplate = item.template;\n                    break;\n                case 'caption':\n                    this.captionFacet = item.template;\n                    break;\n            }\n        });\n    }\n    ngOnChanges(simpleChanges) {\n        if (simpleChanges.value && simpleChanges.value.currentValue?.length < this.numVisible) {\n            this.numVisibleLimit = simpleChanges.value.currentValue.length;\n        }\n        else {\n            this.numVisibleLimit = 0;\n        }\n    }\n    onMaskHide() {\n        this.visible = false;\n        this.visibleChange.emit(false);\n    }\n    onActiveItemChange(index) {\n        if (this.activeIndex !== index) {\n            this.activeIndex = index;\n            this.activeIndexChange.emit(index);\n        }\n    }\n    onAnimationStart(event) {\n        switch (event.toState) {\n            case 'visible':\n                this.enableModality();\n                setTimeout(() => {\n                    DomHandler.focus(DomHandler.findSingle(this.container.nativeElement, '[data-pc-section=\"closebutton\"]'));\n                }, 25);\n                break;\n            case 'void':\n                DomHandler.addClass(this.mask?.nativeElement, 'p-component-overlay-leave');\n                break;\n        }\n    }\n    onAnimationEnd(event) {\n        switch (event.toState) {\n            case 'void':\n                this.disableModality();\n                break;\n        }\n    }\n    enableModality() {\n        DomHandler.blockBodyScroll();\n        this.cd.markForCheck();\n        if (this.mask) {\n            ZIndexUtils.set('modal', this.mask.nativeElement, this.baseZIndex || this.config.zIndex.modal);\n        }\n    }\n    disableModality() {\n        DomHandler.unblockBodyScroll();\n        this.maskVisible = false;\n        this.cd.markForCheck();\n        if (this.mask) {\n            ZIndexUtils.clear(this.mask.nativeElement);\n        }\n    }\n    ngOnDestroy() {\n        if (this.fullScreen) {\n            DomHandler.removeClass(this.document.body, 'p-overflow-hidden');\n        }\n        if (this.mask) {\n            this.disableModality();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Galleria, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1.PrimeNGConfig }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: Galleria, selector: \"p-galleria\", inputs: { activeIndex: \"activeIndex\", fullScreen: \"fullScreen\", id: \"id\", value: \"value\", numVisible: \"numVisible\", responsiveOptions: \"responsiveOptions\", showItemNavigators: \"showItemNavigators\", showThumbnailNavigators: \"showThumbnailNavigators\", showItemNavigatorsOnHover: \"showItemNavigatorsOnHover\", changeItemOnIndicatorHover: \"changeItemOnIndicatorHover\", circular: \"circular\", autoPlay: \"autoPlay\", shouldStopAutoplayByClick: \"shouldStopAutoplayByClick\", transitionInterval: \"transitionInterval\", showThumbnails: \"showThumbnails\", thumbnailsPosition: \"thumbnailsPosition\", verticalThumbnailViewPortHeight: \"verticalThumbnailViewPortHeight\", showIndicators: \"showIndicators\", showIndicatorsOnItem: \"showIndicatorsOnItem\", indicatorsPosition: \"indicatorsPosition\", baseZIndex: \"baseZIndex\", maskClass: \"maskClass\", containerClass: \"containerClass\", containerStyle: \"containerStyle\", showTransitionOptions: \"showTransitionOptions\", hideTransitionOptions: \"hideTransitionOptions\", visible: \"visible\" }, outputs: { activeIndexChange: \"activeIndexChange\", visibleChange: \"visibleChange\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"mask\", first: true, predicate: [\"mask\"], descendants: true }, { propertyName: \"container\", first: true, predicate: [\"container\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: `\n        <div *ngIf=\"fullScreen; else windowed\" #container>\n            <div\n                *ngIf=\"maskVisible\"\n                #mask\n                [ngClass]=\"{ 'p-galleria-mask p-component-overlay p-component-overlay-enter': true, 'p-galleria-visible': this.visible }\"\n                [class]=\"maskClass\"\n                [attr.role]=\"fullScreen ? 'dialog' : 'region'\"\n                [attr.aria-modal]=\"fullScreen ? 'true' : undefined\"\n            >\n                <p-galleriaContent\n                    *ngIf=\"visible\"\n                    [@animation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n                    (@animation.start)=\"onAnimationStart($event)\"\n                    (@animation.done)=\"onAnimationEnd($event)\"\n                    [value]=\"value\"\n                    [activeIndex]=\"activeIndex\"\n                    [numVisible]=\"numVisibleLimit || numVisible\"\n                    (maskHide)=\"onMaskHide()\"\n                    (activeItemChange)=\"onActiveItemChange($event)\"\n                    [ngStyle]=\"containerStyle\"\n                ></p-galleriaContent>\n            </div>\n        </div>\n\n        <ng-template #windowed>\n            <p-galleriaContent [value]=\"value\" [activeIndex]=\"activeIndex\" [numVisible]=\"numVisibleLimit || numVisible\" (activeItemChange)=\"onActiveItemChange($event)\"></p-galleriaContent>\n        </ng-template>\n    `, isInline: true, styles: [\"@layer primeng{.p-galleria-content{display:flex;flex-direction:column}.p-galleria-item-wrapper{display:flex;flex-direction:column;position:relative}.p-galleria-item-container{position:relative;display:flex;height:100%}.p-galleria-item-nav{position:absolute;top:50%;margin-top:-.5rem;display:inline-flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-item-prev{left:0;border-top-left-radius:0;border-bottom-left-radius:0}.p-galleria-item-next{right:0;border-top-right-radius:0;border-bottom-right-radius:0}.p-galleria-item{display:flex;justify-content:center;align-items:center;height:100%;width:100%}.p-galleria-item-nav-onhover .p-galleria-item-nav{pointer-events:none;opacity:0;transition:opacity .2s ease-in-out}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav{pointer-events:all;opacity:1}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav.p-disabled{pointer-events:none}.p-galleria-caption{position:absolute;bottom:0;left:0;width:100%}.p-galleria-thumbnail-wrapper{display:flex;flex-direction:column;overflow:auto;flex-shrink:0}.p-galleria-thumbnail-prev,.p-galleria-thumbnail-next{align-self:center;flex:0 0 auto;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-galleria-thumbnail-prev span,.p-galleria-thumbnail-next span{display:flex;justify-content:center;align-items:center}.p-galleria-thumbnail-container{display:flex;flex-direction:row}.p-galleria-thumbnail-items-container{overflow:hidden;width:100%}.p-galleria-thumbnail-items{display:flex}.p-galleria-thumbnail-item{overflow:auto;display:flex;align-items:center;justify-content:center;cursor:pointer;opacity:.5}.p-galleria-thumbnail-item:hover{opacity:1;transition:opacity .3s}.p-galleria-thumbnail-item-current{opacity:1}.p-galleria-thumbnails-left .p-galleria-content,.p-galleria-thumbnails-right .p-galleria-content,.p-galleria-thumbnails-left .p-galleria-item-wrapper,.p-galleria-thumbnails-right .p-galleria-item-wrapper{flex-direction:row}.p-galleria-thumbnails-left p-galleriaitem,.p-galleria-thumbnails-top p-galleriaitem{order:2}.p-galleria-thumbnails-left p-galleriathumbnails,.p-galleria-thumbnails-top p-galleriathumbnails{order:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-container,.p-galleria-thumbnails-right .p-galleria-thumbnail-container{flex-direction:column;flex-grow:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-items,.p-galleria-thumbnails-right .p-galleria-thumbnail-items{flex-direction:column;height:100%}.p-galleria-thumbnails-left .p-galleria-thumbnail-wrapper,.p-galleria-thumbnails-right .p-galleria-thumbnail-wrapper{height:100%}.p-galleria-indicators{display:flex;align-items:center;justify-content:center}.p-galleria-indicator>button{display:inline-flex;align-items:center}.p-galleria-indicators-left .p-galleria-item-wrapper,.p-galleria-indicators-right .p-galleria-item-wrapper{flex-direction:row;align-items:center}.p-galleria-indicators-left .p-galleria-item-container,.p-galleria-indicators-top .p-galleria-item-container{order:2}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-top .p-galleria-indicators{order:1}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-right .p-galleria-indicators{flex-direction:column}.p-galleria-indicator-onitem .p-galleria-indicators{position:absolute;display:flex;z-index:1}.p-galleria-indicator-onitem.p-galleria-indicators-top .p-galleria-indicators{top:0;left:0;width:100%;align-items:flex-start}.p-galleria-indicator-onitem.p-galleria-indicators-right .p-galleria-indicators{right:0;top:0;height:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-bottom .p-galleria-indicators{bottom:0;left:0;width:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-left .p-galleria-indicators{left:0;top:0;height:100%;align-items:flex-start}.p-galleria-mask{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:transparent;transition-property:background-color}.p-galleria-close{position:absolute;top:0;right:0;display:flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-mask .p-galleria-item-nav{position:fixed;top:50%;margin-top:-.5rem}.p-galleria-mask.p-galleria-mask-leave{background-color:transparent}.p-items-hidden .p-galleria-thumbnail-item{visibility:hidden}.p-items-hidden .p-galleria-thumbnail-item.p-galleria-thumbnail-item-active{visibility:visible}}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i2.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: i0.forwardRef(() => GalleriaContent), selector: \"p-galleriaContent\", inputs: [\"activeIndex\", \"value\", \"numVisible\"], outputs: [\"maskHide\", \"activeItemChange\"] }], animations: [\n            trigger('animation', [\n                transition('void => visible', [style({ transform: 'scale(0.7)', opacity: 0 }), animate('{{showTransitionParams}}')]),\n                transition('visible => void', [animate('{{hideTransitionParams}}', style({ transform: 'scale(0.7)', opacity: 0 }))])\n            ])\n        ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Galleria, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-galleria', template: `\n        <div *ngIf=\"fullScreen; else windowed\" #container>\n            <div\n                *ngIf=\"maskVisible\"\n                #mask\n                [ngClass]=\"{ 'p-galleria-mask p-component-overlay p-component-overlay-enter': true, 'p-galleria-visible': this.visible }\"\n                [class]=\"maskClass\"\n                [attr.role]=\"fullScreen ? 'dialog' : 'region'\"\n                [attr.aria-modal]=\"fullScreen ? 'true' : undefined\"\n            >\n                <p-galleriaContent\n                    *ngIf=\"visible\"\n                    [@animation]=\"{ value: 'visible', params: { showTransitionParams: showTransitionOptions, hideTransitionParams: hideTransitionOptions } }\"\n                    (@animation.start)=\"onAnimationStart($event)\"\n                    (@animation.done)=\"onAnimationEnd($event)\"\n                    [value]=\"value\"\n                    [activeIndex]=\"activeIndex\"\n                    [numVisible]=\"numVisibleLimit || numVisible\"\n                    (maskHide)=\"onMaskHide()\"\n                    (activeItemChange)=\"onActiveItemChange($event)\"\n                    [ngStyle]=\"containerStyle\"\n                ></p-galleriaContent>\n            </div>\n        </div>\n\n        <ng-template #windowed>\n            <p-galleriaContent [value]=\"value\" [activeIndex]=\"activeIndex\" [numVisible]=\"numVisibleLimit || numVisible\" (activeItemChange)=\"onActiveItemChange($event)\"></p-galleriaContent>\n        </ng-template>\n    `, animations: [\n                        trigger('animation', [\n                            transition('void => visible', [style({ transform: 'scale(0.7)', opacity: 0 }), animate('{{showTransitionParams}}')]),\n                            transition('visible => void', [animate('{{hideTransitionParams}}', style({ transform: 'scale(0.7)', opacity: 0 }))])\n                        ])\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\"@layer primeng{.p-galleria-content{display:flex;flex-direction:column}.p-galleria-item-wrapper{display:flex;flex-direction:column;position:relative}.p-galleria-item-container{position:relative;display:flex;height:100%}.p-galleria-item-nav{position:absolute;top:50%;margin-top:-.5rem;display:inline-flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-item-prev{left:0;border-top-left-radius:0;border-bottom-left-radius:0}.p-galleria-item-next{right:0;border-top-right-radius:0;border-bottom-right-radius:0}.p-galleria-item{display:flex;justify-content:center;align-items:center;height:100%;width:100%}.p-galleria-item-nav-onhover .p-galleria-item-nav{pointer-events:none;opacity:0;transition:opacity .2s ease-in-out}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav{pointer-events:all;opacity:1}.p-galleria-item-nav-onhover .p-galleria-item-wrapper:hover .p-galleria-item-nav.p-disabled{pointer-events:none}.p-galleria-caption{position:absolute;bottom:0;left:0;width:100%}.p-galleria-thumbnail-wrapper{display:flex;flex-direction:column;overflow:auto;flex-shrink:0}.p-galleria-thumbnail-prev,.p-galleria-thumbnail-next{align-self:center;flex:0 0 auto;display:flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-galleria-thumbnail-prev span,.p-galleria-thumbnail-next span{display:flex;justify-content:center;align-items:center}.p-galleria-thumbnail-container{display:flex;flex-direction:row}.p-galleria-thumbnail-items-container{overflow:hidden;width:100%}.p-galleria-thumbnail-items{display:flex}.p-galleria-thumbnail-item{overflow:auto;display:flex;align-items:center;justify-content:center;cursor:pointer;opacity:.5}.p-galleria-thumbnail-item:hover{opacity:1;transition:opacity .3s}.p-galleria-thumbnail-item-current{opacity:1}.p-galleria-thumbnails-left .p-galleria-content,.p-galleria-thumbnails-right .p-galleria-content,.p-galleria-thumbnails-left .p-galleria-item-wrapper,.p-galleria-thumbnails-right .p-galleria-item-wrapper{flex-direction:row}.p-galleria-thumbnails-left p-galleriaitem,.p-galleria-thumbnails-top p-galleriaitem{order:2}.p-galleria-thumbnails-left p-galleriathumbnails,.p-galleria-thumbnails-top p-galleriathumbnails{order:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-container,.p-galleria-thumbnails-right .p-galleria-thumbnail-container{flex-direction:column;flex-grow:1}.p-galleria-thumbnails-left .p-galleria-thumbnail-items,.p-galleria-thumbnails-right .p-galleria-thumbnail-items{flex-direction:column;height:100%}.p-galleria-thumbnails-left .p-galleria-thumbnail-wrapper,.p-galleria-thumbnails-right .p-galleria-thumbnail-wrapper{height:100%}.p-galleria-indicators{display:flex;align-items:center;justify-content:center}.p-galleria-indicator>button{display:inline-flex;align-items:center}.p-galleria-indicators-left .p-galleria-item-wrapper,.p-galleria-indicators-right .p-galleria-item-wrapper{flex-direction:row;align-items:center}.p-galleria-indicators-left .p-galleria-item-container,.p-galleria-indicators-top .p-galleria-item-container{order:2}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-top .p-galleria-indicators{order:1}.p-galleria-indicators-left .p-galleria-indicators,.p-galleria-indicators-right .p-galleria-indicators{flex-direction:column}.p-galleria-indicator-onitem .p-galleria-indicators{position:absolute;display:flex;z-index:1}.p-galleria-indicator-onitem.p-galleria-indicators-top .p-galleria-indicators{top:0;left:0;width:100%;align-items:flex-start}.p-galleria-indicator-onitem.p-galleria-indicators-right .p-galleria-indicators{right:0;top:0;height:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-bottom .p-galleria-indicators{bottom:0;left:0;width:100%;align-items:flex-end}.p-galleria-indicator-onitem.p-galleria-indicators-left .p-galleria-indicators{left:0;top:0;height:100%;align-items:flex-start}.p-galleria-mask{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background-color:transparent;transition-property:background-color}.p-galleria-close{position:absolute;top:0;right:0;display:flex;justify-content:center;align-items:center;overflow:hidden}.p-galleria-mask .p-galleria-item-nav{position:fixed;top:50%;margin-top:-.5rem}.p-galleria-mask.p-galleria-mask-leave{background-color:transparent}.p-items-hidden .p-galleria-thumbnail-item{visibility:hidden}.p-items-hidden .p-galleria-thumbnail-item.p-galleria-thumbnail-item-active{visibility:visible}}\\n\"] }]\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1.PrimeNGConfig }], propDecorators: { activeIndex: [{\n                type: Input\n            }], fullScreen: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], numVisible: [{\n                type: Input\n            }], responsiveOptions: [{\n                type: Input\n            }], showItemNavigators: [{\n                type: Input\n            }], showThumbnailNavigators: [{\n                type: Input\n            }], showItemNavigatorsOnHover: [{\n                type: Input\n            }], changeItemOnIndicatorHover: [{\n                type: Input\n            }], circular: [{\n                type: Input\n            }], autoPlay: [{\n                type: Input\n            }], shouldStopAutoplayByClick: [{\n                type: Input\n            }], transitionInterval: [{\n                type: Input\n            }], showThumbnails: [{\n                type: Input\n            }], thumbnailsPosition: [{\n                type: Input\n            }], verticalThumbnailViewPortHeight: [{\n                type: Input\n            }], showIndicators: [{\n                type: Input\n            }], showIndicatorsOnItem: [{\n                type: Input\n            }], indicatorsPosition: [{\n                type: Input\n            }], baseZIndex: [{\n                type: Input\n            }], maskClass: [{\n                type: Input\n            }], containerClass: [{\n                type: Input\n            }], containerStyle: [{\n                type: Input\n            }], showTransitionOptions: [{\n                type: Input\n            }], hideTransitionOptions: [{\n                type: Input\n            }], visible: [{\n                type: Input\n            }], activeIndexChange: [{\n                type: Output\n            }], visibleChange: [{\n                type: Output\n            }], mask: [{\n                type: ViewChild,\n                args: ['mask']\n            }], container: [{\n                type: ViewChild,\n                args: ['container']\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass GalleriaContent {\n    galleria;\n    cd;\n    differs;\n    config;\n    get activeIndex() {\n        return this._activeIndex;\n    }\n    set activeIndex(activeIndex) {\n        this._activeIndex = activeIndex;\n    }\n    value = [];\n    numVisible;\n    maskHide = new EventEmitter();\n    activeItemChange = new EventEmitter();\n    closeButton;\n    id;\n    _activeIndex = 0;\n    slideShowActive = true;\n    interval;\n    styleClass;\n    differ;\n    constructor(galleria, cd, differs, config) {\n        this.galleria = galleria;\n        this.cd = cd;\n        this.differs = differs;\n        this.config = config;\n        this.id = this.galleria.id || UniqueComponentId();\n        this.differ = this.differs.find(this.galleria).create();\n    }\n    ngDoCheck() {\n        if (isPlatformBrowser(this.galleria.platformId)) {\n            const changes = this.differ.diff(this.galleria);\n            if (changes && changes.forEachItem.length > 0) {\n                // Because we change the properties of the parent component,\n                // and the children take our entity from the injector.\n                // We can tell the children to redraw themselves when we change the properties of the parent component.\n                // Since we have an onPush strategy\n                this.cd.markForCheck();\n            }\n        }\n    }\n    galleriaClass() {\n        const thumbnailsPosClass = this.galleria.showThumbnails && this.getPositionClass('p-galleria-thumbnails', this.galleria.thumbnailsPosition);\n        const indicatorPosClass = this.galleria.showIndicators && this.getPositionClass('p-galleria-indicators', this.galleria.indicatorsPosition);\n        return (this.galleria.containerClass ? this.galleria.containerClass + ' ' : '') + (thumbnailsPosClass ? thumbnailsPosClass + ' ' : '') + (indicatorPosClass ? indicatorPosClass + ' ' : '');\n    }\n    startSlideShow() {\n        if (isPlatformBrowser(this.galleria.platformId)) {\n            this.interval = setInterval(() => {\n                let activeIndex = this.galleria.circular && this.value.length - 1 === this.activeIndex ? 0 : this.activeIndex + 1;\n                this.onActiveIndexChange(activeIndex);\n                this.activeIndex = activeIndex;\n            }, this.galleria.transitionInterval);\n            this.slideShowActive = true;\n        }\n    }\n    stopSlideShow() {\n        if (this.galleria.autoPlay && !this.galleria.shouldStopAutoplayByClick) {\n            return;\n        }\n        if (this.interval) {\n            clearInterval(this.interval);\n        }\n        this.slideShowActive = false;\n    }\n    getPositionClass(preClassName, position) {\n        const positions = ['top', 'left', 'bottom', 'right'];\n        const pos = positions.find((item) => item === position);\n        return pos ? `${preClassName}-${pos}` : '';\n    }\n    isVertical() {\n        return this.galleria.thumbnailsPosition === 'left' || this.galleria.thumbnailsPosition === 'right';\n    }\n    onActiveIndexChange(index) {\n        if (this.activeIndex !== index) {\n            this.activeIndex = index;\n            this.activeItemChange.emit(this.activeIndex);\n        }\n    }\n    closeAriaLabel() {\n        return this.config.translation.aria ? this.config.translation.aria.close : undefined;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: GalleriaContent, deps: [{ token: Galleria }, { token: i0.ChangeDetectorRef }, { token: i0.KeyValueDiffers }, { token: i1.PrimeNGConfig }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: GalleriaContent, selector: \"p-galleriaContent\", inputs: { activeIndex: \"activeIndex\", value: \"value\", numVisible: \"numVisible\" }, outputs: { maskHide: \"maskHide\", activeItemChange: \"activeItemChange\" }, viewQueries: [{ propertyName: \"closeButton\", first: true, predicate: [\"closeButton\"], descendants: true }], ngImport: i0, template: `\n        <div\n            [attr.id]=\"id\"\n            [attr.role]=\"'region'\"\n            *ngIf=\"value && value.length > 0\"\n            [ngClass]=\"{\n                'p-galleria p-component': true,\n                'p-galleria-fullscreen': this.galleria.fullScreen,\n                'p-galleria-indicator-onitem': this.galleria.showIndicatorsOnItem,\n                'p-galleria-item-nav-onhover': this.galleria.showItemNavigatorsOnHover && !this.galleria.fullScreen\n            }\"\n            [ngStyle]=\"!galleria.fullScreen ? galleria.containerStyle : {}\"\n            [class]=\"galleriaClass()\"\n            pFocusTrap\n        >\n            <button *ngIf=\"galleria.fullScreen\" type=\"button\" class=\"p-galleria-close p-link\" (click)=\"maskHide.emit()\" pRipple [attr.aria-label]=\"closeAriaLabel()\" [attr.data-pc-section]=\"'closebutton'\">\n                <TimesIcon *ngIf=\"!galleria.closeIconTemplate\" [styleClass]=\"'p-galleria-close-icon'\" />\n                <ng-template *ngTemplateOutlet=\"galleria.closeIconTemplate\"></ng-template>\n            </button>\n            <div *ngIf=\"galleria.templates && galleria.headerFacet\" class=\"p-galleria-header\">\n                <p-galleriaItemSlot type=\"header\" [templates]=\"galleria.templates\"></p-galleriaItemSlot>\n            </div>\n            <div class=\"p-galleria-content\" [attr.aria-live]=\"galleria.autoPlay ? 'polite' : 'off'\">\n                <p-galleriaItem\n                    [id]=\"id\"\n                    [value]=\"value\"\n                    [activeIndex]=\"activeIndex\"\n                    [circular]=\"galleria.circular\"\n                    [templates]=\"galleria.templates\"\n                    (onActiveIndexChange)=\"onActiveIndexChange($event)\"\n                    [showIndicators]=\"galleria.showIndicators\"\n                    [changeItemOnIndicatorHover]=\"galleria.changeItemOnIndicatorHover\"\n                    [indicatorFacet]=\"galleria.indicatorFacet\"\n                    [captionFacet]=\"galleria.captionFacet\"\n                    [showItemNavigators]=\"galleria.showItemNavigators\"\n                    [autoPlay]=\"galleria.autoPlay\"\n                    [slideShowActive]=\"slideShowActive\"\n                    (startSlideShow)=\"startSlideShow()\"\n                    (stopSlideShow)=\"stopSlideShow()\"\n                ></p-galleriaItem>\n\n                <p-galleriaThumbnails\n                    *ngIf=\"galleria.showThumbnails\"\n                    [containerId]=\"id\"\n                    [value]=\"value\"\n                    (onActiveIndexChange)=\"onActiveIndexChange($event)\"\n                    [activeIndex]=\"activeIndex\"\n                    [templates]=\"galleria.templates\"\n                    [numVisible]=\"numVisible\"\n                    [responsiveOptions]=\"galleria.responsiveOptions\"\n                    [circular]=\"galleria.circular\"\n                    [isVertical]=\"isVertical()\"\n                    [contentHeight]=\"galleria.verticalThumbnailViewPortHeight\"\n                    [showThumbnailNavigators]=\"galleria.showThumbnailNavigators\"\n                    [slideShowActive]=\"slideShowActive\"\n                    (stopSlideShow)=\"stopSlideShow()\"\n                ></p-galleriaThumbnails>\n            </div>\n            <div *ngIf=\"galleria.templates && galleria.footerFacet\" class=\"p-galleria-footer\">\n                <p-galleriaItemSlot type=\"footer\" [templates]=\"galleria.templates\"></p-galleriaItemSlot>\n            </div>\n        </div>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i2.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i0.forwardRef(() => i3.Ripple), selector: \"[pRipple]\" }, { kind: \"component\", type: i0.forwardRef(() => TimesIcon), selector: \"TimesIcon\" }, { kind: \"directive\", type: i0.forwardRef(() => i4.FocusTrap), selector: \"[pFocusTrap]\", inputs: [\"pFocusTrapDisabled\"] }, { kind: \"component\", type: i0.forwardRef(() => GalleriaItemSlot), selector: \"p-galleriaItemSlot\", inputs: [\"templates\", \"index\", \"item\", \"type\"] }, { kind: \"component\", type: i0.forwardRef(() => GalleriaItem), selector: \"p-galleriaItem\", inputs: [\"id\", \"circular\", \"value\", \"showItemNavigators\", \"showIndicators\", \"slideShowActive\", \"changeItemOnIndicatorHover\", \"autoPlay\", \"templates\", \"indicatorFacet\", \"captionFacet\", \"activeIndex\"], outputs: [\"startSlideShow\", \"stopSlideShow\", \"onActiveIndexChange\"] }, { kind: \"component\", type: i0.forwardRef(() => GalleriaThumbnails), selector: \"p-galleriaThumbnails\", inputs: [\"containerId\", \"value\", \"isVertical\", \"slideShowActive\", \"circular\", \"responsiveOptions\", \"contentHeight\", \"showThumbnailNavigators\", \"templates\", \"numVisible\", \"activeIndex\"], outputs: [\"onActiveIndexChange\", \"stopSlideShow\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: GalleriaContent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-galleriaContent',\n                    template: `\n        <div\n            [attr.id]=\"id\"\n            [attr.role]=\"'region'\"\n            *ngIf=\"value && value.length > 0\"\n            [ngClass]=\"{\n                'p-galleria p-component': true,\n                'p-galleria-fullscreen': this.galleria.fullScreen,\n                'p-galleria-indicator-onitem': this.galleria.showIndicatorsOnItem,\n                'p-galleria-item-nav-onhover': this.galleria.showItemNavigatorsOnHover && !this.galleria.fullScreen\n            }\"\n            [ngStyle]=\"!galleria.fullScreen ? galleria.containerStyle : {}\"\n            [class]=\"galleriaClass()\"\n            pFocusTrap\n        >\n            <button *ngIf=\"galleria.fullScreen\" type=\"button\" class=\"p-galleria-close p-link\" (click)=\"maskHide.emit()\" pRipple [attr.aria-label]=\"closeAriaLabel()\" [attr.data-pc-section]=\"'closebutton'\">\n                <TimesIcon *ngIf=\"!galleria.closeIconTemplate\" [styleClass]=\"'p-galleria-close-icon'\" />\n                <ng-template *ngTemplateOutlet=\"galleria.closeIconTemplate\"></ng-template>\n            </button>\n            <div *ngIf=\"galleria.templates && galleria.headerFacet\" class=\"p-galleria-header\">\n                <p-galleriaItemSlot type=\"header\" [templates]=\"galleria.templates\"></p-galleriaItemSlot>\n            </div>\n            <div class=\"p-galleria-content\" [attr.aria-live]=\"galleria.autoPlay ? 'polite' : 'off'\">\n                <p-galleriaItem\n                    [id]=\"id\"\n                    [value]=\"value\"\n                    [activeIndex]=\"activeIndex\"\n                    [circular]=\"galleria.circular\"\n                    [templates]=\"galleria.templates\"\n                    (onActiveIndexChange)=\"onActiveIndexChange($event)\"\n                    [showIndicators]=\"galleria.showIndicators\"\n                    [changeItemOnIndicatorHover]=\"galleria.changeItemOnIndicatorHover\"\n                    [indicatorFacet]=\"galleria.indicatorFacet\"\n                    [captionFacet]=\"galleria.captionFacet\"\n                    [showItemNavigators]=\"galleria.showItemNavigators\"\n                    [autoPlay]=\"galleria.autoPlay\"\n                    [slideShowActive]=\"slideShowActive\"\n                    (startSlideShow)=\"startSlideShow()\"\n                    (stopSlideShow)=\"stopSlideShow()\"\n                ></p-galleriaItem>\n\n                <p-galleriaThumbnails\n                    *ngIf=\"galleria.showThumbnails\"\n                    [containerId]=\"id\"\n                    [value]=\"value\"\n                    (onActiveIndexChange)=\"onActiveIndexChange($event)\"\n                    [activeIndex]=\"activeIndex\"\n                    [templates]=\"galleria.templates\"\n                    [numVisible]=\"numVisible\"\n                    [responsiveOptions]=\"galleria.responsiveOptions\"\n                    [circular]=\"galleria.circular\"\n                    [isVertical]=\"isVertical()\"\n                    [contentHeight]=\"galleria.verticalThumbnailViewPortHeight\"\n                    [showThumbnailNavigators]=\"galleria.showThumbnailNavigators\"\n                    [slideShowActive]=\"slideShowActive\"\n                    (stopSlideShow)=\"stopSlideShow()\"\n                ></p-galleriaThumbnails>\n            </div>\n            <div *ngIf=\"galleria.templates && galleria.footerFacet\" class=\"p-galleria-footer\">\n                <p-galleriaItemSlot type=\"footer\" [templates]=\"galleria.templates\"></p-galleriaItemSlot>\n            </div>\n        </div>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush\n                }]\n        }], ctorParameters: () => [{ type: Galleria }, { type: i0.ChangeDetectorRef }, { type: i0.KeyValueDiffers }, { type: i1.PrimeNGConfig }], propDecorators: { activeIndex: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], numVisible: [{\n                type: Input\n            }], maskHide: [{\n                type: Output\n            }], activeItemChange: [{\n                type: Output\n            }], closeButton: [{\n                type: ViewChild,\n                args: ['closeButton']\n            }] } });\nclass GalleriaItemSlot {\n    templates;\n    index;\n    get item() {\n        return this._item;\n    }\n    set item(item) {\n        this._item = item;\n        if (this.templates) {\n            this.templates.forEach((item) => {\n                if (item.getType() === this.type) {\n                    switch (this.type) {\n                        case 'item':\n                        case 'caption':\n                        case 'thumbnail':\n                            this.context = { $implicit: this.item };\n                            this.contentTemplate = item.template;\n                            break;\n                    }\n                }\n            });\n        }\n    }\n    type;\n    contentTemplate;\n    context;\n    _item;\n    ngAfterContentInit() {\n        this.templates?.forEach((item) => {\n            if (item.getType() === this.type) {\n                switch (this.type) {\n                    case 'item':\n                    case 'caption':\n                    case 'thumbnail':\n                        this.context = { $implicit: this.item };\n                        this.contentTemplate = item.template;\n                        break;\n                    case 'indicator':\n                        this.context = { $implicit: this.index };\n                        this.contentTemplate = item.template;\n                        break;\n                    default:\n                        this.context = {};\n                        this.contentTemplate = item.template;\n                        break;\n                }\n            }\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: GalleriaItemSlot, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: GalleriaItemSlot, selector: \"p-galleriaItemSlot\", inputs: { templates: \"templates\", index: \"index\", item: \"item\", type: \"type\" }, ngImport: i0, template: `\n        <ng-container *ngIf=\"contentTemplate\">\n            <ng-container *ngTemplateOutlet=\"contentTemplate; context: context\"></ng-container>\n        </ng-container>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i2.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i2.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: GalleriaItemSlot, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-galleriaItemSlot',\n                    template: `\n        <ng-container *ngIf=\"contentTemplate\">\n            <ng-container *ngTemplateOutlet=\"contentTemplate; context: context\"></ng-container>\n        </ng-container>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush\n                }]\n        }], propDecorators: { templates: [{\n                type: Input\n            }], index: [{\n                type: Input\n            }], item: [{\n                type: Input\n            }], type: [{\n                type: Input\n            }] } });\nclass GalleriaItem {\n    galleria;\n    id;\n    circular = false;\n    value;\n    showItemNavigators = false;\n    showIndicators = true;\n    slideShowActive = true;\n    changeItemOnIndicatorHover = true;\n    autoPlay = false;\n    templates;\n    indicatorFacet;\n    captionFacet;\n    startSlideShow = new EventEmitter();\n    stopSlideShow = new EventEmitter();\n    onActiveIndexChange = new EventEmitter();\n    get activeIndex() {\n        return this._activeIndex;\n    }\n    set activeIndex(activeIndex) {\n        this._activeIndex = activeIndex;\n    }\n    get activeItem() {\n        return this.value && this.value[this._activeIndex];\n    }\n    _activeIndex = 0;\n    constructor(galleria) {\n        this.galleria = galleria;\n    }\n    ngOnChanges({ autoPlay }) {\n        if (autoPlay?.currentValue) {\n            this.startSlideShow.emit();\n        }\n        if (autoPlay && autoPlay.currentValue === false) {\n            this.stopTheSlideShow();\n        }\n    }\n    next() {\n        let nextItemIndex = this.activeIndex + 1;\n        let activeIndex = this.circular && this.value.length - 1 === this.activeIndex ? 0 : nextItemIndex;\n        this.onActiveIndexChange.emit(activeIndex);\n    }\n    prev() {\n        let prevItemIndex = this.activeIndex !== 0 ? this.activeIndex - 1 : 0;\n        let activeIndex = this.circular && this.activeIndex === 0 ? this.value.length - 1 : prevItemIndex;\n        this.onActiveIndexChange.emit(activeIndex);\n    }\n    stopTheSlideShow() {\n        if (this.slideShowActive && this.stopSlideShow) {\n            this.stopSlideShow.emit();\n        }\n    }\n    navForward(e) {\n        this.stopTheSlideShow();\n        this.next();\n        if (e && e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    navBackward(e) {\n        this.stopTheSlideShow();\n        this.prev();\n        if (e && e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    onIndicatorClick(index) {\n        this.stopTheSlideShow();\n        this.onActiveIndexChange.emit(index);\n    }\n    onIndicatorMouseEnter(index) {\n        if (this.changeItemOnIndicatorHover) {\n            this.stopTheSlideShow();\n            this.onActiveIndexChange.emit(index);\n        }\n    }\n    onIndicatorKeyDown(event, index) {\n        switch (event.code) {\n            case 'Enter':\n            case 'Space':\n                this.stopTheSlideShow();\n                this.onActiveIndexChange.emit(index);\n                event.preventDefault();\n                break;\n            case 'ArrowDown':\n            case 'ArrowUp':\n                event.preventDefault();\n                break;\n            default:\n                break;\n        }\n    }\n    isNavForwardDisabled() {\n        return !this.circular && this.activeIndex === this.value.length - 1;\n    }\n    isNavBackwardDisabled() {\n        return !this.circular && this.activeIndex === 0;\n    }\n    isIndicatorItemActive(index) {\n        return this.activeIndex === index;\n    }\n    ariaSlideLabel() {\n        return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.slide : undefined;\n    }\n    ariaSlideNumber(value) {\n        return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.slideNumber.replace(/{slideNumber}/g, value) : undefined;\n    }\n    ariaPageLabel(value) {\n        return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.pageLabel.replace(/{page}/g, value) : undefined;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: GalleriaItem, deps: [{ token: Galleria }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: GalleriaItem, selector: \"p-galleriaItem\", inputs: { id: \"id\", circular: \"circular\", value: \"value\", showItemNavigators: \"showItemNavigators\", showIndicators: \"showIndicators\", slideShowActive: \"slideShowActive\", changeItemOnIndicatorHover: \"changeItemOnIndicatorHover\", autoPlay: \"autoPlay\", templates: \"templates\", indicatorFacet: \"indicatorFacet\", captionFacet: \"captionFacet\", activeIndex: \"activeIndex\" }, outputs: { startSlideShow: \"startSlideShow\", stopSlideShow: \"stopSlideShow\", onActiveIndexChange: \"onActiveIndexChange\" }, usesOnChanges: true, ngImport: i0, template: `\n        <div class=\"p-galleria-item-wrapper\">\n            <div class=\"p-galleria-item-container\">\n                <button\n                    *ngIf=\"showItemNavigators\"\n                    type=\"button\"\n                    role=\"navigation\"\n                    [ngClass]=\"{ 'p-galleria-item-prev p-galleria-item-nav p-link': true, 'p-disabled': this.isNavBackwardDisabled() }\"\n                    (click)=\"navBackward($event)\"\n                    [disabled]=\"isNavBackwardDisabled()\"\n                    pRipple\n                >\n                    <ChevronLeftIcon *ngIf=\"!galleria.itemPreviousIconTemplate\" [styleClass]=\"'p-galleria-item-prev-icon'\" />\n                    <ng-template *ngTemplateOutlet=\"galleria.itemPreviousIconTemplate\"></ng-template>\n                </button>\n                <div [id]=\"id + '_item_' + activeIndex\" role=\"group\" [attr.aria-label]=\"ariaSlideNumber(activeIndex + 1)\" [attr.aria-roledescription]=\"ariaSlideLabel()\" [style.width]=\"'100%'\">\n                    <p-galleriaItemSlot type=\"item\" [item]=\"activeItem\" [templates]=\"templates\" class=\"p-galleria-item\"></p-galleriaItemSlot>\n                </div>\n                <button\n                    *ngIf=\"showItemNavigators\"\n                    type=\"button\"\n                    [ngClass]=\"{ 'p-galleria-item-next p-galleria-item-nav p-link': true, 'p-disabled': this.isNavForwardDisabled() }\"\n                    (click)=\"navForward($event)\"\n                    [disabled]=\"isNavForwardDisabled()\"\n                    pRipple\n                    role=\"navigation\"\n                >\n                    <ChevronRightIcon *ngIf=\"!galleria.itemNextIconTemplate\" [styleClass]=\"'p-galleria-item-next-icon'\" />\n                    <ng-template *ngTemplateOutlet=\"galleria.itemNextIconTemplate\"></ng-template>\n                </button>\n                <div class=\"p-galleria-caption\" *ngIf=\"captionFacet\">\n                    <p-galleriaItemSlot type=\"caption\" [item]=\"activeItem\" [templates]=\"templates\"></p-galleriaItemSlot>\n                </div>\n            </div>\n            <ul *ngIf=\"showIndicators\" class=\"p-galleria-indicators p-reset\">\n                <li\n                    *ngFor=\"let item of value; let index = index\"\n                    tabindex=\"0\"\n                    (click)=\"onIndicatorClick(index)\"\n                    (mouseenter)=\"onIndicatorMouseEnter(index)\"\n                    (keydown)=\"onIndicatorKeyDown($event, index)\"\n                    [ngClass]=\"{ 'p-galleria-indicator': true, 'p-highlight': isIndicatorItemActive(index) }\"\n                    [attr.aria-label]=\"ariaPageLabel(index + 1)\"\n                    [attr.aria-selected]=\"activeIndex === index\"\n                    [attr.aria-controls]=\"id + '_item_' + index\"\n                >\n                    <button type=\"button\" tabIndex=\"-1\" class=\"p-link\" *ngIf=\"!indicatorFacet\"></button>\n                    <p-galleriaItemSlot type=\"indicator\" [index]=\"index\" [templates]=\"templates\"></p-galleriaItemSlot>\n                </li>\n            </ul>\n        </div>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i2.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgForOf), selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i3.Ripple), selector: \"[pRipple]\" }, { kind: \"component\", type: i0.forwardRef(() => ChevronRightIcon), selector: \"ChevronRightIcon\" }, { kind: \"component\", type: i0.forwardRef(() => ChevronLeftIcon), selector: \"ChevronLeftIcon\" }, { kind: \"component\", type: i0.forwardRef(() => GalleriaItemSlot), selector: \"p-galleriaItemSlot\", inputs: [\"templates\", \"index\", \"item\", \"type\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: GalleriaItem, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-galleriaItem',\n                    template: `\n        <div class=\"p-galleria-item-wrapper\">\n            <div class=\"p-galleria-item-container\">\n                <button\n                    *ngIf=\"showItemNavigators\"\n                    type=\"button\"\n                    role=\"navigation\"\n                    [ngClass]=\"{ 'p-galleria-item-prev p-galleria-item-nav p-link': true, 'p-disabled': this.isNavBackwardDisabled() }\"\n                    (click)=\"navBackward($event)\"\n                    [disabled]=\"isNavBackwardDisabled()\"\n                    pRipple\n                >\n                    <ChevronLeftIcon *ngIf=\"!galleria.itemPreviousIconTemplate\" [styleClass]=\"'p-galleria-item-prev-icon'\" />\n                    <ng-template *ngTemplateOutlet=\"galleria.itemPreviousIconTemplate\"></ng-template>\n                </button>\n                <div [id]=\"id + '_item_' + activeIndex\" role=\"group\" [attr.aria-label]=\"ariaSlideNumber(activeIndex + 1)\" [attr.aria-roledescription]=\"ariaSlideLabel()\" [style.width]=\"'100%'\">\n                    <p-galleriaItemSlot type=\"item\" [item]=\"activeItem\" [templates]=\"templates\" class=\"p-galleria-item\"></p-galleriaItemSlot>\n                </div>\n                <button\n                    *ngIf=\"showItemNavigators\"\n                    type=\"button\"\n                    [ngClass]=\"{ 'p-galleria-item-next p-galleria-item-nav p-link': true, 'p-disabled': this.isNavForwardDisabled() }\"\n                    (click)=\"navForward($event)\"\n                    [disabled]=\"isNavForwardDisabled()\"\n                    pRipple\n                    role=\"navigation\"\n                >\n                    <ChevronRightIcon *ngIf=\"!galleria.itemNextIconTemplate\" [styleClass]=\"'p-galleria-item-next-icon'\" />\n                    <ng-template *ngTemplateOutlet=\"galleria.itemNextIconTemplate\"></ng-template>\n                </button>\n                <div class=\"p-galleria-caption\" *ngIf=\"captionFacet\">\n                    <p-galleriaItemSlot type=\"caption\" [item]=\"activeItem\" [templates]=\"templates\"></p-galleriaItemSlot>\n                </div>\n            </div>\n            <ul *ngIf=\"showIndicators\" class=\"p-galleria-indicators p-reset\">\n                <li\n                    *ngFor=\"let item of value; let index = index\"\n                    tabindex=\"0\"\n                    (click)=\"onIndicatorClick(index)\"\n                    (mouseenter)=\"onIndicatorMouseEnter(index)\"\n                    (keydown)=\"onIndicatorKeyDown($event, index)\"\n                    [ngClass]=\"{ 'p-galleria-indicator': true, 'p-highlight': isIndicatorItemActive(index) }\"\n                    [attr.aria-label]=\"ariaPageLabel(index + 1)\"\n                    [attr.aria-selected]=\"activeIndex === index\"\n                    [attr.aria-controls]=\"id + '_item_' + index\"\n                >\n                    <button type=\"button\" tabIndex=\"-1\" class=\"p-link\" *ngIf=\"!indicatorFacet\"></button>\n                    <p-galleriaItemSlot type=\"indicator\" [index]=\"index\" [templates]=\"templates\"></p-galleriaItemSlot>\n                </li>\n            </ul>\n        </div>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush\n                }]\n        }], ctorParameters: () => [{ type: Galleria }], propDecorators: { id: [{\n                type: Input\n            }], circular: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], showItemNavigators: [{\n                type: Input\n            }], showIndicators: [{\n                type: Input\n            }], slideShowActive: [{\n                type: Input\n            }], changeItemOnIndicatorHover: [{\n                type: Input\n            }], autoPlay: [{\n                type: Input\n            }], templates: [{\n                type: Input\n            }], indicatorFacet: [{\n                type: Input\n            }], captionFacet: [{\n                type: Input\n            }], startSlideShow: [{\n                type: Output\n            }], stopSlideShow: [{\n                type: Output\n            }], onActiveIndexChange: [{\n                type: Output\n            }], activeIndex: [{\n                type: Input\n            }] } });\nclass GalleriaThumbnails {\n    galleria;\n    document;\n    platformId;\n    renderer;\n    cd;\n    containerId;\n    value;\n    isVertical = false;\n    slideShowActive = false;\n    circular = false;\n    responsiveOptions;\n    contentHeight = '300px';\n    showThumbnailNavigators = true;\n    templates;\n    onActiveIndexChange = new EventEmitter();\n    stopSlideShow = new EventEmitter();\n    itemsContainer;\n    get numVisible() {\n        return this._numVisible;\n    }\n    set numVisible(numVisible) {\n        this._numVisible = numVisible;\n        this._oldNumVisible = this.d_numVisible;\n        this.d_numVisible = numVisible;\n    }\n    get activeIndex() {\n        return this._activeIndex;\n    }\n    set activeIndex(activeIndex) {\n        this._oldactiveIndex = this._activeIndex;\n        this._activeIndex = activeIndex;\n    }\n    index;\n    startPos = null;\n    thumbnailsStyle = null;\n    sortedResponsiveOptions = null;\n    totalShiftedItems = 0;\n    page = 0;\n    documentResizeListener;\n    _numVisible = 0;\n    d_numVisible = 0;\n    _oldNumVisible = 0;\n    _activeIndex = 0;\n    _oldactiveIndex = 0;\n    constructor(galleria, document, platformId, renderer, cd) {\n        this.galleria = galleria;\n        this.document = document;\n        this.platformId = platformId;\n        this.renderer = renderer;\n        this.cd = cd;\n    }\n    ngOnInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            this.createStyle();\n            if (this.responsiveOptions) {\n                this.bindDocumentListeners();\n            }\n        }\n    }\n    ngAfterContentChecked() {\n        let totalShiftedItems = this.totalShiftedItems;\n        if ((this._oldNumVisible !== this.d_numVisible || this._oldactiveIndex !== this._activeIndex) && this.itemsContainer) {\n            if (this._activeIndex <= this.getMedianItemIndex()) {\n                totalShiftedItems = 0;\n            }\n            else if (this.value.length - this.d_numVisible + this.getMedianItemIndex() < this._activeIndex) {\n                totalShiftedItems = this.d_numVisible - this.value.length;\n            }\n            else if (this.value.length - this.d_numVisible < this._activeIndex && this.d_numVisible % 2 === 0) {\n                totalShiftedItems = this._activeIndex * -1 + this.getMedianItemIndex() + 1;\n            }\n            else {\n                totalShiftedItems = this._activeIndex * -1 + this.getMedianItemIndex();\n            }\n            if (totalShiftedItems !== this.totalShiftedItems) {\n                this.totalShiftedItems = totalShiftedItems;\n            }\n            if (this.itemsContainer && this.itemsContainer.nativeElement) {\n                this.itemsContainer.nativeElement.style.transform = this.isVertical ? `translate3d(0, ${totalShiftedItems * (100 / this.d_numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this.d_numVisible)}%, 0, 0)`;\n            }\n            if (this._oldactiveIndex !== this._activeIndex) {\n                DomHandler.removeClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n                this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n            }\n            this._oldactiveIndex = this._activeIndex;\n            this._oldNumVisible = this.d_numVisible;\n        }\n    }\n    ngAfterViewInit() {\n        if (platformBrowser(this.platformId)) {\n            this.calculatePosition();\n        }\n    }\n    createStyle() {\n        if (!this.thumbnailsStyle) {\n            this.thumbnailsStyle = this.document.createElement('style');\n            this.document.body.appendChild(this.thumbnailsStyle);\n        }\n        let innerHTML = `\n            #${this.containerId} .p-galleria-thumbnail-item {\n                flex: 1 0 ${100 / this.d_numVisible}%\n            }\n        `;\n        if (this.responsiveOptions) {\n            this.sortedResponsiveOptions = [...this.responsiveOptions];\n            this.sortedResponsiveOptions.sort((data1, data2) => {\n                const value1 = data1.breakpoint;\n                const value2 = data2.breakpoint;\n                let result = null;\n                if (value1 == null && value2 != null)\n                    result = -1;\n                else if (value1 != null && value2 == null)\n                    result = 1;\n                else if (value1 == null && value2 == null)\n                    result = 0;\n                else if (typeof value1 === 'string' && typeof value2 === 'string')\n                    result = value1.localeCompare(value2, undefined, { numeric: true });\n                else\n                    result = value1 < value2 ? -1 : value1 > value2 ? 1 : 0;\n                return -1 * result;\n            });\n            for (let i = 0; i < this.sortedResponsiveOptions.length; i++) {\n                let res = this.sortedResponsiveOptions[i];\n                innerHTML += `\n                    @media screen and (max-width: ${res.breakpoint}) {\n                        #${this.containerId} .p-galleria-thumbnail-item {\n                            flex: 1 0 ${100 / res.numVisible}%\n                        }\n                    }\n                `;\n            }\n        }\n        this.thumbnailsStyle.innerHTML = innerHTML;\n    }\n    calculatePosition() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.itemsContainer && this.sortedResponsiveOptions) {\n                let windowWidth = window.innerWidth;\n                let matchedResponsiveData = {\n                    numVisible: this._numVisible\n                };\n                for (let i = 0; i < this.sortedResponsiveOptions.length; i++) {\n                    let res = this.sortedResponsiveOptions[i];\n                    if (parseInt(res.breakpoint, 10) >= windowWidth) {\n                        matchedResponsiveData = res;\n                    }\n                }\n                if (this.d_numVisible !== matchedResponsiveData.numVisible) {\n                    this.d_numVisible = matchedResponsiveData.numVisible;\n                    this.cd.markForCheck();\n                }\n            }\n        }\n    }\n    getTabIndex(index) {\n        return this.isItemActive(index) ? 0 : null;\n    }\n    navForward(e) {\n        this.stopTheSlideShow();\n        let nextItemIndex = this._activeIndex + 1;\n        if (nextItemIndex + this.totalShiftedItems > this.getMedianItemIndex() && (-1 * this.totalShiftedItems < this.getTotalPageNumber() - 1 || this.circular)) {\n            this.step(-1);\n        }\n        let activeIndex = this.circular && this.value.length - 1 === this._activeIndex ? 0 : nextItemIndex;\n        this.onActiveIndexChange.emit(activeIndex);\n        if (e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    navBackward(e) {\n        this.stopTheSlideShow();\n        let prevItemIndex = this._activeIndex !== 0 ? this._activeIndex - 1 : 0;\n        let diff = prevItemIndex + this.totalShiftedItems;\n        if (this.d_numVisible - diff - 1 > this.getMedianItemIndex() && (-1 * this.totalShiftedItems !== 0 || this.circular)) {\n            this.step(1);\n        }\n        let activeIndex = this.circular && this._activeIndex === 0 ? this.value.length - 1 : prevItemIndex;\n        this.onActiveIndexChange.emit(activeIndex);\n        if (e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    onItemClick(index) {\n        this.stopTheSlideShow();\n        let selectedItemIndex = index;\n        if (selectedItemIndex !== this._activeIndex) {\n            const diff = selectedItemIndex + this.totalShiftedItems;\n            let dir = 0;\n            if (selectedItemIndex < this._activeIndex) {\n                dir = this.d_numVisible - diff - 1 - this.getMedianItemIndex();\n                if (dir > 0 && -1 * this.totalShiftedItems !== 0) {\n                    this.step(dir);\n                }\n            }\n            else {\n                dir = this.getMedianItemIndex() - diff;\n                if (dir < 0 && -1 * this.totalShiftedItems < this.getTotalPageNumber() - 1) {\n                    this.step(dir);\n                }\n            }\n            this.activeIndex = selectedItemIndex;\n            this.onActiveIndexChange.emit(this.activeIndex);\n        }\n    }\n    onThumbnailKeydown(event, index) {\n        if (event.code === 'Enter' || event.code === 'Space') {\n            this.onItemClick(index);\n            event.preventDefault();\n        }\n        switch (event.code) {\n            case 'ArrowRight':\n                this.onRightKey();\n                break;\n            case 'ArrowLeft':\n                this.onLeftKey();\n                break;\n            case 'Home':\n                this.onHomeKey();\n                event.preventDefault();\n                break;\n            case 'End':\n                this.onEndKey();\n                event.preventDefault();\n                break;\n            case 'ArrowUp':\n            case 'ArrowDown':\n                event.preventDefault();\n                break;\n            case 'Tab':\n                this.onTabKey();\n                break;\n            default:\n                break;\n        }\n    }\n    onRightKey() {\n        const indicators = DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]');\n        const activeIndex = this.findFocusedIndicatorIndex();\n        this.changedFocusedIndicator(activeIndex, activeIndex + 1 === indicators.length ? indicators.length - 1 : activeIndex + 1);\n    }\n    onLeftKey() {\n        const activeIndex = this.findFocusedIndicatorIndex();\n        this.changedFocusedIndicator(activeIndex, activeIndex - 1 <= 0 ? 0 : activeIndex - 1);\n    }\n    onHomeKey() {\n        const activeIndex = this.findFocusedIndicatorIndex();\n        this.changedFocusedIndicator(activeIndex, 0);\n    }\n    onEndKey() {\n        const indicators = DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]');\n        const activeIndex = this.findFocusedIndicatorIndex();\n        this.changedFocusedIndicator(activeIndex, indicators.length - 1);\n    }\n    onTabKey() {\n        const indicators = [...DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]')];\n        const highlightedIndex = indicators.findIndex((ind) => DomHandler.getAttribute(ind, 'data-p-active') === true);\n        const activeIndicator = DomHandler.findSingle(this.itemsContainer.nativeElement, '[tabindex=\"0\"]');\n        const activeIndex = indicators.findIndex((ind) => ind === activeIndicator.parentElement);\n        indicators[activeIndex].children[0].tabIndex = '-1';\n        indicators[highlightedIndex].children[0].tabIndex = '0';\n    }\n    findFocusedIndicatorIndex() {\n        const indicators = [...DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]')];\n        const activeIndicator = DomHandler.findSingle(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"] > [tabindex=\"0\"]');\n        return indicators.findIndex((ind) => ind === activeIndicator.parentElement);\n    }\n    changedFocusedIndicator(prevInd, nextInd) {\n        const indicators = DomHandler.find(this.itemsContainer.nativeElement, '[data-pc-section=\"thumbnailitem\"]');\n        indicators[prevInd].children[0].tabIndex = '-1';\n        indicators[nextInd].children[0].tabIndex = '0';\n        indicators[nextInd].children[0].focus();\n    }\n    step(dir) {\n        let totalShiftedItems = this.totalShiftedItems + dir;\n        if (dir < 0 && -1 * totalShiftedItems + this.d_numVisible > this.value.length - 1) {\n            totalShiftedItems = this.d_numVisible - this.value.length;\n        }\n        else if (dir > 0 && totalShiftedItems > 0) {\n            totalShiftedItems = 0;\n        }\n        if (this.circular) {\n            if (dir < 0 && this.value.length - 1 === this._activeIndex) {\n                totalShiftedItems = 0;\n            }\n            else if (dir > 0 && this._activeIndex === 0) {\n                totalShiftedItems = this.d_numVisible - this.value.length;\n            }\n        }\n        if (this.itemsContainer) {\n            DomHandler.removeClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n            this.itemsContainer.nativeElement.style.transform = this.isVertical ? `translate3d(0, ${totalShiftedItems * (100 / this.d_numVisible)}%, 0)` : `translate3d(${totalShiftedItems * (100 / this.d_numVisible)}%, 0, 0)`;\n            this.itemsContainer.nativeElement.style.transition = 'transform 500ms ease 0s';\n        }\n        this.totalShiftedItems = totalShiftedItems;\n    }\n    stopTheSlideShow() {\n        if (this.slideShowActive && this.stopSlideShow) {\n            this.stopSlideShow.emit();\n        }\n    }\n    changePageOnTouch(e, diff) {\n        if (diff < 0) {\n            // left\n            this.navForward(e);\n        }\n        else {\n            // right\n            this.navBackward(e);\n        }\n    }\n    getTotalPageNumber() {\n        return this.value.length > this.d_numVisible ? this.value.length - this.d_numVisible + 1 : 0;\n    }\n    getMedianItemIndex() {\n        let index = Math.floor(this.d_numVisible / 2);\n        return this.d_numVisible % 2 ? index : index - 1;\n    }\n    onTransitionEnd() {\n        if (this.itemsContainer && this.itemsContainer.nativeElement) {\n            DomHandler.addClass(this.itemsContainer.nativeElement, 'p-items-hidden');\n            this.itemsContainer.nativeElement.style.transition = '';\n        }\n    }\n    onTouchEnd(e) {\n        let touchobj = e.changedTouches[0];\n        if (this.isVertical) {\n            this.changePageOnTouch(e, touchobj.pageY - this.startPos.y);\n        }\n        else {\n            this.changePageOnTouch(e, touchobj.pageX - this.startPos.x);\n        }\n    }\n    onTouchMove(e) {\n        if (e.cancelable) {\n            e.preventDefault();\n        }\n    }\n    onTouchStart(e) {\n        let touchobj = e.changedTouches[0];\n        this.startPos = {\n            x: touchobj.pageX,\n            y: touchobj.pageY\n        };\n    }\n    isNavBackwardDisabled() {\n        return (!this.circular && this._activeIndex === 0) || this.value.length <= this.d_numVisible;\n    }\n    isNavForwardDisabled() {\n        return (!this.circular && this._activeIndex === this.value.length - 1) || this.value.length <= this.d_numVisible;\n    }\n    firstItemAciveIndex() {\n        return this.totalShiftedItems * -1;\n    }\n    lastItemActiveIndex() {\n        return this.firstItemAciveIndex() + this.d_numVisible - 1;\n    }\n    isItemActive(index) {\n        return this.firstItemAciveIndex() <= index && this.lastItemActiveIndex() >= index;\n    }\n    bindDocumentListeners() {\n        if (isPlatformBrowser(this.platformId)) {\n            const window = this.document.defaultView || 'window';\n            this.documentResizeListener = this.renderer.listen(window, 'resize', () => {\n                this.calculatePosition();\n            });\n        }\n    }\n    unbindDocumentListeners() {\n        if (this.documentResizeListener) {\n            this.documentResizeListener();\n            this.documentResizeListener = null;\n        }\n    }\n    ngOnDestroy() {\n        if (this.responsiveOptions) {\n            this.unbindDocumentListeners();\n        }\n        if (this.thumbnailsStyle) {\n            this.thumbnailsStyle.parentNode?.removeChild(this.thumbnailsStyle);\n        }\n    }\n    ariaPrevButtonLabel() {\n        return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.prevPageLabel : undefined;\n    }\n    ariaNextButtonLabel() {\n        return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.nextPageLabel : undefined;\n    }\n    ariaPageLabel(value) {\n        return this.galleria.config.translation.aria ? this.galleria.config.translation.aria.pageLabel.replace(/{page}/g, value) : undefined;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: GalleriaThumbnails, deps: [{ token: Galleria }, { token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: GalleriaThumbnails, selector: \"p-galleriaThumbnails\", inputs: { containerId: \"containerId\", value: \"value\", isVertical: \"isVertical\", slideShowActive: \"slideShowActive\", circular: \"circular\", responsiveOptions: \"responsiveOptions\", contentHeight: \"contentHeight\", showThumbnailNavigators: \"showThumbnailNavigators\", templates: \"templates\", numVisible: \"numVisible\", activeIndex: \"activeIndex\" }, outputs: { onActiveIndexChange: \"onActiveIndexChange\", stopSlideShow: \"stopSlideShow\" }, viewQueries: [{ propertyName: \"itemsContainer\", first: true, predicate: [\"itemsContainer\"], descendants: true }], ngImport: i0, template: `\n        <div class=\"p-galleria-thumbnail-wrapper\">\n            <div class=\"p-galleria-thumbnail-container\">\n                <button\n                    *ngIf=\"showThumbnailNavigators\"\n                    type=\"button\"\n                    [ngClass]=\"{ 'p-galleria-thumbnail-prev p-link': true, 'p-disabled': this.isNavBackwardDisabled() }\"\n                    (click)=\"navBackward($event)\"\n                    [disabled]=\"isNavBackwardDisabled()\"\n                    pRipple\n                    [attr.aria-label]=\"ariaPrevButtonLabel()\"\n                >\n                    <ng-container *ngIf=\"!galleria.previousThumbnailIconTemplate\">\n                        <ChevronLeftIcon *ngIf=\"!isVertical\" [styleClass]=\"'p-galleria-thumbnail-prev-icon'\" />\n                        <ChevronUpIcon *ngIf=\"isVertical\" [styleClass]=\"'p-galleria-thumbnail-prev-icon'\" />\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"galleria.previousThumbnailIconTemplate\"></ng-template>\n                </button>\n                <div class=\"p-galleria-thumbnail-items-container\" [ngStyle]=\"{ height: isVertical ? contentHeight : '' }\">\n                    <div #itemsContainer class=\"p-galleria-thumbnail-items\" (transitionend)=\"onTransitionEnd()\" (touchstart)=\"onTouchStart($event)\" (touchmove)=\"onTouchMove($event)\" role=\"tablist\">\n                        <div\n                            *ngFor=\"let item of value; let index = index\"\n                            [ngClass]=\"{\n                                'p-galleria-thumbnail-item': true,\n                                'p-galleria-thumbnail-item-current': activeIndex === index,\n                                'p-galleria-thumbnail-item-active': isItemActive(index),\n                                'p-galleria-thumbnail-item-start': firstItemAciveIndex() === index,\n                                'p-galleria-thumbnail-item-end': lastItemActiveIndex() === index\n                            }\"\n                            [attr.aria-selected]=\"activeIndex === index\"\n                            [attr.aria-controls]=\"containerId + '_item_' + index\"\n                            [attr.data-pc-section]=\"'thumbnailitem'\"\n                            [attr.data-p-active]=\"activeIndex === index\"\n                            (keydown)=\"onThumbnailKeydown($event, index)\"\n                        >\n                            <div\n                                class=\"p-galleria-thumbnail-item-content\"\n                                [attr.tabindex]=\"activeIndex === index ? 0 : -1\"\n                                [attr.aria-current]=\"activeIndex === index ? 'page' : undefined\"\n                                [attr.aria-label]=\"ariaPageLabel(index + 1)\"\n                                (click)=\"onItemClick(index)\"\n                                (touchend)=\"onItemClick(index)\"\n                                (keydown.enter)=\"onItemClick(index)\"\n                            >\n                                <p-galleriaItemSlot type=\"thumbnail\" [item]=\"item\" [templates]=\"templates\"></p-galleriaItemSlot>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                <button\n                    *ngIf=\"showThumbnailNavigators\"\n                    type=\"button\"\n                    [ngClass]=\"{ 'p-galleria-thumbnail-next p-link': true, 'p-disabled': this.isNavForwardDisabled() }\"\n                    (click)=\"navForward($event)\"\n                    [disabled]=\"isNavForwardDisabled()\"\n                    pRipple\n                    [attr.aria-label]=\"ariaNextButtonLabel()\"\n                >\n                    <ng-container *ngIf=\"!galleria.nextThumbnailIconTemplate\">\n                        <ChevronRightIcon *ngIf=\"!isVertical\" [ngClass]=\"'p-galleria-thumbnail-next-icon'\" />\n                        <ChevronDownIcon *ngIf=\"isVertical\" [ngClass]=\"'p-galleria-thumbnail-next-icon'\" />\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"galleria.nextThumbnailIconTemplate\"></ng-template>\n                </button>\n            </div>\n        </div>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i2.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgForOf), selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i2.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i0.forwardRef(() => i3.Ripple), selector: \"[pRipple]\" }, { kind: \"component\", type: i0.forwardRef(() => ChevronRightIcon), selector: \"ChevronRightIcon\" }, { kind: \"component\", type: i0.forwardRef(() => ChevronLeftIcon), selector: \"ChevronLeftIcon\" }, { kind: \"component\", type: i0.forwardRef(() => GalleriaItemSlot), selector: \"p-galleriaItemSlot\", inputs: [\"templates\", \"index\", \"item\", \"type\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: GalleriaThumbnails, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-galleriaThumbnails',\n                    template: `\n        <div class=\"p-galleria-thumbnail-wrapper\">\n            <div class=\"p-galleria-thumbnail-container\">\n                <button\n                    *ngIf=\"showThumbnailNavigators\"\n                    type=\"button\"\n                    [ngClass]=\"{ 'p-galleria-thumbnail-prev p-link': true, 'p-disabled': this.isNavBackwardDisabled() }\"\n                    (click)=\"navBackward($event)\"\n                    [disabled]=\"isNavBackwardDisabled()\"\n                    pRipple\n                    [attr.aria-label]=\"ariaPrevButtonLabel()\"\n                >\n                    <ng-container *ngIf=\"!galleria.previousThumbnailIconTemplate\">\n                        <ChevronLeftIcon *ngIf=\"!isVertical\" [styleClass]=\"'p-galleria-thumbnail-prev-icon'\" />\n                        <ChevronUpIcon *ngIf=\"isVertical\" [styleClass]=\"'p-galleria-thumbnail-prev-icon'\" />\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"galleria.previousThumbnailIconTemplate\"></ng-template>\n                </button>\n                <div class=\"p-galleria-thumbnail-items-container\" [ngStyle]=\"{ height: isVertical ? contentHeight : '' }\">\n                    <div #itemsContainer class=\"p-galleria-thumbnail-items\" (transitionend)=\"onTransitionEnd()\" (touchstart)=\"onTouchStart($event)\" (touchmove)=\"onTouchMove($event)\" role=\"tablist\">\n                        <div\n                            *ngFor=\"let item of value; let index = index\"\n                            [ngClass]=\"{\n                                'p-galleria-thumbnail-item': true,\n                                'p-galleria-thumbnail-item-current': activeIndex === index,\n                                'p-galleria-thumbnail-item-active': isItemActive(index),\n                                'p-galleria-thumbnail-item-start': firstItemAciveIndex() === index,\n                                'p-galleria-thumbnail-item-end': lastItemActiveIndex() === index\n                            }\"\n                            [attr.aria-selected]=\"activeIndex === index\"\n                            [attr.aria-controls]=\"containerId + '_item_' + index\"\n                            [attr.data-pc-section]=\"'thumbnailitem'\"\n                            [attr.data-p-active]=\"activeIndex === index\"\n                            (keydown)=\"onThumbnailKeydown($event, index)\"\n                        >\n                            <div\n                                class=\"p-galleria-thumbnail-item-content\"\n                                [attr.tabindex]=\"activeIndex === index ? 0 : -1\"\n                                [attr.aria-current]=\"activeIndex === index ? 'page' : undefined\"\n                                [attr.aria-label]=\"ariaPageLabel(index + 1)\"\n                                (click)=\"onItemClick(index)\"\n                                (touchend)=\"onItemClick(index)\"\n                                (keydown.enter)=\"onItemClick(index)\"\n                            >\n                                <p-galleriaItemSlot type=\"thumbnail\" [item]=\"item\" [templates]=\"templates\"></p-galleriaItemSlot>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                <button\n                    *ngIf=\"showThumbnailNavigators\"\n                    type=\"button\"\n                    [ngClass]=\"{ 'p-galleria-thumbnail-next p-link': true, 'p-disabled': this.isNavForwardDisabled() }\"\n                    (click)=\"navForward($event)\"\n                    [disabled]=\"isNavForwardDisabled()\"\n                    pRipple\n                    [attr.aria-label]=\"ariaNextButtonLabel()\"\n                >\n                    <ng-container *ngIf=\"!galleria.nextThumbnailIconTemplate\">\n                        <ChevronRightIcon *ngIf=\"!isVertical\" [ngClass]=\"'p-galleria-thumbnail-next-icon'\" />\n                        <ChevronDownIcon *ngIf=\"isVertical\" [ngClass]=\"'p-galleria-thumbnail-next-icon'\" />\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"galleria.nextThumbnailIconTemplate\"></ng-template>\n                </button>\n            </div>\n        </div>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush\n                }]\n        }], ctorParameters: () => [{ type: Galleria }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }], propDecorators: { containerId: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], isVertical: [{\n                type: Input\n            }], slideShowActive: [{\n                type: Input\n            }], circular: [{\n                type: Input\n            }], responsiveOptions: [{\n                type: Input\n            }], contentHeight: [{\n                type: Input\n            }], showThumbnailNavigators: [{\n                type: Input\n            }], templates: [{\n                type: Input\n            }], onActiveIndexChange: [{\n                type: Output\n            }], stopSlideShow: [{\n                type: Output\n            }], itemsContainer: [{\n                type: ViewChild,\n                args: ['itemsContainer']\n            }], numVisible: [{\n                type: Input\n            }], activeIndex: [{\n                type: Input\n            }] } });\nclass GalleriaModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: GalleriaModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: GalleriaModule, declarations: [Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails], imports: [CommonModule, SharedModule, RippleModule, TimesIcon, ChevronRightIcon, ChevronLeftIcon, WindowMaximizeIcon, WindowMinimizeIcon, FocusTrapModule], exports: [CommonModule, Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: GalleriaModule, imports: [CommonModule, SharedModule, RippleModule, TimesIcon, ChevronRightIcon, ChevronLeftIcon, WindowMaximizeIcon, WindowMinimizeIcon, FocusTrapModule, CommonModule, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: GalleriaModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, SharedModule, RippleModule, TimesIcon, ChevronRightIcon, ChevronLeftIcon, WindowMaximizeIcon, WindowMinimizeIcon, FocusTrapModule],\n                    exports: [CommonModule, Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails, SharedModule],\n                    declarations: [Galleria, GalleriaContent, GalleriaItemSlot, GalleriaItem, GalleriaThumbnails]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Galleria, GalleriaContent, GalleriaItem, GalleriaItemSlot, GalleriaModule, GalleriaThumbnails };\n"], "mappings": "AAAA,SAASA,KAAK,EAAEC,OAAO,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AACzE,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,QAAQ,EAAEC,iBAAiB,EAAEC,YAAY,QAAQ,iBAAiB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AAC7K,OAAO,KAAKC,EAAE,MAAM,aAAa;AACjC,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,WAAW,EAAEC,iBAAiB,QAAQ,eAAe;AAC9D,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,eAAe,QAAQ,2BAA2B;;AAE3D;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAA;EAAA,sBAAAA;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAD,EAAA,EAAAE,EAAA;EAAAC,oBAAA,EAAAH,EAAA;EAAAI,oBAAA,EAAAF;AAAA;AAAA,MAAAG,GAAA,GAAAL,EAAA;EAAAM,KAAA;EAAAC,MAAA,EAAAP;AAAA;AAAA,SAAAQ,kDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAgS6F1C,EAAE,CAAA2C,gBAAA;IAAF3C,EAAE,CAAA4C,cAAA,0BAsB/E,CAAC;IAtB4E5C,EAAE,CAAA6C,UAAA,8BAAAC,yGAAAC,MAAA;MAAF/C,EAAE,CAAAgD,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAcvDF,MAAA,CAAAG,gBAAA,CAAAL,MAAuB,CAAC;IAAA,EAAC,6BAAAM,wGAAAN,MAAA;MAd4B/C,EAAE,CAAAgD,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAexDF,MAAA,CAAAK,cAAA,CAAAP,MAAqB,CAAC;IAAA,EAAC,sBAAAQ,wFAAA;MAf+BvD,EAAE,CAAAgD,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAmB/DF,MAAA,CAAAO,UAAA,CAAW,CAAC;IAAA,EAAC,8BAAAC,gGAAAV,MAAA;MAnBgD/C,EAAE,CAAAgD,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAoBvDF,MAAA,CAAAS,kBAAA,CAAAX,MAAyB,CAAC;IAAA,EAAC;IApB0B/C,EAAE,CAAA2D,YAAA,CAsB3D,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAS,MAAA,GAtBwDjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA4D,UAAA,eAAF5D,EAAE,CAAA6D,eAAA,IAAAzB,GAAA,EAAFpC,EAAE,CAAA8D,eAAA,IAAA9B,GAAA,EAAAiB,MAAA,CAAAc,qBAAA,EAAAd,MAAA,CAAAe,qBAAA,EAa6D,CAAC,UAAAf,MAAA,CAAAZ,KAG3H,CAAC,gBAAAY,MAAA,CAAAgB,WACW,CAAC,eAAAhB,MAAA,CAAAiB,eAAA,IAAAjB,MAAA,CAAAkB,UACgB,CAAC,YAAAlB,MAAA,CAAAmB,cAGnB,CAAC;EAAA;AAAA;AAAA,SAAAC,8BAAA7B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArB+CxC,EAAE,CAAA4C,cAAA,eAUnF,CAAC;IAVgF5C,EAAE,CAAAsE,UAAA,IAAA/B,iDAAA,+BAsB/E,CAAC;IAtB4EvC,EAAE,CAAA2D,YAAA,CAuB9E,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAS,MAAA,GAvB2EjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAuE,UAAA,CAAAtB,MAAA,CAAAuB,SAO7D,CAAC;IAP0DxE,EAAE,CAAA4D,UAAA,YAAF5D,EAAE,CAAA6D,eAAA,IAAA/B,GAAA,EAAAmB,MAAA,CAAAwB,OAAA,CAMyC,CAAC;IAN5CzE,EAAE,CAAA0E,WAAA,SAAAzB,MAAA,CAAA0B,UAAA,sCAAA1B,MAAA,CAAA0B,UAAA,YAAAC,SAAA;IAAF5E,EAAE,CAAA6E,SAAA,EAY9D,CAAC;IAZ2D7E,EAAE,CAAA4D,UAAA,SAAAX,MAAA,CAAAwB,OAY9D,CAAC;EAAA;AAAA;AAAA,SAAAK,wBAAAtC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAZ2DxC,EAAE,CAAA4C,cAAA,kBAEtC,CAAC;IAFmC5C,EAAE,CAAAsE,UAAA,IAAAD,6BAAA,gBAUnF,CAAC;IAVgFrE,EAAE,CAAA2D,YAAA,CAwBlF,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAS,MAAA,GAxB+EjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA6E,SAAA,EAI9D,CAAC;IAJ2D7E,EAAE,CAAA4D,UAAA,SAAAX,MAAA,CAAA8B,WAI9D,CAAC;EAAA;AAAA;AAAA,SAAAC,gCAAAxC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAyC,GAAA,GAJ2DjF,EAAE,CAAA2C,gBAAA;IAAF3C,EAAE,CAAA4C,cAAA,0BA2BwE,CAAC;IA3B3E5C,EAAE,CAAA6C,UAAA,8BAAAqC,8EAAAnC,MAAA;MAAF/C,EAAE,CAAAgD,aAAA,CAAAiC,GAAA;MAAA,MAAAhC,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CA2B6CF,MAAA,CAAAS,kBAAA,CAAAX,MAAyB,CAAC;IAAA,EAAC;IA3B1E/C,EAAE,CAAA2D,YAAA,CA2B4F,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAS,MAAA,GA3B/FjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA4D,UAAA,UAAAX,MAAA,CAAAZ,KA2BlD,CAAC,gBAAAY,MAAA,CAAAgB,WAA2B,CAAC,eAAAhB,MAAA,CAAAiB,eAAA,IAAAjB,MAAA,CAAAkB,UAA4C,CAAC;EAAA;AAAA;AAAA,MAAAgB,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAArD,EAAA,EAAAE,EAAA,EAAAoD,EAAA;EAAA;EAAA,yBAAAtD,EAAA;EAAA,+BAAAE,EAAA;EAAA,+BAAAoD;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAA;AAAA,SAAAC,oDAAA/C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3B1BxC,EAAE,CAAAwF,SAAA,mBAwPQ,CAAC;EAAA;EAAA,IAAAhD,EAAA;IAxPXxC,EAAE,CAAA4D,UAAA,sCAwPK,CAAC;EAAA;AAAA;AAAA,SAAA6B,wDAAAjD,EAAA,EAAAC,GAAA;AAAA,SAAAiD,0CAAAlD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxPRxC,EAAE,CAAAsE,UAAA,IAAAmB,uDAAA,qBAyPpB,CAAC;EAAA;AAAA;AAAA,SAAAE,wCAAAnD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAoD,GAAA,GAzPiB5F,EAAE,CAAA2C,gBAAA;IAAF3C,EAAE,CAAA4C,cAAA,eAuP4G,CAAC;IAvP/G5C,EAAE,CAAA6C,UAAA,mBAAAgD,gEAAA;MAAF7F,EAAE,CAAAgD,aAAA,CAAA4C,GAAA;MAAA,MAAAE,MAAA,GAAF9F,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAuPQ2C,MAAA,CAAAC,QAAA,CAAAC,IAAA,CAAc,CAAC;IAAA,EAAC;IAvP1BhG,EAAE,CAAAsE,UAAA,IAAAiB,mDAAA,sBAwPQ,CAAC,IAAAG,yCAAA,gBAC7B,CAAC;IAzPiB1F,EAAE,CAAA2D,YAAA,CA0P3E,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAsD,MAAA,GA1PwE9F,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA0E,WAAA,eAAAoB,MAAA,CAAAG,cAAA;IAAFjG,EAAE,CAAA6E,SAAA,CAwPnC,CAAC;IAxPgC7E,EAAE,CAAA4D,UAAA,UAAAkC,MAAA,CAAAI,QAAA,CAAAC,iBAwPnC,CAAC;IAxPgCnG,EAAE,CAAA6E,SAAA,CAyPtB,CAAC;IAzPmB7E,EAAE,CAAA4D,UAAA,qBAAAkC,MAAA,CAAAI,QAAA,CAAAC,iBAyPtB,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAA5D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzPmBxC,EAAE,CAAA4C,cAAA,aA2PF,CAAC;IA3PD5C,EAAE,CAAAwF,SAAA,4BA4PQ,CAAC;IA5PXxF,EAAE,CAAA2D,YAAA,CA6P9E,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAsD,MAAA,GA7P2E9F,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA6E,SAAA,CA4Pd,CAAC;IA5PW7E,EAAE,CAAA4D,UAAA,cAAAkC,MAAA,CAAAI,QAAA,CAAAG,SA4Pd,CAAC;EAAA;AAAA;AAAA,SAAAC,sDAAA9D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+D,GAAA,GA5PWvG,EAAE,CAAA2C,gBAAA;IAAF3C,EAAE,CAAA4C,cAAA,8BAgS/E,CAAC;IAhS4E5C,EAAE,CAAA6C,UAAA,iCAAA2D,0GAAAzD,MAAA;MAAF/C,EAAE,CAAAgD,aAAA,CAAAuD,GAAA;MAAA,MAAAT,MAAA,GAAF9F,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAqRpD2C,MAAA,CAAAW,mBAAA,CAAA1D,MAA0B,CAAC;IAAA,EAAC,2BAAA2D,oGAAA;MArRsB1G,EAAE,CAAAgD,aAAA,CAAAuD,GAAA;MAAA,MAAAT,MAAA,GAAF9F,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CA+R1D2C,MAAA,CAAAa,aAAA,CAAc,CAAC;IAAA,EAAC;IA/RwC3G,EAAE,CAAA2D,YAAA,CAgSxD,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAsD,MAAA,GAhSqD9F,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA4D,UAAA,gBAAAkC,MAAA,CAAAc,EAmR1D,CAAC,UAAAd,MAAA,CAAAzD,KACJ,CAAC,gBAAAyD,MAAA,CAAA7B,WAEW,CAAC,cAAA6B,MAAA,CAAAI,QAAA,CAAAG,SACI,CAAC,eAAAP,MAAA,CAAA3B,UACR,CAAC,sBAAA2B,MAAA,CAAAI,QAAA,CAAAW,iBACsB,CAAC,aAAAf,MAAA,CAAAI,QAAA,CAAAY,QACnB,CAAC,eAAAhB,MAAA,CAAAiB,UAAA,EACJ,CAAC,kBAAAjB,MAAA,CAAAI,QAAA,CAAAc,+BAC8B,CAAC,4BAAAlB,MAAA,CAAAI,QAAA,CAAAe,uBACC,CAAC,oBAAAnB,MAAA,CAAAoB,eAC1B,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAA3E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9RsCxC,EAAE,CAAA4C,cAAA,aAkSF,CAAC;IAlSD5C,EAAE,CAAAwF,SAAA,4BAmSQ,CAAC;IAnSXxF,EAAE,CAAA2D,YAAA,CAoS9E,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAsD,MAAA,GApS2E9F,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA6E,SAAA,CAmSd,CAAC;IAnSW7E,EAAE,CAAA4D,UAAA,cAAAkC,MAAA,CAAAI,QAAA,CAAAG,SAmSd,CAAC;EAAA;AAAA;AAAA,SAAAe,+BAAA5E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAnSW1C,EAAE,CAAA2C,gBAAA;IAAF3C,EAAE,CAAA4C,cAAA,YAsPvF,CAAC;IAtPoF5C,EAAE,CAAAsE,UAAA,IAAAqB,uCAAA,mBAuP4G,CAAC,IAAAS,oCAAA,gBAI/G,CAAC;IA3PDpG,EAAE,CAAA4C,cAAA,YA8PI,CAAC,uBAiBpF,CAAC;IA/Q4E5C,EAAE,CAAA6C,UAAA,iCAAAwE,6EAAAtE,MAAA;MAAF/C,EAAE,CAAAgD,aAAA,CAAAN,GAAA;MAAA,MAAAoD,MAAA,GAAF9F,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAqQpD2C,MAAA,CAAAW,mBAAA,CAAA1D,MAA0B,CAAC;IAAA,EAAC,4BAAAuE,wEAAA;MArQsBtH,EAAE,CAAAgD,aAAA,CAAAN,GAAA;MAAA,MAAAoD,MAAA,GAAF9F,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CA6QzD2C,MAAA,CAAAyB,cAAA,CAAe,CAAC;IAAA,EAAC,2BAAAC,uEAAA;MA7QsCxH,EAAE,CAAAgD,aAAA,CAAAN,GAAA;MAAA,MAAAoD,MAAA,GAAF9F,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CA8Q1D2C,MAAA,CAAAa,aAAA,CAAc,CAAC;IAAA,EAAC;IA9QwC3G,EAAE,CAAA2D,YAAA,CA+Q9D,CAAC;IA/Q2D3D,EAAE,CAAAsE,UAAA,IAAAgC,qDAAA,kCAgS/E,CAAC;IAhS4EtG,EAAE,CAAA2D,YAAA,CAiS9E,CAAC;IAjS2E3D,EAAE,CAAAsE,UAAA,IAAA6C,oCAAA,gBAkSF,CAAC;IAlSDnH,EAAE,CAAA2D,YAAA,CAqSlF,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAsD,MAAA,GArS+E9F,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAAuE,UAAA,CAAAuB,MAAA,CAAA2B,aAAA,EAoP3D,CAAC;IApPwDzH,EAAE,CAAA4D,UAAA,YAAF5D,EAAE,CAAA0H,eAAA,KAAAtC,GAAA,EAAAU,MAAA,CAAAI,QAAA,CAAAvB,UAAA,EAAAmB,MAAA,CAAAI,QAAA,CAAAyB,oBAAA,EAAA7B,MAAA,CAAAI,QAAA,CAAA0B,yBAAA,KAAA9B,MAAA,CAAAI,QAAA,CAAAvB,UAAA,CAkPlF,CAAC,aAAAmB,MAAA,CAAAI,QAAA,CAAAvB,UAAA,GAAAmB,MAAA,CAAAI,QAAA,CAAA9B,cAAA,GAlP+EpE,EAAE,CAAA6H,eAAA,KAAAvC,GAAA,CAmPrB,CAAC;IAnPkBtF,EAAE,CAAA0E,WAAA,OAAAoB,MAAA,CAAAc,EAAA;IAAF5G,EAAE,CAAA6E,SAAA,CAuPlD,CAAC;IAvP+C7E,EAAE,CAAA4D,UAAA,SAAAkC,MAAA,CAAAI,QAAA,CAAAvB,UAuPlD,CAAC;IAvP+C3E,EAAE,CAAA6E,SAAA,CA2P9B,CAAC;IA3P2B7E,EAAE,CAAA4D,UAAA,SAAAkC,MAAA,CAAAI,QAAA,CAAAG,SAAA,IAAAP,MAAA,CAAAI,QAAA,CAAA4B,WA2P9B,CAAC;IA3P2B9H,EAAE,CAAA6E,SAAA,CA8PG,CAAC;IA9PN7E,EAAE,CAAA0E,WAAA,cAAAoB,MAAA,CAAAI,QAAA,CAAA6B,QAAA;IAAF/H,EAAE,CAAA6E,SAAA,CAgQnE,CAAC;IAhQgE7E,EAAE,CAAA4D,UAAA,OAAAkC,MAAA,CAAAc,EAgQnE,CAAC,UAAAd,MAAA,CAAAzD,KACK,CAAC,gBAAAyD,MAAA,CAAA7B,WACW,CAAC,aAAA6B,MAAA,CAAAI,QAAA,CAAAY,QACE,CAAC,cAAAhB,MAAA,CAAAI,QAAA,CAAAG,SACC,CAAC,mBAAAP,MAAA,CAAAI,QAAA,CAAA8B,cAES,CAAC,+BAAAlC,MAAA,CAAAI,QAAA,CAAA+B,0BACuB,CAAC,mBAAAnC,MAAA,CAAAI,QAAA,CAAAgC,cACzB,CAAC,iBAAApC,MAAA,CAAAI,QAAA,CAAAiC,YACL,CAAC,uBAAArC,MAAA,CAAAI,QAAA,CAAAkC,kBACW,CAAC,aAAAtC,MAAA,CAAAI,QAAA,CAAA6B,QACrB,CAAC,oBAAAjC,MAAA,CAAAoB,eACI,CAAC;IA5QsClH,EAAE,CAAA6E,SAAA,CAkR9C,CAAC;IAlR2C7E,EAAE,CAAA4D,UAAA,SAAAkC,MAAA,CAAAI,QAAA,CAAAmC,cAkR9C,CAAC;IAlR2CrI,EAAE,CAAA6E,SAAA,CAkS9B,CAAC;IAlS2B7E,EAAE,CAAA4D,UAAA,SAAAkC,MAAA,CAAAI,QAAA,CAAAG,SAAA,IAAAP,MAAA,CAAAI,QAAA,CAAAoC,WAkS9B,CAAC;EAAA;AAAA;AAAA,SAAAC,wDAAA/F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlS2BxC,EAAE,CAAAwI,kBAAA,EA+aD,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAAjG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/aFxC,EAAE,CAAA0I,uBAAA,EA8alD,CAAC;IA9a+C1I,EAAE,CAAAsE,UAAA,IAAAiE,uDAAA,yBA+ahB,CAAC;IA/aavI,EAAE,CAAA2I,qBAAA;EAAA;EAAA,IAAAnG,EAAA;IAAA,MAAAoG,MAAA,GAAF5I,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA6E,SAAA,CA+alC,CAAC;IA/a+B7E,EAAE,CAAA4D,UAAA,qBAAAgF,MAAA,CAAAC,eA+alC,CAAC,4BAAAD,MAAA,CAAAE,OAAe,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA,GAAAhH,EAAA;EAAA;EAAA,cAAAA;AAAA;AAAA,MAAAiH,GAAA,GAAAjH,EAAA;EAAA;EAAA,cAAAA;AAAA;AAAA,MAAAkH,IAAA,GAAAlH,EAAA;EAAA;EAAA,eAAAA;AAAA;AAAA,SAAAmH,iDAAA1G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/aexC,EAAE,CAAAwF,SAAA,yBAkkB6B,CAAC;EAAA;EAAA,IAAAhD,EAAA;IAlkBhCxC,EAAE,CAAA4D,UAAA,0CAkkB0B,CAAC;EAAA;AAAA;AAAA,SAAAuF,+CAAA3G,EAAA,EAAAC,GAAA;AAAA,SAAA2G,iCAAA5G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlkB7BxC,EAAE,CAAAsE,UAAA,IAAA6E,8CAAA,qBAmkBT,CAAC;EAAA;AAAA;AAAA,SAAAE,+BAAA7G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAnkBM1C,EAAE,CAAA2C,gBAAA;IAAF3C,EAAE,CAAA4C,cAAA,eAikB/E,CAAC;IAjkB4E5C,EAAE,CAAA6C,UAAA,mBAAAyG,uDAAAvG,MAAA;MAAF/C,EAAE,CAAAgD,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CA8jBlEF,MAAA,CAAAsG,WAAA,CAAAxG,MAAkB,CAAC;IAAA,EAAC;IA9jB4C/C,EAAE,CAAAsE,UAAA,IAAA4E,gDAAA,4BAkkB6B,CAAC,IAAAE,gCAAA,gBACvC,CAAC;IAnkBMpJ,EAAE,CAAA2D,YAAA,CAokBvE,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAS,MAAA,GApkBoEjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA4D,UAAA,YAAF5D,EAAE,CAAA6D,eAAA,IAAAkF,GAAA,EAAA9F,MAAA,CAAAuG,qBAAA,GA6jBuC,CAAC,aAAAvG,MAAA,CAAAuG,qBAAA,EAEhF,CAAC;IA/jBqCxJ,EAAE,CAAA6E,SAAA,CAkkBlB,CAAC;IAlkBe7E,EAAE,CAAA4D,UAAA,UAAAX,MAAA,CAAAiD,QAAA,CAAAuD,wBAkkBlB,CAAC;IAlkBezJ,EAAE,CAAA6E,SAAA,CAmkBX,CAAC;IAnkBQ7E,EAAE,CAAA4D,UAAA,qBAAAX,MAAA,CAAAiD,QAAA,CAAAuD,wBAmkBX,CAAC;EAAA;AAAA;AAAA,SAAAC,kDAAAlH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnkBQxC,EAAE,CAAAwF,SAAA,0BAilB0B,CAAC;EAAA;EAAA,IAAAhD,EAAA;IAjlB7BxC,EAAE,CAAA4D,UAAA,0CAilBuB,CAAC;EAAA;AAAA;AAAA,SAAA+F,+CAAAnH,EAAA,EAAAC,GAAA;AAAA,SAAAmH,iCAAApH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjlB1BxC,EAAE,CAAAsE,UAAA,IAAAqF,8CAAA,qBAklBb,CAAC;EAAA;AAAA;AAAA,SAAAE,+BAAArH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAyC,GAAA,GAllBUjF,EAAE,CAAA2C,gBAAA;IAAF3C,EAAE,CAAA4C,cAAA,gBAglB/E,CAAC;IAhlB4E5C,EAAE,CAAA6C,UAAA,mBAAAiH,uDAAA/G,MAAA;MAAF/C,EAAE,CAAAgD,aAAA,CAAAiC,GAAA;MAAA,MAAAhC,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CA4kBlEF,MAAA,CAAA8G,UAAA,CAAAhH,MAAiB,CAAC;IAAA,EAAC;IA5kB6C/C,EAAE,CAAAsE,UAAA,IAAAoF,iDAAA,6BAilB0B,CAAC,IAAAE,gCAAA,gBACxC,CAAC;IAllBU5J,EAAE,CAAA2D,YAAA,CAmlBvE,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAS,MAAA,GAnlBoEjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA4D,UAAA,YAAF5D,EAAE,CAAA6D,eAAA,IAAAmF,GAAA,EAAA/F,MAAA,CAAA+G,oBAAA,GA2kBsC,CAAC,aAAA/G,MAAA,CAAA+G,oBAAA,EAEhF,CAAC;IA7kBsChK,EAAE,CAAA6E,SAAA,CAilBrB,CAAC;IAjlBkB7E,EAAE,CAAA4D,UAAA,UAAAX,MAAA,CAAAiD,QAAA,CAAA+D,oBAilBrB,CAAC;IAjlBkBjK,EAAE,CAAA6E,SAAA,CAklBf,CAAC;IAllBY7E,EAAE,CAAA4D,UAAA,qBAAAX,MAAA,CAAAiD,QAAA,CAAA+D,oBAklBf,CAAC;EAAA;AAAA;AAAA,SAAAC,4BAAA1H,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAllBYxC,EAAE,CAAA4C,cAAA,aAolB3B,CAAC;IAplBwB5C,EAAE,CAAAwF,SAAA,4BAqlBwB,CAAC;IArlB3BxF,EAAE,CAAA2D,YAAA,CAslB1E,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAS,MAAA,GAtlBuEjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA6E,SAAA,CAqlBtB,CAAC;IArlBmB7E,EAAE,CAAA4D,UAAA,SAAAX,MAAA,CAAAkH,UAqlBtB,CAAC,cAAAlH,MAAA,CAAAoD,SAAuB,CAAC;EAAA;AAAA;AAAA,SAAA+D,yCAAA5H,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArlBLxC,EAAE,CAAAwF,SAAA,gBAomBQ,CAAC;EAAA;AAAA;AAAA,SAAA6E,gCAAA7H,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+D,GAAA,GApmBXvG,EAAE,CAAA2C,gBAAA;IAAF3C,EAAE,CAAA4C,cAAA,YAmmB/E,CAAC;IAnmB4E5C,EAAE,CAAA6C,UAAA,mBAAAyH,oDAAA;MAAA,MAAAC,QAAA,GAAFvK,EAAE,CAAAgD,aAAA,CAAAuD,GAAA,EAAAiE,KAAA;MAAA,MAAAvH,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CA4lBlEF,MAAA,CAAAwH,gBAAA,CAAAF,QAAsB,CAAC;IAAA,EAAC,wBAAAG,yDAAA;MAAA,MAAAH,QAAA,GA5lBwCvK,EAAE,CAAAgD,aAAA,CAAAuD,GAAA,EAAAiE,KAAA;MAAA,MAAAvH,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CA6lB7DF,MAAA,CAAA0H,qBAAA,CAAAJ,QAA2B,CAAC;IAAA,EAAC,qBAAAK,sDAAA7H,MAAA;MAAA,MAAAwH,QAAA,GA7lB8BvK,EAAE,CAAAgD,aAAA,CAAAuD,GAAA,EAAAiE,KAAA;MAAA,MAAAvH,MAAA,GAAFjD,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CA8lBhEF,MAAA,CAAA4H,kBAAA,CAAA9H,MAAA,EAAAwH,QAAgC,CAAC;IAAA,EAAC;IA9lB4BvK,EAAE,CAAAsE,UAAA,IAAA8F,wCAAA,oBAomBD,CAAC;IApmBFpK,EAAE,CAAAwF,SAAA,4BAqmBsB,CAAC;IArmBzBxF,EAAE,CAAA2D,YAAA,CAsmB3E,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAA+H,QAAA,GAAA9H,GAAA,CAAA+H,KAAA;IAAA,MAAAvH,MAAA,GAtmBwEjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA4D,UAAA,YAAF5D,EAAE,CAAA6D,eAAA,IAAAoF,IAAA,EAAAhG,MAAA,CAAA6H,qBAAA,CAAAP,QAAA,EA+lBa,CAAC;IA/lBhBvK,EAAE,CAAA0E,WAAA,eAAAzB,MAAA,CAAA8H,aAAA,CAAAR,QAAA,wBAAAtH,MAAA,CAAAgB,WAAA,KAAAsG,QAAA,mBAAAtH,MAAA,CAAA2D,EAAA,cAAA2D,QAAA;IAAFvK,EAAE,CAAA6E,SAAA,CAomBH,CAAC;IApmBA7E,EAAE,CAAA4D,UAAA,UAAAX,MAAA,CAAAiF,cAomBH,CAAC;IApmBAlI,EAAE,CAAA6E,SAAA,CAqmBxB,CAAC;IArmBqB7E,EAAE,CAAA4D,UAAA,UAAA2G,QAqmBxB,CAAC,cAAAtH,MAAA,CAAAoD,SAAuB,CAAC;EAAA;AAAA;AAAA,SAAA2E,2BAAAxI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArmBHxC,EAAE,CAAA4C,cAAA,YAwlBnB,CAAC;IAxlBgB5C,EAAE,CAAAsE,UAAA,IAAA+F,+BAAA,gBAmmB/E,CAAC;IAnmB4ErK,EAAE,CAAA2D,YAAA,CAumB/E,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAS,MAAA,GAvmB4EjD,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA6E,SAAA,CA0lBjD,CAAC;IA1lB8C7E,EAAE,CAAA4D,UAAA,YAAAX,MAAA,CAAAZ,KA0lBjD,CAAC;EAAA;AAAA;AAAA,MAAA4I,IAAA;AAAA,MAAAC,IAAA,GAAAnJ,EAAA;EAAAoJ,MAAA,EAAApJ;AAAA;AAAA,MAAAqJ,IAAA,GAAArJ,EAAA;EAAA;EAAA,cAAAA;AAAA;AAAA,MAAAsJ,IAAA,GAAAA,CAAAtJ,EAAA,EAAAE,EAAA,EAAAoD,EAAA,EAAAiG,EAAA;EAAA;EAAA,qCAAAvJ,EAAA;EAAA,oCAAAE,EAAA;EAAA,mCAAAoD,EAAA;EAAA,iCAAAiG;AAAA;AAAA,MAAAC,IAAA,GAAAxJ,EAAA;EAAA;EAAA,cAAAA;AAAA;AAAA,SAAAyJ,sEAAAhJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1lB8CxC,EAAE,CAAAwF,SAAA,yBAylCe,CAAC;EAAA;EAAA,IAAAhD,EAAA;IAzlClBxC,EAAE,CAAA4D,UAAA,+CAylCY,CAAC;EAAA;AAAA;AAAA,SAAA6H,oEAAAjJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzlCfxC,EAAE,CAAAwF,SAAA,uBA0lCY,CAAC;EAAA;EAAA,IAAAhD,EAAA;IA1lCfxC,EAAE,CAAA4D,UAAA,+CA0lCS,CAAC;EAAA;AAAA;AAAA,SAAA8H,oDAAAlJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1lCZxC,EAAE,CAAA0I,uBAAA,EAwlCd,CAAC;IAxlCW1I,EAAE,CAAAsE,UAAA,IAAAkH,qEAAA,6BAylCe,CAAC,IAAAC,mEAAA,2BACJ,CAAC;IA1lCfzL,EAAE,CAAA2I,qBAAA;EAAA;EAAA,IAAAnG,EAAA;IAAA,MAAAsD,MAAA,GAAF9F,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA6E,SAAA,CAylCrC,CAAC;IAzlCkC7E,EAAE,CAAA4D,UAAA,UAAAkC,MAAA,CAAAiB,UAylCrC,CAAC;IAzlCkC/G,EAAE,CAAA6E,SAAA,CA0lCxC,CAAC;IA1lCqC7E,EAAE,CAAA4D,UAAA,SAAAkC,MAAA,CAAAiB,UA0lCxC,CAAC;EAAA;AAAA;AAAA,SAAA4E,qDAAAnJ,EAAA,EAAAC,GAAA;AAAA,SAAAmJ,uCAAApJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1lCqCxC,EAAE,CAAAsE,UAAA,IAAAqH,oDAAA,qBA4lCJ,CAAC;EAAA;AAAA;AAAA,SAAAE,qCAAArJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAoD,GAAA,GA5lCC5F,EAAE,CAAA2C,gBAAA;IAAF3C,EAAE,CAAA4C,cAAA,eAulC/E,CAAC;IAvlC4E5C,EAAE,CAAA6C,UAAA,mBAAAiJ,6DAAA/I,MAAA;MAAF/C,EAAE,CAAAgD,aAAA,CAAA4C,GAAA;MAAA,MAAAE,MAAA,GAAF9F,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAmlClE2C,MAAA,CAAAyD,WAAA,CAAAxG,MAAkB,CAAC;IAAA,EAAC;IAnlC4C/C,EAAE,CAAAsE,UAAA,IAAAoH,mDAAA,yBAwlCd,CAAC,IAAAE,sCAAA,eAIS,CAAC;IA5lCC5L,EAAE,CAAA2D,YAAA,CA6lCvE,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAsD,MAAA,GA7lCoE9F,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA4D,UAAA,YAAF5D,EAAE,CAAA6D,eAAA,IAAAuH,IAAA,EAAAtF,MAAA,CAAA0D,qBAAA,GAklCwB,CAAC,aAAA1D,MAAA,CAAA0D,qBAAA,EAEjE,CAAC;IAplCqCxJ,EAAE,CAAA0E,WAAA,eAAAoB,MAAA,CAAAiG,mBAAA;IAAF/L,EAAE,CAAA6E,SAAA,CAwlChB,CAAC;IAxlCa7E,EAAE,CAAA4D,UAAA,UAAAkC,MAAA,CAAAI,QAAA,CAAA8F,6BAwlChB,CAAC;IAxlCahM,EAAE,CAAA6E,SAAA,CA4lCN,CAAC;IA5lCG7E,EAAE,CAAA4D,UAAA,qBAAAkC,MAAA,CAAAI,QAAA,CAAA8F,6BA4lCN,CAAC;EAAA;AAAA;AAAA,SAAAC,kCAAAzJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+D,GAAA,GA5lCGvG,EAAE,CAAA2C,gBAAA;IAAF3C,EAAE,CAAA4C,cAAA,aA8mCvE,CAAC;IA9mCoE5C,EAAE,CAAA6C,UAAA,qBAAAqJ,yDAAAnJ,MAAA;MAAA,MAAAwH,QAAA,GAAFvK,EAAE,CAAAgD,aAAA,CAAAuD,GAAA,EAAAiE,KAAA;MAAA,MAAA1E,MAAA,GAAF9F,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CA6mCxD2C,MAAA,CAAAqG,kBAAA,CAAApJ,MAAA,EAAAwH,QAAgC,CAAC;IAAA,EAAC;IA7mCoBvK,EAAE,CAAA4C,cAAA,aAunCnE,CAAC;IAvnCgE5C,EAAE,CAAA6C,UAAA,mBAAAuJ,uDAAA;MAAA,MAAA7B,QAAA,GAAFvK,EAAE,CAAAgD,aAAA,CAAAuD,GAAA,EAAAiE,KAAA;MAAA,MAAA1E,MAAA,GAAF9F,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAonCtD2C,MAAA,CAAAuG,WAAA,CAAA9B,QAAiB,CAAC;IAAA,EAAC,sBAAA+B,0DAAA;MAAA,MAAA/B,QAAA,GApnCiCvK,EAAE,CAAAgD,aAAA,CAAAuD,GAAA,EAAAiE,KAAA;MAAA,MAAA1E,MAAA,GAAF9F,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAqnCnD2C,MAAA,CAAAuG,WAAA,CAAA9B,QAAiB,CAAC;IAAA,EAAC,2BAAAgC,+DAAA;MAAA,MAAAhC,QAAA,GArnC8BvK,EAAE,CAAAgD,aAAA,CAAAuD,GAAA,EAAAiE,KAAA;MAAA,MAAA1E,MAAA,GAAF9F,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAsnC9C2C,MAAA,CAAAuG,WAAA,CAAA9B,QAAiB,CAAC;IAAA,EAAC;IAtnCyBvK,EAAE,CAAAwF,SAAA,4BAwnCgC,CAAC;IAxnCnCxF,EAAE,CAAA2D,YAAA,CAynC9D,CAAC,CACL,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAgK,OAAA,GAAA/J,GAAA,CAAAgK,SAAA;IAAA,MAAAlC,QAAA,GAAA9H,GAAA,CAAA+H,KAAA;IAAA,MAAA1E,MAAA,GA1nC+D9F,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA4D,UAAA,YAAF5D,EAAE,CAAA0M,eAAA,KAAArB,IAAA,EAAAvF,MAAA,CAAA7B,WAAA,KAAAsG,QAAA,EAAAzE,MAAA,CAAA6G,YAAA,CAAApC,QAAA,GAAAzE,MAAA,CAAA8G,mBAAA,OAAArC,QAAA,EAAAzE,MAAA,CAAA+G,mBAAA,OAAAtC,QAAA,CAwmClE,CAAC;IAxmC+DvK,EAAE,CAAA0E,WAAA,kBAAAoB,MAAA,CAAA7B,WAAA,KAAAsG,QAAA,mBAAAzE,MAAA,CAAAgH,WAAA,cAAAvC,QAAA,uDAAAzE,MAAA,CAAA7B,WAAA,KAAAsG,QAAA;IAAFvK,EAAE,CAAA6E,SAAA,CAinChB,CAAC;IAjnCa7E,EAAE,CAAA0E,WAAA,aAAAoB,MAAA,CAAA7B,WAAA,KAAAsG,QAAA,2BAAAzE,MAAA,CAAA7B,WAAA,KAAAsG,QAAA,YAAA3F,SAAA,gBAAAkB,MAAA,CAAAiF,aAAA,CAAAR,QAAA;IAAFvK,EAAE,CAAA6E,SAAA,CAwnCd,CAAC;IAxnCW7E,EAAE,CAAA4D,UAAA,SAAA4I,OAwnCd,CAAC,cAAA1G,MAAA,CAAAO,SAAuB,CAAC;EAAA;AAAA;AAAA,SAAA0G,uEAAAvK,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxnCbxC,EAAE,CAAAwF,SAAA,0BAuoCa,CAAC;EAAA;EAAA,IAAAhD,EAAA;IAvoChBxC,EAAE,CAAA4D,UAAA,4CAuoCU,CAAC;EAAA;AAAA;AAAA,SAAAoJ,sEAAAxK,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvoCbxC,EAAE,CAAAwF,SAAA,yBAwoCW,CAAC;EAAA;EAAA,IAAAhD,EAAA;IAxoCdxC,EAAE,CAAA4D,UAAA,4CAwoCQ,CAAC;EAAA;AAAA;AAAA,SAAAqJ,oDAAAzK,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxoCXxC,EAAE,CAAA0I,uBAAA,EAsoClB,CAAC;IAtoCe1I,EAAE,CAAAsE,UAAA,IAAAyI,sEAAA,8BAuoCa,CAAC,IAAAC,qEAAA,6BACH,CAAC;IAxoCdhN,EAAE,CAAA2I,qBAAA;EAAA;EAAA,IAAAnG,EAAA;IAAA,MAAAsD,MAAA,GAAF9F,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA6E,SAAA,CAuoCpC,CAAC;IAvoCiC7E,EAAE,CAAA4D,UAAA,UAAAkC,MAAA,CAAAiB,UAuoCpC,CAAC;IAvoCiC/G,EAAE,CAAA6E,SAAA,CAwoCtC,CAAC;IAxoCmC7E,EAAE,CAAA4D,UAAA,SAAAkC,MAAA,CAAAiB,UAwoCtC,CAAC;EAAA;AAAA;AAAA,SAAAmG,qDAAA1K,EAAA,EAAAC,GAAA;AAAA,SAAA0K,uCAAA3K,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAxoCmCxC,EAAE,CAAAsE,UAAA,IAAA4I,oDAAA,qBA0oCR,CAAC;EAAA;AAAA;AAAA,SAAAE,qCAAA5K,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA6K,GAAA,GA1oCKrN,EAAE,CAAA2C,gBAAA;IAAF3C,EAAE,CAAA4C,cAAA,eAqoC/E,CAAC;IAroC4E5C,EAAE,CAAA6C,UAAA,mBAAAyK,6DAAAvK,MAAA;MAAF/C,EAAE,CAAAgD,aAAA,CAAAqK,GAAA;MAAA,MAAAvH,MAAA,GAAF9F,EAAE,CAAAkD,aAAA;MAAA,OAAFlD,EAAE,CAAAmD,WAAA,CAioClE2C,MAAA,CAAAiE,UAAA,CAAAhH,MAAiB,CAAC;IAAA,EAAC;IAjoC6C/C,EAAE,CAAAsE,UAAA,IAAA2I,mDAAA,yBAsoClB,CAAC,IAAAE,sCAAA,eAIS,CAAC;IA1oCKnN,EAAE,CAAA2D,YAAA,CA2oCvE,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAsD,MAAA,GA3oCoE9F,EAAE,CAAAkD,aAAA;IAAFlD,EAAE,CAAA4D,UAAA,YAAF5D,EAAE,CAAA6D,eAAA,IAAA0H,IAAA,EAAAzF,MAAA,CAAAkE,oBAAA,GAgoCuB,CAAC,aAAAlE,MAAA,CAAAkE,oBAAA,EAEjE,CAAC;IAloCsChK,EAAE,CAAA0E,WAAA,eAAAoB,MAAA,CAAAyH,mBAAA;IAAFvN,EAAE,CAAA6E,SAAA,CAsoCpB,CAAC;IAtoCiB7E,EAAE,CAAA4D,UAAA,UAAAkC,MAAA,CAAAI,QAAA,CAAAsH,yBAsoCpB,CAAC;IAtoCiBxN,EAAE,CAAA6E,SAAA,CA0oCV,CAAC;IA1oCO7E,EAAE,CAAA4D,UAAA,qBAAAkC,MAAA,CAAAI,QAAA,CAAAsH,yBA0oCV,CAAC;EAAA;AAAA;AAt6CtF,MAAMC,QAAQ,CAAC;EACXC,QAAQ;EACRC,UAAU;EACVC,OAAO;EACPC,EAAE;EACFC,MAAM;EACN;AACJ;AACA;AACA;EACI,IAAI7J,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC8J,YAAY;EAC5B;EACA,IAAI9J,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,CAAC8J,YAAY,GAAG9J,WAAW;EACnC;EACA;AACJ;AACA;AACA;EACIU,UAAU,GAAG,KAAK;EAClB;AACJ;AACA;AACA;EACIiC,EAAE;EACF;AACJ;AACA;AACA;EACIvE,KAAK;EACL;AACJ;AACA;AACA;EACI8B,UAAU,GAAG,CAAC;EACd;AACJ;AACA;AACA;AACA;EACI0C,iBAAiB;EACjB;AACJ;AACA;AACA;EACIuB,kBAAkB,GAAG,KAAK;EAC1B;AACJ;AACA;AACA;EACInB,uBAAuB,GAAG,IAAI;EAC9B;AACJ;AACA;AACA;EACIW,yBAAyB,GAAG,KAAK;EACjC;AACJ;AACA;AACA;EACIK,0BAA0B,GAAG,KAAK;EAClC;AACJ;AACA;AACA;EACInB,QAAQ,GAAG,KAAK;EAChB;AACJ;AACA;AACA;EACIiB,QAAQ,GAAG,KAAK;EAChB;AACJ;AACA;AACA;EACIiG,yBAAyB,GAAG,IAAI;EAChC;AACJ;AACA;AACA;EACIC,kBAAkB,GAAG,IAAI;EACzB;AACJ;AACA;AACA;EACI5F,cAAc,GAAG,IAAI;EACrB;AACJ;AACA;AACA;EACI6F,kBAAkB,GAAG,QAAQ;EAC7B;AACJ;AACA;AACA;EACIlH,+BAA+B,GAAG,OAAO;EACzC;AACJ;AACA;AACA;EACIgB,cAAc,GAAG,KAAK;EACtB;AACJ;AACA;AACA;EACIL,oBAAoB,GAAG,KAAK;EAC5B;AACJ;AACA;AACA;EACIwG,kBAAkB,GAAG,QAAQ;EAC7B;AACJ;AACA;AACA;EACIC,UAAU,GAAG,CAAC;EACd;AACJ;AACA;AACA;EACI5J,SAAS;EACT;AACJ;AACA;AACA;EACI6J,cAAc;EACd;AACJ;AACA;AACA;EACIjK,cAAc;EACd;AACJ;AACA;AACA;EACIL,qBAAqB,GAAG,kCAAkC;EAC1D;AACJ;AACA;AACA;EACIC,qBAAqB,GAAG,kCAAkC;EAC1D;AACJ;AACA;AACA;EACI,IAAIS,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC6J,QAAQ;EACxB;EACA,IAAI7J,OAAOA,CAACA,OAAO,EAAE;IACjB,IAAI,CAAC6J,QAAQ,GAAG7J,OAAO;IACvB,IAAI,IAAI,CAAC6J,QAAQ,IAAI,CAAC,IAAI,CAACvJ,WAAW,EAAE;MACpC,IAAI,CAACA,WAAW,GAAG,IAAI;IAC3B;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIwJ,iBAAiB,GAAG,IAAItO,YAAY,CAAC,CAAC;EACtC;AACJ;AACA;AACA;AACA;EACIuO,aAAa,GAAG,IAAIvO,YAAY,CAAC,CAAC;EAClCwO,IAAI;EACJC,SAAS;EACTrI,SAAS;EACTiI,QAAQ,GAAG,KAAK;EAChBP,YAAY,GAAG,CAAC;EAChBjG,WAAW;EACXQ,WAAW;EACXJ,cAAc;EACdC,YAAY;EACZhC,iBAAiB;EACjB6F,6BAA6B;EAC7BwB,yBAAyB;EACzB/D,wBAAwB;EACxBQ,oBAAoB;EACpBlF,WAAW,GAAG,KAAK;EACnBb,eAAe,GAAG,CAAC;EACnByK,WAAWA,CAACjB,QAAQ,EAAEC,UAAU,EAAEC,OAAO,EAAEC,EAAE,EAAEC,MAAM,EAAE;IACnD,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,MAAM,GAAGA,MAAM;EACxB;EACAc,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACvI,SAAS,EAAEwI,OAAO,CAAEC,IAAI,IAAK;MAC9B,QAAQA,IAAI,CAACC,OAAO,CAAC,CAAC;QAClB,KAAK,QAAQ;UACT,IAAI,CAACjH,WAAW,GAAGgH,IAAI,CAACE,QAAQ;UAChC;QACJ,KAAK,QAAQ;UACT,IAAI,CAAC1G,WAAW,GAAGwG,IAAI,CAACE,QAAQ;UAChC;QACJ,KAAK,WAAW;UACZ,IAAI,CAAC9G,cAAc,GAAG4G,IAAI,CAACE,QAAQ;UACnC;QACJ,KAAK,WAAW;UACZ,IAAI,CAAC7I,iBAAiB,GAAG2I,IAAI,CAACE,QAAQ;UACtC;QACJ,KAAK,cAAc;UACf,IAAI,CAAC/E,oBAAoB,GAAG6E,IAAI,CAACE,QAAQ;UACzC;QACJ,KAAK,kBAAkB;UACnB,IAAI,CAACvF,wBAAwB,GAAGqF,IAAI,CAACE,QAAQ;UAC7C;QACJ,KAAK,uBAAuB;UACxB,IAAI,CAAChD,6BAA6B,GAAG8C,IAAI,CAACE,QAAQ;UAClD;QACJ,KAAK,mBAAmB;UACpB,IAAI,CAACxB,yBAAyB,GAAGsB,IAAI,CAACE,QAAQ;UAC9C;QACJ,KAAK,SAAS;UACV,IAAI,CAAC7G,YAAY,GAAG2G,IAAI,CAACE,QAAQ;UACjC;MACR;IACJ,CAAC,CAAC;EACN;EACAC,WAAWA,CAACC,aAAa,EAAE;IACvB,IAAIA,aAAa,CAAC7M,KAAK,IAAI6M,aAAa,CAAC7M,KAAK,CAAC8M,YAAY,EAAEC,MAAM,GAAG,IAAI,CAACjL,UAAU,EAAE;MACnF,IAAI,CAACD,eAAe,GAAGgL,aAAa,CAAC7M,KAAK,CAAC8M,YAAY,CAACC,MAAM;IAClE,CAAC,MACI;MACD,IAAI,CAAClL,eAAe,GAAG,CAAC;IAC5B;EACJ;EACAV,UAAUA,CAAA,EAAG;IACT,IAAI,CAACiB,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC+J,aAAa,CAACxI,IAAI,CAAC,KAAK,CAAC;EAClC;EACAtC,kBAAkBA,CAAC8G,KAAK,EAAE;IACtB,IAAI,IAAI,CAACvG,WAAW,KAAKuG,KAAK,EAAE;MAC5B,IAAI,CAACvG,WAAW,GAAGuG,KAAK;MACxB,IAAI,CAAC+D,iBAAiB,CAACvI,IAAI,CAACwE,KAAK,CAAC;IACtC;EACJ;EACApH,gBAAgBA,CAACiM,KAAK,EAAE;IACpB,QAAQA,KAAK,CAACC,OAAO;MACjB,KAAK,SAAS;QACV,IAAI,CAACC,cAAc,CAAC,CAAC;QACrBC,UAAU,CAAC,MAAM;UACbzO,UAAU,CAAC0O,KAAK,CAAC1O,UAAU,CAAC2O,UAAU,CAAC,IAAI,CAAChB,SAAS,CAACiB,aAAa,EAAE,iCAAiC,CAAC,CAAC;QAC5G,CAAC,EAAE,EAAE,CAAC;QACN;MACJ,KAAK,MAAM;QACP5O,UAAU,CAAC6O,QAAQ,CAAC,IAAI,CAACnB,IAAI,EAAEkB,aAAa,EAAE,2BAA2B,CAAC;QAC1E;IACR;EACJ;EACArM,cAAcA,CAAC+L,KAAK,EAAE;IAClB,QAAQA,KAAK,CAACC,OAAO;MACjB,KAAK,MAAM;QACP,IAAI,CAACO,eAAe,CAAC,CAAC;QACtB;IACR;EACJ;EACAN,cAAcA,CAAA,EAAG;IACbxO,UAAU,CAAC+O,eAAe,CAAC,CAAC;IAC5B,IAAI,CAACjC,EAAE,CAACkC,YAAY,CAAC,CAAC;IACtB,IAAI,IAAI,CAACtB,IAAI,EAAE;MACXlN,WAAW,CAACyO,GAAG,CAAC,OAAO,EAAE,IAAI,CAACvB,IAAI,CAACkB,aAAa,EAAE,IAAI,CAACvB,UAAU,IAAI,IAAI,CAACN,MAAM,CAACmC,MAAM,CAACC,KAAK,CAAC;IAClG;EACJ;EACAL,eAAeA,CAAA,EAAG;IACd9O,UAAU,CAACoP,iBAAiB,CAAC,CAAC;IAC9B,IAAI,CAACpL,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC8I,EAAE,CAACkC,YAAY,CAAC,CAAC;IACtB,IAAI,IAAI,CAACtB,IAAI,EAAE;MACXlN,WAAW,CAAC6O,KAAK,CAAC,IAAI,CAAC3B,IAAI,CAACkB,aAAa,CAAC;IAC9C;EACJ;EACAU,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC1L,UAAU,EAAE;MACjB5D,UAAU,CAACuP,WAAW,CAAC,IAAI,CAAC5C,QAAQ,CAAC6C,IAAI,EAAE,mBAAmB,CAAC;IACnE;IACA,IAAI,IAAI,CAAC9B,IAAI,EAAE;MACX,IAAI,CAACoB,eAAe,CAAC,CAAC;IAC1B;EACJ;EACA,OAAOW,IAAI,YAAAC,iBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFjD,QAAQ,EAAlBzN,EAAE,CAAA2Q,iBAAA,CAAkC9Q,QAAQ,GAA5CG,EAAE,CAAA2Q,iBAAA,CAAuDzQ,WAAW,GAApEF,EAAE,CAAA2Q,iBAAA,CAA+E3Q,EAAE,CAAC4Q,UAAU,GAA9F5Q,EAAE,CAAA2Q,iBAAA,CAAyG3Q,EAAE,CAAC6Q,iBAAiB,GAA/H7Q,EAAE,CAAA2Q,iBAAA,CAA0I/P,EAAE,CAACkQ,aAAa;EAAA;EACrP,OAAOC,IAAI,kBAD8E/Q,EAAE,CAAAgR,iBAAA;IAAAC,IAAA,EACJxD,QAAQ;IAAAyD,SAAA;IAAAC,cAAA,WAAAC,wBAAA5O,EAAA,EAAAC,GAAA,EAAA4O,QAAA;MAAA,IAAA7O,EAAA;QADNxC,EAAE,CAAAsR,cAAA,CAAAD,QAAA,EAC4rCxQ,aAAa;MAAA;MAAA,IAAA2B,EAAA;QAAA,IAAA+O,EAAA;QAD3sCvR,EAAE,CAAAwR,cAAA,CAAAD,EAAA,GAAFvR,EAAE,CAAAyR,WAAA,QAAAhP,GAAA,CAAA4D,SAAA,GAAAkL,EAAA;MAAA;IAAA;IAAAG,SAAA,WAAAC,eAAAnP,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFxC,EAAE,CAAA4R,WAAA,CAAAhQ,GAAA;QAAF5B,EAAE,CAAA4R,WAAA,CAAA/P,GAAA;MAAA;MAAA,IAAAW,EAAA;QAAA,IAAA+O,EAAA;QAAFvR,EAAE,CAAAwR,cAAA,CAAAD,EAAA,GAAFvR,EAAE,CAAAyR,WAAA,QAAAhP,GAAA,CAAAgM,IAAA,GAAA8C,EAAA,CAAAM,KAAA;QAAF7R,EAAE,CAAAwR,cAAA,CAAAD,EAAA,GAAFvR,EAAE,CAAAyR,WAAA,QAAAhP,GAAA,CAAAiM,SAAA,GAAA6C,EAAA,CAAAM,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,MAAA;MAAA9N,WAAA;MAAAU,UAAA;MAAAiC,EAAA;MAAAvE,KAAA;MAAA8B,UAAA;MAAA0C,iBAAA;MAAAuB,kBAAA;MAAAnB,uBAAA;MAAAW,yBAAA;MAAAK,0BAAA;MAAAnB,QAAA;MAAAiB,QAAA;MAAAiG,yBAAA;MAAAC,kBAAA;MAAA5F,cAAA;MAAA6F,kBAAA;MAAAlH,+BAAA;MAAAgB,cAAA;MAAAL,oBAAA;MAAAwG,kBAAA;MAAAC,UAAA;MAAA5J,SAAA;MAAA6J,cAAA;MAAAjK,cAAA;MAAAL,qBAAA;MAAAC,qBAAA;MAAAS,OAAA;IAAA;IAAAuN,OAAA;MAAAzD,iBAAA;MAAAC,aAAA;IAAA;IAAAyD,QAAA,GAAFjS,EAAE,CAAAkS,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAArD,QAAA,WAAAsD,kBAAA9P,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFxC,EAAE,CAAAsE,UAAA,IAAAQ,uBAAA,gBAEtC,CAAC,IAAAE,+BAAA,gCAFmChF,EAAE,CAAAuS,sBA0BjE,CAAC;MAAA;MAAA,IAAA/P,EAAA;QAAA,MAAAgQ,WAAA,GA1B8DxS,EAAE,CAAAyS,WAAA;QAAFzS,EAAE,CAAA4D,UAAA,SAAAnB,GAAA,CAAAkC,UAEhE,CAAC,aAAA6N,WAAY,CAAC;MAAA;IAAA;IAAAE,YAAA,EAAAA,CAAA,MA2Bo+I9S,EAAE,CAAC+S,OAAO,EAAyG/S,EAAE,CAACgT,IAAI,EAAkHhT,EAAE,CAACiT,OAAO,EAAgGC,eAAe;IAAAC,MAAA;IAAAC,aAAA;IAAAC,IAAA;MAAAC,SAAA,EAA4I,CACt/JvT,OAAO,CAAC,WAAW,EAAE,CACjBD,UAAU,CAAC,iBAAiB,EAAE,CAACF,KAAK,CAAC;QAAE2T,SAAS,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAE,CAAC,CAAC,EAAE3T,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,EACpHC,UAAU,CAAC,iBAAiB,EAAE,CAACD,OAAO,CAAC,0BAA0B,EAAED,KAAK,CAAC;QAAE2T,SAAS,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CACvH,CAAC;IACL;IAAAC,eAAA;EAAA;AACT;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApC6FtT,EAAE,CAAAuT,iBAAA,CAoCJ9F,QAAQ,EAAc,CAAC;IACtGwD,IAAI,EAAE9Q,SAAS;IACfqT,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAEzE,QAAQ,EAAG;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAE0E,UAAU,EAAE,CACK/T,OAAO,CAAC,WAAW,EAAE,CACjBD,UAAU,CAAC,iBAAiB,EAAE,CAACF,KAAK,CAAC;QAAE2T,SAAS,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAE,CAAC,CAAC,EAAE3T,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC,EACpHC,UAAU,CAAC,iBAAiB,EAAE,CAACD,OAAO,CAAC,0BAA0B,EAAED,KAAK,CAAC;QAAE2T,SAAS,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CACvH,CAAC,CACL;MAAEC,eAAe,EAAEjT,uBAAuB,CAACuT,MAAM;MAAEX,aAAa,EAAE3S,iBAAiB,CAACuT,IAAI;MAAEC,IAAI,EAAE;QAC7FC,KAAK,EAAE;MACX,CAAC;MAAEf,MAAM,EAAE,CAAC,g7IAAg7I;IAAE,CAAC;EAC38I,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE9B,IAAI,EAAE8C,QAAQ;IAAEC,UAAU,EAAE,CAAC;MAC9C/C,IAAI,EAAE3Q,MAAM;MACZkT,IAAI,EAAE,CAAC3T,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEoR,IAAI,EAAErM,SAAS;IAAEoP,UAAU,EAAE,CAAC;MAClC/C,IAAI,EAAE3Q,MAAM;MACZkT,IAAI,EAAE,CAACtT,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAE+Q,IAAI,EAAEjR,EAAE,CAAC4Q;EAAW,CAAC,EAAE;IAAEK,IAAI,EAAEjR,EAAE,CAAC6Q;EAAkB,CAAC,EAAE;IAAEI,IAAI,EAAErQ,EAAE,CAACkQ;EAAc,CAAC,CAAC,EAAkB;IAAE7M,WAAW,EAAE,CAAC;MAC5HgN,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEoE,UAAU,EAAE,CAAC;MACbsM,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEqG,EAAE,EAAE,CAAC;MACLqK,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE8B,KAAK,EAAE,CAAC;MACR4O,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE4D,UAAU,EAAE,CAAC;MACb8M,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEsG,iBAAiB,EAAE,CAAC;MACpBoK,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE6H,kBAAkB,EAAE,CAAC;MACrB6I,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE0G,uBAAuB,EAAE,CAAC;MAC1BgK,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEqH,yBAAyB,EAAE,CAAC;MAC5BqJ,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE0H,0BAA0B,EAAE,CAAC;MAC7BgJ,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEuG,QAAQ,EAAE,CAAC;MACXmK,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEwH,QAAQ,EAAE,CAAC;MACXkJ,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEyN,yBAAyB,EAAE,CAAC;MAC5BiD,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE0N,kBAAkB,EAAE,CAAC;MACrBgD,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE8H,cAAc,EAAE,CAAC;MACjB4I,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE2N,kBAAkB,EAAE,CAAC;MACrB+C,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEyG,+BAA+B,EAAE,CAAC;MAClCiK,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEyH,cAAc,EAAE,CAAC;MACjBiJ,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEoH,oBAAoB,EAAE,CAAC;MACvBsJ,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE4N,kBAAkB,EAAE,CAAC;MACrB8C,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE6N,UAAU,EAAE,CAAC;MACb6C,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEiE,SAAS,EAAE,CAAC;MACZyM,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE8N,cAAc,EAAE,CAAC;MACjB4C,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE6D,cAAc,EAAE,CAAC;MACjB6M,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEwD,qBAAqB,EAAE,CAAC;MACxBkN,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEyD,qBAAqB,EAAE,CAAC;MACxBiN,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEkE,OAAO,EAAE,CAAC;MACVwM,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEgO,iBAAiB,EAAE,CAAC;MACpB0C,IAAI,EAAEzQ;IACV,CAAC,CAAC;IAAEgO,aAAa,EAAE,CAAC;MAChByC,IAAI,EAAEzQ;IACV,CAAC,CAAC;IAAEiO,IAAI,EAAE,CAAC;MACPwC,IAAI,EAAExQ,SAAS;MACf+S,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC,CAAC;IAAE9E,SAAS,EAAE,CAAC;MACZuC,IAAI,EAAExQ,SAAS;MACf+S,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAEnN,SAAS,EAAE,CAAC;MACZ4K,IAAI,EAAEvQ,eAAe;MACrB8S,IAAI,EAAE,CAAC3S,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMiS,eAAe,CAAC;EAClB5M,QAAQ;EACR2H,EAAE;EACFoG,OAAO;EACPnG,MAAM;EACN,IAAI7J,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC8J,YAAY;EAC5B;EACA,IAAI9J,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,CAAC8J,YAAY,GAAG9J,WAAW;EACnC;EACA5B,KAAK,GAAG,EAAE;EACV8B,UAAU;EACV4B,QAAQ,GAAG,IAAI9F,YAAY,CAAC,CAAC;EAC7BiU,gBAAgB,GAAG,IAAIjU,YAAY,CAAC,CAAC;EACrCkU,WAAW;EACXvN,EAAE;EACFmH,YAAY,GAAG,CAAC;EAChB7G,eAAe,GAAG,IAAI;EACtBkN,QAAQ;EACRC,UAAU;EACVC,MAAM;EACN3F,WAAWA,CAACzI,QAAQ,EAAE2H,EAAE,EAAEoG,OAAO,EAAEnG,MAAM,EAAE;IACvC,IAAI,CAAC5H,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC2H,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACoG,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACnG,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAClH,EAAE,GAAG,IAAI,CAACV,QAAQ,CAACU,EAAE,IAAIpF,iBAAiB,CAAC,CAAC;IACjD,IAAI,CAAC8S,MAAM,GAAG,IAAI,CAACL,OAAO,CAACM,IAAI,CAAC,IAAI,CAACrO,QAAQ,CAAC,CAACsO,MAAM,CAAC,CAAC;EAC3D;EACAC,SAASA,CAAA,EAAG;IACR,IAAI3U,iBAAiB,CAAC,IAAI,CAACoG,QAAQ,CAACyH,UAAU,CAAC,EAAE;MAC7C,MAAM+G,OAAO,GAAG,IAAI,CAACJ,MAAM,CAACK,IAAI,CAAC,IAAI,CAACzO,QAAQ,CAAC;MAC/C,IAAIwO,OAAO,IAAIA,OAAO,CAACE,WAAW,CAACxF,MAAM,GAAG,CAAC,EAAE;QAC3C;QACA;QACA;QACA;QACA,IAAI,CAACvB,EAAE,CAACkC,YAAY,CAAC,CAAC;MAC1B;IACJ;EACJ;EACAtI,aAAaA,CAAA,EAAG;IACZ,MAAMoN,kBAAkB,GAAG,IAAI,CAAC3O,QAAQ,CAACmC,cAAc,IAAI,IAAI,CAACyM,gBAAgB,CAAC,uBAAuB,EAAE,IAAI,CAAC5O,QAAQ,CAACgI,kBAAkB,CAAC;IAC3I,MAAM6G,iBAAiB,GAAG,IAAI,CAAC7O,QAAQ,CAAC8B,cAAc,IAAI,IAAI,CAAC8M,gBAAgB,CAAC,uBAAuB,EAAE,IAAI,CAAC5O,QAAQ,CAACiI,kBAAkB,CAAC;IAC1I,OAAO,CAAC,IAAI,CAACjI,QAAQ,CAACmI,cAAc,GAAG,IAAI,CAACnI,QAAQ,CAACmI,cAAc,GAAG,GAAG,GAAG,EAAE,KAAKwG,kBAAkB,GAAGA,kBAAkB,GAAG,GAAG,GAAG,EAAE,CAAC,IAAIE,iBAAiB,GAAGA,iBAAiB,GAAG,GAAG,GAAG,EAAE,CAAC;EAC/L;EACAxN,cAAcA,CAAA,EAAG;IACb,IAAIzH,iBAAiB,CAAC,IAAI,CAACoG,QAAQ,CAACyH,UAAU,CAAC,EAAE;MAC7C,IAAI,CAACyG,QAAQ,GAAGY,WAAW,CAAC,MAAM;QAC9B,IAAI/Q,WAAW,GAAG,IAAI,CAACiC,QAAQ,CAACY,QAAQ,IAAI,IAAI,CAACzE,KAAK,CAAC+M,MAAM,GAAG,CAAC,KAAK,IAAI,CAACnL,WAAW,GAAG,CAAC,GAAG,IAAI,CAACA,WAAW,GAAG,CAAC;QACjH,IAAI,CAACwC,mBAAmB,CAACxC,WAAW,CAAC;QACrC,IAAI,CAACA,WAAW,GAAGA,WAAW;MAClC,CAAC,EAAE,IAAI,CAACiC,QAAQ,CAAC+H,kBAAkB,CAAC;MACpC,IAAI,CAAC/G,eAAe,GAAG,IAAI;IAC/B;EACJ;EACAP,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACT,QAAQ,CAAC6B,QAAQ,IAAI,CAAC,IAAI,CAAC7B,QAAQ,CAAC8H,yBAAyB,EAAE;MACpE;IACJ;IACA,IAAI,IAAI,CAACoG,QAAQ,EAAE;MACfa,aAAa,CAAC,IAAI,CAACb,QAAQ,CAAC;IAChC;IACA,IAAI,CAAClN,eAAe,GAAG,KAAK;EAChC;EACA4N,gBAAgBA,CAACI,YAAY,EAAEC,QAAQ,EAAE;IACrC,MAAMC,SAAS,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC;IACpD,MAAMC,GAAG,GAAGD,SAAS,CAACb,IAAI,CAAEzF,IAAI,IAAKA,IAAI,KAAKqG,QAAQ,CAAC;IACvD,OAAOE,GAAG,GAAI,GAAEH,YAAa,IAAGG,GAAI,EAAC,GAAG,EAAE;EAC9C;EACAtO,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAACb,QAAQ,CAACgI,kBAAkB,KAAK,MAAM,IAAI,IAAI,CAAChI,QAAQ,CAACgI,kBAAkB,KAAK,OAAO;EACtG;EACAzH,mBAAmBA,CAAC+D,KAAK,EAAE;IACvB,IAAI,IAAI,CAACvG,WAAW,KAAKuG,KAAK,EAAE;MAC5B,IAAI,CAACvG,WAAW,GAAGuG,KAAK;MACxB,IAAI,CAAC0J,gBAAgB,CAAClO,IAAI,CAAC,IAAI,CAAC/B,WAAW,CAAC;IAChD;EACJ;EACAgC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC6H,MAAM,CAACwH,WAAW,CAACC,IAAI,GAAG,IAAI,CAACzH,MAAM,CAACwH,WAAW,CAACC,IAAI,CAACC,KAAK,GAAG5Q,SAAS;EACxF;EACA,OAAO4L,IAAI,YAAAiF,wBAAA/E,CAAA;IAAA,YAAAA,CAAA,IAAwFoC,eAAe,EAvOzB9S,EAAE,CAAA2Q,iBAAA,CAuOyClD,QAAQ,GAvOnDzN,EAAE,CAAA2Q,iBAAA,CAuO8D3Q,EAAE,CAAC6Q,iBAAiB,GAvOpF7Q,EAAE,CAAA2Q,iBAAA,CAuO+F3Q,EAAE,CAAC0V,eAAe,GAvOnH1V,EAAE,CAAA2Q,iBAAA,CAuO8H/P,EAAE,CAACkQ,aAAa;EAAA;EACzO,OAAOC,IAAI,kBAxO8E/Q,EAAE,CAAAgR,iBAAA;IAAAC,IAAA,EAwOJ6B,eAAe;IAAA5B,SAAA;IAAAQ,SAAA,WAAAiE,sBAAAnT,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAxObxC,EAAE,CAAA4R,WAAA,CAAAzM,GAAA;MAAA;MAAA,IAAA3C,EAAA;QAAA,IAAA+O,EAAA;QAAFvR,EAAE,CAAAwR,cAAA,CAAAD,EAAA,GAAFvR,EAAE,CAAAyR,WAAA,QAAAhP,GAAA,CAAA0R,WAAA,GAAA5C,EAAA,CAAAM,KAAA;MAAA;IAAA;IAAAE,MAAA;MAAA9N,WAAA;MAAA5B,KAAA;MAAA8B,UAAA;IAAA;IAAA6N,OAAA;MAAAjM,QAAA;MAAAmO,gBAAA;IAAA;IAAA/B,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAArD,QAAA,WAAA4G,yBAAApT,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFxC,EAAE,CAAAsE,UAAA,IAAA8C,8BAAA,iBAsPvF,CAAC;MAAA;MAAA,IAAA5E,EAAA;QAtPoFxC,EAAE,CAAA4D,UAAA,SAAAnB,GAAA,CAAAJ,KAAA,IAAAI,GAAA,CAAAJ,KAAA,CAAA+M,MAAA,IA4OpD,CAAC;MAAA;IAAA;IAAAsD,YAAA,EAAAA,CAAA,MA0DyC9S,EAAE,CAAC+S,OAAO,EAAyG/S,EAAE,CAACgT,IAAI,EAAkHhT,EAAE,CAACiW,gBAAgB,EAAyKjW,EAAE,CAACiT,OAAO,EAAgGxR,EAAE,CAACyU,MAAM,EAA2E5U,SAAS,EAA2EO,EAAE,CAACsU,SAAS,EAA8GC,gBAAgB,EAAoIC,YAAY,EAA6VC,kBAAkB;IAAAlD,aAAA;IAAAK,eAAA;EAAA;AACx5C;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAxS6FtT,EAAE,CAAAuT,iBAAA,CAwSJT,eAAe,EAAc,CAAC;IAC7G7B,IAAI,EAAE9Q,SAAS;IACfqT,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BzE,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MACeqE,eAAe,EAAEjT,uBAAuB,CAACuT;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE1C,IAAI,EAAExD;EAAS,CAAC,EAAE;IAAEwD,IAAI,EAAEjR,EAAE,CAAC6Q;EAAkB,CAAC,EAAE;IAAEI,IAAI,EAAEjR,EAAE,CAAC0V;EAAgB,CAAC,EAAE;IAAEzE,IAAI,EAAErQ,EAAE,CAACkQ;EAAc,CAAC,CAAC,EAAkB;IAAE7M,WAAW,EAAE,CAAC;MAClKgN,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE8B,KAAK,EAAE,CAAC;MACR4O,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE4D,UAAU,EAAE,CAAC;MACb8M,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEwF,QAAQ,EAAE,CAAC;MACXkL,IAAI,EAAEzQ;IACV,CAAC,CAAC;IAAE0T,gBAAgB,EAAE,CAAC;MACnBjD,IAAI,EAAEzQ;IACV,CAAC,CAAC;IAAE2T,WAAW,EAAE,CAAC;MACdlD,IAAI,EAAExQ,SAAS;MACf+S,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMwC,gBAAgB,CAAC;EACnB3P,SAAS;EACTmE,KAAK;EACL,IAAIsE,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACqH,KAAK;EACrB;EACA,IAAIrH,IAAIA,CAACA,IAAI,EAAE;IACX,IAAI,CAACqH,KAAK,GAAGrH,IAAI;IACjB,IAAI,IAAI,CAACzI,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACwI,OAAO,CAAEC,IAAI,IAAK;QAC7B,IAAIA,IAAI,CAACC,OAAO,CAAC,CAAC,KAAK,IAAI,CAACkC,IAAI,EAAE;UAC9B,QAAQ,IAAI,CAACA,IAAI;YACb,KAAK,MAAM;YACX,KAAK,SAAS;YACd,KAAK,WAAW;cACZ,IAAI,CAACnI,OAAO,GAAG;gBAAE2D,SAAS,EAAE,IAAI,CAACqC;cAAK,CAAC;cACvC,IAAI,CAACjG,eAAe,GAAGiG,IAAI,CAACE,QAAQ;cACpC;UACR;QACJ;MACJ,CAAC,CAAC;IACN;EACJ;EACAiC,IAAI;EACJpI,eAAe;EACfC,OAAO;EACPqN,KAAK;EACLvH,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACvI,SAAS,EAAEwI,OAAO,CAAEC,IAAI,IAAK;MAC9B,IAAIA,IAAI,CAACC,OAAO,CAAC,CAAC,KAAK,IAAI,CAACkC,IAAI,EAAE;QAC9B,QAAQ,IAAI,CAACA,IAAI;UACb,KAAK,MAAM;UACX,KAAK,SAAS;UACd,KAAK,WAAW;YACZ,IAAI,CAACnI,OAAO,GAAG;cAAE2D,SAAS,EAAE,IAAI,CAACqC;YAAK,CAAC;YACvC,IAAI,CAACjG,eAAe,GAAGiG,IAAI,CAACE,QAAQ;YACpC;UACJ,KAAK,WAAW;YACZ,IAAI,CAAClG,OAAO,GAAG;cAAE2D,SAAS,EAAE,IAAI,CAACjC;YAAM,CAAC;YACxC,IAAI,CAAC3B,eAAe,GAAGiG,IAAI,CAACE,QAAQ;YACpC;UACJ;YACI,IAAI,CAAClG,OAAO,GAAG,CAAC,CAAC;YACjB,IAAI,CAACD,eAAe,GAAGiG,IAAI,CAACE,QAAQ;YACpC;QACR;MACJ;IACJ,CAAC,CAAC;EACN;EACA,OAAOwB,IAAI,YAAA4F,yBAAA1F,CAAA;IAAA,YAAAA,CAAA,IAAwFsF,gBAAgB;EAAA;EACnH,OAAOjF,IAAI,kBA7a8E/Q,EAAE,CAAAgR,iBAAA;IAAAC,IAAA,EA6aJ+E,gBAAgB;IAAA9E,SAAA;IAAAa,MAAA;MAAA1L,SAAA;MAAAmE,KAAA;MAAAsE,IAAA;MAAAmC,IAAA;IAAA;IAAAkB,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAArD,QAAA,WAAAqH,0BAAA7T,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA7adxC,EAAE,CAAAsE,UAAA,IAAAmE,wCAAA,yBA8alD,CAAC;MAAA;MAAA,IAAAjG,EAAA;QA9a+CxC,EAAE,CAAA4D,UAAA,SAAAnB,GAAA,CAAAoG,eA8apD,CAAC;MAAA;IAAA;IAAA6J,YAAA,GAGqB9S,EAAE,CAACgT,IAAI,EAA6FhT,EAAE,CAACiW,gBAAgB;IAAA7C,aAAA;IAAAK,eAAA;EAAA;AACxL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAnb6FtT,EAAE,CAAAuT,iBAAA,CAmbJyC,gBAAgB,EAAc,CAAC;IAC9G/E,IAAI,EAAE9Q,SAAS;IACfqT,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BzE,QAAQ,EAAG;AAC/B;AACA;AACA;AACA,KAAK;MACeqE,eAAe,EAAEjT,uBAAuB,CAACuT;IAC7C,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEtN,SAAS,EAAE,CAAC;MAC1B4K,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEiK,KAAK,EAAE,CAAC;MACRyG,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEuO,IAAI,EAAE,CAAC;MACPmC,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE0Q,IAAI,EAAE,CAAC;MACPA,IAAI,EAAE1Q;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM0V,YAAY,CAAC;EACf/P,QAAQ;EACRU,EAAE;EACFE,QAAQ,GAAG,KAAK;EAChBzE,KAAK;EACL+F,kBAAkB,GAAG,KAAK;EAC1BJ,cAAc,GAAG,IAAI;EACrBd,eAAe,GAAG,IAAI;EACtBe,0BAA0B,GAAG,IAAI;EACjCF,QAAQ,GAAG,KAAK;EAChB1B,SAAS;EACT6B,cAAc;EACdC,YAAY;EACZZ,cAAc,GAAG,IAAItH,YAAY,CAAC,CAAC;EACnC0G,aAAa,GAAG,IAAI1G,YAAY,CAAC,CAAC;EAClCwG,mBAAmB,GAAG,IAAIxG,YAAY,CAAC,CAAC;EACxC,IAAIgE,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC8J,YAAY;EAC5B;EACA,IAAI9J,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,CAAC8J,YAAY,GAAG9J,WAAW;EACnC;EACA,IAAIkG,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC9H,KAAK,IAAI,IAAI,CAACA,KAAK,CAAC,IAAI,CAAC0L,YAAY,CAAC;EACtD;EACAA,YAAY,GAAG,CAAC;EAChBY,WAAWA,CAACzI,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA+I,WAAWA,CAAC;IAAElH;EAAS,CAAC,EAAE;IACtB,IAAIA,QAAQ,EAAEoH,YAAY,EAAE;MACxB,IAAI,CAAC5H,cAAc,CAACvB,IAAI,CAAC,CAAC;IAC9B;IACA,IAAI+B,QAAQ,IAAIA,QAAQ,CAACoH,YAAY,KAAK,KAAK,EAAE;MAC7C,IAAI,CAACmH,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACAC,IAAIA,CAAA,EAAG;IACH,IAAIC,aAAa,GAAG,IAAI,CAACvS,WAAW,GAAG,CAAC;IACxC,IAAIA,WAAW,GAAG,IAAI,CAAC6C,QAAQ,IAAI,IAAI,CAACzE,KAAK,CAAC+M,MAAM,GAAG,CAAC,KAAK,IAAI,CAACnL,WAAW,GAAG,CAAC,GAAGuS,aAAa;IACjG,IAAI,CAAC/P,mBAAmB,CAACT,IAAI,CAAC/B,WAAW,CAAC;EAC9C;EACAwS,IAAIA,CAAA,EAAG;IACH,IAAIC,aAAa,GAAG,IAAI,CAACzS,WAAW,KAAK,CAAC,GAAG,IAAI,CAACA,WAAW,GAAG,CAAC,GAAG,CAAC;IACrE,IAAIA,WAAW,GAAG,IAAI,CAAC6C,QAAQ,IAAI,IAAI,CAAC7C,WAAW,KAAK,CAAC,GAAG,IAAI,CAAC5B,KAAK,CAAC+M,MAAM,GAAG,CAAC,GAAGsH,aAAa;IACjG,IAAI,CAACjQ,mBAAmB,CAACT,IAAI,CAAC/B,WAAW,CAAC;EAC9C;EACAqS,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACpP,eAAe,IAAI,IAAI,CAACP,aAAa,EAAE;MAC5C,IAAI,CAACA,aAAa,CAACX,IAAI,CAAC,CAAC;IAC7B;EACJ;EACA+D,UAAUA,CAAC4M,CAAC,EAAE;IACV,IAAI,CAACL,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACC,IAAI,CAAC,CAAC;IACX,IAAII,CAAC,IAAIA,CAAC,CAACC,UAAU,EAAE;MACnBD,CAAC,CAACE,cAAc,CAAC,CAAC;IACtB;EACJ;EACAtN,WAAWA,CAACoN,CAAC,EAAE;IACX,IAAI,CAACL,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACG,IAAI,CAAC,CAAC;IACX,IAAIE,CAAC,IAAIA,CAAC,CAACC,UAAU,EAAE;MACnBD,CAAC,CAACE,cAAc,CAAC,CAAC;IACtB;EACJ;EACApM,gBAAgBA,CAACD,KAAK,EAAE;IACpB,IAAI,CAAC8L,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAAC7P,mBAAmB,CAACT,IAAI,CAACwE,KAAK,CAAC;EACxC;EACAG,qBAAqBA,CAACH,KAAK,EAAE;IACzB,IAAI,IAAI,CAACvC,0BAA0B,EAAE;MACjC,IAAI,CAACqO,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAAC7P,mBAAmB,CAACT,IAAI,CAACwE,KAAK,CAAC;IACxC;EACJ;EACAK,kBAAkBA,CAACwE,KAAK,EAAE7E,KAAK,EAAE;IAC7B,QAAQ6E,KAAK,CAACyH,IAAI;MACd,KAAK,OAAO;MACZ,KAAK,OAAO;QACR,IAAI,CAACR,gBAAgB,CAAC,CAAC;QACvB,IAAI,CAAC7P,mBAAmB,CAACT,IAAI,CAACwE,KAAK,CAAC;QACpC6E,KAAK,CAACwH,cAAc,CAAC,CAAC;QACtB;MACJ,KAAK,WAAW;MAChB,KAAK,SAAS;QACVxH,KAAK,CAACwH,cAAc,CAAC,CAAC;QACtB;MACJ;QACI;IACR;EACJ;EACA7M,oBAAoBA,CAAA,EAAG;IACnB,OAAO,CAAC,IAAI,CAAClD,QAAQ,IAAI,IAAI,CAAC7C,WAAW,KAAK,IAAI,CAAC5B,KAAK,CAAC+M,MAAM,GAAG,CAAC;EACvE;EACA5F,qBAAqBA,CAAA,EAAG;IACpB,OAAO,CAAC,IAAI,CAAC1C,QAAQ,IAAI,IAAI,CAAC7C,WAAW,KAAK,CAAC;EACnD;EACA6G,qBAAqBA,CAACN,KAAK,EAAE;IACzB,OAAO,IAAI,CAACvG,WAAW,KAAKuG,KAAK;EACrC;EACAuM,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC7Q,QAAQ,CAAC4H,MAAM,CAACwH,WAAW,CAACC,IAAI,GAAG,IAAI,CAACrP,QAAQ,CAAC4H,MAAM,CAACwH,WAAW,CAACC,IAAI,CAACyB,KAAK,GAAGpS,SAAS;EAC1G;EACAqS,eAAeA,CAAC5U,KAAK,EAAE;IACnB,OAAO,IAAI,CAAC6D,QAAQ,CAAC4H,MAAM,CAACwH,WAAW,CAACC,IAAI,GAAG,IAAI,CAACrP,QAAQ,CAAC4H,MAAM,CAACwH,WAAW,CAACC,IAAI,CAAC2B,WAAW,CAACC,OAAO,CAAC,gBAAgB,EAAE9U,KAAK,CAAC,GAAGuC,SAAS;EACjJ;EACAmG,aAAaA,CAAC1I,KAAK,EAAE;IACjB,OAAO,IAAI,CAAC6D,QAAQ,CAAC4H,MAAM,CAACwH,WAAW,CAACC,IAAI,GAAG,IAAI,CAACrP,QAAQ,CAAC4H,MAAM,CAACwH,WAAW,CAACC,IAAI,CAAC6B,SAAS,CAACD,OAAO,CAAC,SAAS,EAAE9U,KAAK,CAAC,GAAGuC,SAAS;EACxI;EACA,OAAO4L,IAAI,YAAA6G,qBAAA3G,CAAA;IAAA,YAAAA,CAAA,IAAwFuF,YAAY,EArjBtBjW,EAAE,CAAA2Q,iBAAA,CAqjBsClD,QAAQ;EAAA;EACzI,OAAOsD,IAAI,kBAtjB8E/Q,EAAE,CAAAgR,iBAAA;IAAAC,IAAA,EAsjBJgF,YAAY;IAAA/E,SAAA;IAAAa,MAAA;MAAAnL,EAAA;MAAAE,QAAA;MAAAzE,KAAA;MAAA+F,kBAAA;MAAAJ,cAAA;MAAAd,eAAA;MAAAe,0BAAA;MAAAF,QAAA;MAAA1B,SAAA;MAAA6B,cAAA;MAAAC,YAAA;MAAAlE,WAAA;IAAA;IAAA+N,OAAA;MAAAzK,cAAA;MAAAZ,aAAA;MAAAF,mBAAA;IAAA;IAAAwL,QAAA,GAtjBVjS,EAAE,CAAAkS,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAArD,QAAA,WAAAsI,sBAAA9U,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFxC,EAAE,CAAA4C,cAAA,YAujBnD,CAAC,YACK,CAAC;QAxjB0C5C,EAAE,CAAAsE,UAAA,IAAA+E,8BAAA,mBAikB/E,CAAC;QAjkB4ErJ,EAAE,CAAA4C,cAAA,YAqkBgG,CAAC;QArkBnG5C,EAAE,CAAAwF,SAAA,2BAskB6C,CAAC;QAtkBhDxF,EAAE,CAAA2D,YAAA,CAukB1E,CAAC;QAvkBuE3D,EAAE,CAAAsE,UAAA,IAAAuF,8BAAA,mBAglB/E,CAAC,IAAAK,2BAAA,gBAImD,CAAC;QAplBwBlK,EAAE,CAAA2D,YAAA,CAulB9E,CAAC;QAvlB2E3D,EAAE,CAAAsE,UAAA,IAAA0G,0BAAA,eAwlBnB,CAAC;QAxlBgBhL,EAAE,CAAA2D,YAAA,CAwmBlF,CAAC;MAAA;MAAA,IAAAnB,EAAA;QAxmB+ExC,EAAE,CAAA6E,SAAA,EA0jBnD,CAAC;QA1jBgD7E,EAAE,CAAA4D,UAAA,SAAAnB,GAAA,CAAA2F,kBA0jBnD,CAAC;QA1jBgDpI,EAAE,CAAA6E,SAAA,CAqkB+F,CAAC;QArkBlG7E,EAAE,CAAAuX,WAAA,gBAqkB+F,CAAC;QArkBlGvX,EAAE,CAAA4D,UAAA,OAAAnB,GAAA,CAAAmE,EAAA,cAAAnE,GAAA,CAAAwB,WAqkBzC,CAAC;QArkBsCjE,EAAE,CAAA0E,WAAA,eAAAjC,GAAA,CAAAwU,eAAA,CAAAxU,GAAA,CAAAwB,WAAA,+BAAAxB,GAAA,CAAAsU,cAAA;QAAF/W,EAAE,CAAA6E,SAAA,CAskBzB,CAAC;QAtkBsB7E,EAAE,CAAA4D,UAAA,SAAAnB,GAAA,CAAA0H,UAskBzB,CAAC,cAAA1H,GAAA,CAAA4D,SAAuB,CAAC;QAtkBFrG,EAAE,CAAA6E,SAAA,CAykBnD,CAAC;QAzkBgD7E,EAAE,CAAA4D,UAAA,SAAAnB,GAAA,CAAA2F,kBAykBnD,CAAC;QAzkBgDpI,EAAE,CAAA6E,SAAA,CAolB7B,CAAC;QAplB0B7E,EAAE,CAAA4D,UAAA,SAAAnB,GAAA,CAAA0F,YAolB7B,CAAC;QAplB0BnI,EAAE,CAAA6E,SAAA,CAwlB3D,CAAC;QAxlBwD7E,EAAE,CAAA4D,UAAA,SAAAnB,GAAA,CAAAuF,cAwlB3D,CAAC;MAAA;IAAA;IAAA0K,YAAA,EAAAA,CAAA,MAiBgD9S,EAAE,CAAC+S,OAAO,EAAyG/S,EAAE,CAAC4X,OAAO,EAAwI5X,EAAE,CAACgT,IAAI,EAAkHhT,EAAE,CAACiW,gBAAgB,EAAyKxU,EAAE,CAACyU,MAAM,EAA2E7U,gBAAgB,EAAkFD,eAAe,EAAiFgV,gBAAgB;IAAAhD,aAAA;IAAAK,eAAA;EAAA;AACr7B;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3mB6FtT,EAAE,CAAAuT,iBAAA,CA2mBJ0C,YAAY,EAAc,CAAC;IAC1GhF,IAAI,EAAE9Q,SAAS;IACfqT,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1BzE,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MACeqE,eAAe,EAAEjT,uBAAuB,CAACuT;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE1C,IAAI,EAAExD;EAAS,CAAC,CAAC,EAAkB;IAAE7G,EAAE,EAAE,CAAC;MAC/DqK,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEuG,QAAQ,EAAE,CAAC;MACXmK,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE8B,KAAK,EAAE,CAAC;MACR4O,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE6H,kBAAkB,EAAE,CAAC;MACrB6I,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEyH,cAAc,EAAE,CAAC;MACjBiJ,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE2G,eAAe,EAAE,CAAC;MAClB+J,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE0H,0BAA0B,EAAE,CAAC;MAC7BgJ,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEwH,QAAQ,EAAE,CAAC;MACXkJ,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE8F,SAAS,EAAE,CAAC;MACZ4K,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE2H,cAAc,EAAE,CAAC;MACjB+I,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE4H,YAAY,EAAE,CAAC;MACf8I,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEgH,cAAc,EAAE,CAAC;MACjB0J,IAAI,EAAEzQ;IACV,CAAC,CAAC;IAAEmG,aAAa,EAAE,CAAC;MAChBsK,IAAI,EAAEzQ;IACV,CAAC,CAAC;IAAEiG,mBAAmB,EAAE,CAAC;MACtBwK,IAAI,EAAEzQ;IACV,CAAC,CAAC;IAAEyD,WAAW,EAAE,CAAC;MACdgN,IAAI,EAAE1Q;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM2V,kBAAkB,CAAC;EACrBhQ,QAAQ;EACRwH,QAAQ;EACRC,UAAU;EACV8J,QAAQ;EACR5J,EAAE;EACFf,WAAW;EACXzK,KAAK;EACL0E,UAAU,GAAG,KAAK;EAClBG,eAAe,GAAG,KAAK;EACvBJ,QAAQ,GAAG,KAAK;EAChBD,iBAAiB;EACjB6Q,aAAa,GAAG,OAAO;EACvBzQ,uBAAuB,GAAG,IAAI;EAC9BZ,SAAS;EACTI,mBAAmB,GAAG,IAAIxG,YAAY,CAAC,CAAC;EACxC0G,aAAa,GAAG,IAAI1G,YAAY,CAAC,CAAC;EAClC0X,cAAc;EACd,IAAIxT,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACyT,WAAW;EAC3B;EACA,IAAIzT,UAAUA,CAACA,UAAU,EAAE;IACvB,IAAI,CAACyT,WAAW,GAAGzT,UAAU;IAC7B,IAAI,CAAC0T,cAAc,GAAG,IAAI,CAACC,YAAY;IACvC,IAAI,CAACA,YAAY,GAAG3T,UAAU;EAClC;EACA,IAAIF,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC8J,YAAY;EAC5B;EACA,IAAI9J,WAAWA,CAACA,WAAW,EAAE;IACzB,IAAI,CAAC8T,eAAe,GAAG,IAAI,CAAChK,YAAY;IACxC,IAAI,CAACA,YAAY,GAAG9J,WAAW;EACnC;EACAuG,KAAK;EACLwN,QAAQ,GAAG,IAAI;EACfC,eAAe,GAAG,IAAI;EACtBC,uBAAuB,GAAG,IAAI;EAC9BC,iBAAiB,GAAG,CAAC;EACrBC,IAAI,GAAG,CAAC;EACRC,sBAAsB;EACtBT,WAAW,GAAG,CAAC;EACfE,YAAY,GAAG,CAAC;EAChBD,cAAc,GAAG,CAAC;EAClB9J,YAAY,GAAG,CAAC;EAChBgK,eAAe,GAAG,CAAC;EACnBpJ,WAAWA,CAACzI,QAAQ,EAAEwH,QAAQ,EAAEC,UAAU,EAAE8J,QAAQ,EAAE5J,EAAE,EAAE;IACtD,IAAI,CAAC3H,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACwH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC8J,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC5J,EAAE,GAAGA,EAAE;EAChB;EACAyK,QAAQA,CAAA,EAAG;IACP,IAAIxY,iBAAiB,CAAC,IAAI,CAAC6N,UAAU,CAAC,EAAE;MACpC,IAAI,CAAC4K,WAAW,CAAC,CAAC;MAClB,IAAI,IAAI,CAAC1R,iBAAiB,EAAE;QACxB,IAAI,CAAC2R,qBAAqB,CAAC,CAAC;MAChC;IACJ;EACJ;EACAC,qBAAqBA,CAAA,EAAG;IACpB,IAAIN,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;IAC9C,IAAI,CAAC,IAAI,CAACN,cAAc,KAAK,IAAI,CAACC,YAAY,IAAI,IAAI,CAACC,eAAe,KAAK,IAAI,CAAChK,YAAY,KAAK,IAAI,CAAC4J,cAAc,EAAE;MAClH,IAAI,IAAI,CAAC5J,YAAY,IAAI,IAAI,CAAC2K,kBAAkB,CAAC,CAAC,EAAE;QAChDP,iBAAiB,GAAG,CAAC;MACzB,CAAC,MACI,IAAI,IAAI,CAAC9V,KAAK,CAAC+M,MAAM,GAAG,IAAI,CAAC0I,YAAY,GAAG,IAAI,CAACY,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAAC3K,YAAY,EAAE;QAC5FoK,iBAAiB,GAAG,IAAI,CAACL,YAAY,GAAG,IAAI,CAACzV,KAAK,CAAC+M,MAAM;MAC7D,CAAC,MACI,IAAI,IAAI,CAAC/M,KAAK,CAAC+M,MAAM,GAAG,IAAI,CAAC0I,YAAY,GAAG,IAAI,CAAC/J,YAAY,IAAI,IAAI,CAAC+J,YAAY,GAAG,CAAC,KAAK,CAAC,EAAE;QAC/FK,iBAAiB,GAAG,IAAI,CAACpK,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC2K,kBAAkB,CAAC,CAAC,GAAG,CAAC;MAC9E,CAAC,MACI;QACDP,iBAAiB,GAAG,IAAI,CAACpK,YAAY,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC2K,kBAAkB,CAAC,CAAC;MAC1E;MACA,IAAIP,iBAAiB,KAAK,IAAI,CAACA,iBAAiB,EAAE;QAC9C,IAAI,CAACA,iBAAiB,GAAGA,iBAAiB;MAC9C;MACA,IAAI,IAAI,CAACR,cAAc,IAAI,IAAI,CAACA,cAAc,CAAChI,aAAa,EAAE;QAC1D,IAAI,CAACgI,cAAc,CAAChI,aAAa,CAACnQ,KAAK,CAAC2T,SAAS,GAAG,IAAI,CAACpM,UAAU,GAAI,kBAAiBoR,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAACL,YAAY,CAAE,OAAM,GAAI,eAAcK,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAACL,YAAY,CAAE,UAAS;MACzN;MACA,IAAI,IAAI,CAACC,eAAe,KAAK,IAAI,CAAChK,YAAY,EAAE;QAC5ChN,UAAU,CAACuP,WAAW,CAAC,IAAI,CAACqH,cAAc,CAAChI,aAAa,EAAE,gBAAgB,CAAC;QAC3E,IAAI,CAACgI,cAAc,CAAChI,aAAa,CAACnQ,KAAK,CAACE,UAAU,GAAG,yBAAyB;MAClF;MACA,IAAI,CAACqY,eAAe,GAAG,IAAI,CAAChK,YAAY;MACxC,IAAI,CAAC8J,cAAc,GAAG,IAAI,CAACC,YAAY;IAC3C;EACJ;EACAa,eAAeA,CAAA,EAAG;IACd,IAAIhX,eAAe,CAAC,IAAI,CAACgM,UAAU,CAAC,EAAE;MAClC,IAAI,CAACiL,iBAAiB,CAAC,CAAC;IAC5B;EACJ;EACAL,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACN,eAAe,EAAE;MACvB,IAAI,CAACA,eAAe,GAAG,IAAI,CAACvK,QAAQ,CAACmL,aAAa,CAAC,OAAO,CAAC;MAC3D,IAAI,CAACnL,QAAQ,CAAC6C,IAAI,CAACuI,WAAW,CAAC,IAAI,CAACb,eAAe,CAAC;IACxD;IACA,IAAIc,SAAS,GAAI;AACzB,eAAe,IAAI,CAACjM,WAAY;AAChC,4BAA4B,GAAG,GAAG,IAAI,CAACgL,YAAa;AACpD;AACA,SAAS;IACD,IAAI,IAAI,CAACjR,iBAAiB,EAAE;MACxB,IAAI,CAACqR,uBAAuB,GAAG,CAAC,GAAG,IAAI,CAACrR,iBAAiB,CAAC;MAC1D,IAAI,CAACqR,uBAAuB,CAACc,IAAI,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;QAChD,MAAMC,MAAM,GAAGF,KAAK,CAACG,UAAU;QAC/B,MAAMC,MAAM,GAAGH,KAAK,CAACE,UAAU;QAC/B,IAAIE,MAAM,GAAG,IAAI;QACjB,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EAChCC,MAAM,GAAG,CAAC,CAAC,CAAC,KACX,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EACrCC,MAAM,GAAG,CAAC,CAAC,KACV,IAAIH,MAAM,IAAI,IAAI,IAAIE,MAAM,IAAI,IAAI,EACrCC,MAAM,GAAG,CAAC,CAAC,KACV,IAAI,OAAOH,MAAM,KAAK,QAAQ,IAAI,OAAOE,MAAM,KAAK,QAAQ,EAC7DC,MAAM,GAAGH,MAAM,CAACI,aAAa,CAACF,MAAM,EAAEzU,SAAS,EAAE;UAAE4U,OAAO,EAAE;QAAK,CAAC,CAAC,CAAC,KAEpEF,MAAM,GAAGH,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAGF,MAAM,GAAGE,MAAM,GAAG,CAAC,GAAG,CAAC;QAC3D,OAAO,CAAC,CAAC,GAAGC,MAAM;MACtB,CAAC,CAAC;MACF,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvB,uBAAuB,CAAC9I,MAAM,EAAEqK,CAAC,EAAE,EAAE;QAC1D,IAAIC,GAAG,GAAG,IAAI,CAACxB,uBAAuB,CAACuB,CAAC,CAAC;QACzCV,SAAS,IAAK;AAC9B,oDAAoDW,GAAG,CAACN,UAAW;AACnE,2BAA2B,IAAI,CAACtM,WAAY;AAC5C,wCAAwC,GAAG,GAAG4M,GAAG,CAACvV,UAAW;AAC7D;AACA;AACA,iBAAiB;MACL;IACJ;IACA,IAAI,CAAC8T,eAAe,CAACc,SAAS,GAAGA,SAAS;EAC9C;EACAH,iBAAiBA,CAAA,EAAG;IAChB,IAAI9Y,iBAAiB,CAAC,IAAI,CAAC6N,UAAU,CAAC,EAAE;MACpC,IAAI,IAAI,CAACgK,cAAc,IAAI,IAAI,CAACO,uBAAuB,EAAE;QACrD,IAAIyB,WAAW,GAAGC,MAAM,CAACC,UAAU;QACnC,IAAIC,qBAAqB,GAAG;UACxB3V,UAAU,EAAE,IAAI,CAACyT;QACrB,CAAC;QACD,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvB,uBAAuB,CAAC9I,MAAM,EAAEqK,CAAC,EAAE,EAAE;UAC1D,IAAIC,GAAG,GAAG,IAAI,CAACxB,uBAAuB,CAACuB,CAAC,CAAC;UACzC,IAAIM,QAAQ,CAACL,GAAG,CAACN,UAAU,EAAE,EAAE,CAAC,IAAIO,WAAW,EAAE;YAC7CG,qBAAqB,GAAGJ,GAAG;UAC/B;QACJ;QACA,IAAI,IAAI,CAAC5B,YAAY,KAAKgC,qBAAqB,CAAC3V,UAAU,EAAE;UACxD,IAAI,CAAC2T,YAAY,GAAGgC,qBAAqB,CAAC3V,UAAU;UACpD,IAAI,CAAC0J,EAAE,CAACkC,YAAY,CAAC,CAAC;QAC1B;MACJ;IACJ;EACJ;EACAiK,WAAWA,CAACxP,KAAK,EAAE;IACf,OAAO,IAAI,CAACmC,YAAY,CAACnC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI;EAC9C;EACAT,UAAUA,CAAC4M,CAAC,EAAE;IACV,IAAI,CAACL,gBAAgB,CAAC,CAAC;IACvB,IAAIE,aAAa,GAAG,IAAI,CAACzI,YAAY,GAAG,CAAC;IACzC,IAAIyI,aAAa,GAAG,IAAI,CAAC2B,iBAAiB,GAAG,IAAI,CAACO,kBAAkB,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAACP,iBAAiB,GAAG,IAAI,CAAC8B,kBAAkB,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAACnT,QAAQ,CAAC,EAAE;MACtJ,IAAI,CAACoT,IAAI,CAAC,CAAC,CAAC,CAAC;IACjB;IACA,IAAIjW,WAAW,GAAG,IAAI,CAAC6C,QAAQ,IAAI,IAAI,CAACzE,KAAK,CAAC+M,MAAM,GAAG,CAAC,KAAK,IAAI,CAACrB,YAAY,GAAG,CAAC,GAAGyI,aAAa;IAClG,IAAI,CAAC/P,mBAAmB,CAACT,IAAI,CAAC/B,WAAW,CAAC;IAC1C,IAAI0S,CAAC,CAACC,UAAU,EAAE;MACdD,CAAC,CAACE,cAAc,CAAC,CAAC;IACtB;EACJ;EACAtN,WAAWA,CAACoN,CAAC,EAAE;IACX,IAAI,CAACL,gBAAgB,CAAC,CAAC;IACvB,IAAII,aAAa,GAAG,IAAI,CAAC3I,YAAY,KAAK,CAAC,GAAG,IAAI,CAACA,YAAY,GAAG,CAAC,GAAG,CAAC;IACvE,IAAI4G,IAAI,GAAG+B,aAAa,GAAG,IAAI,CAACyB,iBAAiB;IACjD,IAAI,IAAI,CAACL,YAAY,GAAGnD,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC+D,kBAAkB,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAACP,iBAAiB,KAAK,CAAC,IAAI,IAAI,CAACrR,QAAQ,CAAC,EAAE;MAClH,IAAI,CAACoT,IAAI,CAAC,CAAC,CAAC;IAChB;IACA,IAAIjW,WAAW,GAAG,IAAI,CAAC6C,QAAQ,IAAI,IAAI,CAACiH,YAAY,KAAK,CAAC,GAAG,IAAI,CAAC1L,KAAK,CAAC+M,MAAM,GAAG,CAAC,GAAGsH,aAAa;IAClG,IAAI,CAACjQ,mBAAmB,CAACT,IAAI,CAAC/B,WAAW,CAAC;IAC1C,IAAI0S,CAAC,CAACC,UAAU,EAAE;MACdD,CAAC,CAACE,cAAc,CAAC,CAAC;IACtB;EACJ;EACAxK,WAAWA,CAAC7B,KAAK,EAAE;IACf,IAAI,CAAC8L,gBAAgB,CAAC,CAAC;IACvB,IAAI6D,iBAAiB,GAAG3P,KAAK;IAC7B,IAAI2P,iBAAiB,KAAK,IAAI,CAACpM,YAAY,EAAE;MACzC,MAAM4G,IAAI,GAAGwF,iBAAiB,GAAG,IAAI,CAAChC,iBAAiB;MACvD,IAAIiC,GAAG,GAAG,CAAC;MACX,IAAID,iBAAiB,GAAG,IAAI,CAACpM,YAAY,EAAE;QACvCqM,GAAG,GAAG,IAAI,CAACtC,YAAY,GAAGnD,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC+D,kBAAkB,CAAC,CAAC;QAC9D,IAAI0B,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAACjC,iBAAiB,KAAK,CAAC,EAAE;UAC9C,IAAI,CAAC+B,IAAI,CAACE,GAAG,CAAC;QAClB;MACJ,CAAC,MACI;QACDA,GAAG,GAAG,IAAI,CAAC1B,kBAAkB,CAAC,CAAC,GAAG/D,IAAI;QACtC,IAAIyF,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAACjC,iBAAiB,GAAG,IAAI,CAAC8B,kBAAkB,CAAC,CAAC,GAAG,CAAC,EAAE;UACxE,IAAI,CAACC,IAAI,CAACE,GAAG,CAAC;QAClB;MACJ;MACA,IAAI,CAACnW,WAAW,GAAGkW,iBAAiB;MACpC,IAAI,CAAC1T,mBAAmB,CAACT,IAAI,CAAC,IAAI,CAAC/B,WAAW,CAAC;IACnD;EACJ;EACAkI,kBAAkBA,CAACkD,KAAK,EAAE7E,KAAK,EAAE;IAC7B,IAAI6E,KAAK,CAACyH,IAAI,KAAK,OAAO,IAAIzH,KAAK,CAACyH,IAAI,KAAK,OAAO,EAAE;MAClD,IAAI,CAACzK,WAAW,CAAC7B,KAAK,CAAC;MACvB6E,KAAK,CAACwH,cAAc,CAAC,CAAC;IAC1B;IACA,QAAQxH,KAAK,CAACyH,IAAI;MACd,KAAK,YAAY;QACb,IAAI,CAACuD,UAAU,CAAC,CAAC;QACjB;MACJ,KAAK,WAAW;QACZ,IAAI,CAACC,SAAS,CAAC,CAAC;QAChB;MACJ,KAAK,MAAM;QACP,IAAI,CAACC,SAAS,CAAC,CAAC;QAChBlL,KAAK,CAACwH,cAAc,CAAC,CAAC;QACtB;MACJ,KAAK,KAAK;QACN,IAAI,CAAC2D,QAAQ,CAAC,CAAC;QACfnL,KAAK,CAACwH,cAAc,CAAC,CAAC;QACtB;MACJ,KAAK,SAAS;MACd,KAAK,WAAW;QACZxH,KAAK,CAACwH,cAAc,CAAC,CAAC;QACtB;MACJ,KAAK,KAAK;QACN,IAAI,CAAC4D,QAAQ,CAAC,CAAC;QACf;MACJ;QACI;IACR;EACJ;EACAJ,UAAUA,CAAA,EAAG;IACT,MAAMK,UAAU,GAAG3Z,UAAU,CAACwT,IAAI,CAAC,IAAI,CAACoD,cAAc,CAAChI,aAAa,EAAE,mCAAmC,CAAC;IAC1G,MAAM1L,WAAW,GAAG,IAAI,CAAC0W,yBAAyB,CAAC,CAAC;IACpD,IAAI,CAACC,uBAAuB,CAAC3W,WAAW,EAAEA,WAAW,GAAG,CAAC,KAAKyW,UAAU,CAACtL,MAAM,GAAGsL,UAAU,CAACtL,MAAM,GAAG,CAAC,GAAGnL,WAAW,GAAG,CAAC,CAAC;EAC9H;EACAqW,SAASA,CAAA,EAAG;IACR,MAAMrW,WAAW,GAAG,IAAI,CAAC0W,yBAAyB,CAAC,CAAC;IACpD,IAAI,CAACC,uBAAuB,CAAC3W,WAAW,EAAEA,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,WAAW,GAAG,CAAC,CAAC;EACzF;EACAsW,SAASA,CAAA,EAAG;IACR,MAAMtW,WAAW,GAAG,IAAI,CAAC0W,yBAAyB,CAAC,CAAC;IACpD,IAAI,CAACC,uBAAuB,CAAC3W,WAAW,EAAE,CAAC,CAAC;EAChD;EACAuW,QAAQA,CAAA,EAAG;IACP,MAAME,UAAU,GAAG3Z,UAAU,CAACwT,IAAI,CAAC,IAAI,CAACoD,cAAc,CAAChI,aAAa,EAAE,mCAAmC,CAAC;IAC1G,MAAM1L,WAAW,GAAG,IAAI,CAAC0W,yBAAyB,CAAC,CAAC;IACpD,IAAI,CAACC,uBAAuB,CAAC3W,WAAW,EAAEyW,UAAU,CAACtL,MAAM,GAAG,CAAC,CAAC;EACpE;EACAqL,QAAQA,CAAA,EAAG;IACP,MAAMC,UAAU,GAAG,CAAC,GAAG3Z,UAAU,CAACwT,IAAI,CAAC,IAAI,CAACoD,cAAc,CAAChI,aAAa,EAAE,mCAAmC,CAAC,CAAC;IAC/G,MAAMkL,gBAAgB,GAAGH,UAAU,CAACI,SAAS,CAAEC,GAAG,IAAKha,UAAU,CAACia,YAAY,CAACD,GAAG,EAAE,eAAe,CAAC,KAAK,IAAI,CAAC;IAC9G,MAAME,eAAe,GAAGla,UAAU,CAAC2O,UAAU,CAAC,IAAI,CAACiI,cAAc,CAAChI,aAAa,EAAE,gBAAgB,CAAC;IAClG,MAAM1L,WAAW,GAAGyW,UAAU,CAACI,SAAS,CAAEC,GAAG,IAAKA,GAAG,KAAKE,eAAe,CAACC,aAAa,CAAC;IACxFR,UAAU,CAACzW,WAAW,CAAC,CAACkX,QAAQ,CAAC,CAAC,CAAC,CAACC,QAAQ,GAAG,IAAI;IACnDV,UAAU,CAACG,gBAAgB,CAAC,CAACM,QAAQ,CAAC,CAAC,CAAC,CAACC,QAAQ,GAAG,GAAG;EAC3D;EACAT,yBAAyBA,CAAA,EAAG;IACxB,MAAMD,UAAU,GAAG,CAAC,GAAG3Z,UAAU,CAACwT,IAAI,CAAC,IAAI,CAACoD,cAAc,CAAChI,aAAa,EAAE,mCAAmC,CAAC,CAAC;IAC/G,MAAMsL,eAAe,GAAGla,UAAU,CAAC2O,UAAU,CAAC,IAAI,CAACiI,cAAc,CAAChI,aAAa,EAAE,oDAAoD,CAAC;IACtI,OAAO+K,UAAU,CAACI,SAAS,CAAEC,GAAG,IAAKA,GAAG,KAAKE,eAAe,CAACC,aAAa,CAAC;EAC/E;EACAN,uBAAuBA,CAACS,OAAO,EAAEC,OAAO,EAAE;IACtC,MAAMZ,UAAU,GAAG3Z,UAAU,CAACwT,IAAI,CAAC,IAAI,CAACoD,cAAc,CAAChI,aAAa,EAAE,mCAAmC,CAAC;IAC1G+K,UAAU,CAACW,OAAO,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAC,CAACC,QAAQ,GAAG,IAAI;IAC/CV,UAAU,CAACY,OAAO,CAAC,CAACH,QAAQ,CAAC,CAAC,CAAC,CAACC,QAAQ,GAAG,GAAG;IAC9CV,UAAU,CAACY,OAAO,CAAC,CAACH,QAAQ,CAAC,CAAC,CAAC,CAAC1L,KAAK,CAAC,CAAC;EAC3C;EACAyK,IAAIA,CAACE,GAAG,EAAE;IACN,IAAIjC,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,GAAGiC,GAAG;IACpD,IAAIA,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC,GAAGjC,iBAAiB,GAAG,IAAI,CAACL,YAAY,GAAG,IAAI,CAACzV,KAAK,CAAC+M,MAAM,GAAG,CAAC,EAAE;MAC/E+I,iBAAiB,GAAG,IAAI,CAACL,YAAY,GAAG,IAAI,CAACzV,KAAK,CAAC+M,MAAM;IAC7D,CAAC,MACI,IAAIgL,GAAG,GAAG,CAAC,IAAIjC,iBAAiB,GAAG,CAAC,EAAE;MACvCA,iBAAiB,GAAG,CAAC;IACzB;IACA,IAAI,IAAI,CAACrR,QAAQ,EAAE;MACf,IAAIsT,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC/X,KAAK,CAAC+M,MAAM,GAAG,CAAC,KAAK,IAAI,CAACrB,YAAY,EAAE;QACxDoK,iBAAiB,GAAG,CAAC;MACzB,CAAC,MACI,IAAIiC,GAAG,GAAG,CAAC,IAAI,IAAI,CAACrM,YAAY,KAAK,CAAC,EAAE;QACzCoK,iBAAiB,GAAG,IAAI,CAACL,YAAY,GAAG,IAAI,CAACzV,KAAK,CAAC+M,MAAM;MAC7D;IACJ;IACA,IAAI,IAAI,CAACuI,cAAc,EAAE;MACrB5W,UAAU,CAACuP,WAAW,CAAC,IAAI,CAACqH,cAAc,CAAChI,aAAa,EAAE,gBAAgB,CAAC;MAC3E,IAAI,CAACgI,cAAc,CAAChI,aAAa,CAACnQ,KAAK,CAAC2T,SAAS,GAAG,IAAI,CAACpM,UAAU,GAAI,kBAAiBoR,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAACL,YAAY,CAAE,OAAM,GAAI,eAAcK,iBAAiB,IAAI,GAAG,GAAG,IAAI,CAACL,YAAY,CAAE,UAAS;MACrN,IAAI,CAACH,cAAc,CAAChI,aAAa,CAACnQ,KAAK,CAACE,UAAU,GAAG,yBAAyB;IAClF;IACA,IAAI,CAACyY,iBAAiB,GAAGA,iBAAiB;EAC9C;EACA7B,gBAAgBA,CAAA,EAAG;IACf,IAAI,IAAI,CAACpP,eAAe,IAAI,IAAI,CAACP,aAAa,EAAE;MAC5C,IAAI,CAACA,aAAa,CAACX,IAAI,CAAC,CAAC;IAC7B;EACJ;EACAuV,iBAAiBA,CAAC5E,CAAC,EAAEhC,IAAI,EAAE;IACvB,IAAIA,IAAI,GAAG,CAAC,EAAE;MACV;MACA,IAAI,CAAC5K,UAAU,CAAC4M,CAAC,CAAC;IACtB,CAAC,MACI;MACD;MACA,IAAI,CAACpN,WAAW,CAACoN,CAAC,CAAC;IACvB;EACJ;EACAsD,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC5X,KAAK,CAAC+M,MAAM,GAAG,IAAI,CAAC0I,YAAY,GAAG,IAAI,CAACzV,KAAK,CAAC+M,MAAM,GAAG,IAAI,CAAC0I,YAAY,GAAG,CAAC,GAAG,CAAC;EAChG;EACAY,kBAAkBA,CAAA,EAAG;IACjB,IAAIlO,KAAK,GAAGgR,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC3D,YAAY,GAAG,CAAC,CAAC;IAC7C,OAAO,IAAI,CAACA,YAAY,GAAG,CAAC,GAAGtN,KAAK,GAAGA,KAAK,GAAG,CAAC;EACpD;EACAkR,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC/D,cAAc,IAAI,IAAI,CAACA,cAAc,CAAChI,aAAa,EAAE;MAC1D5O,UAAU,CAAC6O,QAAQ,CAAC,IAAI,CAAC+H,cAAc,CAAChI,aAAa,EAAE,gBAAgB,CAAC;MACxE,IAAI,CAACgI,cAAc,CAAChI,aAAa,CAACnQ,KAAK,CAACE,UAAU,GAAG,EAAE;IAC3D;EACJ;EACAic,UAAUA,CAAChF,CAAC,EAAE;IACV,IAAIiF,QAAQ,GAAGjF,CAAC,CAACkF,cAAc,CAAC,CAAC,CAAC;IAClC,IAAI,IAAI,CAAC9U,UAAU,EAAE;MACjB,IAAI,CAACwU,iBAAiB,CAAC5E,CAAC,EAAEiF,QAAQ,CAACE,KAAK,GAAG,IAAI,CAAC9D,QAAQ,CAAC+D,CAAC,CAAC;IAC/D,CAAC,MACI;MACD,IAAI,CAACR,iBAAiB,CAAC5E,CAAC,EAAEiF,QAAQ,CAACI,KAAK,GAAG,IAAI,CAAChE,QAAQ,CAACiE,CAAC,CAAC;IAC/D;EACJ;EACAC,WAAWA,CAACvF,CAAC,EAAE;IACX,IAAIA,CAAC,CAACC,UAAU,EAAE;MACdD,CAAC,CAACE,cAAc,CAAC,CAAC;IACtB;EACJ;EACAsF,YAAYA,CAACxF,CAAC,EAAE;IACZ,IAAIiF,QAAQ,GAAGjF,CAAC,CAACkF,cAAc,CAAC,CAAC,CAAC;IAClC,IAAI,CAAC7D,QAAQ,GAAG;MACZiE,CAAC,EAAEL,QAAQ,CAACI,KAAK;MACjBD,CAAC,EAAEH,QAAQ,CAACE;IAChB,CAAC;EACL;EACAtS,qBAAqBA,CAAA,EAAG;IACpB,OAAQ,CAAC,IAAI,CAAC1C,QAAQ,IAAI,IAAI,CAACiH,YAAY,KAAK,CAAC,IAAK,IAAI,CAAC1L,KAAK,CAAC+M,MAAM,IAAI,IAAI,CAAC0I,YAAY;EAChG;EACA9N,oBAAoBA,CAAA,EAAG;IACnB,OAAQ,CAAC,IAAI,CAAClD,QAAQ,IAAI,IAAI,CAACiH,YAAY,KAAK,IAAI,CAAC1L,KAAK,CAAC+M,MAAM,GAAG,CAAC,IAAK,IAAI,CAAC/M,KAAK,CAAC+M,MAAM,IAAI,IAAI,CAAC0I,YAAY;EACpH;EACAlL,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACuL,iBAAiB,GAAG,CAAC,CAAC;EACtC;EACAtL,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACD,mBAAmB,CAAC,CAAC,GAAG,IAAI,CAACkL,YAAY,GAAG,CAAC;EAC7D;EACAnL,YAAYA,CAACnC,KAAK,EAAE;IAChB,OAAO,IAAI,CAACoC,mBAAmB,CAAC,CAAC,IAAIpC,KAAK,IAAI,IAAI,CAACqC,mBAAmB,CAAC,CAAC,IAAIrC,KAAK;EACrF;EACAgO,qBAAqBA,CAAA,EAAG;IACpB,IAAI1Y,iBAAiB,CAAC,IAAI,CAAC6N,UAAU,CAAC,EAAE;MACpC,MAAMiM,MAAM,GAAG,IAAI,CAAClM,QAAQ,CAAC0O,WAAW,IAAI,QAAQ;MACpD,IAAI,CAAC/D,sBAAsB,GAAG,IAAI,CAACZ,QAAQ,CAAC4E,MAAM,CAACzC,MAAM,EAAE,QAAQ,EAAE,MAAM;QACvE,IAAI,CAAChB,iBAAiB,CAAC,CAAC;MAC5B,CAAC,CAAC;IACN;EACJ;EACA0D,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACjE,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACA,sBAAsB,GAAG,IAAI;IACtC;EACJ;EACAhI,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACxJ,iBAAiB,EAAE;MACxB,IAAI,CAACyV,uBAAuB,CAAC,CAAC;IAClC;IACA,IAAI,IAAI,CAACrE,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACsE,UAAU,EAAEC,WAAW,CAAC,IAAI,CAACvE,eAAe,CAAC;IACtE;EACJ;EACAlM,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC7F,QAAQ,CAAC4H,MAAM,CAACwH,WAAW,CAACC,IAAI,GAAG,IAAI,CAACrP,QAAQ,CAAC4H,MAAM,CAACwH,WAAW,CAACC,IAAI,CAACkH,aAAa,GAAG7X,SAAS;EAClH;EACA2I,mBAAmBA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACrH,QAAQ,CAAC4H,MAAM,CAACwH,WAAW,CAACC,IAAI,GAAG,IAAI,CAACrP,QAAQ,CAAC4H,MAAM,CAACwH,WAAW,CAACC,IAAI,CAACmH,aAAa,GAAG9X,SAAS;EAClH;EACAmG,aAAaA,CAAC1I,KAAK,EAAE;IACjB,OAAO,IAAI,CAAC6D,QAAQ,CAAC4H,MAAM,CAACwH,WAAW,CAACC,IAAI,GAAG,IAAI,CAACrP,QAAQ,CAAC4H,MAAM,CAACwH,WAAW,CAACC,IAAI,CAAC6B,SAAS,CAACD,OAAO,CAAC,SAAS,EAAE9U,KAAK,CAAC,GAAGuC,SAAS;EACxI;EACA,OAAO4L,IAAI,YAAAmM,2BAAAjM,CAAA;IAAA,YAAAA,CAAA,IAAwFwF,kBAAkB,EA3kC5BlW,EAAE,CAAA2Q,iBAAA,CA2kC4ClD,QAAQ,GA3kCtDzN,EAAE,CAAA2Q,iBAAA,CA2kCiE9Q,QAAQ,GA3kC3EG,EAAE,CAAA2Q,iBAAA,CA2kCsFzQ,WAAW,GA3kCnGF,EAAE,CAAA2Q,iBAAA,CA2kC8G3Q,EAAE,CAAC4c,SAAS,GA3kC5H5c,EAAE,CAAA2Q,iBAAA,CA2kCuI3Q,EAAE,CAAC6Q,iBAAiB;EAAA;EACtP,OAAOE,IAAI,kBA5kC8E/Q,EAAE,CAAAgR,iBAAA;IAAAC,IAAA,EA4kCJiF,kBAAkB;IAAAhF,SAAA;IAAAQ,SAAA,WAAAmL,yBAAAra,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA5kChBxC,EAAE,CAAA4R,WAAA,CAAA3G,IAAA;MAAA;MAAA,IAAAzI,EAAA;QAAA,IAAA+O,EAAA;QAAFvR,EAAE,CAAAwR,cAAA,CAAAD,EAAA,GAAFvR,EAAE,CAAAyR,WAAA,QAAAhP,GAAA,CAAAkV,cAAA,GAAApG,EAAA,CAAAM,KAAA;MAAA;IAAA;IAAAE,MAAA;MAAAjF,WAAA;MAAAzK,KAAA;MAAA0E,UAAA;MAAAG,eAAA;MAAAJ,QAAA;MAAAD,iBAAA;MAAA6Q,aAAA;MAAAzQ,uBAAA;MAAAZ,SAAA;MAAAlC,UAAA;MAAAF,WAAA;IAAA;IAAA+N,OAAA;MAAAvL,mBAAA;MAAAE,aAAA;IAAA;IAAAwL,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAArD,QAAA,WAAA8N,4BAAAta,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAAE,GAAA,GAAF1C,EAAE,CAAA2C,gBAAA;QAAF3C,EAAE,CAAA4C,cAAA,YA6kC9C,CAAC,YACK,CAAC;QA9kCqC5C,EAAE,CAAAsE,UAAA,IAAAuH,oCAAA,mBAulC/E,CAAC;QAvlC4E7L,EAAE,CAAA4C,cAAA,YA8lC0B,CAAC,eAC0E,CAAC;QA/lCxG5C,EAAE,CAAA6C,UAAA,2BAAAka,yDAAA;UAAF/c,EAAE,CAAAgD,aAAA,CAAAN,GAAA;UAAA,OAAF1C,EAAE,CAAAmD,WAAA,CA+lCFV,GAAA,CAAAiZ,eAAA,CAAgB,CAAC;QAAA,EAAC,wBAAAsB,sDAAAja,MAAA;UA/lClB/C,EAAE,CAAAgD,aAAA,CAAAN,GAAA;UAAA,OAAF1C,EAAE,CAAAmD,WAAA,CA+lC+BV,GAAA,CAAA0Z,YAAA,CAAApZ,MAAmB,CAAC;QAAA,EAAC,uBAAAka,qDAAAla,MAAA;UA/lCtD/C,EAAE,CAAAgD,aAAA,CAAAN,GAAA;UAAA,OAAF1C,EAAE,CAAAmD,WAAA,CA+lCkEV,GAAA,CAAAyZ,WAAA,CAAAnZ,MAAkB,CAAC;QAAA,EAAC;QA/lCxF/C,EAAE,CAAAsE,UAAA,IAAA2H,iCAAA,iBA8mCvE,CAAC;QA9mCoEjM,EAAE,CAAA2D,YAAA,CA2nCtE,CAAC,CACL,CAAC;QA5nCuE3D,EAAE,CAAAsE,UAAA,IAAA8I,oCAAA,mBAqoC/E,CAAC;QAroC4EpN,EAAE,CAAA2D,YAAA,CA4oC9E,CAAC,CACL,CAAC;MAAA;MAAA,IAAAnB,EAAA;QA7oC+ExC,EAAE,CAAA6E,SAAA,EAglC9C,CAAC;QAhlC2C7E,EAAE,CAAA4D,UAAA,SAAAnB,GAAA,CAAAwE,uBAglC9C,CAAC;QAhlC2CjH,EAAE,CAAA6E,SAAA,CA8lCyB,CAAC;QA9lC5B7E,EAAE,CAAA4D,UAAA,YAAF5D,EAAE,CAAA6D,eAAA,IAAAqH,IAAA,EAAAzI,GAAA,CAAAsE,UAAA,GAAAtE,GAAA,CAAAiV,aAAA,MA8lCyB,CAAC;QA9lC5B1X,EAAE,CAAA6E,SAAA,EAimCzC,CAAC;QAjmCsC7E,EAAE,CAAA4D,UAAA,YAAAnB,GAAA,CAAAJ,KAimCzC,CAAC;QAjmCsCrC,EAAE,CAAA6E,SAAA,CA8nC9C,CAAC;QA9nC2C7E,EAAE,CAAA4D,UAAA,SAAAnB,GAAA,CAAAwE,uBA8nC9C,CAAC;MAAA;IAAA;IAAAyL,YAAA,EAAAA,CAAA,MAgBmC9S,EAAE,CAAC+S,OAAO,EAAyG/S,EAAE,CAAC4X,OAAO,EAAwI5X,EAAE,CAACgT,IAAI,EAAkHhT,EAAE,CAACiW,gBAAgB,EAAyKjW,EAAE,CAACiT,OAAO,EAAgGxR,EAAE,CAACyU,MAAM,EAA2E7U,gBAAgB,EAAkFD,eAAe,EAAiFgV,gBAAgB;IAAAhD,aAAA;IAAAK,eAAA;EAAA;AAC/hC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhpC6FtT,EAAE,CAAAuT,iBAAA,CAgpCJ2C,kBAAkB,EAAc,CAAC;IAChHjF,IAAI,EAAE9Q,SAAS;IACfqT,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCzE,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MACeqE,eAAe,EAAEjT,uBAAuB,CAACuT;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE1C,IAAI,EAAExD;EAAS,CAAC,EAAE;IAAEwD,IAAI,EAAE8C,QAAQ;IAAEC,UAAU,EAAE,CAAC;MAClE/C,IAAI,EAAE3Q,MAAM;MACZkT,IAAI,EAAE,CAAC3T,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEoR,IAAI,EAAErM,SAAS;IAAEoP,UAAU,EAAE,CAAC;MAClC/C,IAAI,EAAE3Q,MAAM;MACZkT,IAAI,EAAE,CAACtT,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAE+Q,IAAI,EAAEjR,EAAE,CAAC4c;EAAU,CAAC,EAAE;IAAE3L,IAAI,EAAEjR,EAAE,CAAC6Q;EAAkB,CAAC,CAAC,EAAkB;IAAE/D,WAAW,EAAE,CAAC;MAC/FmE,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE8B,KAAK,EAAE,CAAC;MACR4O,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEwG,UAAU,EAAE,CAAC;MACbkK,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE2G,eAAe,EAAE,CAAC;MAClB+J,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEuG,QAAQ,EAAE,CAAC;MACXmK,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEsG,iBAAiB,EAAE,CAAC;MACpBoK,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEmX,aAAa,EAAE,CAAC;MAChBzG,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE0G,uBAAuB,EAAE,CAAC;MAC1BgK,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE8F,SAAS,EAAE,CAAC;MACZ4K,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAEkG,mBAAmB,EAAE,CAAC;MACtBwK,IAAI,EAAEzQ;IACV,CAAC,CAAC;IAAEmG,aAAa,EAAE,CAAC;MAChBsK,IAAI,EAAEzQ;IACV,CAAC,CAAC;IAAEmX,cAAc,EAAE,CAAC;MACjB1G,IAAI,EAAExQ,SAAS;MACf+S,IAAI,EAAE,CAAC,gBAAgB;IAC3B,CAAC,CAAC;IAAErP,UAAU,EAAE,CAAC;MACb8M,IAAI,EAAE1Q;IACV,CAAC,CAAC;IAAE0D,WAAW,EAAE,CAAC;MACdgN,IAAI,EAAE1Q;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM2c,cAAc,CAAC;EACjB,OAAO1M,IAAI,YAAA2M,uBAAAzM,CAAA;IAAA,YAAAA,CAAA,IAAwFwM,cAAc;EAAA;EACjH,OAAOE,IAAI,kBA/vC8Epd,EAAE,CAAAqd,gBAAA;IAAApM,IAAA,EA+vCSiM;EAAc;EAClH,OAAOI,IAAI,kBAhwC8Etd,EAAE,CAAAud,gBAAA;IAAAC,OAAA,GAgwCmCzd,YAAY,EAAEe,YAAY,EAAEQ,YAAY,EAAEJ,SAAS,EAAED,gBAAgB,EAAED,eAAe,EAAEG,kBAAkB,EAAEC,kBAAkB,EAAEM,eAAe,EAAE3B,YAAY,EAAEe,YAAY;EAAA;AAC7S;AACA;EAAA,QAAAwS,SAAA,oBAAAA,SAAA,KAlwC6FtT,EAAE,CAAAuT,iBAAA,CAkwCJ2J,cAAc,EAAc,CAAC;IAC5GjM,IAAI,EAAEtQ,QAAQ;IACd6S,IAAI,EAAE,CAAC;MACCgK,OAAO,EAAE,CAACzd,YAAY,EAAEe,YAAY,EAAEQ,YAAY,EAAEJ,SAAS,EAAED,gBAAgB,EAAED,eAAe,EAAEG,kBAAkB,EAAEC,kBAAkB,EAAEM,eAAe,CAAC;MAC1J+b,OAAO,EAAE,CAAC1d,YAAY,EAAE0N,QAAQ,EAAEqF,eAAe,EAAEkD,gBAAgB,EAAEC,YAAY,EAAEC,kBAAkB,EAAEpV,YAAY,CAAC;MACpH4c,YAAY,EAAE,CAACjQ,QAAQ,EAAEqF,eAAe,EAAEkD,gBAAgB,EAAEC,YAAY,EAAEC,kBAAkB;IAChG,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASzI,QAAQ,EAAEqF,eAAe,EAAEmD,YAAY,EAAED,gBAAgB,EAAEkH,cAAc,EAAEhH,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}