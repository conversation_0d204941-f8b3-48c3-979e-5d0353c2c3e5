{"ast": null, "code": "import { CMS_APIContstant } from 'src/app/constants/api.constants';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ResetPasswordService {\n  constructor(http) {\n    this.http = http;\n  }\n  resetPassword(data) {\n    return this.http.post(CMS_APIContstant.RESET_PASSWORD, data);\n  }\n  static {\n    this.ɵfac = function ResetPasswordService_Factory(t) {\n      return new (t || ResetPasswordService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ResetPasswordService,\n      factory: ResetPasswordService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["CMS_APIContstant", "ResetPasswordService", "constructor", "http", "resetPassword", "data", "post", "RESET_PASSWORD", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\session\\reset-password\\reset-password.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { CMS_APIContstant } from 'src/app/constants/api.constants';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ResetPasswordService {\r\n\r\n  constructor(private http: HttpClient) { }\r\n\r\n  resetPassword(data: any) {\r\n    return this.http.post(CMS_APIContstant.RESET_PASSWORD, data);\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,gBAAgB,QAAQ,iCAAiC;;;AAKlE,OAAM,MAAOC,oBAAoB;EAE/BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAgB;EAExCC,aAAaA,CAACC,IAAS;IACrB,OAAO,IAAI,CAACF,IAAI,CAACG,IAAI,CAACN,gBAAgB,CAACO,cAAc,EAAEF,IAAI,CAAC;EAC9D;;;uBANWJ,oBAAoB,EAAAO,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAApBV,oBAAoB;MAAAW,OAAA,EAApBX,oBAAoB,CAAAY,IAAA;MAAAC,UAAA,EAFnB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}