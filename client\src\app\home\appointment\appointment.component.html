<!--APPOINTMENT BOOKING SEC-->
<section class="appointment-sec relative pt-8 pb-8">
    <div class="appointment-body relative max-w-1200 w-full mx-auto px-4">
        <div class="appointment-header mb-6">
            <h2 class="m-0 mb-4 text-6xl line-height-2 font-bold text-color">Book Appointment</h2>
            <p class="text-lg text-color-secondary" *ngIf="serviceId">
                Booking appointment for Service ID: <strong>{{ serviceId }}</strong>
            </p>
        </div>

        <div class="appointment-form-container bg-white p-6 shadow-1 border-round">
            <form (ngSubmit)="onSubmitAppointment()" #appointmentForm="ngForm">
                <div class="grid">
                    <!-- Personal Information -->
                    <div class="col-12">
                        <h3 class="text-xl font-semibold mb-4 text-color">Personal Information</h3>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <label for="firstName" class="block text-900 font-medium mb-2">First Name *</label>
                        <input type="text" pInputText id="firstName" name="firstName" 
                               class="w-full" placeholder="Enter your first name" required />
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <label for="lastName" class="block text-900 font-medium mb-2">Last Name *</label>
                        <input type="text" pInputText id="lastName" name="lastName" 
                               class="w-full" placeholder="Enter your last name" required />
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <label for="email" class="block text-900 font-medium mb-2">Email *</label>
                        <input type="email" pInputText id="email" name="email" 
                               class="w-full" placeholder="Enter your email" required />
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <label for="phone" class="block text-900 font-medium mb-2">Phone Number *</label>
                        <input type="tel" pInputText id="phone" name="phone" 
                               class="w-full" placeholder="Enter your phone number" required />
                    </div>

                    <!-- Appointment Details -->
                    <div class="col-12 mt-4">
                        <h3 class="text-xl font-semibold mb-4 text-color">Appointment Details</h3>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <label for="appointmentDate" class="block text-900 font-medium mb-2">Preferred Date *</label>
                        <p-calendar id="appointmentDate" name="appointmentDate"
                                   [showIcon]="true" inputId="icon"
                                   placeholder="Select date" styleClass="w-full"></p-calendar>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <label for="appointmentTime" class="block text-900 font-medium mb-2">Preferred Time *</label>
                        <p-dropdown id="appointmentTime" name="appointmentTime"
                                   [options]="timeSlots" optionLabel="label" optionValue="value"
                                   placeholder="Select time" styleClass="w-full"></p-dropdown>
                    </div>
                    
                    <div class="col-12">
                        <label for="message" class="block text-900 font-medium mb-2">Additional Message</label>
                        <textarea pInputTextarea id="message" name="message" rows="4" 
                                 class="w-full" placeholder="Any additional information or special requests"></textarea>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-12 mt-4">
                        <div class="flex gap-3">
                            <button type="submit" 
                                    class="px-6 py-3 p-element p-ripple p-button-rounded p-button p-component justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none">
                                Book Appointment
                            </button>
                            <button type="button" 
                                    class="px-6 py-3 p-element p-ripple p-button-rounded p-button p-component p-button-outlined justify-content-center gap-2 line-height-2 text-color font-semibold"
                                    routerLink="/ps">
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>
<!--APPOINTMENT BOOKING SEC-->
