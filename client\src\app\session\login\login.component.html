<section class="login-sec bg-white h-screen lg:pt-6 pb-6 md:pt-4 pb-4">
  <div class="login-page-body flex flex-column justify-content-between p-20 gap-8 m-auto h-full px-5">
    <div class="login-header relative flex align-items-center justify-content-between">
      <div class="logo w-full xl:max-w-15rem lg:max-w-12rem max-w-12rem">
        <a href="#" class="flex w-full"><img [src]="logo" alt="Logo" class="w-full" /></a>
      </div>
      <div class="sign-up flex align-items-center justify-content-center text-primary gap-3 text-base font-medium">
        You don't have any account?
        <button type="button" [routerLink]="['/auth/signup']"
          class="sign-up-btn p-element p-ripple p-button-outlined p-button-rounded p-button p-component w-8rem justify-content-center gap-1">
          <span class="material-symbols-rounded text-2xl">person</span> Sign Up
        </button>
      </div>
    </div>
    <div class="login-form mx-auto w-full bg-white border-round-3xl shadow-2 lg:p-6 md:p-4">
      <form class="flex flex-column position-relative" [formGroup]="loginForm">
        <h1 class="mb-2 flex justify-content-center text-4xl font-bold text-primary">{{ loginDetails.Title }} Welcome
        </h1>
        <p class="mb-5 flex justify-content-center text-base font-medium text-gray-900">{{ loginDetails.Sub_Title }}
          Login
          in to your account.</p>
        <div class="form-group relative mb-3">
          <input type="email" class="p-inputtext p-component p-element w-full bg-gray-50" id="username"
            formControlName="email" placeholder="Enter Email Address" />
        </div>
        <div class="form-group relative mb-3">
          <input [type]="showPass ? 'text' : 'password'" class="p-inputtext p-component p-element w-full bg-gray-50"
            formControlName="password" placeholder="Enter Password" />
          <button type="button"
            class="pass-show-btn absolute top-0 bottom-0 m-auto p-0 border-none bg-white-alpha-10 text-gray-500 cursor-pointer"><span
              class="material-symbols-rounded" (click)="showPass = !showPass">visibility</span></button>
        </div>
        <div class="form-group relative mb-4 flex align-items-center justify-content-between">
          <div class="flex align-items-center gap-1">
            <input type="checkbox" class="form-check-box m-0 w-1rem h-1rem" formControlName="rememberMe"
              id="exampleCheck1" />
            <label class="text-m text-gray-700" for="exampleCheck1">Remember Me</label>
          </div>
          <button type="button"
            class="p-component flex justify-content-center text-base font-medium text-gray-900 border-none bg-white-alpha-10 underline cursor-pointer"
            (click)="forgotPassword()">Having Trouble in Login?</button>
        </div>
        <div class="form-footer">
          <button type="button"
            class="p-element p-ripple p-button-rounded p-button p-component w-full justify-content-center h-3-3rem font-semibold"
            [disabled]="!!loginForm.invalid || isSubmitting" (click)="login()"> Login</button>
          <div class="mt-3 mb-3 flex justify-content-center text-base font-medium text-gray-900"><span>Or Login
              With</span></div>
          <a type="button"
            class="p-button-outlined p-button-rounded p-button p-component w-full h-3-3rem justify-content-center gap-3 font-semibold border-2 border-black-alpha-20"
            [attr.href]="API_ENDPOINT + '/api/auth/login?origin='+rorigin"><span class="material-symbols-rounded">key</span> Login with
            SSO</a>
        </div>

      </form>
    </div>
    <div class="copyright-sec flex flex-column position-relative">
      <p class="m-0 flex justify-content-center text-base font-medium text-gray-900">{{
        commonContent?.i18n?.['label.copyright']
        || '' }}</p>
      <ul class="p-0 flex position-relative align-items-center justify-content-center list-none gap-3">
        <li><a [href]="commonContent?.i18n?.['link.tearmsAndConditions'] || ''" target="_blank"
            class="flex justify-content-center text-base font-medium text-primary underline">{{
            commonContent?.i18n?.['label.tearmsAndConditions']
            || '' }}</a></li>
        <li><a [href]="commonContent?.i18n?.['link.privacyPolicy'] || ''" target="_blank"
            class="flex justify-content-center text-base font-medium text-primary underline">{{
            commonContent?.i18n?.['label.privacyPolicy']

            || '' }}</a></li>
      </ul>

      <app-forgot-password [(visible)]="isDialogVisible"></app-forgot-password>

    </div>
  </div>
</section>