# Use Node.js 20 as base image
FROM node:20

# Set working directory
WORKDIR /app

# Copy only the final output from pipeline (you’ve already run npm install + build)
COPY dist ./dist
COPY types ./types
COPY public ./public
COPY node_modules ./node_modules
COPY package.json ./package.json
COPY package-lock.json ./package-lock.json
COPY tsconfig.json ./tsconfig.json

# Expose Strapi default port
EXPOSE 1337

# Start Strapi
CMD ["npm", "start"]
