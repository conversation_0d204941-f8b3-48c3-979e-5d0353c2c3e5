<section class="login-sec bg-white min-h-screen flex align-items-center lg:pt-6 pb-6 md:pt-4 pb-4">
    <div class="login-page-body flex align-items-center justify-content-center gap-8 m-auto h-full px-5">
        <div class="login-form mx-auto p-5 w-full bg-white border-round-3xl shadow-2">
            <form class="flex flex-column position-relative" [formGroup]="form">
                <h1 class="mb-2 flex justify-content-center text-4xl font-bold text-primary">Reset Password
                </h1>
                <div class="p-fluid p-formgrid grid">
                    <div class="field col-12 mb-0">
                        <label class="text-base font-medium text-gray-600">Password</label>
                        <div class="form-group relative">
                            <div class="relative">
                                <input type="password" formControlName="password"
                                    class="p-inputtext p-component p-element w-full bg-gray-50"
                                    [ngClass]="{ 'is-invalid': submitted && f['password'].errors }" />
                                <button type="button"
                                    class="pass-show-btn absolute top-0 bottom-0 m-auto p-0 border-none bg-white-alpha-10 text-gray-500 cursor-pointer"><span
                                        class="material-symbols-rounded">visibility</span></button>
                            </div>
                            <div *ngIf="submitted && f['password'].errors" class="invalid-feedback text-red-500">
                                <div *ngIf="f['password'].errors['required']">
                                    This field is required</div>
                                <div *ngIf="f['password'].errors['minlength']">
                                    Must be at least 8 characters</div>
                                <div *ngIf="f['password'].errors['hasNumber']">
                                    Must contain at least one number</div>
                                <div *ngIf="f['password'].errors['hasCapitalCase']">
                                    Must contain at least one Letter in Capital Case</div>
                                <div *ngIf="f['password'].errors['hasSmallCase']">
                                    Must contain at least one Letter in Small Case</div>
                                <div *ngIf="f['password'].errors['hasSpecialCharacters']">
                                    Must contain at least one Special Character</div>
                            </div>
                        </div>
                    </div>
                    <div class="field col-12 mb-0">
                        <label class="text-base font-medium text-gray-600">Retype Password</label>
                        <div class="form-group relative">
                            <div class="relative">
                                <input type="password" formControlName="passwordConfirm"
                                    class="p-inputtext p-component p-element w-full bg-gray-50"
                                    [ngClass]="{ 'is-invalid': submitted && f['passwordConfirm'].errors }" />
                                <button type="button"
                                    class="pass-show-btn absolute top-0 bottom-0 m-auto p-0 border-none bg-white-alpha-10 text-gray-500 cursor-pointer"><span
                                        class="material-symbols-rounded">visibility</span></button>
                            </div>
                            <div *ngIf="submitted && f['passwordConfirm'].errors" class="invalid-feedback text-red-500">
                                <div *ngIf="f['passwordConfirm'].errors['required']">
                                    This field is required</div>
                                <div *ngIf="f['passwordConfirm'].errors['confirmedValidator']">
                                    Passwords must match
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="form-footer mt-4">
                    <div class="p-fluid p-formgrid grid">
                        <div class="field col-12 md:col-6 mb-0">
                            <button type="button"
                                class="p-button-rounded p-button p-component w-full justify-content-center h-3-3rem font-semibold"
                                [disabled]="saving" (click)="onSubmit()">
                                Login</button>
                        </div>
                        <div class="field col-12 md:col-6 mb-0">
                            <button type="button" [routerLink]="['/auth/login']"
                                class="p-button-outlined p-button-rounded p-button p-component w-full h-3-3rem justify-content-center gap-3 font-semibold border-2 border-black-alpha-20">
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>