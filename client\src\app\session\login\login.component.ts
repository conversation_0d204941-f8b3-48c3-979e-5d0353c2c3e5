import { Component, OnInit } from "@angular/core";
import { FormBuilder, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { catchError, of } from "rxjs";
import { ForgotPasswordComponent } from "../forgot-password/forgot-password.component";
import { environment } from "src/environments/environment";
import { AuthService } from "src/app/core/authentication/auth.service";
import { ContentService } from "src/app/core/services/content-vendor.service";


@Component({
  selector: "app-login",
  templateUrl: "./login.component.html",
  styleUrls: ["./login.component.scss"],
})
export class LoginComponent implements OnInit {
  public API_ENDPOINT: any = environment.apiEndpoint;
  public isSubmitting = false;
  public loginForm = this.fb.nonNullable.group({
    email: ["", [Validators.required, Validators.email]],
    password: ["", [Validators.required]],
    rememberMe: [false],
  });
  public errMsg: any = null;
  loading = false;
  loginPageDetails!: any;
  showPass = false;
  logo = '';
  loginDetails: any = {};
  privecyPolicy: any = {};
  termsAndConditions: any = {};
  copyright = "";
  public commonContent!: any;
  public rorigin = window.location.origin;

  constructor(
    private fb: FormBuilder,
    private auth: AuthService,
    public router: Router,
    private route: ActivatedRoute,
    private CMSservice: ContentService,
  ) { }

  ngOnInit(): void {
    this.commonContent = this.route.snapshot.parent?.data['commonContent'];
    const logoComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, "crm.logo");
    if (logoComponent?.length) {
      this.logo = logoComponent[0].Logo?.url || '';
    }
  }

  get email() {
    return this.loginForm.get("email")!;
  }

  get password() {
    return this.loginForm.get("password")!;
  }

  get rememberMe() {
    return this.loginForm.get("rememberMe")!;
  }

  login() {
    this.isSubmitting = true;
    this.auth
      .login(this.email.value, this.password.value, this.rememberMe.value)
      .pipe(catchError((err) => of(err.error)))
      .subscribe((res: any) => {
        this.isSubmitting = false;
        if (res.jwt) {
          this.router.navigate(["store"]);
        } else {
          this.errMsg = res?.error?.message || 'Error while login';
        }
      });
  }

  reset() {
    this.loginForm.reset();
    this.errMsg = null;
  }

  // forgotPassword() {
  //   // this.dialog.open(ForgotPasswordComponent);
  // 


  isDialogVisible: boolean = false;

  forgotPassword() {
    this.isDialogVisible = true;
  }

}
