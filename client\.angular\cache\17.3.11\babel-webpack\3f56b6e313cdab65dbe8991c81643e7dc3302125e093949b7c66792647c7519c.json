{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { HomeRoutingModule } from './home-routing.module';\nimport { HomeComponent } from './home.component';\nimport { SharedModule } from '../shared/shared.module';\nimport { FormsModule } from '@angular/forms';\nimport { ButtonModule } from 'primeng/button';\nimport { CarouselModule } from 'primeng/carousel';\nimport { DialogModule } from 'primeng/dialog';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { GalleriaModule } from 'primeng/galleria';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { MenubarModule } from 'primeng/menubar';\nimport { TagModule } from 'primeng/tag';\nimport * as i0 from \"@angular/core\";\nexport class HomeModule {\n  static {\n    this.ɵfac = function HomeModule_Factory(t) {\n      return new (t || HomeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: HomeModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, HomeRoutingModule, SharedModule, FormsModule, DialogModule, InputTextModule, ButtonModule, DropdownModule, MenubarModule, GalleriaModule, CarouselModule, TagModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HomeModule, {\n    declarations: [HomeComponent],\n    imports: [CommonModule, HomeRoutingModule, SharedModule, FormsModule, DialogModule, InputTextModule, ButtonModule, DropdownModule, MenubarModule, GalleriaModule, CarouselModule, TagModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "HomeRoutingModule", "HomeComponent", "SharedModule", "FormsModule", "ButtonModule", "CarouselModule", "DialogModule", "DropdownModule", "GalleriaModule", "InputTextModule", "MenubarModule", "TagModule", "HomeModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { HomeRoutingModule } from './home-routing.module';\r\nimport { HomeComponent } from './home.component';\r\nimport { HomeLayoutComponent } from './home-layout/home-layout.component';\r\nimport { SharedModule } from '../shared/shared.module';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CarouselModule } from 'primeng/carousel';\r\nimport { DialogModule } from 'primeng/dialog';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { GalleriaModule } from 'primeng/galleria';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { MenubarModule } from 'primeng/menubar';\r\nimport { TagModule } from 'primeng/tag';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    HomeComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    HomeRoutingModule,\r\n    SharedModule,\r\n    FormsModule,\r\n    DialogModule,\r\n    InputTextModule,\r\n    ButtonModule,\r\n    DropdownModule,\r\n    MenubarModule,\r\n    GalleriaModule,\r\n    CarouselModule,\r\n    TagModule,\r\n  ]\r\n})\r\nexport class HomeModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,aAAa,QAAQ,kBAAkB;AAEhD,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,SAAS,QAAQ,aAAa;;AAsBvC,OAAM,MAAOC,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAdnBb,YAAY,EACZC,iBAAiB,EACjBE,YAAY,EACZC,WAAW,EACXG,YAAY,EACZG,eAAe,EACfL,YAAY,EACZG,cAAc,EACdG,aAAa,EACbF,cAAc,EACdH,cAAc,EACdM,SAAS;IAAA;EAAA;;;2EAGAC,UAAU;IAAAC,YAAA,GAjBnBZ,aAAa;IAAAa,OAAA,GAGbf,YAAY,EACZC,iBAAiB,EACjBE,YAAY,EACZC,WAAW,EACXG,YAAY,EACZG,eAAe,EACfL,YAAY,EACZG,cAAc,EACdG,aAAa,EACbF,cAAc,EACdH,cAAc,EACdM,SAAS;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}