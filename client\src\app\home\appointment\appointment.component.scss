.appointment-sec {
  min-height: 80vh;
  background: #f8f9fa;
}

.appointment-form-container {
  max-width: 800px;
  margin: 0 auto;
}

.appointment-header {
  text-align: center;
  
  h2 {
    color: #2c3e50;
  }
}

// Form styling
.grid {
  .col-12, .col-6 {
    padding: 0.5rem;
  }
}

// Custom button styling
.p-button {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

// Calendar and dropdown styling
::ng-deep {
  .p-calendar,
  .p-dropdown {
    width: 100%;
    
    .p-inputtext,
    .p-dropdown-label {
      padding: 0.75rem;
    }
  }
  
  .p-inputtextarea {
    padding: 0.75rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .appointment-header {
    h2 {
      font-size: 2.5rem;
    }
  }
  
  .appointment-form-container {
    margin: 0 1rem;
    padding: 1rem;
  }
}
