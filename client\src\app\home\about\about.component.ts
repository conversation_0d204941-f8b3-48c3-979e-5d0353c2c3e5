import { Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ContentService } from '../../core/services/content-vendor.service';

@Component({
  selector: 'app-about',
  templateUrl: './about.component.html',
  styleUrl: './about.component.scss'
})
export class AboutComponent {

  content!: any;

  constructor(
    private route: ActivatedRoute,
    private CMSservice: ContentService
  ) { }

  ngOnInit(): void {
    this.content = this.route.snapshot.data['content'];
    console.log('About Content:', this.content);

    // Process content specific to about page
    // Extract any specific components needed for about page
  }
}
