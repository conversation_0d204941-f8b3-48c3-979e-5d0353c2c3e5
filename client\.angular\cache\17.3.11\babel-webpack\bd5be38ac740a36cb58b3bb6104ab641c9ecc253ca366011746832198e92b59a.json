{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../shared/components/header/header.component\";\nimport * as i4 from \"../../shared/components/footer/footer.component\";\nexport class HomeLayoutComponent {\n  constructor(primengConfig, renderer, route) {\n    this.primengConfig = primengConfig;\n    this.renderer = renderer;\n    this.route = route;\n  }\n  ngOnInit() {\n    // Get common content from route data\n    this.commonContent = this.route.snapshot.data['commonContent'];\n    console.log('Common Content:', this.commonContent);\n    // Inject theme \n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\n    const link = this.renderer.createElement('link');\n    this.renderer.setAttribute(link, 'id', 'theme-link');\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\n    this.renderer.setAttribute(link, 'type', 'text/css');\n    this.renderer.setAttribute(link, 'href', href);\n    // Append the link tag to the head of the document\n    this.renderer.appendChild(document.head, link);\n    this.primengConfig.ripple = true; //enables core ripple functionality\n  }\n  ngOnDestroy() {\n    // Find and remove the link tag when the component is destroyed\n    const link = document.getElementById('theme-link');\n    if (link) {\n      link.remove();\n    }\n  }\n  static {\n    this.ɵfac = function HomeLayoutComponent_Factory(t) {\n      return new (t || HomeLayoutComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.ActivatedRoute));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeLayoutComponent,\n      selectors: [[\"app-home-layout\"]],\n      decls: 4,\n      vars: 1,\n      consts: [[3, \"commonContent\"], [1, \"home-layout-content\"]],\n      template: function HomeLayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-header\", 0);\n          i0.ɵɵelementStart(1, \"main\", 1);\n          i0.ɵɵelement(2, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"app-footer\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"commonContent\", ctx.commonContent);\n        }\n      },\n      dependencies: [i2.RouterOutlet, i3.HeaderComponent, i4.FooterComponent],\n      styles: [\".home-layout-content[_ngcontent-%COMP%] {\\n  min-height: calc(100vh - 200px);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvaG9tZS9ob21lLWxheW91dC9ob21lLWxheW91dC5jb21wb25lbnQuc2NzcyIsIndlYnBhY2s6Ly8uLy4uLy4uLy4uL1B1YmxpYyUyMFNlcnZpY2UvU05KWUEtUFVCTElDLVNFUlZJQ0UvY2xpZW50L3NyYy9hcHAvaG9tZS9ob21lLWxheW91dC9ob21lLWxheW91dC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLCtCQUFBO0FDQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIuaG9tZS1sYXlvdXQtY29udGVudCB7XG4gIG1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSAyMDBweCk7IC8vIEFkanVzdCBiYXNlZCBvbiBoZWFkZXIvZm9vdGVyIGhlaWdodFxuICBcbiAgLy8gQWRkIGFueSBjb21tb24gc3R5bGluZyBmb3IgdGhlIGNvbnRlbnQgYXJlYVxuICAvLyBUaGlzIGVuc3VyZXMgY29uc2lzdGVudCBzcGFjaW5nIGFuZCBsYXlvdXQgZm9yIGFsbCBjaGlsZCByb3V0ZXNcbn1cbiIsIi5ob21lLWxheW91dC1jb250ZW50IHtcbiAgbWluLWhlaWdodDogY2FsYygxMDB2aCAtIDIwMHB4KTtcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["HomeLayoutComponent", "constructor", "primengConfig", "renderer", "route", "ngOnInit", "commonContent", "snapshot", "data", "console", "log", "href", "link", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "document", "head", "ripple", "ngOnDestroy", "getElementById", "remove", "i0", "ɵɵdirectiveInject", "i1", "PrimeNGConfig", "Renderer2", "i2", "ActivatedRoute", "selectors", "decls", "vars", "consts", "template", "HomeLayoutComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵproperty"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home-layout\\home-layout.component.ts", "D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home-layout\\home-layout.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, Renderer2 } from '@angular/core';\nimport { ActivatedRoute } from '@angular/router';\nimport { PrimeNGConfig } from 'primeng/api';\n\n@Component({\n  selector: 'app-home-layout',\n  templateUrl: './home-layout.component.html',\n  styleUrl: './home-layout.component.scss'\n})\nexport class HomeLayoutComponent implements OnInit, OnDestroy {\n\n  commonContent!: any;\n\n  constructor(\n    private primengConfig: PrimeNGConfig,\n    private renderer: Renderer2,\n    private route: ActivatedRoute\n  ) { }\n\n  ngOnInit(): void {\n    // Get common content from route data\n    this.commonContent = this.route.snapshot.data['commonContent'];\n    console.log('Common Content:', this.commonContent);\n\n    // Inject theme \n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\n    const link = this.renderer.createElement('link');\n    this.renderer.setAttribute(link, 'id', 'theme-link');\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\n    this.renderer.setAttribute(link, 'type', 'text/css');\n    this.renderer.setAttribute(link, 'href', href);\n\n    // Append the link tag to the head of the document\n    this.renderer.appendChild(document.head, link);\n\n    this.primengConfig.ripple = true; //enables core ripple functionality\n  }\n\n  ngOnDestroy(): void {\n    // Find and remove the link tag when the component is destroyed\n    const link = document.getElementById('theme-link');\n    if (link) {\n      link.remove();\n    }\n  }\n}\n", "<app-header [commonContent]=\"commonContent\"></app-header>\n\n<!-- Main content area where child routes will be rendered -->\n<main class=\"home-layout-content\">\n  <router-outlet></router-outlet>\n</main>\n\n<app-footer></app-footer>\n"], "mappings": ";;;;;AASA,OAAM,MAAOA,mBAAmB;EAI9BC,YACUC,aAA4B,EAC5BC,QAAmB,EACnBC,KAAqB;IAFrB,KAAAF,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,KAAK,GAALA,KAAK;EACX;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,aAAa,GAAG,IAAI,CAACF,KAAK,CAACG,QAAQ,CAACC,IAAI,CAAC,eAAe,CAAC;IAC9DC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACJ,aAAa,CAAC;IAElD;IACA,MAAMK,IAAI,GAAG,wDAAwD;IACrE,MAAMC,IAAI,GAAG,IAAI,CAACT,QAAQ,CAACU,aAAa,CAAC,MAAM,CAAC;IAChD,IAAI,CAACV,QAAQ,CAACW,YAAY,CAACF,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC;IACpD,IAAI,CAACT,QAAQ,CAACW,YAAY,CAACF,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC;IACrD,IAAI,CAACT,QAAQ,CAACW,YAAY,CAACF,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC;IACpD,IAAI,CAACT,QAAQ,CAACW,YAAY,CAACF,IAAI,EAAE,MAAM,EAAED,IAAI,CAAC;IAE9C;IACA,IAAI,CAACR,QAAQ,CAACY,WAAW,CAACC,QAAQ,CAACC,IAAI,EAAEL,IAAI,CAAC;IAE9C,IAAI,CAACV,aAAa,CAACgB,MAAM,GAAG,IAAI,CAAC,CAAC;EACpC;EAEAC,WAAWA,CAAA;IACT;IACA,MAAMP,IAAI,GAAGI,QAAQ,CAACI,cAAc,CAAC,YAAY,CAAC;IAClD,IAAIR,IAAI,EAAE;MACRA,IAAI,CAACS,MAAM,EAAE;IACf;EACF;;;uBAnCWrB,mBAAmB,EAAAsB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,SAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAnB5B,mBAAmB;MAAA6B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCThCb,EAAA,CAAAe,SAAA,oBAAyD;UAGzDf,EAAA,CAAAgB,cAAA,cAAkC;UAChChB,EAAA,CAAAe,SAAA,oBAA+B;UACjCf,EAAA,CAAAiB,YAAA,EAAO;UAEPjB,EAAA,CAAAe,SAAA,iBAAyB;;;UAPbf,EAAA,CAAAkB,UAAA,kBAAAJ,GAAA,CAAA9B,aAAA,CAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}