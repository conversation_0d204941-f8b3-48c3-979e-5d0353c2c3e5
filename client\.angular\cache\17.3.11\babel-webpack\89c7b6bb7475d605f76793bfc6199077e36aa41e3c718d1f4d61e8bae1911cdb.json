{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { HomeComponent } from './home.component';\nimport { HomeLayoutComponent } from './home-layout/home-layout.component';\nimport { AboutComponent } from './about/about.component';\nimport { contentResolver } from '../core/content-resolver';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: HomeLayoutComponent,\n  children: [{\n    path: '',\n    component: HomeComponent,\n    resolve: {\n      content: contentResolver\n    },\n    data: {\n      slug: 'home'\n    }\n  }, {\n    path: 'about',\n    component: AboutComponent,\n    resolve: {\n      content: contentResolver\n    },\n    data: {\n      slug: 'about'\n    }\n  }\n  // Add more child routes here for other lazy-loaded modules\n  ]\n}];\nexport class HomeRoutingModule {\n  static {\n    this.ɵfac = function HomeRoutingModule_Factory(t) {\n      return new (t || HomeRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: HomeRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(HomeRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "HomeComponent", "HomeLayoutComponent", "AboutComponent", "contentResolver", "routes", "path", "component", "children", "resolve", "content", "data", "slug", "HomeRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { HomeComponent } from './home.component';\r\nimport { HomeLayoutComponent } from './home-layout/home-layout.component';\r\nimport { AboutComponent } from './about/about.component';\r\nimport { contentResolver } from '../core/content-resolver';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: HomeLayoutComponent,\r\n    children: [\r\n      {\r\n        path: '',\r\n        component: HomeComponent,\r\n        resolve: {\r\n          content: contentResolver,\r\n        },\r\n        data: {\r\n          slug: 'home',\r\n        },\r\n      },\r\n      {\r\n        path: 'about',\r\n        component: AboutComponent,\r\n        resolve: {\r\n          content: contentResolver,\r\n        },\r\n        data: {\r\n          slug: 'about',\r\n        },\r\n      }\r\n      // Add more child routes here for other lazy-loaded modules\r\n    ]\r\n  }\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class HomeRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;;;AAE1D,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEL,mBAAmB;EAC9BM,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEN,aAAa;IACxBQ,OAAO,EAAE;MACPC,OAAO,EAAEN;KACV;IACDO,IAAI,EAAE;MACJC,IAAI,EAAE;;GAET,EACD;IACEN,IAAI,EAAE,OAAO;IACbC,SAAS,EAAEJ,cAAc;IACzBM,OAAO,EAAE;MACPC,OAAO,EAAEN;KACV;IACDO,IAAI,EAAE;MACJC,IAAI,EAAE;;;EAGV;EAAA;CAEH,CACF;AAMD,OAAM,MAAOC,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA;IAAiB;EAAA;;;gBAHlBb,YAAY,CAACc,QAAQ,CAACT,MAAM,CAAC,EAC7BL,YAAY;IAAA;EAAA;;;2EAEXa,iBAAiB;IAAAE,OAAA,GAAAC,EAAA,CAAAhB,YAAA;IAAAiB,OAAA,GAFlBjB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}