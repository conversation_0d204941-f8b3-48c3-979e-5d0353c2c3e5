{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./service/app.layout.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/core/services/content-vendor.service\";\nimport * as i4 from \"./app.menu.component\";\nconst _c0 = [\"menuContainer\"];\nconst _c1 = () => [\"/\"];\nexport class AppSidebarComponent {\n  constructor(layoutService, el, route, CMSservice) {\n    this.layoutService = layoutService;\n    this.el = el;\n    this.route = route;\n    this.CMSservice = CMSservice;\n    this.timeout = null;\n    this.logo = '';\n  }\n  ngOnInit() {\n    const commonContent = this.route.snapshot.data['commonContent'];\n    const logoComponent = this.CMSservice.getDataByComponentName(commonContent.body, \"crm.logo\");\n    if (logoComponent?.length) {\n      this.logo = logoComponent[0].Logo?.url || '';\n    }\n  }\n  onMouseEnter() {\n    if (!this.layoutService.state.anchored) {\n      if (this.timeout) {\n        clearTimeout(this.timeout);\n        this.timeout = null;\n      }\n      this.layoutService.state.sidebarActive = true;\n    }\n  }\n  onMouseLeave() {\n    if (!this.layoutService.state.anchored) {\n      if (!this.timeout) {\n        this.timeout = setTimeout(() => this.layoutService.state.sidebarActive = false, 300);\n      }\n    }\n  }\n  anchor() {\n    this.layoutService.state.anchored = !this.layoutService.state.anchored;\n  }\n  static {\n    this.ɵfac = function AppSidebarComponent_Factory(t) {\n      return new (t || AppSidebarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ContentService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppSidebarComponent,\n      selectors: [[\"app-sidebar\"]],\n      viewQuery: function AppSidebarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuContainer = _t.first);\n        }\n      },\n      decls: 11,\n      vars: 4,\n      consts: [[\"menuContainer\", \"\"], [1, \"layout-sidebar\", 3, \"mouseenter\", \"mouseleave\"], [1, \"sidebar-header\"], [1, \"app-logo\", 3, \"routerLink\"], [1, \"app-logo-small\", \"h-2rem\"], [3, \"src\"], [1, \"app-logo-normal\"], [1, \"h-2rem\", 3, \"src\"], [\"type\", \"button\", 1, \"layout-sidebar-anchor\", \"p-link\", \"z-2\", 3, \"click\"], [1, \"layout-menu-container\"]],\n      template: function AppSidebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵlistener(\"mouseenter\", function AppSidebarComponent_Template_div_mouseenter_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMouseEnter());\n          })(\"mouseleave\", function AppSidebarComponent_Template_div_mouseleave_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onMouseLeave());\n          });\n          i0.ɵɵelementStart(1, \"div\", 2)(2, \"a\", 3)(3, \"div\", 4);\n          i0.ɵɵelement(4, \"img\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 6);\n          i0.ɵɵelement(6, \"img\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function AppSidebarComponent_Template_button_click_7_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.anchor());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 9, 0);\n          i0.ɵɵelement(10, \"app-menu\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(3, _c1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"src\", ctx.logo, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"src\", ctx.logo, i0.ɵɵsanitizeUrl);\n        }\n      },\n      dependencies: [i2.RouterLink, i4.AppMenuComponent],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["AppSidebarComponent", "constructor", "layoutService", "el", "route", "CMSservice", "timeout", "logo", "ngOnInit", "commonContent", "snapshot", "data", "logoComponent", "getDataByComponentName", "body", "length", "Logo", "url", "onMouseEnter", "state", "anchored", "clearTimeout", "sidebarActive", "onMouseLeave", "setTimeout", "anchor", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "ElementRef", "i2", "ActivatedRoute", "i3", "ContentService", "selectors", "viewQuery", "AppSidebarComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "AppSidebarComponent_Template_div_mouseenter_0_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "AppSidebarComponent_Template_div_mouseleave_0_listener", "ɵɵelement", "ɵɵelementEnd", "AppSidebarComponent_Template_button_click_7_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c1", "ɵɵsanitizeUrl"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\backoffice\\layout\\app.sidebar.component.ts", "D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\backoffice\\layout\\app.sidebar.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { LayoutService } from './service/app.layout.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { ContentService } from 'src/app/core/services/content-vendor.service';\r\n\r\n@Component({\r\n    selector: 'app-sidebar',\r\n    templateUrl: './app.sidebar.component.html'\r\n})\r\nexport class AppSidebarComponent implements OnInit {\r\n    timeout: any = null;\r\n    public logo = '';\r\n\r\n    @ViewChild('menuContainer') menuContainer!: ElementRef;\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        public el: ElementRef,\r\n        private route: ActivatedRoute,\r\n        private CMSservice: ContentService\r\n    ) { }\r\n\r\n    ngOnInit(): void {\r\n        const commonContent = this.route.snapshot.data['commonContent'];\r\n        const logoComponent = this.CMSservice.getDataByComponentName(commonContent.body, \"crm.logo\");\r\n        if (logoComponent?.length) {\r\n            this.logo = logoComponent[0].Logo?.url || '';\r\n        }\r\n    }\r\n\r\n    onMouseEnter() {\r\n        if (!this.layoutService.state.anchored) {\r\n            if (this.timeout) {\r\n                clearTimeout(this.timeout);\r\n                this.timeout = null;\r\n            }\r\n            this.layoutService.state.sidebarActive = true;\r\n\r\n\r\n        }\r\n    }\r\n\r\n    onMouseLeave() {\r\n        if (!this.layoutService.state.anchored) {\r\n            if (!this.timeout) {\r\n                this.timeout = setTimeout(() => this.layoutService.state.sidebarActive = false, 300);\r\n            }\r\n        }\r\n    }\r\n\r\n    anchor() {\r\n        this.layoutService.state.anchored = !this.layoutService.state.anchored;\r\n    }\r\n\r\n}\r\n", "<div class=\"layout-sidebar\" (mouseenter)=\"onMouseEnter()\" (mouseleave)=\"onMouseLeave()\">\r\n    <div class=\"sidebar-header\">\r\n        <a [routerLink]=\"['/']\" class=\"app-logo\">\r\n            <div class=\"app-logo-small h-2rem\" >\r\n                <img  [src]=\"logo\">\r\n            </div>\r\n            <div class=\"app-logo-normal \">\r\n                <img class=\"h-2rem\" [src]=\"logo\">\r\n            </div>\r\n        </a>\r\n        <button class=\"layout-sidebar-anchor p-link z-2 \" type=\"button\" (click)=\"anchor()\"></button>\r\n    </div>\r\n \r\n\r\n    <div #menuContainer class=\"layout-menu-container\">\r\n        <app-menu></app-menu>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;AASA,OAAM,MAAOA,mBAAmB;EAK5BC,YACWC,aAA4B,EAC5BC,EAAc,EACbC,KAAqB,EACrBC,UAA0B;IAH3B,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,EAAE,GAAFA,EAAE;IACD,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IARtB,KAAAC,OAAO,GAAQ,IAAI;IACZ,KAAAC,IAAI,GAAG,EAAE;EAQZ;EAEJC,QAAQA,CAAA;IACJ,MAAMC,aAAa,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,IAAI,CAAC,eAAe,CAAC;IAC/D,MAAMC,aAAa,GAAG,IAAI,CAACP,UAAU,CAACQ,sBAAsB,CAACJ,aAAa,CAACK,IAAI,EAAE,UAAU,CAAC;IAC5F,IAAIF,aAAa,EAAEG,MAAM,EAAE;MACvB,IAAI,CAACR,IAAI,GAAGK,aAAa,CAAC,CAAC,CAAC,CAACI,IAAI,EAAEC,GAAG,IAAI,EAAE;IAChD;EACJ;EAEAC,YAAYA,CAAA;IACR,IAAI,CAAC,IAAI,CAAChB,aAAa,CAACiB,KAAK,CAACC,QAAQ,EAAE;MACpC,IAAI,IAAI,CAACd,OAAO,EAAE;QACde,YAAY,CAAC,IAAI,CAACf,OAAO,CAAC;QAC1B,IAAI,CAACA,OAAO,GAAG,IAAI;MACvB;MACA,IAAI,CAACJ,aAAa,CAACiB,KAAK,CAACG,aAAa,GAAG,IAAI;IAGjD;EACJ;EAEAC,YAAYA,CAAA;IACR,IAAI,CAAC,IAAI,CAACrB,aAAa,CAACiB,KAAK,CAACC,QAAQ,EAAE;MACpC,IAAI,CAAC,IAAI,CAACd,OAAO,EAAE;QACf,IAAI,CAACA,OAAO,GAAGkB,UAAU,CAAC,MAAM,IAAI,CAACtB,aAAa,CAACiB,KAAK,CAACG,aAAa,GAAG,KAAK,EAAE,GAAG,CAAC;MACxF;IACJ;EACJ;EAEAG,MAAMA,CAAA;IACF,IAAI,CAACvB,aAAa,CAACiB,KAAK,CAACC,QAAQ,GAAG,CAAC,IAAI,CAAClB,aAAa,CAACiB,KAAK,CAACC,QAAQ;EAC1E;;;uBA1CSpB,mBAAmB,EAAA0B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,UAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAnBlC,mBAAmB;MAAAmC,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCThCZ,EAAA,CAAAc,cAAA,aAAwF;UAA9Bd,EAA9B,CAAAe,UAAA,wBAAAC,uDAAA;YAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;YAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAcN,GAAA,CAAArB,YAAA,EAAc;UAAA,EAAC,wBAAA4B,uDAAA;YAAApB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;YAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAeN,GAAA,CAAAhB,YAAA,EAAc;UAAA,EAAC;UAG3EG,EAFR,CAAAc,cAAA,aAA4B,WACiB,aACD;UAChCd,EAAA,CAAAqB,SAAA,aAAmB;UACvBrB,EAAA,CAAAsB,YAAA,EAAM;UACNtB,EAAA,CAAAc,cAAA,aAA8B;UAC1Bd,EAAA,CAAAqB,SAAA,aAAiC;UAEzCrB,EADI,CAAAsB,YAAA,EAAM,EACN;UACJtB,EAAA,CAAAc,cAAA,gBAAmF;UAAnBd,EAAA,CAAAe,UAAA,mBAAAQ,qDAAA;YAAAvB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;YAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASN,GAAA,CAAAd,MAAA,EAAQ;UAAA,EAAC;UACtFC,EADuF,CAAAsB,YAAA,EAAS,EAC1F;UAGNtB,EAAA,CAAAc,cAAA,gBAAkD;UAC9Cd,EAAA,CAAAqB,SAAA,gBAAqB;UAE7BrB,EADI,CAAAsB,YAAA,EAAM,EACJ;;;UAfKtB,EAAA,CAAAwB,SAAA,GAAoB;UAApBxB,EAAA,CAAAyB,UAAA,eAAAzB,EAAA,CAAA0B,eAAA,IAAAC,GAAA,EAAoB;UAET3B,EAAA,CAAAwB,SAAA,GAAY;UAAZxB,EAAA,CAAAyB,UAAA,QAAAZ,GAAA,CAAAhC,IAAA,EAAAmB,EAAA,CAAA4B,aAAA,CAAY;UAGE5B,EAAA,CAAAwB,SAAA,GAAY;UAAZxB,EAAA,CAAAyB,UAAA,QAAAZ,GAAA,CAAAhC,IAAA,EAAAmB,EAAA,CAAA4B,aAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}