{"ast": null, "code": "import { HttpHeaders, HttpParams } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class CustomerService {\n  constructor(http) {\n    this.http = http;\n    this.apiUrl = 'https://displaymaxsnjyadev.cfapps.us10-001.hana.ondemand.com/api';\n    this.headers = new HttpHeaders({\n      Authorization: 'bearer 81fee757cde39bf0217839d6dbbfea69cc1fc65a899a51b0bbf86741b9d28ab48b6aaf190edcb03f51f95ae15aeb5147b517301ab736ccd46e9437e646051f3e125446640c210ed0bc8d7fc64ca0a39badd3c9aca86fb24ac1c8bdacaaa0641c38e5d835970bf33971327fbc50602d6753ce1dcd424e6a44b55b882cc8c34695' // Replace with your actual token\n    });\n  }\n  getCustomers(page, pageSize, sortField, sortOrder, searchTerm) {\n    let params = new HttpParams().set('pagination[page]', page.toString()).set('pagination[pageSize]', pageSize.toString());\n    if (sortField && sortOrder !== undefined) {\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\n      params = params.set('sort', `${sortField}:${order}`);\n    }\n    if (searchTerm) {\n      params = params.set('filters[$or][0][bp_id][$containsi]', searchTerm).set('filters[$or][1][bp_full_name][$containsi]', searchTerm).set('filters[$or][2][email][$containsi]', searchTerm).set('filters[$or][3][phone][$containsi]', searchTerm);\n    }\n    return this.http.get(`${this.apiUrl}/business-partners`, {\n      headers: this.headers,\n      params\n    });\n  }\n  getCustomerByID(custid) {\n    return this.http.get(`${this.apiUrl}/customers?filters[bp_id][$eq]=${custid}&populate=*`, {\n      headers: this.headers\n    });\n  }\n  getCustomerByIDName(customerdata) {\n    let params = new HttpParams();\n    if (customerdata) {\n      params = params.set('filters[$or][0][customer_id][$containsi]', customerdata).set('filters[$or][1][customer_name][$containsi]', customerdata);\n    }\n    return this.http.get(`${this.apiUrl}/customers`, {\n      headers: this.headers,\n      params\n    });\n  }\n  static {\n    this.ɵfac = function CustomerService_Factory(t) {\n      return new (t || CustomerService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CustomerService,\n      factory: CustomerService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "HttpParams", "CustomerService", "constructor", "http", "apiUrl", "headers", "Authorization", "getCustomers", "page", "pageSize", "sortField", "sortOrder", "searchTerm", "params", "set", "toString", "undefined", "order", "get", "getCustomerByID", "custid", "getCustomerByIDName", "customerdata", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\backoffice\\customer\\customer.service.ts"], "sourcesContent": ["import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\n\r\n@Injectable({\r\n  providedIn: 'root',\r\n})\r\nexport class CustomerService {\r\n  private apiUrl =\r\n    'https://displaymaxsnjyadev.cfapps.us10-001.hana.ondemand.com/api';\r\n  private headers = new HttpHeaders({\r\n    Authorization:\r\n      'bearer 81fee757cde39bf0217839d6dbbfea69cc1fc65a899a51b0bbf86741b9d28ab48b6aaf190edcb03f51f95ae15aeb5147b517301ab736ccd46e9437e646051f3e125446640c210ed0bc8d7fc64ca0a39badd3c9aca86fb24ac1c8bdacaaa0641c38e5d835970bf33971327fbc50602d6753ce1dcd424e6a44b55b882cc8c34695', // Replace with your actual token\r\n  });\r\n\r\n  constructor(private http: HttpClient) {}\r\n\r\n  getCustomers(\r\n    page: number,\r\n    pageSize: number,\r\n    sortField?: string,\r\n    sortOrder?: number,\r\n    searchTerm?: string\r\n  ): Observable<any[]> {\r\n    let params = new HttpParams()\r\n      .set('pagination[page]', page.toString())\r\n      .set('pagination[pageSize]', pageSize.toString());\r\n    if (sortField && sortOrder !== undefined) {\r\n      const order = sortOrder === 1 ? 'asc' : 'desc'; // 1: asc, -1: desc\r\n      params = params.set('sort', `${sortField}:${order}`);\r\n    }\r\n    if (searchTerm) {\r\n      params = params\r\n        .set('filters[$or][0][bp_id][$containsi]', searchTerm)\r\n        .set('filters[$or][1][bp_full_name][$containsi]', searchTerm)\r\n        .set('filters[$or][2][email][$containsi]', searchTerm)\r\n        .set('filters[$or][3][phone][$containsi]', searchTerm);\r\n    }\r\n    return this.http.get<any[]>(`${this.apiUrl}/business-partners`, {\r\n      headers: this.headers,\r\n      params,\r\n    });\r\n  }\r\n  getCustomerByID(custid: string) {\r\n    return this.http.get<any[]>(\r\n      `${this.apiUrl}/customers?filters[bp_id][$eq]=${custid}&populate=*`,\r\n      { headers: this.headers }\r\n    );\r\n  }\r\n\r\n  getCustomerByIDName(customerdata: string): Observable<any[]> {\r\n    let params = new HttpParams();\r\n    if (customerdata) {\r\n      params = params\r\n        .set('filters[$or][0][customer_id][$containsi]', customerdata)\r\n        .set('filters[$or][1][customer_name][$containsi]', customerdata);\r\n    }\r\n\r\n    return this.http.get<any[]>(`${this.apiUrl}/customers`, {\r\n      headers: this.headers,\r\n      params,\r\n    });\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAqBA,WAAW,EAAEC,UAAU,QAAQ,sBAAsB;;;AAO1E,OAAM,MAAOC,eAAe;EAQ1BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAPhB,KAAAC,MAAM,GACZ,kEAAkE;IAC5D,KAAAC,OAAO,GAAG,IAAIN,WAAW,CAAC;MAChCO,aAAa,EACX,yQAAyQ,CAAE;KAC9Q,CAAC;EAEqC;EAEvCC,YAAYA,CACVC,IAAY,EACZC,QAAgB,EAChBC,SAAkB,EAClBC,SAAkB,EAClBC,UAAmB;IAEnB,IAAIC,MAAM,GAAG,IAAIb,UAAU,EAAE,CAC1Bc,GAAG,CAAC,kBAAkB,EAAEN,IAAI,CAACO,QAAQ,EAAE,CAAC,CACxCD,GAAG,CAAC,sBAAsB,EAAEL,QAAQ,CAACM,QAAQ,EAAE,CAAC;IACnD,IAAIL,SAAS,IAAIC,SAAS,KAAKK,SAAS,EAAE;MACxC,MAAMC,KAAK,GAAGN,SAAS,KAAK,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC;MAChDE,MAAM,GAAGA,MAAM,CAACC,GAAG,CAAC,MAAM,EAAE,GAAGJ,SAAS,IAAIO,KAAK,EAAE,CAAC;IACtD;IACA,IAAIL,UAAU,EAAE;MACdC,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC,CACrDE,GAAG,CAAC,2CAA2C,EAAEF,UAAU,CAAC,CAC5DE,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC,CACrDE,GAAG,CAAC,oCAAoC,EAAEF,UAAU,CAAC;IAC1D;IACA,OAAO,IAAI,CAACT,IAAI,CAACe,GAAG,CAAQ,GAAG,IAAI,CAACd,MAAM,oBAAoB,EAAE;MAC9DC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBQ;KACD,CAAC;EACJ;EACAM,eAAeA,CAACC,MAAc;IAC5B,OAAO,IAAI,CAACjB,IAAI,CAACe,GAAG,CAClB,GAAG,IAAI,CAACd,MAAM,kCAAkCgB,MAAM,aAAa,EACnE;MAAEf,OAAO,EAAE,IAAI,CAACA;IAAO,CAAE,CAC1B;EACH;EAEAgB,mBAAmBA,CAACC,YAAoB;IACtC,IAAIT,MAAM,GAAG,IAAIb,UAAU,EAAE;IAC7B,IAAIsB,YAAY,EAAE;MAChBT,MAAM,GAAGA,MAAM,CACZC,GAAG,CAAC,0CAA0C,EAAEQ,YAAY,CAAC,CAC7DR,GAAG,CAAC,4CAA4C,EAAEQ,YAAY,CAAC;IACpE;IAEA,OAAO,IAAI,CAACnB,IAAI,CAACe,GAAG,CAAQ,GAAG,IAAI,CAACd,MAAM,YAAY,EAAE;MACtDC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBQ;KACD,CAAC;EACJ;;;uBAvDWZ,eAAe,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAfzB,eAAe;MAAA0B,OAAA,EAAf1B,eAAe,CAAA2B,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}