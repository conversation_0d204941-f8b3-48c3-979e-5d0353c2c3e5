<section class="login-sec bg-white h-screen pt-6 pb-6">
  <div class="login-page-body flex flex-column justify-content-between p-20 gap-8 m-auto h-full px-5">
    <div class="login-header relative flex align-items-center justify-content-between">
      <div class="logo w-full max-w-15rem"><a href="#" class="flex w-full"><img src="/assets/layout/images/chs-logo.svg"
            alt="Logo" class="w-full" /></a></div>
      <div class="sign-up flex align-items-center justify-content-center text-primary gap-3 text-base font-medium">
        Do you have an account?
        <button type="button" [routerLink]="['/login']"
          class="sign-up-btn p-element p-ripple p-button-outlined p-button-rounded p-button p-component w-8rem justify-content-center gap-1">
          <span class="material-symbols-rounded text-2xl">input</span> Login
        </button>
      </div>
    </div>
    <div class="login-form mx-auto p-5 max-w-30rem w-full bg-white border-round-3xl shadow-2">
      <form class="flex flex-column position-relative">
        <h1 class="mb-2 flex justify-content-center text-4xl font-bold text-primary">Registration
        </h1>
        <p class="mb-5 flex justify-content-center text-base font-medium text-gray-900">Enter your details below to create
          an account and get started.</p>
        <div class="p-fluid p-formgrid grid">
          <div class="field col-12 md:col-6 mb-0">
            <label class="text-base font-medium text-gray-600">First Name</label>
            <input type="text" class="p-inputtext p-component p-element w-full bg-gray-50" id="username" />
          </div>
          <div class="field col-12 md:col-6 mb-0">
            <label class="text-base font-medium text-gray-600">Last Name</label>
            <input type="text" class="p-inputtext p-component p-element w-full bg-gray-50" id="username" />
          </div>
          <div class="field col-12 md:col-6 mb-0">
            <label class="text-base font-medium text-gray-600">Phone Number</label>
            <input type="text" class="p-inputtext p-component p-element w-full bg-gray-50" id="username" />
          </div>
          <div class="field col-12 md:col-6 mb-0">
            <label class="text-base font-medium text-gray-600">Extension</label>
            <input type="text" class="p-inputtext p-component p-element w-full bg-gray-50" id="username" />
          </div>
          <div class="field col-12 md:col-12 mb-0">
            <label class="text-base font-medium text-gray-600">E-mail</label>
            <input type="email" class="p-inputtext p-component p-element w-full bg-gray-50" id="username" />
          </div>
          <div class="field col-12 md:col-12 mb-0">
            <label class="text-base font-medium text-gray-600">Address</label>
            <input type="text" class="p-inputtext p-component p-element w-full bg-gray-50" id="username" />
          </div>
          <div class="field col-12 md:col-6 mb-0">
            <label class="text-base font-medium text-gray-600">Country</label>
            <input type="text" class="p-inputtext p-component p-element w-full bg-gray-50" id="username" />
          </div>
          <div class="field col-12 md:col-6 mb-0">
            <label class="text-base font-medium text-gray-600">State</label>
            <input type="text" class="p-inputtext p-component p-element w-full bg-gray-50" id="username" />
          </div>
          <div class="field col-12 md:col-6 mb-0">
            <label class="text-base font-medium text-gray-600">City</label>
            <input type="text" class="p-inputtext p-component p-element w-full bg-gray-50" id="username" />
          </div>
          <div class="field col-12 md:col-6 mb-0">
            <label class="text-base font-medium text-gray-600">Zip Code</label>
            <input type="text" class="p-inputtext p-component p-element w-full bg-gray-50" id="username" />
          </div>
          <div class="field col-12 md:col-6 mb-0">
            <label class="text-base font-medium text-gray-600">Password</label>
            <div class="form-group relative">
              <input type="password" class="p-inputtext p-component p-element w-full bg-gray-50" />
              <button type="button"
                class="pass-show-btn absolute top-0 bottom-0 m-auto p-0 border-none bg-white-alpha-10 text-gray-500 cursor-pointer"><span
                  class="material-symbols-rounded">visibility</span></button>
            </div>
          </div>
          <div class="field col-12 md:col-6 mb-0">
            <label class="text-base font-medium text-gray-600">Retype Password</label>
            <div class="form-group relative">
              <input type="password" class="p-inputtext p-component p-element w-full bg-gray-50" />
              <button type="button"
                class="pass-show-btn absolute top-0 bottom-0 m-auto p-0 border-none bg-white-alpha-10 text-gray-500 cursor-pointer"><span
                  class="material-symbols-rounded">visibility</span></button>
            </div>
          </div>
          <div class="field col-12 md:col-12 mb-0">
            <label class="text-base font-medium text-gray-600">Security Question 1</label>
            <p-dropdown [options]="cities" [(ngModel)]="selectedCity" optionLabel="name" [showClear]="true" [styleClass]="'p-inputtext p-component p-element w-full bg-gray-50'"></p-dropdown>
          </div>
          <div class="field col-12 md:col-12 mb-0">
            <label class="text-base font-medium text-gray-600">Answer</label>
            <input type="text" class="p-inputtext p-component p-element w-full bg-gray-50" id="username" />
          </div>
          <div class="field col-12 md:col-12 mb-0">
            <label class="text-base font-medium text-gray-600">Security Question 2</label>
            <p-dropdown [options]="cities" [(ngModel)]="selectedCity" optionLabel="name" [showClear]="true" [styleClass]="'p-inputtext p-component p-element w-full bg-gray-50'"></p-dropdown>
          </div>
          <div class="field col-12 md:col-12 mb-0">
            <label class="text-base font-medium text-gray-600">Answer</label>
            <input type="text" class="p-inputtext p-component p-element w-full bg-gray-50" id="username" />
          </div>
        </div>
        <div class="form-footer mt-4">
          <div class="p-fluid p-formgrid grid">
            <div class="field col-12 md:col-6 mb-0">
              <button type="button" [routerLink]="['/login']"
                class="p-button-outlined p-button-rounded p-button p-component w-full h-3-3rem justify-content-center gap-3 font-semibold border-2 border-black-alpha-20">
                Go Back</button>
            </div>
            <div class="field col-12 md:col-6 mb-0">
              <button type="button"
                class="p-button-rounded p-button p-component w-full justify-content-center h-3-3rem font-semibold">
                Login</button>
            </div>
          </div>
        </div>
      </form>
    </div>
    <div class="copyright-sec flex flex-column position-relative">
      <p class="m-0 flex justify-content-center text-base font-medium text-gray-900">© 2024 Consolidated Hospitality
        Supplies</p>
      <ul class="p-0 flex position-relative align-items-center justify-content-center list-none gap-3">
        <li><a target="_blank" class="flex justify-content-center text-base font-medium text-primary underline">Terms &
            Conditions</a></li>
        <li><a target="_blank" class="flex justify-content-center text-base font-medium text-primary underline">Privacy
            Policy</a></li>
      </ul>
    </div>
  </div>
</section>