import { Component } from '@angular/core';
import { Router } from "@angular/router";

interface City {
  name: string,
  code: string
}
@Component({
  selector: 'app-signup',
  templateUrl: './signup.component.html',
  styleUrl: './signup.component.scss'
})
export class SignupComponent {

  cities: City[];
  selectedCity: City | any;

  constructor(
    public routter: Router,
  ) {
    this.cities = [
      { name: "What's was your first car?", code: "NY" },
      { name: "What was your favorite school teacher's name?", code: "RM" },
      { name: 'What is your date of birth?', code: 'LDN' },
      { name: 'What’s your favorite movie?', code: 'IST' },
      { name: 'What is your astrological sign?', code: 'PRS' }
    ];
  }

}
