import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
    name: 'initials'
})
export class InitialsPipe implements PipeTransform {

    transform(value: string): string {
        const words = value?.trim().split(' ');
        const firstInitial = words[0]?.charAt(0).toUpperCase() || '';
        const secondInitial = words[1]?.charAt(0).toUpperCase() || '';
        return firstInitial + secondInitial;
    }
    
}