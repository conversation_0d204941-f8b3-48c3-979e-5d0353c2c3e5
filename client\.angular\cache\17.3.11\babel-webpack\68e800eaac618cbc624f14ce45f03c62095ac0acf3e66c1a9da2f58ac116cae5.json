{"ast": null, "code": "import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function buffer(closingNotifier) {\n  return operate((source, subscriber) => {\n    let currentBuffer = [];\n    source.subscribe(createOperatorSubscriber(subscriber, value => currentBuffer.push(value), () => {\n      subscriber.next(currentBuffer);\n      subscriber.complete();\n    }));\n    innerFrom(closingNotifier).subscribe(createOperatorSubscriber(subscriber, () => {\n      const b = currentBuffer;\n      currentBuffer = [];\n      subscriber.next(b);\n    }, noop));\n    return () => {\n      currentBuffer = null;\n    };\n  });\n}", "map": {"version": 3, "names": ["operate", "noop", "createOperatorSubscriber", "innerFrom", "buffer", "closingNotifier", "source", "subscriber", "current<PERSON><PERSON><PERSON>", "subscribe", "value", "push", "next", "complete", "b"], "sources": ["D:/Code/ASAR/Azure/Public Service/SNJYA-PUBLIC-SERVICE/client/node_modules/rxjs/dist/esm/internal/operators/buffer.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function buffer(closingNotifier) {\n    return operate((source, subscriber) => {\n        let currentBuffer = [];\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => currentBuffer.push(value), () => {\n            subscriber.next(currentBuffer);\n            subscriber.complete();\n        }));\n        innerFrom(closingNotifier).subscribe(createOperatorSubscriber(subscriber, () => {\n            const b = currentBuffer;\n            currentBuffer = [];\n            subscriber.next(b);\n        }, noop));\n        return () => {\n            currentBuffer = null;\n        };\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAO,SAASC,MAAMA,CAACC,eAAe,EAAE;EACpC,OAAOL,OAAO,CAAC,CAACM,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,aAAa,GAAG,EAAE;IACtBF,MAAM,CAACG,SAAS,CAACP,wBAAwB,CAACK,UAAU,EAAGG,KAAK,IAAKF,aAAa,CAACG,IAAI,CAACD,KAAK,CAAC,EAAE,MAAM;MAC9FH,UAAU,CAACK,IAAI,CAACJ,aAAa,CAAC;MAC9BD,UAAU,CAACM,QAAQ,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;IACHV,SAAS,CAACE,eAAe,CAAC,CAACI,SAAS,CAACP,wBAAwB,CAACK,UAAU,EAAE,MAAM;MAC5E,MAAMO,CAAC,GAAGN,aAAa;MACvBA,aAAa,GAAG,EAAE;MAClBD,UAAU,CAACK,IAAI,CAACE,CAAC,CAAC;IACtB,CAAC,EAAEb,IAAI,CAAC,CAAC;IACT,OAAO,MAAM;MACTO,aAAa,GAAG,IAAI;IACxB,CAAC;EACL,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}