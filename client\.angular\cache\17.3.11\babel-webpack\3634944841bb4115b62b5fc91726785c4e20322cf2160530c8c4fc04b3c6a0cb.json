{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../core/services/content-vendor.service\";\nimport * as i3 from \"@angular/common\";\nfunction AboutComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r0.content.description || \"About content will be loaded here\", i0.ɵɵsanitizeHtml);\n  }\n}\nfunction AboutComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"p\", 7);\n    i0.ɵɵtext(2, \"This is the about page. Content will be loaded from the CMS based on the 'about' slug.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AboutComponent {\n  constructor(route, CMSservice) {\n    this.route = route;\n    this.CMSservice = CMSservice;\n  }\n  ngOnInit() {\n    this.content = this.route.snapshot.data['content'];\n    console.log('About Content:', this.content);\n    // Process content specific to about page\n    // Extract any specific components needed for about page\n  }\n  static {\n    this.ɵfac = function AboutComponent_Factory(t) {\n      return new (t || AboutComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ContentService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AboutComponent,\n      selectors: [[\"app-about\"]],\n      decls: 6,\n      vars: 2,\n      consts: [[1, \"about-page-sec\", \"relative\"], [1, \"about-page-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\"], [1, \"text-6xl\", \"font-bold\", \"mb-6\"], [3, \"innerHTML\", 4, \"ngIf\"], [\"class\", \"placeholder-content\", 4, \"ngIf\"], [3, \"innerHTML\"], [1, \"placeholder-content\"], [1, \"text-lg\"]],\n      template: function AboutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵtext(3, \"About Us\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, AboutComponent_div_4_Template, 1, 1, \"div\", 3)(5, AboutComponent_div_5_Template, 3, 0, \"div\", 4);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.content);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.content);\n        }\n      },\n      dependencies: [i3.NgIf],\n      styles: [\".about-page-sec[_ngcontent-%COMP%] {\\n  padding: 2rem 0;\\n}\\n.about-page-sec[_ngcontent-%COMP%]   .about-page-body[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  padding: 2rem;\\n  border-radius: 8px;\\n  border-left: 4px solid #007bff;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvaG9tZS9hYm91dC9hYm91dC5jb21wb25lbnQuc2NzcyIsIndlYnBhY2s6Ly8uLy4uLy4uLy4uL1B1YmxpYyUyMFNlcnZpY2UvU05KWUEtUFVCTElDLVNFUlZJQ0UvY2xpZW50L3NyYy9hcHAvaG9tZS9hYm91dC9hYm91dC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGVBQUE7QUNDRjtBREVJO0VBQ0UsbUJBQUE7RUFDQSxhQUFBO0VBQ0Esa0JBQUE7RUFDQSw4QkFBQTtBQ0FOIiwic291cmNlc0NvbnRlbnQiOlsiLmFib3V0LXBhZ2Utc2VjIHtcbiAgcGFkZGluZzogMnJlbSAwO1xuICBcbiAgLmFib3V0LXBhZ2UtYm9keSB7XG4gICAgLnBsYWNlaG9sZGVyLWNvbnRlbnQge1xuICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTtcbiAgICAgIHBhZGRpbmc6IDJyZW07XG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICBib3JkZXItbGVmdDogNHB4IHNvbGlkICMwMDdiZmY7XG4gICAgfVxuICB9XG59XG4iLCIuYWJvdXQtcGFnZS1zZWMge1xuICBwYWRkaW5nOiAycmVtIDA7XG59XG4uYWJvdXQtcGFnZS1zZWMgLmFib3V0LXBhZ2UtYm9keSAucGxhY2Vob2xkZXItY29udGVudCB7XG4gIGJhY2tncm91bmQ6ICNmOGY5ZmE7XG4gIHBhZGRpbmc6IDJyZW07XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgYm9yZGVyLWxlZnQ6IDRweCBzb2xpZCAjMDA3YmZmO1xufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "content", "description", "ɵɵsanitizeHtml", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "AboutComponent", "constructor", "route", "CMSservice", "ngOnInit", "snapshot", "data", "console", "log", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "ContentService", "selectors", "decls", "vars", "consts", "template", "AboutComponent_Template", "rf", "ctx", "ɵɵtemplate", "AboutComponent_div_4_Template", "AboutComponent_div_5_Template", "ɵɵadvance"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\about\\about.component.ts", "D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\about\\about.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { ActivatedRoute } from '@angular/router';\nimport { ContentService } from '../../core/services/content-vendor.service';\n\n@Component({\n  selector: 'app-about',\n  templateUrl: './about.component.html',\n  styleUrl: './about.component.scss'\n})\nexport class AboutComponent {\n\n  content!: any;\n\n  constructor(\n    private route: ActivatedRoute,\n    private CMSservice: ContentService\n  ) { }\n\n  ngOnInit(): void {\n    this.content = this.route.snapshot.data['content'];\n    console.log('About Content:', this.content);\n\n    // Process content specific to about page\n    // Extract any specific components needed for about page\n  }\n}\n", "<!-- About page content -->\n<section class=\"about-page-sec relative\">\n    <div class=\"about-page-body relative max-w-1200 w-full mx-auto px-4\">\n        <h1 class=\"text-6xl font-bold mb-6\">About Us</h1>\n        \n        <!-- Content will be dynamically loaded from CMS -->\n        <div *ngIf=\"content\" [innerHTML]=\"content.description || 'About content will be loaded here'\"></div>\n        \n        <!-- Placeholder content if no CMS content -->\n        <div *ngIf=\"!content\" class=\"placeholder-content\">\n            <p class=\"text-lg\">This is the about page. Content will be loaded from the CMS based on the 'about' slug.</p>\n        </div>\n    </div>\n</section>\n"], "mappings": ";;;;;;ICMQA,EAAA,CAAAC,SAAA,aAAoG;;;;IAA/ED,EAAA,CAAAE,UAAA,cAAAC,MAAA,CAAAC,OAAA,CAAAC,WAAA,yCAAAL,EAAA,CAAAM,cAAA,CAAwE;;;;;IAIzFN,EADJ,CAAAO,cAAA,aAAkD,WAC3B;IAAAP,EAAA,CAAAQ,MAAA,6FAAsF;IAC7GR,EAD6G,CAAAS,YAAA,EAAI,EAC3G;;;ADFd,OAAM,MAAOC,cAAc;EAIzBC,YACUC,KAAqB,EACrBC,UAA0B;IAD1B,KAAAD,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;EAChB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACV,OAAO,GAAG,IAAI,CAACQ,KAAK,CAACG,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;IAClDC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACd,OAAO,CAAC;IAE3C;IACA;EACF;;;uBAfWM,cAAc,EAAAV,EAAA,CAAAmB,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAArB,EAAA,CAAAmB,iBAAA,CAAAG,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAdb,cAAc;MAAAc,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCNnB9B,EAFR,CAAAO,cAAA,iBAAyC,aACgC,YAC7B;UAAAP,EAAA,CAAAQ,MAAA,eAAQ;UAAAR,EAAA,CAAAS,YAAA,EAAK;UAMjDT,EAHA,CAAAgC,UAAA,IAAAC,6BAAA,iBAA8F,IAAAC,6BAAA,iBAG5C;UAI1DlC,EADI,CAAAS,YAAA,EAAM,EACA;;;UAPIT,EAAA,CAAAmC,SAAA,GAAa;UAAbnC,EAAA,CAAAE,UAAA,SAAA6B,GAAA,CAAA3B,OAAA,CAAa;UAGbJ,EAAA,CAAAmC,SAAA,EAAc;UAAdnC,EAAA,CAAAE,UAAA,UAAA6B,GAAA,CAAA3B,OAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}