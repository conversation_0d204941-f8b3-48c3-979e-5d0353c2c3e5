{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../core/services/content-vendor.service\";\nimport * as i4 from \"primeng/inputtext\";\nexport class HomeComponent {\n  constructor(primengConfig, renderer, route, CMSservice) {\n    this.primengConfig = primengConfig;\n    this.renderer = renderer;\n    this.route = route;\n    this.CMSservice = CMSservice;\n    this.logo = '';\n    this.menuItems = [];\n  }\n  ngOnInit() {\n    this.commonContent = this.route.snapshot.data['commonContent'];\n    console.log('Common Content:', this.commonContent);\n    // Extract logo\n    const logoComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.logo\");\n    if (logoComponent?.length) {\n      this.logo = logoComponent[0].Logo?.url || '';\n    }\n    // Extract menu\n    const menuComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.menu\");\n    if (menuComponent?.length) {\n      this.menuItems = menuComponent[0].Menu_Item || [];\n    }\n    this.content = this.route.snapshot.data['content'];\n    console.log('Home Content:', this.content);\n    console.log('Logo:', this.logo);\n    console.log('Menu Items:', this.menuItems);\n    // Inject theme \n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\n    const link = this.renderer.createElement('link');\n    this.renderer.setAttribute(link, 'id', 'theme-link');\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\n    this.renderer.setAttribute(link, 'type', 'text/css');\n    this.renderer.setAttribute(link, 'href', href);\n    // Append the link tag to the head of the document\n    this.renderer.appendChild(document.head, link);\n    this.primengConfig.ripple = true; //enables core ripple functionality\n  }\n  ngOnDestroy() {\n    // Find and remove the link tag when the component is destroyed\n    const link = document.getElementById('theme-link');\n    if (link) {\n      link.remove();\n    }\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ContentService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 457,\n      vars: 0,\n      consts: [[1, \"main-header\", \"fixed\", \"top-0\", \"w-full\", \"bg-white\", \"z-5\"], [1, \"header-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"align-items-center\"], [1, \"header-logo\", \"relative\", \"pr-6\", \"flex\", \"align-items-center\", \"w-18rem\", \"h-8rem\", \"secondary-bg-color\"], [\"src\", \"/assets/layout/images/snjya-public-services-logo.png\", \"alt\", \"\", 1, \"w-full\", \"h-fit\"], [1, \"header-menu-sec\", \"pl-5\", \"relative\", \"flex\", \"align-items-center\", \"justify-content-between\", \"flex-1\"], [1, \"menu-list\"], [1, \"p-0\", \"m-0\", \"flex\", \"align-items-center\", \"gap-5\"], [1, \"flex\"], [\"href\", \"\", 1, \"flex\", \"flex-column\", \"gap-1\", \"text-lg\", \"font-semibold\", \"text-color\", \"line-height-1\"], [1, \"text-sm\", \"font-normal\", \"text-color-secondary\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-8rem\", \"h-3rem\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-bluegray-100\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"material-symbols-rounded\", \"text-xl\"], [1, \"banner-sec\", \"relative\"], [1, \"banner-body\", \"h-full\", \"flex\", \"align-items-center\"], [1, \"banner-box\", \"relative\", \"flex-1\", \"p-8\"], [1, \"banner-box\", \"relative\", \"flex-1\", \"bg-white\", \"p-8\", \"flex\", \"flex-column\", \"justify-content-center\"], [1, \"mb-4\", \"text-yellow-400\"], [1, \"m-0\", \"mb-5\", \"text-8xl\", \"line-height-1\", \"font-extrabold\", \"text-color\"], [1, \"m-0\", \"text-lg\", \"font-medium\"], [1, \"services-sec\", \"w-full\"], [1, \"services-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\"], [1, \"services-box\", \"s-box-1\", \"p-5\", \"flex-1\", \"flex\", \"flex-column\", \"gap-2\"], [\"src\", \"/assets/layout/images/id-services.png\", \"alt\", \"\", 1, \"h-fit\", \"w-5rem\"], [1, \"font-bold\", \"text-white\"], [1, \"m-0\", \"mb-5\", \"p-0\", \"flex\", \"flex-1\", \"flex-column\", \"gap-2\", \"list-none\"], [1, \"text-white\", \"flex\", \"align-items-center\", \"gap-2\"], [1, \"material-symbols-rounded\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"h-3rem\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-white\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"services-box\", \"s-box-2\", \"p-5\", \"flex-1\", \"flex\", \"flex-column\", \"gap-2\"], [\"src\", \"/assets/layout/images/car-services.png\", \"alt\", \"\", 1, \"h-fit\", \"w-5rem\"], [1, \"services-box\", \"s-box-3\", \"p-5\", \"flex-1\", \"flex\", \"flex-column\", \"gap-2\"], [\"src\", \"/assets/layout/images/business-services.png\", \"alt\", \"\", 1, \"h-fit\", \"w-5rem\"], [1, \"services-box\", \"s-box-4\", \"p-5\", \"flex-1\", \"flex\", \"flex-column\", \"gap-2\"], [\"src\", \"/assets/layout/images/senior-services.png\", \"alt\", \"\", 1, \"h-fit\", \"w-5rem\"], [1, \"about-sec\", \"relative\"], [1, \"about-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"flex-wrap\"], [1, \"about-box\", \"pr-8\", \"w-6\", \"secondary-bg-color\"], [\"src\", \"/assets/layout/images/director-img.jpg\", \"alt\", \"\", 1, \"w-full\"], [1, \"about-box\", \"p-8\", \"pr-0\", \"w-6\", \"secondary-bg-color\", \"flex\", \"flex-column\", \"justify-content-start\"], [1, \"line\", \"m-0\", \"mb-6\", \"relative\", \"pb-5\", \"text-6xl\", \"line-height-2\", \"font-bold\", \"text-white\"], [1, \"m-0\", \"text-lg\", \"text-white\", \"flex-1\"], [\"type\", \"button\", 1, \"px-5\", \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"h-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-white\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"city-members-sec\", \"relative\"], [1, \"city-members-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"flex-wrap\"], [1, \"city-members-left\", \"pt-8\", \"pr-7\", \"pb-8\", \"pl-0\", \"relative\", \"flex\", \"bg-orange-50\"], [1, \"cm-time-box\"], [\"src\", \"/assets/layout/images/time-screen.png\", \"alt\", \"\", 1, \"w-full\"], [1, \"cm-info\"], [1, \"line\", \"m-0\", \"mb-6\", \"relative\", \"pb-5\", \"text-4xl\", \"line-height-2\", \"font-bold\", \"text-color\"], [1, \"m-0\", \"text-lg\", \"text-color\", \"flex-1\"], [1, \"city-members-right\", \"p-7\", \"pr-0\", \"relative\", \"flex\", \"flex-column\", \"gap-8\"], [1, \"cm-right-box\"], [1, \"flex\", \"align-items-start\", \"text-8xl\", \"font-extrabold\", \"line-height-1\"], [1, \"relative\", \"text-4xl\", \"font-bold\", \"inline-block\"], [1, \"uppercase\"], [1, \"text-color-secondary\"], [1, \"quick-access-sec\", \"relative\"], [1, \"quick-access-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"flex-wrap\"], [1, \"quick-access-left\", \"relative\", \"flex\", \"flex-column\", \"pt-8\", \"pb-8\"], [1, \"mb-4\", \"flex\", \"align-items-start\", \"text-6xl\", \"font-extrabold\", \"line-height-1\"], [1, \"line-2\", \"inline-flex\", \"w-fit\", \"m-0\", \"mb-6\", \"relative\", \"pb-5\", \"text-xl\", \"line-height-2\", \"font-bold\", \"text-yellow-400\"], [1, \"quick-access-list\", \"d-grid\", \"w-full\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-bottom-none\", \"border-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/001-toll.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"m-0\", \"mb-4\", \"uppercase\", \"flex-1\"], [1, \"text-sm\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-none\", \"border-top-1\", \"border-right-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/006-virus.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [\"src\", \"/assets/layout/images/003-trash-can.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/004-parking.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"quick-access-box\", \"p-4\", \"w-full\", \"flex\", \"flex-column\", \"border-none\", \"border-top-1\", \"border-right-1\", \"border-bottom-1\", \"border-solid\", \"surface-border\"], [\"src\", \"/assets/layout/images/005-lecture.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [\"src\", \"/assets/layout/images/008-basketball.png\", \"alt\", \"\", 1, \"mb-4\", \"w-4rem\"], [1, \"quick-access-right\"], [1, \"news-sec\", \"relative\", \"pt-7\", \"pb-8\", \"secondary-bg-color\"], [1, \"news-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\"], [1, \"news-title\"], [1, \"line\", \"m-0\", \"mb-5\", \"pb-5\", \"relative\", \"inline-flex\", \"w-fit\", \"text-yellow-400\"], [1, \"m-0\", \"mb-6\", \"relative\", \"text-6xl\", \"line-height-2\", \"font-bold\", \"text-white\"], [1, \"news-list\", \"d-grid\", \"w-full\", \"gap-6\"], [1, \"news-box\", \"w-full\", \"flex\", \"flex-column\"], [1, \"new-img\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/new-img-1.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"text-white\", \"text-xl\", \"flex-1\"], [1, \"mb-3\", \"flex\", \"gap-3\", \"align-items-center\"], [1, \"text\", \"flex\", \"align-items-center\", \"font-medium\", \"text-sm\", \"gap-1\", \"text-white\"], [1, \"mb-5\", \"text-sm\", \"text-white\"], [\"type\", \"button\", 1, \"px-5\", \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"h-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-yellow-400\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"news-letter-sec\", \"relative\"], [1, \"news-letter-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\"], [1, \"news-letter-box-list\", \"flex\", \"bg-white\", \"shadow-1\"], [1, \"news-letter-box\", \"w-6\", \"p-7\", \"flex\", \"flex-column\", \"justify-content-center\"], [1, \"line-2\", \"relative\", \"mb-5\", \"pb-5\", \"inline-flex\", \"w-fit\", \"align-items-start\", \"text-6xl\", \"font-extrabold\", \"line-height-1\"], [1, \"news-letter-form\", \"mt-5\", \"relative\", \"flex\", \"align-items-center\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Your email address\", 1, \"h-3-3rem\", \"border-noround\", \"flex-1\"], [\"type\", \"button\", 1, \"px-6\", \"p-element\", \"p-ripple\", \"border-noround\", \"p-button\", \"p-component\", \"h-3-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-yellow-400\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"news-letter-img\", \"w-6\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/news-letter-img.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"what-happning-sec\", \"mt-8\", \"relative\", \"px-4\"], [1, \"what-happning-body\", \"relative\", \"w-full\", \"mx-auto\", \"flex\", \"bg-orange-50\"], [1, \"what-happning-img\", \"w-6\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/whats-happnening-mg.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"what-happning-right\", \"w-6\", \"p-8\"], [1, \"what-happning-title\"], [1, \"line-2\", \"m-0\", \"mb-5\", \"pb-5\", \"relative\", \"inline-flex\", \"w-fit\", \"text-yellow-400\"], [1, \"m-0\", \"mb-6\", \"relative\", \"text-6xl\", \"line-height-2\", \"font-bold\", \"text-color\"], [1, \"what-happning-list\"], [1, \"what-happning-box\", \"mb-4\", \"pb-4\", \"flex\", \"align-items-center\", \"gap-5\", \"border-none\", \"border-bottom-1\", \"border-solid\", \"surface-border\"], [1, \"wh-img-box\", \"flex\", \"align-items-center\", \"justify-content-center\", \"overflow-hidden\"], [\"src\", \"/assets/layout/images/arches-1.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"wh-cnt-sec\", \"flex-1\", \"flex\", \"align-items-center\", \"gap-6\"], [1, \"wh-cnt\", \"flex\", \"flex-column\"], [1, \"text-xl\", \"font-bold\"], [1, \"flex\", \"align-items-center\", \"gap-3\", \"flex-wrap\"], [1, \"flex\", \"w-fit\", \"align-items-center\", \"gap-2\", \"py-2\", \"p-3\", \"bg-orange-100\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"type\", \"button\", 1, \"px-4\", \"p-element\", \"p-ripple\", \"p-button\", \"p-component\", \"border-noround\", \"p-button-outlined\", \"h-3-3rem\", \"w-fit\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"text-color\", \"font-semibold\"], [\"src\", \"/assets/layout/images/arches-2.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"what-happning-box\", \"flex\", \"align-items-center\", \"gap-5\"], [\"src\", \"/assets/layout/images/arches-3.jpg\", \"alt\", \"\", 1, \"w-full\", \"h-full\", \"object-fit-cover\"], [1, \"footer-sec\", \"relative\", \"secondary-bg-color\"], [1, \"footer-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\"], [1, \"top-footer\", \"py-7\", \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-5\", \"md:col-5\"], [1, \"line\", \"flex\", \"mb-5\", \"pb-3\", \"relative\", \"text-white\", \"text-xl\"], [1, \"p-0\", \"m-0\", \"flex\", \"flex-column\", \"gap-4\"], [1, \"flex\", \"w-full\"], [\"href\", \"#\", 1, \"flex\", \"w-full\", \"text-white\"], [1, \"flex\", \"w-full\", \"text-white\"], [\"href\", \"\", 1, \"flex\", \"w-full\", \"text-white\"], [1, \"col-12\", \"lg:col-7\", \"md:col-7\", \"py-0\"], [1, \"grid\", \"mt-0\"], [1, \"col-12\", \"lg:col-4\", \"md:col-4\"], [1, \"middle-footer\", \"py-5\", \"w-full\", \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-blue-100\"], [1, \"bottom-footer\", \"py-5\", \"w-full\", \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [1, \"m-0\", \"mb-3\", \"p-0\", \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-3\", \"list-none\"], [\"href\", \"#\", 1, \"inline-flex\", \"w-fit\", \"text-white\"], [1, \"m-0\", \"text-white\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"header\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"img\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"ul\", 6)(7, \"li\", 7)(8, \"a\", 8);\n          i0.ɵɵtext(9, \"City News \");\n          i0.ɵɵelementStart(10, \"span\", 9);\n          i0.ɵɵtext(11, \"Current Updates\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"li\", 7)(13, \"a\", 8);\n          i0.ɵɵtext(14, \"Features \");\n          i0.ɵɵelementStart(15, \"span\", 9);\n          i0.ɵɵtext(16, \"Theme Features\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"li\", 7)(18, \"a\", 8);\n          i0.ɵɵtext(19, \"Events \");\n          i0.ɵɵelementStart(20, \"span\", 9);\n          i0.ɵɵtext(21, \"Join the fun\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"li\", 7)(23, \"a\", 8);\n          i0.ɵɵtext(24, \"Residents \");\n          i0.ɵɵelementStart(25, \"span\", 9);\n          i0.ɵɵtext(26, \"Get useful info\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(27, \"button\", 10)(28, \"span\", 11);\n          i0.ɵɵtext(29, \"login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(30, \" Login \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(31, \"section\", 12)(32, \"div\", 13);\n          i0.ɵɵelement(33, \"div\", 14);\n          i0.ɵɵelementStart(34, \"div\", 15)(35, \"h4\", 16);\n          i0.ɵɵtext(36, \"Far away from the every day!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"h1\", 17);\n          i0.ɵɵtext(38, \"Community \");\n          i0.ɵɵelement(39, \"br\");\n          i0.ɵɵtext(40, \"of endless \");\n          i0.ɵɵelement(41, \"br\");\n          i0.ɵɵtext(42, \"beauty & Calm\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"p\", 18);\n          i0.ɵɵtext(44, \"Drawn by clean air and mythical light, visitors come to experience traditions, \");\n          i0.ɵɵelement(45, \"br\");\n          i0.ɵɵtext(46, \"fine art, great cuisine and natural beauty of the landscape.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(47, \"section\", 19)(48, \"div\", 20)(49, \"div\", 21);\n          i0.ɵɵelement(50, \"img\", 22);\n          i0.ɵɵelementStart(51, \"h4\", 23);\n          i0.ɵɵtext(52, \"Driver & ID Services\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"ul\", 24)(54, \"li\", 25)(55, \"i\", 26);\n          i0.ɵɵtext(56, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(57, \" Renew driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"li\", 25)(59, \"i\", 26);\n          i0.ɵɵtext(60, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(61, \" Replace driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"li\", 25)(63, \"i\", 26);\n          i0.ɵɵtext(64, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(65, \" Get a REAL ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"li\", 25)(67, \"i\", 26);\n          i0.ɵɵtext(68, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(69, \" First-time driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(70, \"button\", 27);\n          i0.ɵɵtext(71, \" Driver & ID Services \");\n          i0.ɵɵelementStart(72, \"span\", 26);\n          i0.ɵɵtext(73, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(74, \"div\", 28);\n          i0.ɵɵelement(75, \"img\", 29);\n          i0.ɵɵelementStart(76, \"h4\", 23);\n          i0.ɵɵtext(77, \"Vehicle & Plate Services\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"ul\", 24)(79, \"li\", 25)(80, \"i\", 26);\n          i0.ɵɵtext(81, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(82, \" Renew driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"li\", 25)(84, \"i\", 26);\n          i0.ɵɵtext(85, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(86, \" Replace driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"li\", 25)(88, \"i\", 26);\n          i0.ɵɵtext(89, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(90, \" Get a REAL ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"li\", 25)(92, \"i\", 26);\n          i0.ɵɵtext(93, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(94, \" First-time driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(95, \"button\", 27);\n          i0.ɵɵtext(96, \" Vehicle & Plate Services \");\n          i0.ɵɵelementStart(97, \"span\", 26);\n          i0.ɵɵtext(98, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(99, \"div\", 30);\n          i0.ɵɵelement(100, \"img\", 31);\n          i0.ɵɵelementStart(101, \"h4\", 23);\n          i0.ɵɵtext(102, \"Business Services\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"ul\", 24)(104, \"li\", 25)(105, \"i\", 26);\n          i0.ɵɵtext(106, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(107, \" Renew driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"li\", 25)(109, \"i\", 26);\n          i0.ɵɵtext(110, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(111, \" Replace driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"li\", 25)(113, \"i\", 26);\n          i0.ɵɵtext(114, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(115, \" Get a REAL ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(116, \"li\", 25)(117, \"i\", 26);\n          i0.ɵɵtext(118, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(119, \" First-time driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(120, \"button\", 27);\n          i0.ɵɵtext(121, \" Business Services \");\n          i0.ɵɵelementStart(122, \"span\", 26);\n          i0.ɵɵtext(123, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(124, \"div\", 32);\n          i0.ɵɵelement(125, \"img\", 33);\n          i0.ɵɵelementStart(126, \"h4\", 23);\n          i0.ɵɵtext(127, \"Senior Services\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(128, \"ul\", 24)(129, \"li\", 25)(130, \"i\", 26);\n          i0.ɵɵtext(131, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(132, \" Renew driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(133, \"li\", 25)(134, \"i\", 26);\n          i0.ɵɵtext(135, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(136, \" Replace driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(137, \"li\", 25)(138, \"i\", 26);\n          i0.ɵɵtext(139, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(140, \" Get a REAL ID\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(141, \"li\", 25)(142, \"i\", 26);\n          i0.ɵɵtext(143, \"arrow_right\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(144, \" First-time driver\\u2019s license/ID card\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(145, \"button\", 27);\n          i0.ɵɵtext(146, \" Senior Services \");\n          i0.ɵɵelementStart(147, \"span\", 26);\n          i0.ɵɵtext(148, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(149, \"section\", 34)(150, \"div\", 35)(151, \"div\", 36);\n          i0.ɵɵelement(152, \"img\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(153, \"div\", 38)(154, \"h4\", 16);\n          i0.ɵɵtext(155, \"Far away from the every day!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(156, \"h2\", 39);\n          i0.ɵɵtext(157, \"A Message From the Director \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(158, \"p\", 40);\n          i0.ɵɵtext(159, \"Old Town of Riverside is a special place with limitless potential, where everyone deserves equal access to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in every neighborhood. It exists to serve you and be a resource for you, your family, and your community.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(160, \"button\", 41);\n          i0.ɵɵtext(161, \" Learn More \");\n          i0.ɵɵelementStart(162, \"span\", 26);\n          i0.ɵɵtext(163, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(164, \"section\", 42)(165, \"div\", 43)(166, \"div\", 44)(167, \"div\", 45);\n          i0.ɵɵelement(168, \"img\", 46);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(169, \"div\", 47)(170, \"h4\", 16);\n          i0.ɵɵtext(171, \"Our city in numbers\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(172, \"h2\", 48);\n          i0.ɵɵtext(173, \"Programs to help launch, grow and expand any business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(174, \"p\", 49);\n          i0.ɵɵtext(175, \"Old Town of Riverside is a special place with limitless potential, where everyone deserves equal access to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in every neighborhood. It exists to serve you and be a resource for you, your family, and your community.\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(176, \"div\", 50)(177, \"div\", 51)(178, \"h3\", 52);\n          i0.ɵɵtext(179, \"41 \");\n          i0.ɵɵelementStart(180, \"sup\", 53);\n          i0.ɵɵtext(181, \"k\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(182, \"h5\", 54);\n          i0.ɵɵtext(183, \"Highly-educated creative workforce\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(184, \"p\", 55);\n          i0.ɵɵtext(185, \"A vibrant destination where innovations in design and technology are born. A place for you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(186, \"div\", 51)(187, \"h3\", 52);\n          i0.ɵɵtext(188, \"8 \");\n          i0.ɵɵelementStart(189, \"sup\", 53);\n          i0.ɵɵtext(190, \"th\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(191, \"h5\", 54);\n          i0.ɵɵtext(192, \"Highest Per Capita Income in Virginia\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(193, \"p\", 55);\n          i0.ɵɵtext(194, \"A vibrant destination where innovations in design and technology are born. A place for you.\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(195, \"section\", 56)(196, \"div\", 57)(197, \"div\", 58)(198, \"h2\", 59);\n          i0.ɵɵtext(199, \"Quick Access\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(200, \"h4\", 60);\n          i0.ɵɵtext(201, \" What can the City help you with today?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(202, \"div\", 61)(203, \"div\", 62);\n          i0.ɵɵelement(204, \"img\", 63);\n          i0.ɵɵelementStart(205, \"h6\", 64);\n          i0.ɵɵtext(206, \"Road construction\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(207, \"p\", 65);\n          i0.ɵɵtext(208, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(209, \"div\", 66);\n          i0.ɵɵelement(210, \"img\", 67);\n          i0.ɵɵelementStart(211, \"h6\", 64);\n          i0.ɵɵtext(212, \"COVID-19 updates\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(213, \"p\", 65);\n          i0.ɵɵtext(214, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(215, \"div\", 66);\n          i0.ɵɵelement(216, \"img\", 68);\n          i0.ɵɵelementStart(217, \"h6\", 64);\n          i0.ɵɵtext(218, \"Garbage pickup\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(219, \"p\", 65);\n          i0.ɵɵtext(220, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(221, \"div\", 69);\n          i0.ɵɵelement(222, \"img\", 70);\n          i0.ɵɵelementStart(223, \"h6\", 64);\n          i0.ɵɵtext(224, \"Parking\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(225, \"p\", 65);\n          i0.ɵɵtext(226, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(227, \"div\", 71);\n          i0.ɵɵelement(228, \"img\", 72);\n          i0.ɵɵelementStart(229, \"h6\", 64);\n          i0.ɵɵtext(230, \"Council meetings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(231, \"p\", 65);\n          i0.ɵɵtext(232, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(233, \"div\", 71);\n          i0.ɵɵelement(234, \"img\", 73);\n          i0.ɵɵelementStart(235, \"h6\", 64);\n          i0.ɵɵtext(236, \"Recreation and sport programs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(237, \"p\", 65);\n          i0.ɵɵtext(238, \"Find current information about construction projects and other city-wide road work impacting you.\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(239, \"div\", 74);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(240, \"section\", 75)(241, \"div\", 76)(242, \"div\", 77)(243, \"h5\", 78);\n          i0.ɵɵtext(244, \"Find out what\\u2019s going on & stay up to date.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(245, \"h2\", 79);\n          i0.ɵɵtext(246, \"Latest News\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(247, \"div\", 80)(248, \"div\", 81)(249, \"div\", 82);\n          i0.ɵɵelement(250, \"img\", 83);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(251, \"h3\", 84);\n          i0.ɵɵtext(252, \"Proposed Downtown District Ordinance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(253, \"div\", 85)(254, \"div\", 86)(255, \"span\", 11);\n          i0.ɵɵtext(256, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(257, \" September 16, 2018 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(258, \"div\", 86)(259, \"span\", 11);\n          i0.ɵɵtext(260, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(261, \" 240 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(262, \"p\", 87);\n          i0.ɵɵtext(263, \"The goal of the proposed ordinance is to create safe venues for customers and to discourage illegal activities. The governing officials of...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(264, \"button\", 88);\n          i0.ɵɵtext(265, \" Learn More \");\n          i0.ɵɵelementStart(266, \"span\", 26);\n          i0.ɵɵtext(267, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(268, \"div\", 81)(269, \"div\", 82);\n          i0.ɵɵelement(270, \"img\", 83);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(271, \"h3\", 84);\n          i0.ɵɵtext(272, \"Annual Water Quality Report (Gallery Post)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(273, \"div\", 85)(274, \"div\", 86)(275, \"span\", 11);\n          i0.ɵɵtext(276, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(277, \" September 16, 2018 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(278, \"div\", 86)(279, \"span\", 11);\n          i0.ɵɵtext(280, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(281, \" 240 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(282, \"p\", 87);\n          i0.ɵɵtext(283, \"The goal of the proposed ordinance is to create safe venues for customers and to discourage illegal activities. The governing officials of...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(284, \"button\", 88);\n          i0.ɵɵtext(285, \" Learn More \");\n          i0.ɵɵelementStart(286, \"span\", 26);\n          i0.ɵɵtext(287, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(288, \"div\", 81)(289, \"div\", 82);\n          i0.ɵɵelement(290, \"img\", 83);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(291, \"h3\", 84);\n          i0.ɵɵtext(292, \"Waste Industries Garbage Pick Up: Embeds\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(293, \"div\", 85)(294, \"div\", 86)(295, \"span\", 11);\n          i0.ɵɵtext(296, \"calendar_month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(297, \" September 16, 2018 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(298, \"div\", 86)(299, \"span\", 11);\n          i0.ɵɵtext(300, \"visibility\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(301, \" 240 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(302, \"p\", 87);\n          i0.ɵɵtext(303, \"The goal of the proposed ordinance is to create safe venues for customers and to discourage illegal activities. The governing officials of...\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(304, \"button\", 88);\n          i0.ɵɵtext(305, \" Learn More \");\n          i0.ɵɵelementStart(306, \"span\", 26);\n          i0.ɵɵtext(307, \"arrow_right_alt\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(308, \"section\", 89)(309, \"div\", 90)(310, \"div\", 91)(311, \"div\", 92)(312, \"h2\", 93);\n          i0.ɵɵtext(313, \" Sign up for News & Alerts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(314, \"p\");\n          i0.ɵɵtext(315, \"Stay connected to the City of Riverside by signing up to receive official news and alerts by email! \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(316, \"div\", 94);\n          i0.ɵɵelement(317, \"input\", 95);\n          i0.ɵɵelementStart(318, \"button\", 96);\n          i0.ɵɵtext(319, \" Sign Up \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(320, \"div\", 97);\n          i0.ɵɵelement(321, \"img\", 98);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(322, \"section\", 99)(323, \"div\", 100)(324, \"div\", 101);\n          i0.ɵɵelement(325, \"img\", 102);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(326, \"div\", 103)(327, \"div\", 104)(328, \"h5\", 105);\n          i0.ɵɵtext(329, \"Join the fun in our city! \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(330, \"h2\", 106);\n          i0.ɵɵtext(331, \"What's Happening\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(332, \"div\", 107)(333, \"div\", 108)(334, \"div\", 109);\n          i0.ɵɵelement(335, \"img\", 110);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(336, \"div\", 111)(337, \"div\", 112)(338, \"h3\", 113);\n          i0.ɵɵtext(339, \"Heritage 10th Anniversary\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(340, \"div\", 114)(341, \"div\", 115)(342, \"span\");\n          i0.ɵɵtext(343, \"June 9, 2027\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(344, \" - \");\n          i0.ɵɵelementStart(345, \"span\");\n          i0.ɵɵtext(346, \"June 18, 2027\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(347, \"div\", 116);\n          i0.ɵɵtext(348, \"All Day at \");\n          i0.ɵɵelementStart(349, \"b\");\n          i0.ɵɵtext(350, \"Baker Hall\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(351, \"button\", 117);\n          i0.ɵɵtext(352, \" Find out more \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(353, \"div\", 108)(354, \"div\", 109);\n          i0.ɵɵelement(355, \"img\", 118);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(356, \"div\", 111)(357, \"div\", 112)(358, \"h3\", 113);\n          i0.ɵɵtext(359, \"Along Pines Run\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(360, \"div\", 114)(361, \"div\", 115)(362, \"span\");\n          i0.ɵɵtext(363, \"July 22, 2027\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(364, \"div\", 116);\n          i0.ɵɵtext(365, \"12:00 am at \");\n          i0.ɵɵelementStart(366, \"b\");\n          i0.ɵɵtext(367, \"Boulder City Council\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(368, \"button\", 117);\n          i0.ɵɵtext(369, \" Find out more \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(370, \"div\", 119)(371, \"div\", 109);\n          i0.ɵɵelement(372, \"img\", 120);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(373, \"div\", 111)(374, \"div\", 112)(375, \"h3\", 113);\n          i0.ɵɵtext(376, \"Touch A Truck\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(377, \"div\", 114)(378, \"div\", 115)(379, \"span\");\n          i0.ɵɵtext(380, \"June 9, 2027\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(381, \" - \");\n          i0.ɵɵelementStart(382, \"span\");\n          i0.ɵɵtext(383, \"June 18, 2027\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(384, \"div\", 116);\n          i0.ɵɵtext(385, \"9:00 am - 5:00 pm at \");\n          i0.ɵɵelementStart(386, \"b\");\n          i0.ɵɵtext(387, \"Baker Hall\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(388, \"button\", 117);\n          i0.ɵɵtext(389, \" Find out more \");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(390, \"section\", 121)(391, \"div\", 122)(392, \"div\", 123)(393, \"div\", 124)(394, \"h3\", 125);\n          i0.ɵɵtext(395, \"Riverside City Hall\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(396, \"ul\", 126)(397, \"li\", 127)(398, \"a\", 128);\n          i0.ɵɵtext(399, \"8353 Sierra Avenue \\u2022 Riverside, CA 91335\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(400, \"li\", 127)(401, \"a\", 128);\n          i0.ɵɵtext(402, \"Phone: (*************\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(403, \"li\", 127)(404, \"a\", 129);\n          i0.ɵɵtext(405, \"Monday - Thursday, 8:00 am - 6:00 pm\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(406, \"li\", 127)(407, \"a\", 130);\n          i0.ɵɵtext(408, \"Email : <EMAIL>\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(409, \"div\", 131)(410, \"div\", 132)(411, \"div\", 133)(412, \"h3\", 125);\n          i0.ɵɵtext(413, \"Services\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(414, \"ul\", 126)(415, \"li\", 127)(416, \"a\", 128);\n          i0.ɵɵtext(417, \"Driver & ID Services\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(418, \"li\", 127)(419, \"a\", 128);\n          i0.ɵɵtext(420, \"Vehicle & Plate Services\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(421, \"li\", 127)(422, \"a\", 129);\n          i0.ɵɵtext(423, \"Business Services\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(424, \"li\", 127)(425, \"a\", 130);\n          i0.ɵɵtext(426, \"Senior Services\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(427, \"div\", 133)(428, \"h3\", 125);\n          i0.ɵɵtext(429, \"Useful Links\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(430, \"ul\", 126)(431, \"li\", 127)(432, \"a\", 128);\n          i0.ɵɵtext(433, \"Frequently Asked Questions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(434, \"li\", 127)(435, \"a\", 128);\n          i0.ɵɵtext(436, \"Latest News\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(437, \"li\", 127)(438, \"a\", 129);\n          i0.ɵɵtext(439, \"Community\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(440, \"li\", 127)(441, \"a\", 130);\n          i0.ɵɵtext(442, \"Help Center\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(443, \"li\", 127)(444, \"a\", 130);\n          i0.ɵɵtext(445, \"Careers\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelement(446, \"div\", 134);\n          i0.ɵɵelementStart(447, \"div\", 135)(448, \"ul\", 136)(449, \"li\")(450, \"a\", 137);\n          i0.ɵɵtext(451, \"Term & Conditions\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(452, \"li\")(453, \"a\", 137);\n          i0.ɵɵtext(454, \"Privacy Policy\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(455, \"p\", 138);\n          i0.ɵɵtext(456, \"\\u00A9 2025 SNJYA. All rights reserved.\");\n          i0.ɵɵelementEnd()()()();\n        }\n      },\n      dependencies: [i4.InputText],\n      styles: [\".max-w-1200 {\\n  max-width: 1200px;\\n}\\n  .secondary-color {\\n  color: #030f5e !important;\\n}\\n  .secondary-bg-color {\\n  background: #030f5e !important;\\n}\\n  .font-extrabold {\\n  font-weight: 900 !important;\\n}\\n  .d-grid {\\n  display: grid !important;\\n}\\n  .object-fit-contain {\\n  object-fit: contain;\\n}\\n  .object-fit-cover {\\n  object-fit: cover;\\n}\\n  .h-3-3rem {\\n  height: 3.3rem !important;\\n}\\n  .bg-blue-75 {\\n  background: rgb(227, 243, 255) !important;\\n}\\n  .main-header:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  height: 8rem;\\n  background: #030f5e !important;\\n  width: 20%;\\n}\\n  .banner-sec {\\n  margin: 8rem 0 0 0;\\n  height: calc(100vh - 8rem);\\n  background: url('banner-img.jpg') center center no-repeat;\\n  background-size: cover;\\n}\\n  .banner-sec .banner-box {\\n  margin: 120px 0 0 0;\\n}\\n  .services-sec {\\n  margin: -87px 0 0 0;\\n}\\n  .services-sec .services-box.s-box-1 {\\n  background: #a8c1cd;\\n}\\n  .services-sec .services-box.s-box-2 {\\n  background: #e3cab0;\\n}\\n  .services-sec .services-box.s-box-3 {\\n  background: #c4a597;\\n}\\n  .services-sec .services-box.s-box-4 {\\n  background: #e8816e;\\n}\\n  .about-sec:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  right: 0;\\n  height: 100%;\\n  background: #030f5e !important;\\n  width: 20%;\\n}\\n  .line::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgba(255, 255, 255, 0.2392156863) !important;\\n  width: 100%;\\n}\\n  .line::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgb(213, 200, 186) !important;\\n  width: 64px;\\n}\\n  .line-2::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgba(0, 0, 0, 0.07) !important;\\n  width: 100%;\\n}\\n  .line-2::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  bottom: 0;\\n  height: 2px;\\n  background: rgb(213, 200, 186) !important;\\n  width: 64px;\\n}\\n  .city-members-sec:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  height: 100%;\\n  background: var(--orange-50) !important;\\n  width: 20%;\\n}\\n  .city-members-left {\\n  width: 70%;\\n}\\n  .city-members-left .cm-time-box {\\n  flex: 0 0 40%;\\n}\\n  .city-members-left .cm-info {\\n  flex: 0 0 60%;\\n  padding: 0 0 0 50px;\\n}\\n  .city-members-right {\\n  width: 30%;\\n}\\n  .city-members-right .cm-right-box h3 sup {\\n  top: 7px;\\n}\\n  .quick-access-sec {\\n  background: url('pexels-vitor-gusmao.jpg') center right no-repeat;\\n  background-size: 38% auto;\\n}\\n  .quick-access-sec .quick-access-left {\\n  flex: 0 0 67%;\\n  width: 67%;\\n}\\n  .quick-access-sec .quick-access-left .quick-access-list {\\n  grid-template-columns: repeat(auto-fill, minmax(33.333%, 1fr));\\n}\\n  .quick-access-sec .quick-access-right {\\n  flex: 0 0 33%;\\n  width: 33%;\\n}\\n  .news-sec .news-list {\\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\\n}\\n  .news-letter-sec {\\n  background: linear-gradient(180deg, #030f5e 50%, transparent 50%);\\n}\\n  .news-letter-sec .news-letter-box-list .news-letter-img {\\n  height: 640px;\\n}\\n  .what-happning-sec .what-happning-body .what-happning-img {\\n  height: 800px;\\n}\\n  .what-happning-sec .what-happning-body .what-happning-box .wh-img-box {\\n  width: 120px;\\n  height: 120px;\\n}\\n  .what-happning-sec .what-happning-body .what-happning-box .wh-cnt {\\n  max-width: 70%;\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["HomeComponent", "constructor", "primengConfig", "renderer", "route", "CMSservice", "logo", "menuItems", "ngOnInit", "commonContent", "snapshot", "data", "console", "log", "logoComponent", "getDataByComponentName", "body", "length", "Logo", "url", "menuComponent", "<PERSON><PERSON>_<PERSON><PERSON>", "content", "href", "link", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "document", "head", "ripple", "ngOnDestroy", "getElementById", "remove", "i0", "ɵɵdirectiveInject", "i1", "PrimeNGConfig", "Renderer2", "i2", "ActivatedRoute", "i3", "ContentService", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home.component.ts", "D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\home\\home.component.html"], "sourcesContent": ["import { Component, Renderer2 } from '@angular/core';\r\nimport { PrimeNGConfig } from 'primeng/api';\r\nimport { ContentService } from '../core/services/content-vendor.service';\r\nimport { ActivatedRoute } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-home',\r\n  templateUrl: './home.component.html',\r\n  styleUrl: './home.component.scss'\r\n})\r\nexport class HomeComponent {\r\n\r\n  content!: any;\r\n  commonContent!: any;\r\n  logo: string = '';\r\n  menuItems: any[] = [];\r\n\r\n  constructor(\r\n    private primengConfig: PrimeNGConfig,\r\n    private renderer: Renderer2,\r\n    private route: ActivatedRoute,\r\n    private CMSservice: ContentService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.commonContent = this.route.snapshot.data['commonContent'];\r\n    console.log('Common Content:', this.commonContent);\r\n\r\n    // Extract logo\r\n    const logoComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.logo\");\r\n    if (logoComponent?.length) {\r\n      this.logo = logoComponent[0].Logo?.url || '';\r\n    }\r\n\r\n    // Extract menu\r\n    const menuComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.menu\");\r\n    if (menuComponent?.length) {\r\n      this.menuItems = menuComponent[0].Menu_Item || [];\r\n    }\r\n\r\n    this.content = this.route.snapshot.data['content'];\r\n    console.log('Home Content:', this.content);\r\n    console.log('Logo:', this.logo);\r\n    console.log('Menu Items:', this.menuItems);\r\n\r\n    // Inject theme \r\n    const href = 'assets/layout/styles/theme/theme-light/snjya/theme.css';\r\n    const link = this.renderer.createElement('link');\r\n    this.renderer.setAttribute(link, 'id', 'theme-link');\r\n    this.renderer.setAttribute(link, 'rel', 'stylesheet');\r\n    this.renderer.setAttribute(link, 'type', 'text/css');\r\n    this.renderer.setAttribute(link, 'href', href);\r\n\r\n    // Append the link tag to the head of the document\r\n    this.renderer.appendChild(document.head, link);\r\n\r\n    this.primengConfig.ripple = true; //enables core ripple functionality\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Find and remove the link tag when the component is destroyed\r\n    const link = document.getElementById('theme-link');\r\n    if (link) {\r\n      link.remove();\r\n    }\r\n  }\r\n\r\n}\r\n", "<!--HEADER SEC-->\r\n<header class=\"main-header fixed top-0 w-full bg-white z-5\">\r\n    <div class=\"header-body relative max-w-1200 w-full mx-auto px-4 flex align-items-center\">\r\n        <div class=\"header-logo relative pr-6 flex align-items-center w-18rem h-8rem secondary-bg-color\">\r\n            <img src=\"/assets/layout/images/snjya-public-services-logo.png\" class=\"w-full h-fit\" alt=\"\" />\r\n        </div>\r\n        <div class=\"header-menu-sec pl-5 relative flex align-items-center justify-content-between flex-1\">\r\n            <div class=\"menu-list\">\r\n                <ul class=\"p-0 m-0 flex align-items-center gap-5\">\r\n                    <li class=\"flex\"><a href=\"\"\r\n                            class=\"flex flex-column gap-1 text-lg font-semibold text-color line-height-1\">City\r\n                            News <span class=\"text-sm font-normal text-color-secondary\">Current Updates</span></a></li>\r\n                    <li class=\"flex\"><a href=\"\"\r\n                            class=\"flex flex-column gap-1 text-lg font-semibold text-color line-height-1\">Features\r\n                            <span class=\"text-sm font-normal text-color-secondary\">Theme Features</span></a></li>\r\n                    <li class=\"flex\"><a href=\"\"\r\n                            class=\"flex flex-column gap-1 text-lg font-semibold text-color line-height-1\">Events <span\r\n                                class=\"text-sm font-normal text-color-secondary\">Join the fun</span></a></li>\r\n                    <li class=\"flex\"><a href=\"\"\r\n                            class=\"flex flex-column gap-1 text-lg font-semibold text-color line-height-1\">Residents\r\n                            <span class=\"text-sm font-normal text-color-secondary\">Get useful info</span></a></li>\r\n                </ul>\r\n            </div>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component w-8rem h-3rem justify-content-center gap-2 line-height-2 bg-bluegray-100 text-color font-semibold border-none\">\r\n                <span class=\"material-symbols-rounded text-xl\">login</span> Login\r\n            </button>\r\n        </div>\r\n    </div>\r\n</header>\r\n<!--HEADER SEC-->\r\n\r\n<!--BANNER SEC-->\r\n<section class=\"banner-sec relative\">\r\n    <div class=\"banner-body h-full flex align-items-center\">\r\n        <div class=\"banner-box relative flex-1 p-8\"></div>\r\n        <div class=\"banner-box relative flex-1 bg-white p-8 flex flex-column justify-content-center\">\r\n            <h4 class=\"mb-4 text-yellow-400\">Far away from the every day!</h4>\r\n            <h1 class=\"m-0 mb-5 text-8xl line-height-1 font-extrabold text-color\">Community <br>of endless <br>beauty &\r\n                Calm</h1>\r\n            <p class=\"m-0 text-lg font-medium\">Drawn by clean air and mythical light, visitors come to experience\r\n                traditions, <br>fine art, great cuisine and natural beauty of the landscape.</p>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--BANNER SEC-->\r\n\r\n<!--SERVICES SEC-->\r\n<section class=\"services-sec w-full\">\r\n    <div class=\"services-body relative max-w-1200 w-full mx-auto px-4 flex\">\r\n        <div class=\"services-box s-box-1 p-5 flex-1 flex flex-column gap-2\">\r\n            <img src=\"/assets/layout/images/id-services.png\" class=\"h-fit w-5rem\" alt=\"\" />\r\n            <h4 class=\"font-bold text-white\">Driver & ID Services</h4>\r\n            <ul class=\"m-0 mb-5 p-0 flex flex-1 flex-column gap-2 list-none\">\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Renew driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Replace driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Get a REAL ID</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    First-time driver’s license/ID card</li>\r\n            </ul>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component h-3rem justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                Driver & ID Services <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n        <div class=\"services-box s-box-2 p-5 flex-1 flex flex-column gap-2\">\r\n            <img src=\"/assets/layout/images/car-services.png\" class=\"h-fit w-5rem\" alt=\"\" />\r\n            <h4 class=\"font-bold text-white\">Vehicle & Plate Services</h4>\r\n            <ul class=\"m-0 mb-5 p-0 flex flex-1 flex-column gap-2 list-none\">\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Renew driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Replace driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Get a REAL ID</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    First-time driver’s license/ID card</li>\r\n            </ul>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component h-3rem justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                Vehicle & Plate Services <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n        <div class=\"services-box s-box-3 p-5 flex-1 flex flex-column gap-2\">\r\n            <img src=\"/assets/layout/images/business-services.png\" class=\"h-fit w-5rem\" alt=\"\" />\r\n            <h4 class=\"font-bold text-white\">Business Services</h4>\r\n            <ul class=\"m-0 mb-5 p-0 flex flex-1 flex-column gap-2 list-none\">\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Renew driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Replace driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Get a REAL ID</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    First-time driver’s license/ID card</li>\r\n            </ul>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component h-3rem justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                Business Services <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n        <div class=\"services-box s-box-4 p-5 flex-1 flex flex-column gap-2\">\r\n            <img src=\"/assets/layout/images/senior-services.png\" class=\"h-fit w-5rem\" alt=\"\" />\r\n            <h4 class=\"font-bold text-white\">Senior Services</h4>\r\n            <ul class=\"m-0 mb-5 p-0 flex flex-1 flex-column gap-2 list-none\">\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Renew driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Replace driver’s license/ID card</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    Get a REAL ID</li>\r\n                <li class=\"text-white flex align-items-center gap-2\"><i class=\"material-symbols-rounded\">arrow_right</i>\r\n                    First-time driver’s license/ID card</li>\r\n            </ul>\r\n            <button type=\"button\"\r\n                class=\"p-element p-ripple p-button-rounded p-button p-component h-3rem justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                Senior Services <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--SERVICES SEC-->\r\n\r\n<!--ABOUT SEC-->\r\n<section class=\"about-sec relative\">\r\n    <div class=\"about-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap\">\r\n        <div class=\"about-box pr-8 w-6 secondary-bg-color\">\r\n            <img src=\"/assets/layout/images/director-img.jpg\" alt=\"\" class=\"w-full\" />\r\n        </div>\r\n        <div class=\"about-box p-8 pr-0 w-6 secondary-bg-color flex flex-column justify-content-start\">\r\n            <h4 class=\"mb-4 text-yellow-400\">Far away from the every day!</h4>\r\n            <h2 class=\"line m-0 mb-6 relative pb-5 text-6xl line-height-2 font-bold text-white\">A Message From the\r\n                Director\r\n            </h2>\r\n            <p class=\"m-0 text-lg text-white flex-1\">Old Town of Riverside is a special place with limitless potential,\r\n                where everyone deserves equal access\r\n                to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in every\r\n                neighborhood. It exists to serve you and be a resource for you, your family, and your community.</p>\r\n\r\n            <button type=\"button\"\r\n                class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-white text-color font-semibold border-none\">\r\n                Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n            </button>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--ABOUT SEC-->\r\n\r\n<!--CITY MEMBERS SEC-->\r\n<section class=\"city-members-sec relative\">\r\n    <div class=\"city-members-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap\">\r\n        <div class=\"city-members-left pt-8 pr-7 pb-8 pl-0 relative flex bg-orange-50\">\r\n            <div class=\"cm-time-box\">\r\n                <img src=\"/assets/layout/images/time-screen.png\" alt=\"\" class=\"w-full\" />\r\n            </div>\r\n            <div class=\"cm-info\">\r\n                <h4 class=\"mb-4 text-yellow-400\">Our city in numbers</h4>\r\n                <h2 class=\"line m-0 mb-6 relative pb-5 text-4xl line-height-2 font-bold text-color\">Programs to help\r\n                    launch,\r\n                    grow and expand any business</h2>\r\n                <p class=\"m-0 text-lg text-color flex-1\">Old Town of Riverside is a special place with limitless\r\n                    potential, where everyone deserves equal access\r\n                    to opportunity, happiness, and a bright future. Our City is here to help foster those ideals in\r\n                    every\r\n                    neighborhood. It exists to serve you and be a resource for you, your family, and your community.</p>\r\n            </div>\r\n        </div>\r\n        <div class=\"city-members-right p-7 pr-0 relative flex flex-column gap-8\">\r\n            <div class=\"cm-right-box\">\r\n                <h3 class=\"flex align-items-start text-8xl font-extrabold line-height-1\">41 <sup\r\n                        class=\"relative text-4xl font-bold inline-block\">k</sup></h3>\r\n                <h5 class=\"uppercase\">Highly-educated creative workforce</h5>\r\n                <p class=\"text-color-secondary\">A vibrant destination where innovations in design and technology are\r\n                    born. A place for you.</p>\r\n            </div>\r\n            <div class=\"cm-right-box\">\r\n                <h3 class=\" flex align-items-start text-8xl font-extrabold line-height-1\">8 <sup\r\n                        class=\"relative text-4xl font-bold inline-block\">th</sup></h3>\r\n                <h5 class=\"uppercase\">Highest Per Capita Income in Virginia</h5>\r\n                <p class=\"text-color-secondary\">A vibrant destination where innovations in design and technology are\r\n                    born. A place for you.</p>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--CITY MEMBERS SEC-->\r\n\r\n<!--QUICK ACCESS SEC-->\r\n<section class=\"quick-access-sec relative\">\r\n    <div class=\"quick-access-body relative max-w-1200 w-full mx-auto px-4 flex flex-wrap\">\r\n        <div class=\"quick-access-left relative flex flex-column pt-8 pb-8\">\r\n            <h2 class=\"mb-4 flex align-items-start text-6xl font-extrabold line-height-1\">Quick Access</h2>\r\n            <h4 class=\"line-2 inline-flex w-fit m-0 mb-6 relative pb-5 text-xl line-height-2 font-bold text-yellow-400\">\r\n                What can the City help you with today?</h4>\r\n\r\n            <div class=\"quick-access-list d-grid w-full\">\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-bottom-none border-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/001-toll.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Road construction</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/006-virus.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">COVID-19 updates</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/003-trash-can.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Garbage pickup</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div class=\"quick-access-box p-4 w-full flex flex-column border-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/004-parking.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Parking</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-bottom-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/005-lecture.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Council meetings</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n                <div\r\n                    class=\"quick-access-box p-4 w-full flex flex-column border-none border-top-1 border-right-1 border-bottom-1 border-solid surface-border\">\r\n                    <img src=\"/assets/layout/images/008-basketball.png\" alt=\"\" class=\"mb-4 w-4rem\" />\r\n                    <h6 class=\"m-0 mb-4 uppercase flex-1\">Recreation and sport programs</h6>\r\n                    <p class=\"text-sm\">Find current information about construction projects and other city-wide road\r\n                        work impacting you.</p>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"quick-access-right\"></div>\r\n    </div>\r\n</section>\r\n<!--QUICK ACCESS SEC-->\r\n\r\n<!--NEWS SEC-->\r\n<section class=\"news-sec relative pt-7 pb-8 secondary-bg-color\">\r\n    <div class=\"news-body relative max-w-1200 w-full mx-auto px-4\">\r\n        <div class=\"news-title\">\r\n            <h5 class=\"line m-0 mb-5 pb-5 relative inline-flex w-fit text-yellow-400\">Find out what’s going on & stay up\r\n                to date.</h5>\r\n            <h2 class=\"m-0 mb-6 relative text-6xl line-height-2 font-bold text-white\">Latest News</h2>\r\n        </div>\r\n        <div class=\"news-list d-grid w-full gap-6\">\r\n            <div class=\"news-box w-full flex flex-column\">\r\n                <div class=\"new-img flex align-items-center justify-content-center overflow-hidden\">\r\n                    <img src=\"/assets/layout/images/new-img-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                </div>\r\n                <h3 class=\"text-white text-xl flex-1\">Proposed Downtown District Ordinance</h3>\r\n                <div class=\"mb-3 flex gap-3 align-items-center\">\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">calendar_month</span> September 16, 2018\r\n                    </div>\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">visibility</span> 240\r\n                    </div>\r\n                </div>\r\n                <p class=\"mb-5 text-sm text-white\">The goal of the proposed ordinance is to create safe venues for\r\n                    customers\r\n                    and to discourage illegal activities. The governing officials of...</p>\r\n\r\n                <button type=\"button\"\r\n                    class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                    Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n                </button>\r\n            </div>\r\n            <div class=\"news-box w-full flex flex-column\">\r\n                <div class=\"new-img flex align-items-center justify-content-center overflow-hidden\">\r\n                    <img src=\"/assets/layout/images/new-img-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                </div>\r\n                <h3 class=\"text-white text-xl flex-1\">Annual Water Quality Report (Gallery Post)</h3>\r\n                <div class=\"mb-3 flex gap-3 align-items-center\">\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">calendar_month</span> September 16, 2018\r\n                    </div>\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">visibility</span> 240\r\n                    </div>\r\n                </div>\r\n                <p class=\"mb-5 text-sm text-white\">The goal of the proposed ordinance is to create safe venues for\r\n                    customers\r\n                    and to discourage illegal activities. The governing officials of...</p>\r\n\r\n                <button type=\"button\"\r\n                    class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                    Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n                </button>\r\n            </div>\r\n            <div class=\"news-box w-full flex flex-column\">\r\n                <div class=\"new-img flex align-items-center justify-content-center overflow-hidden\">\r\n                    <img src=\"/assets/layout/images/new-img-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                </div>\r\n                <h3 class=\"text-white text-xl flex-1\">Waste Industries Garbage Pick Up: Embeds</h3>\r\n                <div class=\"mb-3 flex gap-3 align-items-center\">\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">calendar_month</span> September 16, 2018\r\n                    </div>\r\n                    <div class=\"text flex align-items-center font-medium text-sm gap-1 text-white\">\r\n                        <span class=\"material-symbols-rounded text-xl\">visibility</span> 240\r\n                    </div>\r\n                </div>\r\n                <p class=\"mb-5 text-sm text-white\">The goal of the proposed ordinance is to create safe venues for\r\n                    customers\r\n                    and to discourage illegal activities. The governing officials of...</p>\r\n\r\n                <button type=\"button\"\r\n                    class=\"px-5 p-element p-ripple p-button-rounded p-button p-component h-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                    Learn More <span class=\"material-symbols-rounded\">arrow_right_alt</span>\r\n                </button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--NEWS SEC-->\r\n\r\n<!--NEWS LETTER SEC-->\r\n<section class=\"news-letter-sec relative\">\r\n    <div class=\"news-letter-body relative max-w-1200 w-full mx-auto px-4\">\r\n        <div class=\"news-letter-box-list flex bg-white shadow-1\">\r\n            <div class=\"news-letter-box w-6 p-7 flex flex-column justify-content-center\">\r\n                <h2\r\n                    class=\"line-2 relative mb-5 pb-5 inline-flex w-fit align-items-start text-6xl font-extrabold line-height-1\">\r\n                    Sign up for News & Alerts</h2>\r\n                <p>Stay connected to the City of Riverside by signing up to receive official news and alerts by email!\r\n                </p>\r\n                <div class=\"news-letter-form mt-5 relative flex align-items-center\">\r\n                    <input type=\"text\" pInputText class=\"h-3-3rem border-noround flex-1\"\r\n                        placeholder=\"Your email address\" />\r\n                    <button type=\"button\"\r\n                        class=\"px-6 p-element p-ripple border-noround p-button p-component h-3-3rem w-fit justify-content-center gap-2 line-height-2 bg-yellow-400 text-color font-semibold border-none\">\r\n                        Sign Up\r\n                    </button>\r\n                </div>\r\n            </div>\r\n            <div class=\"news-letter-img w-6 flex align-items-center justify-content-center overflow-hidden\">\r\n                <img src=\"/assets/layout/images/news-letter-img.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--NEWS LETTER SEC-->\r\n\r\n<!--WHAT HAPPNENING SEC-->\r\n<section class=\"what-happning-sec mt-8 relative px-4\">\r\n    <div class=\"what-happning-body relative w-full mx-auto flex bg-orange-50\">\r\n        <div class=\"what-happning-img w-6 flex align-items-center justify-content-center overflow-hidden\">\r\n            <img src=\"/assets/layout/images/whats-happnening-mg.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n        </div>\r\n        <div class=\"what-happning-right w-6 p-8\">\r\n            <div class=\"what-happning-title\">\r\n                <h5 class=\"line-2 m-0 mb-5 pb-5 relative inline-flex w-fit text-yellow-400\">Join the fun in our city!\r\n                </h5>\r\n                <h2 class=\"m-0 mb-6 relative text-6xl line-height-2 font-bold text-color\">What's Happening</h2>\r\n            </div>\r\n            <div class=\"what-happning-list\">\r\n                <div\r\n                    class=\"what-happning-box mb-4 pb-4 flex align-items-center gap-5 border-none border-bottom-1 border-solid surface-border\">\r\n                    <div class=\"wh-img-box flex align-items-center justify-content-center overflow-hidden\">\r\n                        <img src=\"/assets/layout/images/arches-1.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                    </div>\r\n                    <div class=\"wh-cnt-sec flex-1 flex align-items-center gap-6\">\r\n                        <div class=\"wh-cnt flex flex-column\">\r\n                            <h3 class=\"text-xl font-bold\">Heritage 10th Anniversary</h3>\r\n                            <div class=\"flex align-items-center gap-3 flex-wrap\">\r\n                                <div class=\"flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100\">\r\n                                    <span>June 9, 2027</span> - <span>June 18, 2027</span>\r\n                                </div>\r\n                                <div class=\"flex align-items-center gap-2\">All Day at <b>Baker Hall</b></div>\r\n                            </div>\r\n                        </div>\r\n                        <button type=\"button\"\r\n                            class=\"px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold\">\r\n                            Find out more\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div\r\n                    class=\"what-happning-box mb-4 pb-4 flex align-items-center gap-5 border-none border-bottom-1 border-solid surface-border\">\r\n                    <div class=\"wh-img-box flex align-items-center justify-content-center overflow-hidden\">\r\n                        <img src=\"/assets/layout/images/arches-2.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                    </div>\r\n                    <div class=\"wh-cnt-sec flex-1 flex align-items-center gap-6\">\r\n                        <div class=\"wh-cnt flex flex-column\">\r\n                            <h3 class=\"text-xl font-bold\">Along Pines Run</h3>\r\n                            <div class=\"flex align-items-center gap-3 flex-wrap\">\r\n                                <div class=\"flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100\">\r\n                                    <span>July 22, 2027</span>\r\n                                </div>\r\n                                <div class=\"flex align-items-center gap-2\">12:00 am at <b>Boulder City Council</b></div>\r\n                            </div>\r\n                        </div>\r\n                        <button type=\"button\"\r\n                            class=\"px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold\">\r\n                            Find out more\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                <div class=\"what-happning-box flex align-items-center gap-5\">\r\n                    <div class=\"wh-img-box flex align-items-center justify-content-center overflow-hidden\">\r\n                        <img src=\"/assets/layout/images/arches-3.jpg\" alt=\"\" class=\"w-full h-full object-fit-cover\">\r\n                    </div>\r\n                    <div class=\"wh-cnt-sec flex-1 flex align-items-center gap-6\">\r\n                        <div class=\"wh-cnt flex flex-column\">\r\n                            <h3 class=\"text-xl font-bold\">Touch A Truck</h3>\r\n                            <div class=\"flex align-items-center gap-3 flex-wrap\">\r\n                                <div class=\"flex w-fit align-items-center gap-2 py-2 p-3 bg-orange-100\">\r\n                                    <span>June 9, 2027</span> - <span>June 18, 2027</span>\r\n                                </div>\r\n                                <div class=\"flex align-items-center gap-2\">9:00 am - 5:00 pm at <b>Baker Hall</b></div>\r\n                            </div>\r\n                        </div>\r\n                        <button type=\"button\"\r\n                            class=\"px-4 p-element p-ripple p-button p-component border-noround p-button-outlined h-3-3rem w-fit justify-content-center gap-2 line-height-2 text-color font-semibold\">\r\n                            Find out more\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--WHAT HAPPNENING SEC-->\r\n\r\n<!--FOOTER SEC-->\r\n<section class=\"footer-sec relative secondary-bg-color\">\r\n    <div class=\"footer-body relative max-w-1200 w-full mx-auto px-4\">\r\n        <div class=\"top-footer py-7 grid mt-0\">\r\n            <div class=\"col-12 lg:col-5 md:col-5\">\r\n                <h3 class=\"line flex mb-5 pb-3 relative text-white text-xl\">Riverside City Hall</h3>\r\n                <ul class=\"p-0 m-0 flex flex-column gap-4\">\r\n                    <li class=\"flex w-full\">\r\n                        <a href=\"#\" class=\"flex w-full text-white\">8353 Sierra Avenue • Riverside, CA 91335</a>\r\n                    </li>\r\n                    <li class=\"flex w-full\">\r\n                        <a href=\"#\" class=\"flex w-full text-white\">Phone: (*************</a>\r\n                    </li>\r\n                    <li class=\"flex w-full\">\r\n                        <a class=\"flex w-full text-white\">Monday - Thursday, 8:00 am - 6:00 pm</a>\r\n                    </li>\r\n                    <li class=\"flex w-full\">\r\n                        <a href=\"\" class=\"flex w-full text-white\">Email : info&#64;asardigital.com</a>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n            <div class=\"col-12 lg:col-7 md:col-7 py-0\">\r\n                <div class=\"grid mt-0\">\r\n                    <div class=\"col-12 lg:col-4 md:col-4\">\r\n                        <h3 class=\"line flex mb-5 pb-3 relative text-white text-xl\">Services</h3>\r\n                        <ul class=\"p-0 m-0 flex flex-column gap-4\">\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"#\" class=\"flex w-full text-white\">Driver & ID Services</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"#\" class=\"flex w-full text-white\">Vehicle & Plate Services</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a class=\"flex w-full text-white\">Business Services</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"\" class=\"flex w-full text-white\">Senior Services</a>\r\n                            </li>\r\n                        </ul>\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-4 md:col-4\">\r\n                        <h3 class=\"line flex mb-5 pb-3 relative text-white text-xl\">Useful Links</h3>\r\n                        <ul class=\"p-0 m-0 flex flex-column gap-4\">\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"#\" class=\"flex w-full text-white\">Frequently Asked Questions</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"#\" class=\"flex w-full text-white\">Latest News</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a class=\"flex w-full text-white\">Community</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"\" class=\"flex w-full text-white\">Help Center</a>\r\n                            </li>\r\n                            <li class=\"flex w-full\">\r\n                                <a href=\"\" class=\"flex w-full text-white\">Careers</a>\r\n                            </li>\r\n                        </ul>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"middle-footer py-5 w-full flex align-items-center justify-content-center bg-blue-100\">\r\n\r\n        </div>\r\n        <div class=\"bottom-footer py-5 w-full flex flex-column align-items-center justify-content-center\">\r\n            <ul class=\"m-0 mb-3 p-0 flex align-items-center justify-content-center gap-3 list-none\">\r\n                <li><a href=\"#\" class=\"inline-flex w-fit text-white\">Term & Conditions</a></li>\r\n                <li><a href=\"#\" class=\"inline-flex w-fit text-white\">Privacy Policy</a></li>\r\n            </ul>\r\n            <p class=\"m-0 text-white\">© 2025 SNJYA. All rights reserved.</p>\r\n        </div>\r\n    </div>\r\n</section>\r\n<!--FOOTER SEC-->"], "mappings": ";;;;;AAUA,OAAM,MAAOA,aAAa;EAOxBC,YACUC,aAA4B,EAC5BC,QAAmB,EACnBC,KAAqB,EACrBC,UAA0B;IAH1B,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IAPpB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,SAAS,GAAU,EAAE;EAOjB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,GAAG,IAAI,CAACL,KAAK,CAACM,QAAQ,CAACC,IAAI,CAAC,eAAe,CAAC;IAC9DC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACJ,aAAa,CAAC;IAElD;IACA,MAAMK,aAAa,GAAG,IAAI,CAACT,UAAU,CAACU,sBAAsB,CAAC,IAAI,CAACN,aAAa,CAACO,IAAI,EAAE,oBAAoB,CAAC;IAC3G,IAAIF,aAAa,EAAEG,MAAM,EAAE;MACzB,IAAI,CAACX,IAAI,GAAGQ,aAAa,CAAC,CAAC,CAAC,CAACI,IAAI,EAAEC,GAAG,IAAI,EAAE;IAC9C;IAEA;IACA,MAAMC,aAAa,GAAG,IAAI,CAACf,UAAU,CAACU,sBAAsB,CAAC,IAAI,CAACN,aAAa,CAACO,IAAI,EAAE,oBAAoB,CAAC;IAC3G,IAAII,aAAa,EAAEH,MAAM,EAAE;MACzB,IAAI,CAACV,SAAS,GAAGa,aAAa,CAAC,CAAC,CAAC,CAACC,SAAS,IAAI,EAAE;IACnD;IAEA,IAAI,CAACC,OAAO,GAAG,IAAI,CAAClB,KAAK,CAACM,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC;IAClDC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACS,OAAO,CAAC;IAC1CV,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,IAAI,CAACP,IAAI,CAAC;IAC/BM,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACN,SAAS,CAAC;IAE1C;IACA,MAAMgB,IAAI,GAAG,wDAAwD;IACrE,MAAMC,IAAI,GAAG,IAAI,CAACrB,QAAQ,CAACsB,aAAa,CAAC,MAAM,CAAC;IAChD,IAAI,CAACtB,QAAQ,CAACuB,YAAY,CAACF,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC;IACpD,IAAI,CAACrB,QAAQ,CAACuB,YAAY,CAACF,IAAI,EAAE,KAAK,EAAE,YAAY,CAAC;IACrD,IAAI,CAACrB,QAAQ,CAACuB,YAAY,CAACF,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC;IACpD,IAAI,CAACrB,QAAQ,CAACuB,YAAY,CAACF,IAAI,EAAE,MAAM,EAAED,IAAI,CAAC;IAE9C;IACA,IAAI,CAACpB,QAAQ,CAACwB,WAAW,CAACC,QAAQ,CAACC,IAAI,EAAEL,IAAI,CAAC;IAE9C,IAAI,CAACtB,aAAa,CAAC4B,MAAM,GAAG,IAAI,CAAC,CAAC;EACpC;EAEAC,WAAWA,CAAA;IACT;IACA,MAAMP,IAAI,GAAGI,QAAQ,CAACI,cAAc,CAAC,YAAY,CAAC;IAClD,IAAIR,IAAI,EAAE;MACRA,IAAI,CAACS,MAAM,EAAE;IACf;EACF;;;uBAvDWjC,aAAa,EAAAkC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAI,SAAA,GAAAJ,EAAA,CAAAC,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAN,EAAA,CAAAC,iBAAA,CAAAM,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAb1C,aAAa;MAAA2C,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPlBf,EAFR,CAAAiB,cAAA,gBAA4D,aACiC,aACY;UAC7FjB,EAAA,CAAAkB,SAAA,aAA8F;UAClGlB,EAAA,CAAAmB,YAAA,EAAM;UAIuBnB,EAH7B,CAAAiB,cAAA,aAAkG,aACvE,YAC+B,YAC7B,WACqE;UAAAjB,EAAA,CAAAoB,MAAA,iBACzE;UAAApB,EAAA,CAAAiB,cAAA,eAAuD;UAAAjB,EAAA,CAAAoB,MAAA,uBAAe;UAAWpB,EAAX,CAAAmB,YAAA,EAAO,EAAI,EAAK;UAClFnB,EAAjB,CAAAiB,cAAA,aAAiB,YACqE;UAAAjB,EAAA,CAAAoB,MAAA,iBAC9E;UAAApB,EAAA,CAAAiB,cAAA,eAAuD;UAAAjB,EAAA,CAAAoB,MAAA,sBAAc;UAAWpB,EAAX,CAAAmB,YAAA,EAAO,EAAI,EAAK;UAC5EnB,EAAjB,CAAAiB,cAAA,aAAiB,YACqE;UAAAjB,EAAA,CAAAoB,MAAA,eAAO;UAAApB,EAAA,CAAAiB,cAAA,eAChC;UAAAjB,EAAA,CAAAoB,MAAA,oBAAY;UAAWpB,EAAX,CAAAmB,YAAA,EAAO,EAAI,EAAK;UACxEnB,EAAjB,CAAAiB,cAAA,aAAiB,YACqE;UAAAjB,EAAA,CAAAoB,MAAA,kBAC9E;UAAApB,EAAA,CAAAiB,cAAA,eAAuD;UAAAjB,EAAA,CAAAoB,MAAA,uBAAe;UAEtFpB,EAFsF,CAAAmB,YAAA,EAAO,EAAI,EAAK,EAC7F,EACH;UAGFnB,EAFJ,CAAAiB,cAAA,kBACmL,gBAChI;UAAAjB,EAAA,CAAAoB,MAAA,aAAK;UAAApB,EAAA,CAAAmB,YAAA,EAAO;UAACnB,EAAA,CAAAoB,MAAA,eAChE;UAGZpB,EAHY,CAAAmB,YAAA,EAAS,EACP,EACJ,EACD;UAKLnB,EADJ,CAAAiB,cAAA,mBAAqC,eACuB;UACpDjB,EAAA,CAAAkB,SAAA,eAAkD;UAE9ClB,EADJ,CAAAiB,cAAA,eAA6F,cACxD;UAAAjB,EAAA,CAAAoB,MAAA,oCAA4B;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAClEnB,EAAA,CAAAiB,cAAA,cAAsE;UAAAjB,EAAA,CAAAoB,MAAA,kBAAU;UAAApB,EAAA,CAAAkB,SAAA,UAAI;UAAAlB,EAAA,CAAAoB,MAAA,mBAAW;UAAApB,EAAA,CAAAkB,SAAA,UAAI;UAAAlB,EAAA,CAAAoB,MAAA,qBAC3F;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACbnB,EAAA,CAAAiB,cAAA,aAAmC;UAAAjB,EAAA,CAAAoB,MAAA,uFACnB;UAAApB,EAAA,CAAAkB,SAAA,UAAI;UAAAlB,EAAA,CAAAoB,MAAA,oEAA4D;UAG5FpB,EAH4F,CAAAmB,YAAA,EAAI,EAClF,EACJ,EACA;UAMFnB,EAFR,CAAAiB,cAAA,mBAAqC,eACuC,eACA;UAChEjB,EAAA,CAAAkB,SAAA,eAA+E;UAC/ElB,EAAA,CAAAiB,cAAA,cAAiC;UAAAjB,EAAA,CAAAoB,MAAA,4BAAoB;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAEDnB,EADzD,CAAAiB,cAAA,cAAiE,cACR,aAAoC;UAAAjB,EAAA,CAAAoB,MAAA,mBAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACpGnB,EAAA,CAAAoB,MAAA,4CAA8B;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACcnB,EAArD,CAAAiB,cAAA,cAAqD,aAAoC;UAAAjB,EAAA,CAAAoB,MAAA,mBAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACpGnB,EAAA,CAAAoB,MAAA,8CAAgC;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACYnB,EAArD,CAAAiB,cAAA,cAAqD,aAAoC;UAAAjB,EAAA,CAAAoB,MAAA,mBAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACpGnB,EAAA,CAAAoB,MAAA,sBAAa;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAC+BnB,EAArD,CAAAiB,cAAA,cAAqD,aAAoC;UAAAjB,EAAA,CAAAoB,MAAA,mBAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACpGnB,EAAA,CAAAoB,MAAA,iDAAmC;UAC3CpB,EAD2C,CAAAmB,YAAA,EAAK,EAC3C;UACLnB,EAAA,CAAAiB,cAAA,kBACqK;UACjKjB,EAAA,CAAAoB,MAAA,8BAAqB;UAAApB,EAAA,CAAAiB,cAAA,gBAAuC;UAAAjB,EAAA,CAAAoB,MAAA,uBAAe;UAEnFpB,EAFmF,CAAAmB,YAAA,EAAO,EAC7E,EACP;UACNnB,EAAA,CAAAiB,cAAA,eAAoE;UAChEjB,EAAA,CAAAkB,SAAA,eAAgF;UAChFlB,EAAA,CAAAiB,cAAA,cAAiC;UAAAjB,EAAA,CAAAoB,MAAA,gCAAwB;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAELnB,EADzD,CAAAiB,cAAA,cAAiE,cACR,aAAoC;UAAAjB,EAAA,CAAAoB,MAAA,mBAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACpGnB,EAAA,CAAAoB,MAAA,4CAA8B;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACcnB,EAArD,CAAAiB,cAAA,cAAqD,aAAoC;UAAAjB,EAAA,CAAAoB,MAAA,mBAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACpGnB,EAAA,CAAAoB,MAAA,8CAAgC;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACYnB,EAArD,CAAAiB,cAAA,cAAqD,aAAoC;UAAAjB,EAAA,CAAAoB,MAAA,mBAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACpGnB,EAAA,CAAAoB,MAAA,sBAAa;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAC+BnB,EAArD,CAAAiB,cAAA,cAAqD,aAAoC;UAAAjB,EAAA,CAAAoB,MAAA,mBAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACpGnB,EAAA,CAAAoB,MAAA,iDAAmC;UAC3CpB,EAD2C,CAAAmB,YAAA,EAAK,EAC3C;UACLnB,EAAA,CAAAiB,cAAA,kBACqK;UACjKjB,EAAA,CAAAoB,MAAA,kCAAyB;UAAApB,EAAA,CAAAiB,cAAA,gBAAuC;UAAAjB,EAAA,CAAAoB,MAAA,uBAAe;UAEvFpB,EAFuF,CAAAmB,YAAA,EAAO,EACjF,EACP;UACNnB,EAAA,CAAAiB,cAAA,eAAoE;UAChEjB,EAAA,CAAAkB,SAAA,gBAAqF;UACrFlB,EAAA,CAAAiB,cAAA,eAAiC;UAAAjB,EAAA,CAAAoB,MAAA,0BAAiB;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAEEnB,EADzD,CAAAiB,cAAA,eAAiE,eACR,cAAoC;UAAAjB,EAAA,CAAAoB,MAAA,oBAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACpGnB,EAAA,CAAAoB,MAAA,6CAA8B;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACcnB,EAArD,CAAAiB,cAAA,eAAqD,cAAoC;UAAAjB,EAAA,CAAAoB,MAAA,oBAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACpGnB,EAAA,CAAAoB,MAAA,+CAAgC;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACYnB,EAArD,CAAAiB,cAAA,eAAqD,cAAoC;UAAAjB,EAAA,CAAAoB,MAAA,oBAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACpGnB,EAAA,CAAAoB,MAAA,uBAAa;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAC+BnB,EAArD,CAAAiB,cAAA,eAAqD,cAAoC;UAAAjB,EAAA,CAAAoB,MAAA,oBAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACpGnB,EAAA,CAAAoB,MAAA,kDAAmC;UAC3CpB,EAD2C,CAAAmB,YAAA,EAAK,EAC3C;UACLnB,EAAA,CAAAiB,cAAA,mBACqK;UACjKjB,EAAA,CAAAoB,MAAA,4BAAkB;UAAApB,EAAA,CAAAiB,cAAA,iBAAuC;UAAAjB,EAAA,CAAAoB,MAAA,wBAAe;UAEhFpB,EAFgF,CAAAmB,YAAA,EAAO,EAC1E,EACP;UACNnB,EAAA,CAAAiB,cAAA,gBAAoE;UAChEjB,EAAA,CAAAkB,SAAA,gBAAmF;UACnFlB,EAAA,CAAAiB,cAAA,eAAiC;UAAAjB,EAAA,CAAAoB,MAAA,wBAAe;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAEInB,EADzD,CAAAiB,cAAA,eAAiE,eACR,cAAoC;UAAAjB,EAAA,CAAAoB,MAAA,oBAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACpGnB,EAAA,CAAAoB,MAAA,6CAA8B;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACcnB,EAArD,CAAAiB,cAAA,eAAqD,cAAoC;UAAAjB,EAAA,CAAAoB,MAAA,oBAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACpGnB,EAAA,CAAAoB,MAAA,+CAAgC;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACYnB,EAArD,CAAAiB,cAAA,eAAqD,cAAoC;UAAAjB,EAAA,CAAAoB,MAAA,oBAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACpGnB,EAAA,CAAAoB,MAAA,uBAAa;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAC+BnB,EAArD,CAAAiB,cAAA,eAAqD,cAAoC;UAAAjB,EAAA,CAAAoB,MAAA,oBAAW;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACpGnB,EAAA,CAAAoB,MAAA,kDAAmC;UAC3CpB,EAD2C,CAAAmB,YAAA,EAAK,EAC3C;UACLnB,EAAA,CAAAiB,cAAA,mBACqK;UACjKjB,EAAA,CAAAoB,MAAA,0BAAgB;UAAApB,EAAA,CAAAiB,cAAA,iBAAuC;UAAAjB,EAAA,CAAAoB,MAAA,wBAAe;UAItFpB,EAJsF,CAAAmB,YAAA,EAAO,EACxE,EACP,EACJ,EACA;UAMFnB,EAFR,CAAAiB,cAAA,oBAAoC,gBAC+C,gBACxB;UAC/CjB,EAAA,CAAAkB,SAAA,gBAA0E;UAC9ElB,EAAA,CAAAmB,YAAA,EAAM;UAEFnB,EADJ,CAAAiB,cAAA,gBAA8F,eACzD;UAAAjB,EAAA,CAAAoB,MAAA,qCAA4B;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAClEnB,EAAA,CAAAiB,cAAA,eAAoF;UAAAjB,EAAA,CAAAoB,MAAA,qCAEpF;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACLnB,EAAA,CAAAiB,cAAA,cAAyC;UAAAjB,EAAA,CAAAoB,MAAA,uTAG2D;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UAExGnB,EAAA,CAAAiB,cAAA,mBACgL;UAC5KjB,EAAA,CAAAoB,MAAA,qBAAW;UAAApB,EAAA,CAAAiB,cAAA,iBAAuC;UAAAjB,EAAA,CAAAoB,MAAA,wBAAe;UAIjFpB,EAJiF,CAAAmB,YAAA,EAAO,EACnE,EACP,EACJ,EACA;UAOEnB,EAHZ,CAAAiB,cAAA,oBAA2C,gBAC+C,gBACJ,gBACjD;UACrBjB,EAAA,CAAAkB,SAAA,gBAAyE;UAC7ElB,EAAA,CAAAmB,YAAA,EAAM;UAEFnB,EADJ,CAAAiB,cAAA,gBAAqB,eACgB;UAAAjB,EAAA,CAAAoB,MAAA,4BAAmB;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACzDnB,EAAA,CAAAiB,cAAA,eAAoF;UAAAjB,EAAA,CAAAoB,MAAA,8DAEpD;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACrCnB,EAAA,CAAAiB,cAAA,cAAyC;UAAAjB,EAAA,CAAAoB,MAAA,uTAI2D;UAE5GpB,EAF4G,CAAAmB,YAAA,EAAI,EACtG,EACJ;UAGEnB,EAFR,CAAAiB,cAAA,gBAAyE,gBAC3C,eACmD;UAAAjB,EAAA,CAAAoB,MAAA,YAAG;UAAApB,EAAA,CAAAiB,cAAA,gBACnB;UAAAjB,EAAA,CAAAoB,MAAA,UAAC;UAAMpB,EAAN,CAAAmB,YAAA,EAAM,EAAK;UACrEnB,EAAA,CAAAiB,cAAA,eAAsB;UAAAjB,EAAA,CAAAoB,MAAA,2CAAkC;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAC7DnB,EAAA,CAAAiB,cAAA,cAAgC;UAAAjB,EAAA,CAAAoB,MAAA,oGACN;UAC9BpB,EAD8B,CAAAmB,YAAA,EAAI,EAC5B;UAEFnB,EADJ,CAAAiB,cAAA,gBAA0B,eACoD;UAAAjB,EAAA,CAAAoB,MAAA,WAAE;UAAApB,EAAA,CAAAiB,cAAA,gBACnB;UAAAjB,EAAA,CAAAoB,MAAA,WAAE;UAAMpB,EAAN,CAAAmB,YAAA,EAAM,EAAK;UACtEnB,EAAA,CAAAiB,cAAA,eAAsB;UAAAjB,EAAA,CAAAoB,MAAA,8CAAqC;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAChEnB,EAAA,CAAAiB,cAAA,cAAgC;UAAAjB,EAAA,CAAAoB,MAAA,oGACN;UAI1CpB,EAJ0C,CAAAmB,YAAA,EAAI,EAC5B,EACJ,EACJ,EACA;UAOEnB,EAHZ,CAAAiB,cAAA,oBAA2C,gBAC+C,gBACf,eACe;UAAAjB,EAAA,CAAAoB,MAAA,qBAAY;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAC/FnB,EAAA,CAAAiB,cAAA,eAA4G;UACxGjB,EAAA,CAAAoB,MAAA,gDAAsC;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAG3CnB,EADJ,CAAAiB,cAAA,gBAA6C,gBAEwE;UAC7GjB,EAAA,CAAAkB,SAAA,gBAA2E;UAC3ElB,EAAA,CAAAiB,cAAA,eAAsC;UAAAjB,EAAA,CAAAoB,MAAA,0BAAiB;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAC5DnB,EAAA,CAAAiB,cAAA,cAAmB;UAAAjB,EAAA,CAAAoB,MAAA,0GACI;UAC3BpB,EAD2B,CAAAmB,YAAA,EAAI,EACzB;UACNnB,EAAA,CAAAiB,cAAA,gBAC6H;UACzHjB,EAAA,CAAAkB,SAAA,gBAA4E;UAC5ElB,EAAA,CAAAiB,cAAA,eAAsC;UAAAjB,EAAA,CAAAoB,MAAA,yBAAgB;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAC3DnB,EAAA,CAAAiB,cAAA,cAAmB;UAAAjB,EAAA,CAAAoB,MAAA,0GACI;UAC3BpB,EAD2B,CAAAmB,YAAA,EAAI,EACzB;UACNnB,EAAA,CAAAiB,cAAA,gBAC6H;UACzHjB,EAAA,CAAAkB,SAAA,gBAAgF;UAChFlB,EAAA,CAAAiB,cAAA,eAAsC;UAAAjB,EAAA,CAAAoB,MAAA,uBAAc;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACzDnB,EAAA,CAAAiB,cAAA,cAAmB;UAAAjB,EAAA,CAAAoB,MAAA,0GACI;UAC3BpB,EAD2B,CAAAmB,YAAA,EAAI,EACzB;UACNnB,EAAA,CAAAiB,cAAA,gBAA+F;UAC3FjB,EAAA,CAAAkB,SAAA,gBAA8E;UAC9ElB,EAAA,CAAAiB,cAAA,eAAsC;UAAAjB,EAAA,CAAAoB,MAAA,gBAAO;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAClDnB,EAAA,CAAAiB,cAAA,cAAmB;UAAAjB,EAAA,CAAAoB,MAAA,0GACI;UAC3BpB,EAD2B,CAAAmB,YAAA,EAAI,EACzB;UACNnB,EAAA,CAAAiB,cAAA,gBAC6I;UACzIjB,EAAA,CAAAkB,SAAA,gBAA8E;UAC9ElB,EAAA,CAAAiB,cAAA,eAAsC;UAAAjB,EAAA,CAAAoB,MAAA,yBAAgB;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAC3DnB,EAAA,CAAAiB,cAAA,cAAmB;UAAAjB,EAAA,CAAAoB,MAAA,0GACI;UAC3BpB,EAD2B,CAAAmB,YAAA,EAAI,EACzB;UACNnB,EAAA,CAAAiB,cAAA,gBAC6I;UACzIjB,EAAA,CAAAkB,SAAA,gBAAiF;UACjFlB,EAAA,CAAAiB,cAAA,eAAsC;UAAAjB,EAAA,CAAAoB,MAAA,sCAA6B;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACxEnB,EAAA,CAAAiB,cAAA,cAAmB;UAAAjB,EAAA,CAAAoB,MAAA,0GACI;UAGnCpB,EAHmC,CAAAmB,YAAA,EAAI,EACzB,EACJ,EACJ;UACNnB,EAAA,CAAAkB,SAAA,gBAAsC;UAE9ClB,EADI,CAAAmB,YAAA,EAAM,EACA;UAOEnB,EAHZ,CAAAiB,cAAA,oBAAgE,gBACG,gBACnC,eACsD;UAAAjB,EAAA,CAAAoB,MAAA,yDAC9D;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACjBnB,EAAA,CAAAiB,cAAA,eAA0E;UAAAjB,EAAA,CAAAoB,MAAA,oBAAW;UACzFpB,EADyF,CAAAmB,YAAA,EAAK,EACxF;UAGEnB,EAFR,CAAAiB,cAAA,gBAA2C,gBACO,gBAC0C;UAChFjB,EAAA,CAAAkB,SAAA,gBAA6F;UACjGlB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAiB,cAAA,eAAsC;UAAAjB,EAAA,CAAAoB,MAAA,6CAAoC;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAGvEnB,EAFR,CAAAiB,cAAA,gBAAgD,gBACmC,iBAC5B;UAAAjB,EAAA,CAAAoB,MAAA,uBAAc;UAAApB,EAAA,CAAAmB,YAAA,EAAO;UAACnB,EAAA,CAAAoB,MAAA,6BACzE;UAAApB,EAAA,CAAAmB,YAAA,EAAM;UAEFnB,EADJ,CAAAiB,cAAA,gBAA+E,iBAC5B;UAAAjB,EAAA,CAAAoB,MAAA,mBAAU;UAAApB,EAAA,CAAAmB,YAAA,EAAO;UAACnB,EAAA,CAAAoB,MAAA,cACrE;UACJpB,EADI,CAAAmB,YAAA,EAAM,EACJ;UACNnB,EAAA,CAAAiB,cAAA,cAAmC;UAAAjB,EAAA,CAAAoB,MAAA,sJAEoC;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UAE3EnB,EAAA,CAAAiB,cAAA,mBACqL;UACjLjB,EAAA,CAAAoB,MAAA,qBAAW;UAAApB,EAAA,CAAAiB,cAAA,iBAAuC;UAAAjB,EAAA,CAAAoB,MAAA,wBAAe;UAEzEpB,EAFyE,CAAAmB,YAAA,EAAO,EACnE,EACP;UAEFnB,EADJ,CAAAiB,cAAA,gBAA8C,gBAC0C;UAChFjB,EAAA,CAAAkB,SAAA,gBAA6F;UACjGlB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAiB,cAAA,eAAsC;UAAAjB,EAAA,CAAAoB,MAAA,mDAA0C;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAG7EnB,EAFR,CAAAiB,cAAA,gBAAgD,gBACmC,iBAC5B;UAAAjB,EAAA,CAAAoB,MAAA,uBAAc;UAAApB,EAAA,CAAAmB,YAAA,EAAO;UAACnB,EAAA,CAAAoB,MAAA,6BACzE;UAAApB,EAAA,CAAAmB,YAAA,EAAM;UAEFnB,EADJ,CAAAiB,cAAA,gBAA+E,iBAC5B;UAAAjB,EAAA,CAAAoB,MAAA,mBAAU;UAAApB,EAAA,CAAAmB,YAAA,EAAO;UAACnB,EAAA,CAAAoB,MAAA,cACrE;UACJpB,EADI,CAAAmB,YAAA,EAAM,EACJ;UACNnB,EAAA,CAAAiB,cAAA,cAAmC;UAAAjB,EAAA,CAAAoB,MAAA,sJAEoC;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UAE3EnB,EAAA,CAAAiB,cAAA,mBACqL;UACjLjB,EAAA,CAAAoB,MAAA,qBAAW;UAAApB,EAAA,CAAAiB,cAAA,iBAAuC;UAAAjB,EAAA,CAAAoB,MAAA,wBAAe;UAEzEpB,EAFyE,CAAAmB,YAAA,EAAO,EACnE,EACP;UAEFnB,EADJ,CAAAiB,cAAA,gBAA8C,gBAC0C;UAChFjB,EAAA,CAAAkB,SAAA,gBAA6F;UACjGlB,EAAA,CAAAmB,YAAA,EAAM;UACNnB,EAAA,CAAAiB,cAAA,eAAsC;UAAAjB,EAAA,CAAAoB,MAAA,iDAAwC;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAG3EnB,EAFR,CAAAiB,cAAA,gBAAgD,gBACmC,iBAC5B;UAAAjB,EAAA,CAAAoB,MAAA,uBAAc;UAAApB,EAAA,CAAAmB,YAAA,EAAO;UAACnB,EAAA,CAAAoB,MAAA,6BACzE;UAAApB,EAAA,CAAAmB,YAAA,EAAM;UAEFnB,EADJ,CAAAiB,cAAA,gBAA+E,iBAC5B;UAAAjB,EAAA,CAAAoB,MAAA,mBAAU;UAAApB,EAAA,CAAAmB,YAAA,EAAO;UAACnB,EAAA,CAAAoB,MAAA,cACrE;UACJpB,EADI,CAAAmB,YAAA,EAAM,EACJ;UACNnB,EAAA,CAAAiB,cAAA,cAAmC;UAAAjB,EAAA,CAAAoB,MAAA,sJAEoC;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UAE3EnB,EAAA,CAAAiB,cAAA,mBACqL;UACjLjB,EAAA,CAAAoB,MAAA,qBAAW;UAAApB,EAAA,CAAAiB,cAAA,iBAAuC;UAAAjB,EAAA,CAAAoB,MAAA,wBAAe;UAKrFpB,EALqF,CAAAmB,YAAA,EAAO,EACnE,EACP,EACJ,EACJ,EACA;UAQMnB,EAJhB,CAAAiB,cAAA,oBAA0C,gBACgC,gBACT,gBACwB,eAEuC;UAC5GjB,EAAA,CAAAoB,MAAA,mCAAyB;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAClCnB,EAAA,CAAAiB,cAAA,UAAG;UAAAjB,EAAA,CAAAoB,MAAA,6GACH;UAAApB,EAAA,CAAAmB,YAAA,EAAI;UACJnB,EAAA,CAAAiB,cAAA,gBAAoE;UAChEjB,EAAA,CAAAkB,SAAA,kBACuC;UACvClB,EAAA,CAAAiB,cAAA,mBACqL;UACjLjB,EAAA,CAAAoB,MAAA,kBACJ;UAERpB,EAFQ,CAAAmB,YAAA,EAAS,EACP,EACJ;UACNnB,EAAA,CAAAiB,cAAA,gBAAgG;UAC5FjB,EAAA,CAAAkB,SAAA,gBAAmG;UAInHlB,EAHY,CAAAmB,YAAA,EAAM,EACJ,EACJ,EACA;UAMFnB,EAFR,CAAAiB,cAAA,oBAAsD,iBACwB,iBAC4B;UAC9FjB,EAAA,CAAAkB,SAAA,iBAAuG;UAC3GlB,EAAA,CAAAmB,YAAA,EAAM;UAGEnB,EAFR,CAAAiB,cAAA,iBAAyC,iBACJ,gBAC+C;UAAAjB,EAAA,CAAAoB,MAAA,mCAC5E;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UACLnB,EAAA,CAAAiB,cAAA,gBAA0E;UAAAjB,EAAA,CAAAoB,MAAA,yBAAgB;UAC9FpB,EAD8F,CAAAmB,YAAA,EAAK,EAC7F;UAIEnB,EAHR,CAAAiB,cAAA,iBAAgC,iBAEkG,iBACnC;UACnFjB,EAAA,CAAAkB,SAAA,iBAA4F;UAChGlB,EAAA,CAAAmB,YAAA,EAAM;UAGEnB,EAFR,CAAAiB,cAAA,iBAA6D,iBACpB,gBACH;UAAAjB,EAAA,CAAAoB,MAAA,kCAAyB;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAGpDnB,EAFR,CAAAiB,cAAA,iBAAqD,iBACuB,aAC9D;UAAAjB,EAAA,CAAAoB,MAAA,qBAAY;UAAApB,EAAA,CAAAmB,YAAA,EAAO;UAACnB,EAAA,CAAAoB,MAAA,YAAE;UAAApB,EAAA,CAAAiB,cAAA,aAAM;UAAAjB,EAAA,CAAAoB,MAAA,sBAAa;UACnDpB,EADmD,CAAAmB,YAAA,EAAO,EACpD;UACNnB,EAAA,CAAAiB,cAAA,iBAA2C;UAAAjB,EAAA,CAAAoB,MAAA,oBAAW;UAAApB,EAAA,CAAAiB,cAAA,UAAG;UAAAjB,EAAA,CAAAoB,MAAA,mBAAU;UAE3EpB,EAF2E,CAAAmB,YAAA,EAAI,EAAM,EAC3E,EACJ;UACNnB,EAAA,CAAAiB,cAAA,oBAC6K;UACzKjB,EAAA,CAAAoB,MAAA,wBACJ;UAERpB,EAFQ,CAAAmB,YAAA,EAAS,EACP,EACJ;UAGFnB,EAFJ,CAAAiB,cAAA,iBAC8H,iBACnC;UACnFjB,EAAA,CAAAkB,SAAA,iBAA4F;UAChGlB,EAAA,CAAAmB,YAAA,EAAM;UAGEnB,EAFR,CAAAiB,cAAA,iBAA6D,iBACpB,gBACH;UAAAjB,EAAA,CAAAoB,MAAA,wBAAe;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAG1CnB,EAFR,CAAAiB,cAAA,iBAAqD,iBACuB,aAC9D;UAAAjB,EAAA,CAAAoB,MAAA,sBAAa;UACvBpB,EADuB,CAAAmB,YAAA,EAAO,EACxB;UACNnB,EAAA,CAAAiB,cAAA,iBAA2C;UAAAjB,EAAA,CAAAoB,MAAA,qBAAY;UAAApB,EAAA,CAAAiB,cAAA,UAAG;UAAAjB,EAAA,CAAAoB,MAAA,6BAAoB;UAEtFpB,EAFsF,CAAAmB,YAAA,EAAI,EAAM,EACtF,EACJ;UACNnB,EAAA,CAAAiB,cAAA,oBAC6K;UACzKjB,EAAA,CAAAoB,MAAA,wBACJ;UAERpB,EAFQ,CAAAmB,YAAA,EAAS,EACP,EACJ;UAEFnB,EADJ,CAAAiB,cAAA,iBAA6D,iBAC8B;UACnFjB,EAAA,CAAAkB,SAAA,iBAA4F;UAChGlB,EAAA,CAAAmB,YAAA,EAAM;UAGEnB,EAFR,CAAAiB,cAAA,iBAA6D,iBACpB,gBACH;UAAAjB,EAAA,CAAAoB,MAAA,sBAAa;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAGxCnB,EAFR,CAAAiB,cAAA,iBAAqD,iBACuB,aAC9D;UAAAjB,EAAA,CAAAoB,MAAA,qBAAY;UAAApB,EAAA,CAAAmB,YAAA,EAAO;UAACnB,EAAA,CAAAoB,MAAA,YAAE;UAAApB,EAAA,CAAAiB,cAAA,aAAM;UAAAjB,EAAA,CAAAoB,MAAA,sBAAa;UACnDpB,EADmD,CAAAmB,YAAA,EAAO,EACpD;UACNnB,EAAA,CAAAiB,cAAA,iBAA2C;UAAAjB,EAAA,CAAAoB,MAAA,8BAAqB;UAAApB,EAAA,CAAAiB,cAAA,UAAG;UAAAjB,EAAA,CAAAoB,MAAA,mBAAU;UAErFpB,EAFqF,CAAAmB,YAAA,EAAI,EAAM,EACrF,EACJ;UACNnB,EAAA,CAAAiB,cAAA,oBAC6K;UACzKjB,EAAA,CAAAoB,MAAA,wBACJ;UAMxBpB,EANwB,CAAAmB,YAAA,EAAS,EACP,EACJ,EACJ,EACJ,EACJ,EACA;UAQMnB,EAJhB,CAAAiB,cAAA,qBAAwD,iBACa,iBACtB,iBACG,gBAC0B;UAAAjB,EAAA,CAAAoB,MAAA,4BAAmB;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAG5EnB,EAFR,CAAAiB,cAAA,gBAA2C,gBACf,eACuB;UAAAjB,EAAA,CAAAoB,MAAA,sDAAwC;UACvFpB,EADuF,CAAAmB,YAAA,EAAI,EACtF;UAEDnB,EADJ,CAAAiB,cAAA,gBAAwB,eACuB;UAAAjB,EAAA,CAAAoB,MAAA,8BAAqB;UACpEpB,EADoE,CAAAmB,YAAA,EAAI,EACnE;UAEDnB,EADJ,CAAAiB,cAAA,gBAAwB,eACc;UAAAjB,EAAA,CAAAoB,MAAA,6CAAoC;UAC1EpB,EAD0E,CAAAmB,YAAA,EAAI,EACzE;UAEDnB,EADJ,CAAAiB,cAAA,gBAAwB,eACsB;UAAAjB,EAAA,CAAAoB,MAAA,qCAAgC;UAGtFpB,EAHsF,CAAAmB,YAAA,EAAI,EAC7E,EACJ,EACH;UAIMnB,EAHZ,CAAAiB,cAAA,iBAA2C,iBAChB,iBACmB,gBAC0B;UAAAjB,EAAA,CAAAoB,MAAA,iBAAQ;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAGjEnB,EAFR,CAAAiB,cAAA,gBAA2C,gBACf,eACuB;UAAAjB,EAAA,CAAAoB,MAAA,6BAAoB;UACnEpB,EADmE,CAAAmB,YAAA,EAAI,EAClE;UAEDnB,EADJ,CAAAiB,cAAA,gBAAwB,eACuB;UAAAjB,EAAA,CAAAoB,MAAA,iCAAwB;UACvEpB,EADuE,CAAAmB,YAAA,EAAI,EACtE;UAEDnB,EADJ,CAAAiB,cAAA,gBAAwB,eACc;UAAAjB,EAAA,CAAAoB,MAAA,0BAAiB;UACvDpB,EADuD,CAAAmB,YAAA,EAAI,EACtD;UAEDnB,EADJ,CAAAiB,cAAA,gBAAwB,eACsB;UAAAjB,EAAA,CAAAoB,MAAA,wBAAe;UAGrEpB,EAHqE,CAAAmB,YAAA,EAAI,EAC5D,EACJ,EACH;UAEFnB,EADJ,CAAAiB,cAAA,iBAAsC,gBAC0B;UAAAjB,EAAA,CAAAoB,MAAA,qBAAY;UAAApB,EAAA,CAAAmB,YAAA,EAAK;UAGrEnB,EAFR,CAAAiB,cAAA,gBAA2C,gBACf,eACuB;UAAAjB,EAAA,CAAAoB,MAAA,mCAA0B;UACzEpB,EADyE,CAAAmB,YAAA,EAAI,EACxE;UAEDnB,EADJ,CAAAiB,cAAA,gBAAwB,eACuB;UAAAjB,EAAA,CAAAoB,MAAA,oBAAW;UAC1DpB,EAD0D,CAAAmB,YAAA,EAAI,EACzD;UAEDnB,EADJ,CAAAiB,cAAA,gBAAwB,eACc;UAAAjB,EAAA,CAAAoB,MAAA,kBAAS;UAC/CpB,EAD+C,CAAAmB,YAAA,EAAI,EAC9C;UAEDnB,EADJ,CAAAiB,cAAA,gBAAwB,eACsB;UAAAjB,EAAA,CAAAoB,MAAA,oBAAW;UACzDpB,EADyD,CAAAmB,YAAA,EAAI,EACxD;UAEDnB,EADJ,CAAAiB,cAAA,gBAAwB,eACsB;UAAAjB,EAAA,CAAAoB,MAAA,gBAAO;UAMzEpB,EANyE,CAAAmB,YAAA,EAAI,EACpD,EACJ,EACH,EACJ,EACJ,EACJ;UACNnB,EAAA,CAAAkB,SAAA,iBAEM;UAGMlB,EAFZ,CAAAiB,cAAA,iBAAkG,gBACN,WAChF,eAAiD;UAAAjB,EAAA,CAAAoB,MAAA,0BAAiB;UAAIpB,EAAJ,CAAAmB,YAAA,EAAI,EAAK;UAC3EnB,EAAJ,CAAAiB,cAAA,WAAI,eAAiD;UAAAjB,EAAA,CAAAoB,MAAA,uBAAc;UACvEpB,EADuE,CAAAmB,YAAA,EAAI,EAAK,EAC3E;UACLnB,EAAA,CAAAiB,cAAA,eAA0B;UAAAjB,EAAA,CAAAoB,MAAA,gDAAkC;UAGxEpB,EAHwE,CAAAmB,YAAA,EAAI,EAC9D,EACJ,EACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}