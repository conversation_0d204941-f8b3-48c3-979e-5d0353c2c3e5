{"ast": null, "code": "import { inject } from '@angular/core';\nimport { of } from 'rxjs';\nimport { ContentVendorService } from './services/content-vendor.service';\nexport const contentResolver = route => {\n  const contentVendor = inject(ContentVendorService);\n  // Get slug from route data or params\n  const slug = route?.data['slug'];\n  if (slug) {\n    return contentVendor.getContentBySlug(slug);\n  } else {\n    return of(null);\n  }\n};", "map": {"version": 3, "names": ["inject", "of", "ContentVendorService", "contentResolver", "route", "contentVendor", "slug", "data", "getContentBySlug"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\core\\content-resolver.ts"], "sourcesContent": ["import { inject } from '@angular/core';\r\nimport { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';\r\nimport { Observable, of } from 'rxjs';\r\nimport { ContentVendorService } from './services/content-vendor.service';\r\n\r\nexport const contentResolver: ResolveFn<any> = (\r\n  route: ActivatedRouteSnapshot\r\n) => {\r\n  const contentVendor = inject(ContentVendorService);\r\n  // Get slug from route data or params\r\n  const slug = route?.data['slug'];\r\n  if (slug) {\r\n    return contentVendor.getContentBySlug(slug) as Observable<any>;\r\n  } else {\r\n    return of(null);\r\n  }\r\n};\r\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,eAAe;AAEtC,SAAqBC,EAAE,QAAQ,MAAM;AACrC,SAASC,oBAAoB,QAAQ,mCAAmC;AAExE,OAAO,MAAMC,eAAe,GAC1BC,KAA6B,IAC3B;EACF,MAAMC,aAAa,GAAGL,MAAM,CAACE,oBAAoB,CAAC;EAClD;EACA,MAAMI,IAAI,GAAGF,KAAK,EAAEG,IAAI,CAAC,MAAM,CAAC;EAChC,IAAID,IAAI,EAAE;IACR,OAAOD,aAAa,CAACG,gBAAgB,CAACF,IAAI,CAAoB;EAChE,CAAC,MAAM;IACL,OAAOL,EAAE,CAAC,IAAI,CAAC;EACjB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}