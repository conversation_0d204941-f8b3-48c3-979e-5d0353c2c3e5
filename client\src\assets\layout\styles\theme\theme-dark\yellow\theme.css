:root {
  font-family: "Inter var", sans-serif;
  font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  font-variation-settings: normal;
  --font-family:"Inter var", sans-serif;
  --font-feature-settings: "cv02","cv03","cv04","cv11";
  --surface-a:rgba(255, 255, 255, 0.2);
  --surface-b:rgba(255, 255, 255, 0.25);
  --surface-c:rgba(255, 255, 255, 0.03);
  --surface-d:rgba(255, 255, 255, 0.1);
  --surface-e:rgba(255, 255, 255, 0.2);
  --surface-f:rgba(255, 255, 255, 0.2);
  --text-color:#FFFFFF;
  --text-color-secondary:#C8CCD8;
  --primary-color:#EEE500;
  --primary-color-text:#0A061A;
  --surface-0: #0a061a;
  --surface-50: #231f31;
  --surface-100: #3b3848;
  --surface-200: #54515f;
  --surface-300: #6c6a76;
  --surface-400: #85838d;
  --surface-500: #9d9ba3;
  --surface-600: #b6b4ba;
  --surface-700: #cecdd1;
  --surface-800: #e7e6e8;
  --surface-900: #ffffff;
  --gray-50: #e7e6e8;
  --gray-100: #cecdd1;
  --gray-200: #b6b4ba;
  --gray-300: #9d9ba3;
  --gray-400: #85838d;
  --gray-500: #6c6a76;
  --gray-600: #54515f;
  --gray-700: #3b3848;
  --gray-800: #231f31;
  --gray-900: #0a061a;
  --content-padding:1.143rem;
  --inline-spacing:0.5rem;
  --border-radius:6px;
  --surface-ground:linear-gradient(180deg, #2E323F 0%, #0A061A 100%);
  --surface-section:#2E323F;
  --surface-card:rgba(255, 255, 255, 0.05);
  --surface-overlay:linear-gradient(180deg, #2E323F 0%, #0A061A 100%);
  --surface-border:rgba(255, 255, 255, 0.15);
  --surface-hover:rgba(255, 255, 255, 0.05);
  --focus-ring: 0 none;
  --maskbg: rgba(0, 0, 0, 0.2);
  --highlight-bg: #EEE500;
  --highlight-text-color: #0A061A;
  color-scheme: dark;
}

:root {
  --blue-50:#f4fafe;
  --blue-100:#cae6fc;
  --blue-200:#a0d2fa;
  --blue-300:#75bef8;
  --blue-400:#4baaf5;
  --blue-500:#2196f3;
  --blue-600:#1c80cf;
  --blue-700:#1769aa;
  --blue-800:#125386;
  --blue-900:#0d3c61;
  --green-50:#f6fbf6;
  --green-100:#d4ecd5;
  --green-200:#b2ddb4;
  --green-300:#90cd93;
  --green-400:#6ebe71;
  --green-500:#4caf50;
  --green-600:#419544;
  --green-700:#357b38;
  --green-800:#2a602c;
  --green-900:#1e4620;
  --yellow-50:#fffcf5;
  --yellow-100:#fef0cd;
  --yellow-200:#fde4a5;
  --yellow-300:#fdd87d;
  --yellow-400:#fccc55;
  --yellow-500:#fbc02d;
  --yellow-600:#d5a326;
  --yellow-700:#b08620;
  --yellow-800:#8a6a19;
  --yellow-900:#644d12;
  --cyan-50:#f2fcfd;
  --cyan-100:#c2eff5;
  --cyan-200:#91e2ed;
  --cyan-300:#61d5e4;
  --cyan-400:#30c9dc;
  --cyan-500:#00bcd4;
  --cyan-600:#00a0b4;
  --cyan-700:#008494;
  --cyan-800:#006775;
  --cyan-900:#004b55;
  --pink-50:#fef4f7;
  --pink-100:#fac9da;
  --pink-200:#f69ebc;
  --pink-300:#f1749e;
  --pink-400:#ed4981;
  --pink-500:#e91e63;
  --pink-600:#c61a54;
  --pink-700:#a31545;
  --pink-800:#801136;
  --pink-900:#5d0c28;
  --indigo-50:#f5f6fb;
  --indigo-100:#d1d5ed;
  --indigo-200:#acb4df;
  --indigo-300:#8893d1;
  --indigo-400:#6372c3;
  --indigo-500:#3f51b5;
  --indigo-600:#36459a;
  --indigo-700:#2c397f;
  --indigo-800:#232d64;
  --indigo-900:#192048;
  --teal-50:#f2faf9;
  --teal-100:#c2e6e2;
  --teal-200:#91d2cc;
  --teal-300:#61beb5;
  --teal-400:#30aa9f;
  --teal-500:#009688;
  --teal-600:#008074;
  --teal-700:#00695f;
  --teal-800:#00534b;
  --teal-900:#003c36;
  --orange-50:#fff8f2;
  --orange-100:#fde0c2;
  --orange-200:#fbc791;
  --orange-300:#f9ae61;
  --orange-400:#f79530;
  --orange-500:#f57c00;
  --orange-600:#d06900;
  --orange-700:#ac5700;
  --orange-800:#874400;
  --orange-900:#623200;
  --bluegray-50:#f7f9f9;
  --bluegray-100:#d9e0e3;
  --bluegray-200:#bbc7cd;
  --bluegray-300:#9caeb7;
  --bluegray-400:#7e96a1;
  --bluegray-500:#607d8b;
  --bluegray-600:#526a76;
  --bluegray-700:#435861;
  --bluegray-800:#35454c;
  --bluegray-900:#263238;
  --purple-50:#faf4fb;
  --purple-100:#e7cbec;
  --purple-200:#d4a2dd;
  --purple-300:#c279ce;
  --purple-400:#af50bf;
  --purple-500:#9c27b0;
  --purple-600:#852196;
  --purple-700:#6d1b7b;
  --purple-800:#561561;
  --purple-900:#3e1046;
  --red-50:#fff5f5;
  --red-100:#ffd0ce;
  --red-200:#ffaca7;
  --red-300:#ff8780;
  --red-400:#ff6259;
  --red-500:#ff3d32;
  --red-600:#d9342b;
  --red-700:#b32b23;
  --red-800:#8c221c;
  --red-900:#661814;
  --primary-50:#fefef2;
  --primary-100:#fbf9c2;
  --primary-200:#f8f491;
  --primary-300:#f4ef61;
  --primary-400:#f1ea30;
  --primary-500:#eee500;
  --primary-600:#cac300;
  --primary-700:#a7a000;
  --primary-800:#837e00;
  --primary-900:#5f5c00;
}

.p-editor-container .p-editor-toolbar {
  background: transparent;
  border-top-right-radius: 6px;
  border-top-left-radius: 6px;
}
.p-editor-container .p-editor-toolbar.ql-snow {
  border: 1px solid rgba(255, 255, 255, 0.15);
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-stroke {
  stroke: #C8CCD8;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-fill {
  fill: #C8CCD8;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label {
  border: 0 none;
  color: #C8CCD8;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label:hover {
  color: #FFFFFF;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label:hover .ql-stroke {
  stroke: #FFFFFF;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker .ql-picker-label:hover .ql-fill {
  fill: #FFFFFF;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  color: #FFFFFF;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-stroke {
  stroke: #FFFFFF;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-label .ql-fill {
  fill: #FFFFFF;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  background: #0a061a;
  border: 0 none;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  border-radius: 6px;
  padding: 0.286rem;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item {
  color: #FFFFFF;
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item:hover {
  color: #FFFFFF;
  background: rgba(255, 255, 255, 0.05);
}
.p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded:not(.ql-icon-picker) .ql-picker-item {
  padding: 0.429rem 0.286rem;
}
.p-editor-container .p-editor-content {
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 6px;
}
.p-editor-container .p-editor-content.ql-snow {
  border: 1px solid rgba(255, 255, 255, 0.15);
}
.p-editor-container .p-editor-content .ql-editor {
  background: rgba(255, 255, 255, 0.05);
  color: #FFFFFF;
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 6px;
}
.p-editor-container .ql-snow.ql-toolbar button:hover,
.p-editor-container .ql-snow.ql-toolbar button:focus {
  color: #FFFFFF;
}
.p-editor-container .ql-snow.ql-toolbar button:hover .ql-stroke,
.p-editor-container .ql-snow.ql-toolbar button:focus .ql-stroke {
  stroke: #FFFFFF;
}
.p-editor-container .ql-snow.ql-toolbar button:hover .ql-fill,
.p-editor-container .ql-snow.ql-toolbar button:focus .ql-fill {
  fill: #FFFFFF;
}
.p-editor-container .ql-snow.ql-toolbar button.ql-active,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-label.ql-active,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-item.ql-selected {
  color: #EEE500;
}
.p-editor-container .ql-snow.ql-toolbar button.ql-active .ql-stroke,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-stroke,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-stroke {
  stroke: #EEE500;
}
.p-editor-container .ql-snow.ql-toolbar button.ql-active .ql-fill,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-fill,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-fill {
  fill: #EEE500;
}
.p-editor-container .ql-snow.ql-toolbar button.ql-active .ql-picker-label,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-label.ql-active .ql-picker-label,
.p-editor-container .ql-snow.ql-toolbar .ql-picker-item.ql-selected .ql-picker-label {
  color: #EEE500;
}

@layer primeng {
  * {
    box-sizing: border-box;
  }

  .p-component {
    font-family: var(--font-family);
    font-feature-settings: var(--font-feature-settings, normal);
    font-size: 1rem;
    font-weight: normal;
  }

  .p-component-overlay {
    background-color: rgba(0, 0, 0, 0.2);
    transition-duration: 0.2s;
  }

  .p-disabled, .p-component:disabled {
    opacity: 0.4;
  }

  .p-error {
    color: #FC6161;
  }

  .p-text-secondary {
    color: #C8CCD8;
  }

  .pi {
    font-size: 1rem;
  }

  .p-icon {
    width: 1rem;
    height: 1rem;
  }

  .p-link {
    font-family: var(--font-family);
    font-feature-settings: var(--font-feature-settings, normal);
    font-size: 1rem;
    border-radius: 6px;
  }
  .p-link:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }

  .p-component-overlay-enter {
    animation: p-component-overlay-enter-animation 150ms forwards;
  }

  .p-component-overlay-leave {
    animation: p-component-overlay-leave-animation 150ms forwards;
  }

  @keyframes p-component-overlay-enter-animation {
    from {
      background-color: transparent;
    }
    to {
      background-color: var(--maskbg);
    }
  }
  @keyframes p-component-overlay-leave-animation {
    from {
      background-color: var(--maskbg);
    }
    to {
      background-color: transparent;
    }
  }

  .p-autocomplete .p-autocomplete-loader {
    right: 0.571rem;
  }
  .p-autocomplete.p-autocomplete-dd .p-autocomplete-loader {
    right: 2.857rem;
  }
  .p-autocomplete:not(.p-disabled):hover .p-autocomplete-multiple-container {
    border-color: transparent;
  }
  .p-autocomplete:not(.p-disabled).p-focus .p-autocomplete-multiple-container {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
    border-color: #EEE500;
  }
  .p-autocomplete .p-autocomplete-multiple-container {
    padding: 0.2145rem 0.571rem;
    gap: 0.5rem;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-input-token {
    padding: 0.2145rem 0;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-input-token input {
    font-family: var(--font-family);
    font-feature-settings: var(--font-feature-settings, normal);
    font-size: 1rem;
    color: #FFFFFF;
    padding: 0;
    margin: 0;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-token {
    padding: 0.2145rem 0.571rem;
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
    border-radius: 16px;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-token .p-autocomplete-token-icon {
    margin-left: 0.5rem;
  }
  .p-autocomplete .p-autocomplete-multiple-container .p-autocomplete-token.p-focus {
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
  }
  .p-autocomplete.p-invalid.p-component > .p-inputtext {
    border-color: #FC6161;
  }

  .p-autocomplete-panel {
    background: #0a061a;
    color: #FFFFFF;
    border: 0 none;
    border-radius: 6px;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-autocomplete-panel .p-autocomplete-items {
    padding: 0.286rem;
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item {
    margin: 0;
    padding: 0.429rem 0.286rem;
    border: 0 none;
    color: #FFFFFF;
    background: transparent;
    transition: box-shadow 0.2s;
    border-radius: 4px;
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item.p-highlight {
    color: #0A061A;
    background: #EEE500;
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item.p-highlight.p-focus {
    background: rgba(238, 229, 0, 0.24);
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item:not(.p-highlight):not(.p-disabled).p-focus {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.1);
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item:not(.p-highlight):not(.p-disabled):hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-autocomplete-panel .p-autocomplete-items .p-autocomplete-item-group {
    margin: 0;
    padding: 0.571rem 0.429rem;
    color: #868C9B;
    background: transparent;
    font-weight: 500;
  }

  p-autocomplete.ng-dirty.ng-invalid > .p-autocomplete > .p-inputtext {
    border-color: #FC6161;
  }

  p-autocomplete.p-autocomplete-clearable .p-inputtext {
    padding-right: 2.142rem;
  }
  p-autocomplete.p-autocomplete-clearable .p-autocomplete-clear-icon {
    color: #C8CCD8;
    right: 0.571rem;
  }

  p-autocomplete.p-autocomplete-clearable .p-autocomplete-dd .p-autocomplete-clear-icon {
    color: #C8CCD8;
    right: 2.857rem;
  }

  p-calendar.ng-dirty.ng-invalid > .p-calendar > .p-inputtext {
    border-color: #FC6161;
  }

  .p-calendar:not(.p-calendar-disabled).p-focus > .p-inputtext {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
    border-color: #EEE500;
  }

  .p-datepicker {
    padding: 0.857rem;
    background: transparent;
    color: #FFFFFF;
    border: 1px solid transparent;
    border-radius: 6px;
  }
  .p-datepicker:not(.p-datepicker-inline) {
    background: #0a061a;
    border: 0 none;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-datepicker:not(.p-datepicker-inline) .p-datepicker-header {
    background: transparent;
  }
  .p-datepicker .p-datepicker-header {
    padding: 0 0 0.75rem 0;
    color: #FFFFFF;
    background: transparent;
    font-weight: 400;
    margin: 0;
    border-bottom: 0 none;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-prev,
.p-datepicker .p-datepicker-header .p-datepicker-next {
    width: 2rem;
    height: 2rem;
    color: #C8CCD8;
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-prev:enabled:hover,
.p-datepicker .p-datepicker-header .p-datepicker-next:enabled:hover {
    color: #FFFFFF;
    border-color: transparent;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-datepicker .p-datepicker-header .p-datepicker-prev:focus-visible,
.p-datepicker .p-datepicker-header .p-datepicker-next:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-title {
    line-height: 2rem;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-year,
.p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-month {
    color: #FFFFFF;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
    font-weight: 400;
    padding: 0.357rem;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-year:enabled:hover,
.p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-month:enabled:hover {
    color: #EEE500;
  }
  .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-month {
    margin-right: 0.5rem;
  }
  .p-datepicker table {
    font-size: 1rem;
    margin: 0.5rem 0;
  }
  .p-datepicker table th {
    padding: 0.357rem;
  }
  .p-datepicker table th > span {
    width: 2.571rem;
    height: 2.571rem;
  }
  .p-datepicker table td {
    padding: 0.357rem;
  }
  .p-datepicker table td > span {
    width: 2.571rem;
    height: 2.571rem;
    border-radius: 6px;
    transition: box-shadow 0.2s;
    border: 2px solid transparent;
  }
  .p-datepicker table td > span.p-highlight {
    color: #0A061A;
    background: #EEE500;
  }
  .p-datepicker table td > span:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-datepicker table td.p-datepicker-today > span {
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
    border-color: rgba(255, 255, 255, 0.25);
  }
  .p-datepicker table td.p-datepicker-today > span.p-highlight {
    color: #0A061A;
    background: #EEE500;
  }
  .p-datepicker .p-datepicker-buttonbar {
    padding: 1rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
  }
  .p-datepicker .p-datepicker-buttonbar .p-button {
    width: auto;
  }
  .p-datepicker .p-timepicker {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    padding: 0.5rem;
  }
  .p-datepicker .p-timepicker button {
    width: 2rem;
    height: 2rem;
    color: #C8CCD8;
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .p-datepicker .p-timepicker button:enabled:hover {
    color: #FFFFFF;
    border-color: transparent;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-datepicker .p-timepicker button:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-datepicker .p-timepicker button:last-child {
    margin-top: 0.2em;
  }
  .p-datepicker .p-timepicker span {
    font-size: 1.25rem;
  }
  .p-datepicker .p-timepicker > div {
    padding: 0 0.5rem;
  }
  .p-datepicker.p-datepicker-timeonly .p-timepicker {
    border-top: 0 none;
  }
  .p-datepicker .p-monthpicker {
    margin: 0.5rem 0;
  }
  .p-datepicker .p-monthpicker .p-monthpicker-month {
    padding: 0.357rem;
    transition: box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-datepicker .p-monthpicker .p-monthpicker-month.p-highlight {
    color: #0A061A;
    background: #EEE500;
  }
  .p-datepicker .p-yearpicker {
    margin: 0.5rem 0;
  }
  .p-datepicker .p-yearpicker .p-yearpicker-year {
    padding: 0.357rem;
    transition: box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-datepicker .p-yearpicker .p-yearpicker-year.p-highlight {
    color: #0A061A;
    background: #EEE500;
  }
  .p-datepicker.p-datepicker-multiple-month .p-datepicker-group {
    border-left: 1px solid rgba(255, 255, 255, 0.05);
    padding-right: 0.857rem;
    padding-left: 0.857rem;
    padding-top: 0;
    padding-bottom: 0;
  }
  .p-datepicker.p-datepicker-multiple-month .p-datepicker-group:first-child {
    padding-left: 0;
    border-left: 0 none;
  }
  .p-datepicker.p-datepicker-multiple-month .p-datepicker-group:last-child {
    padding-right: 0;
  }
  .p-datepicker:not(.p-disabled) table td span:not(.p-highlight):not(.p-disabled):hover {
    background: rgba(255, 255, 255, 0.1);
  }
  .p-datepicker:not(.p-disabled) table td span:not(.p-highlight):not(.p-disabled):focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-datepicker:not(.p-disabled) .p-monthpicker .p-monthpicker-month:not(.p-disabled):not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.1);
  }
  .p-datepicker:not(.p-disabled) .p-monthpicker .p-monthpicker-month:not(.p-disabled):focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-datepicker:not(.p-disabled) .p-yearpicker .p-yearpicker-year:not(.p-disabled):not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.1);
  }
  .p-datepicker:not(.p-disabled) .p-yearpicker .p-yearpicker-year:not(.p-disabled):focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }

  p-calendar.p-calendar-clearable .p-inputtext {
    padding-right: 2.142rem;
  }
  p-calendar.p-calendar-clearable .p-calendar-clear-icon {
    color: #C8CCD8;
    right: 0.571rem;
  }

  p-calendar.p-calendar-clearable .p-calendar-w-btn .p-calendar-clear-icon {
    color: #C8CCD8;
    right: 2.857rem;
  }

  @media screen and (max-width: 769px) {
    .p-datepicker table th, .p-datepicker table td {
      padding: 0;
    }
  }
  .p-cascadeselect {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid transparent;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-cascadeselect:not(.p-disabled):hover {
    border-color: transparent;
  }
  .p-cascadeselect:not(.p-disabled).p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
    border-color: #EEE500;
  }
  .p-cascadeselect .p-cascadeselect-label {
    background: transparent;
    border: 0 none;
    padding: 0.429rem 0.571rem;
  }
  .p-cascadeselect .p-cascadeselect-label.p-placeholder {
    color: #868C9B;
  }
  .p-cascadeselect .p-cascadeselect-label:enabled:focus {
    outline: 0 none;
    box-shadow: none;
  }
  .p-cascadeselect .p-cascadeselect-trigger {
    background: transparent;
    color: #C8CCD8;
    width: 2.357rem;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }
  .p-cascadeselect.p-invalid.p-component {
    border-color: #FC6161;
  }

  .p-cascadeselect-panel {
    background: #0a061a;
    color: #FFFFFF;
    border: 0 none;
    border-radius: 6px;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-cascadeselect-panel .p-cascadeselect-items {
    padding: 0.286rem;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item {
    margin: 0;
    border: 0 none;
    color: #FFFFFF;
    background: transparent;
    transition: box-shadow 0.2s;
    border-radius: 4px;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item.p-highlight {
    color: #0A061A;
    background: #EEE500;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item.p-highlight.p-focus {
    background: rgba(238, 229, 0, 0.24);
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item:not(.p-highlight):not(.p-disabled).p-focus {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.1);
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item:not(.p-highlight):not(.p-disabled):hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item .p-cascadeselect-item-content {
    padding: 0.429rem 0.286rem;
  }
  .p-cascadeselect-panel .p-cascadeselect-items .p-cascadeselect-item .p-cascadeselect-group-icon {
    font-size: 0.875rem;
  }

  .p-input-filled .p-cascadeselect {
    background: rgba(255, 255, 255, 0.05);
  }
  .p-input-filled .p-cascadeselect:not(.p-disabled):hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  .p-input-filled .p-cascadeselect:not(.p-disabled).p-focus {
    background-color: rgba(255, 255, 255, 0.05);
  }

  p-cascadeselect.ng-dirty.ng-invalid > .p-cascadeselect {
    border-color: #FC6161;
  }

  p-cascadeselect.p-cascadeselect-clearable .p-cascadeselect-label {
    padding-right: 0.571rem;
  }
  p-cascadeselect.p-cascadeselect-clearable .p-cascadeselect-clear-icon {
    color: #C8CCD8;
    right: 2.357rem;
  }

  .p-overlay-modal .p-cascadeselect-sublist .p-cascadeselect-panel {
    box-shadow: none;
    border-radius: 0;
    padding: 0.25rem 0 0.25rem 0.5rem;
  }
  .p-overlay-modal .p-cascadeselect-item-active > .p-cascadeselect-item-content .p-cascadeselect-group-icon {
    transform: rotate(90deg);
  }

  .p-checkbox {
    width: 20px;
    height: 20px;
  }
  .p-checkbox .p-checkbox-box {
    border: 2px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.05);
    width: 20px;
    height: 20px;
    color: #FFFFFF;
    border-radius: 6px;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
  }
  .p-checkbox .p-checkbox-box .p-checkbox-icon {
    transition-duration: 0.2s;
    color: #0A061A;
    font-size: 14px;
  }
  .p-checkbox .p-checkbox-box .p-icon {
    width: 14px;
    height: 14px;
  }
  .p-checkbox .p-checkbox-box.p-highlight {
    border-color: rgba(255, 255, 255, 0.25);
    background: #EEE500;
  }
  .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
    border-color: transparent;
  }
  .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
    border-color: #EEE500;
  }
  .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-highlight:hover {
    border-color: #beb700;
    background: #beb700;
    color: #0A061A;
  }

  p-checkbox.ng-dirty.ng-invalid > .p-checkbox > .p-checkbox-box {
    border-color: #FC6161;
  }

  .p-input-filled .p-checkbox .p-checkbox-box {
    background-color: rgba(255, 255, 255, 0.05);
  }
  .p-input-filled .p-checkbox .p-checkbox-box.p-highlight {
    background: #EEE500;
  }
  .p-input-filled .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  .p-input-filled .p-checkbox:not(.p-checkbox-disabled) .p-checkbox-box.p-highlight:hover {
    background: #beb700;
  }

  .p-checkbox-label {
    margin-left: 0.5rem;
  }

  .p-highlight .p-checkbox .p-checkbox-box {
    border-color: #0A061A;
  }

  p-tristatecheckbox.ng-dirty.ng-invalid > .p-checkbox > .p-checkbox-box {
    border-color: #FC6161;
  }

  .p-chips:not(.p-disabled):hover .p-chips-multiple-container {
    border-color: transparent;
  }
  .p-chips:not(.p-disabled).p-focus .p-chips-multiple-container {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
    border-color: #EEE500;
  }
  .p-chips .p-chips-multiple-container {
    padding: 0.2145rem 0.571rem;
    gap: 0.5rem;
  }
  .p-chips .p-chips-multiple-container .p-chips-token {
    padding: 0.2145rem 0.571rem;
    margin-right: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
    border-radius: 16px;
  }
  .p-chips .p-chips-multiple-container .p-chips-token.p-focus {
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
  }
  .p-chips .p-chips-multiple-container .p-chips-token .p-chips-token-icon {
    margin-left: 0.5rem;
  }
  .p-chips .p-chips-multiple-container .p-chips-input-token {
    padding: 0.2145rem 0;
  }
  .p-chips .p-chips-multiple-container .p-chips-input-token input {
    font-family: var(--font-family);
    font-feature-settings: var(--font-feature-settings, normal);
    font-size: 1rem;
    color: #FFFFFF;
    padding: 0;
    margin: 0;
  }

  p-chips.ng-dirty.ng-invalid > .p-chips > .p-inputtext {
    border-color: #FC6161;
  }

  p-chips.p-chips-clearable .p-inputtext {
    padding-right: 1.571rem;
  }
  p-chips.p-chips-clearable .p-chips-clear-icon {
    color: #C8CCD8;
    right: 0.571rem;
  }

  .p-colorpicker-preview,
.p-fluid .p-colorpicker-preview.p-inputtext {
    width: 2rem;
    height: 2rem;
  }

  .p-colorpicker-panel {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  .p-colorpicker-panel .p-colorpicker-color-handle,
.p-colorpicker-panel .p-colorpicker-hue-handle {
    border-color: #FFFFFF;
  }

  .p-colorpicker-overlay-panel {
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }

  .p-dropdown {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid transparent;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-dropdown:not(.p-disabled):hover {
    border-color: transparent;
  }
  .p-dropdown:not(.p-disabled).p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
    border-color: #EEE500;
  }
  .p-dropdown.p-dropdown-clearable .p-dropdown-label {
    padding-right: 1.571rem;
  }
  .p-dropdown .p-dropdown-label {
    background: transparent;
    border: 0 none;
  }
  .p-dropdown .p-dropdown-label.p-placeholder {
    color: #868C9B;
  }
  .p-dropdown .p-dropdown-label:focus, .p-dropdown .p-dropdown-label:enabled:focus {
    outline: 0 none;
    box-shadow: none;
  }
  .p-dropdown .p-dropdown-trigger {
    background: transparent;
    color: #C8CCD8;
    width: 2.357rem;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }
  .p-dropdown .p-dropdown-clear-icon {
    color: #C8CCD8;
    right: 2.357rem;
  }
  .p-dropdown.p-invalid.p-component {
    border-color: #FC6161;
  }

  .p-dropdown-panel {
    background: #0a061a;
    color: #FFFFFF;
    border: 0 none;
    border-radius: 6px;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-dropdown-panel .p-dropdown-header {
    padding: 0.429rem 0.286rem;
    border-bottom: 0 none;
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
    margin: 0;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-dropdown-panel .p-dropdown-header .p-dropdown-filter {
    padding-right: 1.571rem;
    margin-right: -1.571rem;
  }
  .p-dropdown-panel .p-dropdown-header .p-dropdown-filter-icon {
    right: 0.571rem;
    color: #C8CCD8;
  }
  .p-dropdown-panel .p-dropdown-items {
    padding: 0.286rem;
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item {
    margin: 0;
    padding: 0.429rem 0.286rem;
    border: 0 none;
    color: #FFFFFF;
    background: transparent;
    transition: box-shadow 0.2s;
    border-radius: 4px;
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight {
    color: #0A061A;
    background: #EEE500;
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight.p-focus {
    background: rgba(238, 229, 0, 0.24);
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item:not(.p-highlight):not(.p-disabled).p-focus {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.1);
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item:not(.p-highlight):not(.p-disabled):hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item-group {
    margin: 0;
    padding: 0.571rem 0.429rem;
    color: #868C9B;
    background: transparent;
    font-weight: 500;
  }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-empty-message {
    padding: 0.429rem 0.286rem;
    color: #FFFFFF;
    background: transparent;
  }

  .p-input-filled .p-dropdown {
    background: rgba(255, 255, 255, 0.05);
  }
  .p-input-filled .p-dropdown:not(.p-disabled):hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  .p-input-filled .p-dropdown:not(.p-disabled).p-focus {
    background-color: rgba(255, 255, 255, 0.05);
  }
  .p-input-filled .p-dropdown:not(.p-disabled).p-focus .p-inputtext {
    background-color: transparent;
  }

  p-dropdown.ng-dirty.ng-invalid > .p-dropdown {
    border-color: #FC6161;
  }

  .p-inputgroup-addon {
    background: rgba(255, 255, 255, 0.2);
    color: #C8CCD8;
    border-top: 1px solid transparent;
    border-left: 1px solid transparent;
    border-bottom: 1px solid transparent;
    padding: 0.429rem 0.571rem;
    min-width: 2.357rem;
  }
  .p-inputgroup-addon:last-child {
    border-right: 1px solid transparent;
  }

  .p-inputgroup > .p-component,
.p-inputgroup > .p-inputwrapper > .p-inputtext,
.p-inputgroup > .p-float-label > .p-component {
    border-radius: 0;
    margin: 0;
  }
  .p-inputgroup > .p-component + .p-inputgroup-addon,
.p-inputgroup > .p-inputwrapper > .p-inputtext + .p-inputgroup-addon,
.p-inputgroup > .p-float-label > .p-component + .p-inputgroup-addon {
    border-left: 0 none;
  }
  .p-inputgroup > .p-component:focus,
.p-inputgroup > .p-inputwrapper > .p-inputtext:focus,
.p-inputgroup > .p-float-label > .p-component:focus {
    z-index: 1;
  }
  .p-inputgroup > .p-component:focus ~ label,
.p-inputgroup > .p-inputwrapper > .p-inputtext:focus ~ label,
.p-inputgroup > .p-float-label > .p-component:focus ~ label {
    z-index: 1;
  }

  .p-inputgroup-addon:first-child,
.p-inputgroup button:first-child,
.p-inputgroup input:first-child,
.p-inputgroup > .p-inputwrapper:first-child > .p-component,
.p-inputgroup > .p-inputwrapper:first-child > .p-component > .p-inputtext {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
  }

  .p-inputgroup .p-float-label:first-child input {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
  }

  .p-inputgroup-addon:last-child,
.p-inputgroup button:last-child,
.p-inputgroup input:last-child,
.p-inputgroup > .p-inputwrapper:last-child > .p-component,
.p-inputgroup > .p-inputwrapper:last-child > .p-component > .p-inputtext {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  .p-inputgroup .p-float-label:last-child input {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  .p-fluid .p-inputgroup .p-button {
    width: auto;
  }
  .p-fluid .p-inputgroup .p-button.p-button-icon-only {
    width: 2.286rem;
  }

  p-inputmask.ng-dirty.ng-invalid > .p-inputtext {
    border-color: #FC6161;
  }

  p-inputmask.p-inputmask-clearable .p-inputtext {
    padding-right: 2.142rem;
  }
  p-inputmask.p-inputmask-clearable .p-inputmask-clear-icon {
    color: #C8CCD8;
    right: 0.571rem;
  }

  p-inputnumber.ng-dirty.ng-invalid > .p-inputnumber > .p-inputtext {
    border-color: #FC6161;
  }

  p-inputnumber.p-inputnumber-clearable .p-inputnumber-input {
    padding-right: 2.142rem;
  }
  p-inputnumber.p-inputnumber-clearable .p-inputnumber-clear-icon {
    color: #C8CCD8;
    right: 0.571rem;
  }

  p-inputnumber.p-inputnumber-clearable .p-inputnumber-buttons-stacked .p-inputnumber-clear-icon {
    right: 2.857rem;
  }
  p-inputnumber.p-inputnumber-clearable .p-inputnumber-buttons-horizontal .p-inputnumber-clear-icon {
    right: 2.857rem;
  }

  .p-inputswitch {
    width: 2.714rem;
    height: 1.429rem;
  }
  .p-inputswitch .p-inputswitch-slider {
    background: rgba(255, 255, 255, 0.05);
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
    border-radius: 12px;
  }
  .p-inputswitch .p-inputswitch-slider:before {
    background: #C8CCD8;
    width: 1.143rem;
    height: 1.143rem;
    left: 0.25rem;
    margin-top: -0.5715rem;
    border-radius: 8px;
    transition-duration: 0.2s;
  }
  .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider:before {
    transform: translateX(1.143rem);
  }
  .p-inputswitch.p-focus .p-inputswitch-slider {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-inputswitch:not(.p-disabled):hover .p-inputswitch-slider {
    background: rgba(255, 255, 255, 0.1);
  }
  .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider {
    background: #EEE500;
  }
  .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider:before {
    background: #FFFFFF;
  }
  .p-inputswitch.p-inputswitch-checked:not(.p-disabled):hover .p-inputswitch-slider {
    background: #d6ce00;
  }

  p-inputswitch.ng-dirty.ng-invalid > .p-inputswitch > .p-inputswitch-slider {
    border-color: #FC6161;
  }

  .p-inputtext {
    font-family: var(--font-family);
    font-feature-settings: var(--font-feature-settings, normal);
    font-size: 1rem;
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
    padding: 0.429rem 0.571rem;
    border: 1px solid transparent;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
    appearance: none;
    border-radius: 6px;
  }
  .p-inputtext:enabled:hover {
    border-color: transparent;
  }
  .p-inputtext:enabled:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
    border-color: #EEE500;
  }
  .p-inputtext.ng-dirty.ng-invalid {
    border-color: #FC6161;
  }
  .p-inputtext.p-inputtext-sm {
    font-size: 0.875rem;
    padding: 0.375375rem 0.499625rem;
  }
  .p-inputtext.p-inputtext-lg {
    font-size: 1.25rem;
    padding: 0.53625rem 0.71375rem;
  }

  .p-float-label > label {
    left: 0.571rem;
    color: #868C9B;
    transition-duration: 0.2s;
  }

  .p-float-label > .ng-invalid.ng-dirty + label {
    color: #FC6161;
  }

  .p-input-icon-left > .p-icon-wrapper.p-icon,
.p-input-icon-left > i:first-of-type {
    left: 0.571rem;
    color: #C8CCD8;
  }

  .p-input-icon-left > .p-inputtext {
    padding-left: 2.142rem;
  }

  .p-input-icon-left.p-float-label > label {
    left: 2.142rem;
  }

  .p-input-icon-right > .p-icon-wrapper,
.p-input-icon-right > i:last-of-type {
    right: 0.571rem;
    color: #C8CCD8;
  }

  .p-input-icon-right > .p-inputtext {
    padding-right: 2.142rem;
  }

  ::-webkit-input-placeholder {
    color: #868C9B;
  }

  :-moz-placeholder {
    color: #868C9B;
  }

  ::-moz-placeholder {
    color: #868C9B;
  }

  :-ms-input-placeholder {
    color: #868C9B;
  }

  .p-input-filled .p-inputtext {
    background-color: rgba(255, 255, 255, 0.05);
  }
  .p-input-filled .p-inputtext:enabled:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  .p-input-filled .p-inputtext:enabled:focus {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .p-inputtext-sm .p-inputtext {
    font-size: 0.875rem;
    padding: 0.375375rem 0.499625rem;
  }

  .p-inputtext-lg .p-inputtext {
    font-size: 1.25rem;
    padding: 0.53625rem 0.71375rem;
  }

  .p-listbox {
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
    border: 1px solid transparent;
    border-radius: 6px;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
  }
  .p-listbox .p-listbox-header {
    padding: 0.429rem 0.286rem;
    border-bottom: 0 none;
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
    margin: 0;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-listbox .p-listbox-header .p-listbox-filter {
    padding-right: 1.571rem;
  }
  .p-listbox .p-listbox-header .p-listbox-filter-icon {
    right: 0.571rem;
    color: #C8CCD8;
  }
  .p-listbox .p-listbox-header .p-checkbox {
    margin-right: 0.5rem;
  }
  .p-listbox .p-listbox-list {
    padding: 0.286rem;
    outline: 0 none;
  }
  .p-listbox .p-listbox-list .p-listbox-item {
    margin: 0;
    padding: 0.429rem 0.286rem;
    border: 0 none;
    color: #FFFFFF;
    transition: box-shadow 0.2s;
    border-radius: 4px;
  }
  .p-listbox .p-listbox-list .p-listbox-item.p-highlight {
    color: #0A061A;
    background: #EEE500;
  }
  .p-listbox .p-listbox-list .p-listbox-item .p-checkbox {
    margin-right: 0.5rem;
  }
  .p-listbox .p-listbox-list .p-listbox-item-group {
    margin: 0;
    padding: 0.571rem 0.429rem;
    color: #868C9B;
    background: transparent;
    font-weight: 500;
  }
  .p-listbox .p-listbox-list .p-listbox-empty-message {
    padding: 0.429rem 0.286rem;
    color: #FFFFFF;
    background: transparent;
  }
  .p-listbox:not(.p-disabled) .p-listbox-item.p-highlight.p-focus {
    background: rgba(238, 229, 0, 0.24);
  }
  .p-listbox:not(.p-disabled) .p-listbox-item:not(.p-highlight):not(.p-disabled).p-focus {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.1);
  }
  .p-listbox:not(.p-disabled) .p-listbox-item:not(.p-highlight):not(.p-disabled):hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-listbox.p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
    border-color: #EEE500;
  }

  p-listbox.ng-dirty.ng-invalid > .p-listbox {
    border-color: #FC6161;
  }

  .p-multiselect {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid transparent;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-multiselect:not(.p-disabled):hover {
    border-color: transparent;
  }
  .p-multiselect:not(.p-disabled).p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
    border-color: #EEE500;
  }
  .p-multiselect .p-multiselect-label {
    padding: 0.429rem 0.571rem;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
  }
  .p-multiselect .p-multiselect-label.p-placeholder {
    color: #868C9B;
  }
  .p-multiselect.p-multiselect-chip .p-multiselect-token {
    padding: 0.2145rem 0.571rem;
    margin-right: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
    border-radius: 16px;
  }
  .p-multiselect.p-multiselect-chip .p-multiselect-token .p-multiselect-token-icon {
    margin-left: 0.5rem;
  }
  .p-multiselect .p-multiselect-trigger {
    background: transparent;
    color: #C8CCD8;
    width: 2.357rem;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  .p-inputwrapper-filled.p-multiselect.p-multiselect-chip .p-multiselect-label {
    padding: 0.2145rem 0.571rem;
  }

  .p-multiselect-clearable .p-multiselect-label-container {
    padding-right: 1.571rem;
  }
  .p-multiselect-clearable .p-multiselect-clear-icon {
    color: #C8CCD8;
    right: 2.357rem;
  }

  .p-multiselect-panel {
    background: #0a061a;
    color: #FFFFFF;
    border: 0 none;
    border-radius: 6px;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-multiselect-panel .p-multiselect-header {
    padding: 0.429rem 0.286rem;
    border-bottom: 0 none;
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
    margin: 0;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-multiselect-panel .p-multiselect-header .p-multiselect-filter-container .p-inputtext {
    padding-right: 1.571rem;
  }
  .p-multiselect-panel .p-multiselect-header .p-multiselect-filter-container .p-multiselect-filter-icon {
    right: 0.571rem;
    color: #C8CCD8;
  }
  .p-multiselect-panel .p-multiselect-header .p-checkbox {
    margin-right: 0.5rem;
  }
  .p-multiselect-panel .p-multiselect-header .p-multiselect-close {
    margin-left: 0.5rem;
    width: 2rem;
    height: 2rem;
    color: #C8CCD8;
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .p-multiselect-panel .p-multiselect-header .p-multiselect-close:enabled:hover {
    color: #FFFFFF;
    border-color: transparent;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-multiselect-panel .p-multiselect-header .p-multiselect-close:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-multiselect-panel .p-multiselect-items {
    padding: 0.286rem;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item {
    margin: 0;
    padding: 0.429rem 0.286rem;
    border: 0 none;
    color: #FFFFFF;
    background: transparent;
    transition: box-shadow 0.2s;
    border-radius: 4px;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight {
    color: #0A061A;
    background: #EEE500;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight.p-focus {
    background: rgba(238, 229, 0, 0.24);
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item:not(.p-highlight):not(.p-disabled).p-focus {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.1);
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item:not(.p-highlight):not(.p-disabled):hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item .p-checkbox {
    margin-right: 0.5rem;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-item-group {
    margin: 0;
    padding: 0.571rem 0.429rem;
    color: #868C9B;
    background: transparent;
    font-weight: 500;
  }
  .p-multiselect-panel .p-multiselect-items .p-multiselect-empty-message {
    padding: 0.429rem 0.286rem;
    color: #FFFFFF;
    background: transparent;
  }

  .p-input-filled .p-multiselect {
    background: rgba(255, 255, 255, 0.05);
  }
  .p-input-filled .p-multiselect:not(.p-disabled):hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  .p-input-filled .p-multiselect:not(.p-disabled).p-focus {
    background-color: rgba(255, 255, 255, 0.05);
  }

  p-multiselect.ng-dirty.ng-invalid > .p-multiselect {
    border-color: #FC6161;
  }

  p-password.ng-invalid.ng-dirty > .p-password > .p-inputtext {
    border-color: #FC6161;
  }

  .p-password-panel {
    padding: 1.143rem;
    background: transparent;
    color: #FFFFFF;
    border: 0 none;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
    border-radius: 6px;
  }
  .p-password-panel .p-password-meter {
    margin-bottom: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
  }
  .p-password-panel .p-password-meter .p-password-strength.weak {
    background: #eb9a9c;
  }
  .p-password-panel .p-password-meter .p-password-strength.medium {
    background: #ffcf91;
  }
  .p-password-panel .p-password-meter .p-password-strength.strong {
    background: #93deac;
  }

  p-password.p-password-clearable .p-password-input {
    padding-right: 2.142rem;
  }
  p-password.p-password-clearable .p-password-clear-icon {
    color: #C8CCD8;
    right: 0.571rem;
  }

  p-password.p-password-clearable.p-password-mask .p-password-input {
    padding-right: 3.713rem;
  }
  p-password.p-password-clearable.p-password-mask .p-password-clear-icon {
    color: #C8CCD8;
    right: 2.142rem;
  }

  .p-radiobutton {
    width: 20px;
    height: 20px;
  }
  .p-radiobutton .p-radiobutton-box {
    border: 2px solid #868C9B;
    background: rgba(255, 255, 255, 0.05);
    width: 20px;
    height: 20px;
    color: #FFFFFF;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
  }
  .p-radiobutton .p-radiobutton-box:not(.p-disabled):not(.p-highlight):hover {
    border-color: transparent;
  }
  .p-radiobutton .p-radiobutton-box:not(.p-disabled).p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
    border-color: #EEE500;
  }
  .p-radiobutton .p-radiobutton-box .p-radiobutton-icon {
    width: 12px;
    height: 12px;
    transition-duration: 0.2s;
    background-color: #0A061A;
  }
  .p-radiobutton .p-radiobutton-box.p-highlight {
    border-color: #EEE500;
    background: #EEE500;
  }
  .p-radiobutton .p-radiobutton-box.p-highlight:not(.p-disabled):hover {
    border-color: #beb700;
    background: #beb700;
    color: #0A061A;
  }

  p-radiobutton.ng-dirty.ng-invalid > .p-radiobutton > .p-radiobutton-box {
    border-color: #FC6161;
  }

  .p-input-filled .p-radiobutton .p-radiobutton-box {
    background-color: rgba(255, 255, 255, 0.05);
  }
  .p-input-filled .p-radiobutton .p-radiobutton-box:not(.p-disabled):hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  .p-input-filled .p-radiobutton .p-radiobutton-box.p-highlight {
    background: #EEE500;
  }
  .p-input-filled .p-radiobutton .p-radiobutton-box.p-highlight:not(.p-disabled):hover {
    background: #beb700;
  }

  .p-radiobutton-label {
    margin-left: 0.5rem;
  }

  .p-highlight .p-radiobutton .p-radiobutton-box {
    border-color: #0A061A;
  }

  .p-rating {
    gap: 0.5rem;
  }
  .p-rating .p-rating-item .p-rating-icon {
    color: #FFFFFF;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
    font-size: 1.286rem;
  }
  .p-rating .p-rating-item .p-rating-icon.p-icon {
    width: 1.286rem;
    height: 1.286rem;
  }
  .p-rating .p-rating-item .p-rating-icon.p-rating-cancel {
    color: #FC6161;
  }
  .p-rating .p-rating-item.p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
    border-color: #EEE500;
  }
  .p-rating .p-rating-item.p-rating-item-active .p-rating-icon {
    color: #EEE500;
  }
  .p-rating:not(.p-disabled):not(.p-readonly) .p-rating-item:hover .p-rating-icon {
    color: #EEE500;
  }
  .p-rating:not(.p-disabled):not(.p-readonly) .p-rating-item:hover .p-rating-icon.p-rating-cancel {
    color: #FC6161;
  }

  .p-highlight .p-rating .p-rating-item.p-rating-item-active .p-rating-icon {
    color: #0A061A;
  }

  .p-selectbutton .p-button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid transparent;
    color: #FFFFFF;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
  }
  .p-selectbutton .p-button .p-button-icon-left,
.p-selectbutton .p-button .p-button-icon-right {
    color: #C8CCD8;
  }
  .p-selectbutton .p-button:not(.p-disabled):not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: transparent;
    color: #FFFFFF;
  }
  .p-selectbutton .p-button:not(.p-disabled):not(.p-highlight):hover .p-button-icon-left,
.p-selectbutton .p-button:not(.p-disabled):not(.p-highlight):hover .p-button-icon-right {
    color: #C8CCD8;
  }
  .p-selectbutton .p-button.p-highlight {
    background: #EEE500;
    border-color: #EEE500;
    color: #0A061A;
  }
  .p-selectbutton .p-button.p-highlight .p-button-icon-left,
.p-selectbutton .p-button.p-highlight .p-button-icon-right {
    color: #0A061A;
  }
  .p-selectbutton .p-button.p-highlight:hover {
    background: #d6ce00;
    border-color: #d6ce00;
    color: #0A061A;
  }
  .p-selectbutton .p-button.p-highlight:hover .p-button-icon-left,
.p-selectbutton .p-button.p-highlight:hover .p-button-icon-right {
    color: #0A061A;
  }

  p-selectbutton.ng-dirty.ng-invalid > .p-selectbutton > .p-button {
    border-color: #FC6161;
  }

  .p-slider {
    background: rgba(255, 255, 255, 0.1);
    border: 0 none;
    border-radius: 6px;
  }
  .p-slider.p-slider-horizontal {
    height: 0.429rem;
  }
  .p-slider.p-slider-horizontal .p-slider-handle {
    margin-top: -0.5715rem;
    margin-left: -0.5715rem;
  }
  .p-slider.p-slider-vertical {
    width: 0.429rem;
  }
  .p-slider.p-slider-vertical .p-slider-handle {
    margin-left: -0.5715rem;
    margin-bottom: -0.5715rem;
  }
  .p-slider .p-slider-handle {
    height: 1.143rem;
    width: 1.143rem;
    background: #ffffff;
    border: 4px solid #EEE500;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
  }
  .p-slider .p-slider-handle:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-slider .p-slider-range {
    background: #EEE500;
  }
  .p-slider:not(.p-disabled) .p-slider-handle:hover {
    background: #EEE500;
    border-color: #EEE500;
  }
  .p-slider.p-slider-animate.p-slider-horizontal .p-slider-handle {
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, left 0.2s;
  }
  .p-slider.p-slider-animate.p-slider-horizontal .p-slider-range {
    transition: width 0.2s;
  }
  .p-slider.p-slider-animate.p-slider-vertical .p-slider-handle {
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s, bottom 0.2s;
  }
  .p-slider.p-slider-animate.p-slider-vertical .p-slider-range {
    transition: height 0.2s;
  }

  .p-togglebutton.p-button {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid transparent;
    color: #FFFFFF;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
  }
  .p-togglebutton.p-button .p-button-icon-left,
.p-togglebutton.p-button .p-button-icon-right {
    color: #C8CCD8;
  }
  .p-togglebutton.p-button:not(.p-disabled):not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: transparent;
    color: #FFFFFF;
  }
  .p-togglebutton.p-button:not(.p-disabled):not(.p-highlight):hover .p-button-icon-left,
.p-togglebutton.p-button:not(.p-disabled):not(.p-highlight):hover .p-button-icon-right {
    color: #C8CCD8;
  }
  .p-togglebutton.p-button.p-highlight {
    background: #EEE500;
    border-color: #EEE500;
    color: #0A061A;
  }
  .p-togglebutton.p-button.p-highlight .p-button-icon-left,
.p-togglebutton.p-button.p-highlight .p-button-icon-right {
    color: #0A061A;
  }
  .p-togglebutton.p-button.p-highlight:hover {
    background: #d6ce00;
    border-color: #d6ce00;
    color: #0A061A;
  }
  .p-togglebutton.p-button.p-highlight:hover .p-button-icon-left,
.p-togglebutton.p-button.p-highlight:hover .p-button-icon-right {
    color: #0A061A;
  }

  p-togglebutton.ng-dirty.ng-invalid > .p-togglebutton.p-button {
    border-color: #FC6161;
  }

  .p-treeselect {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid transparent;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-treeselect:not(.p-disabled):hover {
    border-color: transparent;
  }
  .p-treeselect:not(.p-disabled).p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
    border-color: #EEE500;
  }
  .p-treeselect .p-treeselect-label {
    padding: 0.429rem 0.571rem;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
  }
  .p-treeselect .p-treeselect-label.p-placeholder {
    color: #868C9B;
  }
  .p-treeselect.p-treeselect-chip .p-treeselect-token {
    padding: 0.2145rem 0.571rem;
    margin-right: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
    border-radius: 16px;
  }
  .p-treeselect .p-treeselect-trigger {
    background: transparent;
    color: #C8CCD8;
    width: 2.357rem;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  p-treeselect.ng-invalid.ng-dirty > .p-treeselect {
    border-color: #FC6161;
  }

  .p-inputwrapper-filled .p-treeselect.p-treeselect-chip .p-treeselect-label {
    padding: 0.2145rem 0.571rem;
  }

  .p-treeselect-panel {
    background: #0a061a;
    color: #FFFFFF;
    border: 0 none;
    border-radius: 6px;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
  }
  .p-treeselect-panel .p-treeselect-header {
    padding: 0.429rem 0.286rem;
    border-bottom: 0 none;
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
    margin: 0;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-filter-container {
    margin-right: 0.5rem;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-filter-container .p-treeselect-filter {
    padding-right: 1.571rem;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-filter-container .p-treeselect-filter-icon {
    right: 0.571rem;
    color: #C8CCD8;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-filter-container.p-treeselect-clearable-filter .p-treeselect-filter {
    padding-right: 3.142rem;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-filter-container.p-treeselect-clearable-filter .p-treeselect-filter-clear-icon {
    right: 2.142rem;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-close {
    width: 2rem;
    height: 2rem;
    color: #C8CCD8;
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-close:enabled:hover {
    color: #FFFFFF;
    border-color: transparent;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-treeselect-panel .p-treeselect-header .p-treeselect-close:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-treeselect-panel .p-treeselect-items-wrapper .p-tree {
    border: 0 none;
  }
  .p-treeselect-panel .p-treeselect-items-wrapper .p-treeselect-empty-message {
    padding: 0.429rem 0.286rem;
    color: #FFFFFF;
    background: transparent;
  }

  .p-input-filled .p-treeselect {
    background: rgba(255, 255, 255, 0.05);
  }
  .p-input-filled .p-treeselect:not(.p-disabled):hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
  .p-input-filled .p-treeselect:not(.p-disabled).p-focus {
    background-color: rgba(255, 255, 255, 0.05);
  }

  p-treeselect.p-treeselect-clearable .p-treeselect-label-container {
    padding-right: 1.571rem;
  }
  p-treeselect.p-treeselect-clearable .p-treeselect-clear-icon {
    color: #C8CCD8;
    right: 2.286rem;
  }

  .p-button {
    color: #0A061A;
    background: #EEE500;
    border: 1px solid #EEE500;
    padding: 0.429rem 0.571rem;
    font-size: 1rem;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-button:not(:disabled):hover {
    background: #d6ce00;
    color: #0A061A;
    border-color: #d6ce00;
  }
  .p-button:not(:disabled):active {
    background: #beb700;
    color: #0A061A;
    border-color: #beb700;
  }
  .p-button.p-button-outlined {
    background-color: transparent;
    color: #EEE500;
    border: 1px solid;
  }
  .p-button.p-button-outlined:not(:disabled):hover {
    background: rgba(238, 229, 0, 0.04);
    color: #EEE500;
    border: 1px solid;
  }
  .p-button.p-button-outlined:not(:disabled):active {
    background: rgba(238, 229, 0, 0.16);
    color: #EEE500;
    border: 1px solid;
  }
  .p-button.p-button-outlined.p-button-plain {
    color: #C8CCD8;
    border-color: #C8CCD8;
  }
  .p-button.p-button-outlined.p-button-plain:not(:disabled):hover {
    background: rgba(255, 255, 255, 0.05);
    color: #C8CCD8;
  }
  .p-button.p-button-outlined.p-button-plain:not(:disabled):active {
    background: rgba(255, 255, 255, 0.16);
    color: #C8CCD8;
  }
  .p-button.p-button-text {
    background-color: transparent;
    color: #EEE500;
    border-color: transparent;
  }
  .p-button.p-button-text:not(:disabled):hover {
    background: rgba(238, 229, 0, 0.04);
    color: #EEE500;
    border-color: transparent;
  }
  .p-button.p-button-text:not(:disabled):active {
    background: rgba(238, 229, 0, 0.16);
    color: #EEE500;
    border-color: transparent;
  }
  .p-button.p-button-text.p-button-plain {
    color: #C8CCD8;
  }
  .p-button.p-button-text.p-button-plain:not(:disabled):hover {
    background: rgba(255, 255, 255, 0.05);
    color: #C8CCD8;
  }
  .p-button.p-button-text.p-button-plain:not(:disabled):active {
    background: rgba(255, 255, 255, 0.16);
    color: #C8CCD8;
  }
  .p-button:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-button .p-button-label {
    transition-duration: 0.2s;
  }
  .p-button .p-button-icon-left {
    margin-right: 0.5rem;
  }
  .p-button .p-button-icon-right {
    margin-left: 0.5rem;
  }
  .p-button .p-button-icon-bottom {
    margin-top: 0.5rem;
  }
  .p-button .p-button-icon-top {
    margin-bottom: 0.5rem;
  }
  .p-button .p-badge {
    margin-left: 0.5rem;
    min-width: 1rem;
    height: 1rem;
    line-height: 1rem;
    color: #EEE500;
    background-color: #0A061A;
  }
  .p-button.p-button-raised {
    box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
  }
  .p-button.p-button-rounded {
    border-radius: 2rem;
  }
  .p-button.p-button-icon-only {
    width: 2.286rem;
    padding: 0.429rem 0;
  }
  .p-button.p-button-icon-only .p-button-icon-left,
.p-button.p-button-icon-only .p-button-icon-right {
    margin: 0;
  }
  .p-button.p-button-icon-only.p-button-rounded {
    border-radius: 50%;
    height: 2.286rem;
  }
  .p-button.p-button-sm {
    font-size: 0.875rem;
    padding: 0.375375rem 0.499625rem;
  }
  .p-button.p-button-sm .p-button-icon {
    font-size: 0.875rem;
  }
  .p-button.p-button-lg {
    font-size: 1.25rem;
    padding: 0.53625rem 0.71375rem;
  }
  .p-button.p-button-lg .p-button-icon {
    font-size: 1.25rem;
  }
  .p-button.p-button-loading-label-only .p-button-label {
    margin-left: 0.5rem;
  }
  .p-button.p-button-loading-label-only .p-button-loading-icon {
    margin-right: 0;
  }

  .p-fluid .p-button {
    width: 100%;
  }
  .p-fluid .p-button-icon-only {
    width: 2.286rem;
  }
  .p-fluid .p-buttonset {
    display: flex;
  }
  .p-fluid .p-buttonset .p-button {
    flex: 1;
  }

  .p-button.p-button-secondary,
.p-buttonset.p-button-secondary > .p-button,
.p-splitbutton.p-button-secondary > .p-button {
    color: #FFFFFF;
    background: #a0a3ad;
    border: 1px solid transparent;
  }
  .p-button.p-button-secondary:not(:disabled):hover,
.p-buttonset.p-button-secondary > .p-button:not(:disabled):hover,
.p-splitbutton.p-button-secondary > .p-button:not(:disabled):hover {
    background: rgba(255, 255, 255, 0.1);
    color: #FFFFFF;
    border-color: transparent;
  }
  .p-button.p-button-secondary:not(:disabled):focus,
.p-buttonset.p-button-secondary > .p-button:not(:disabled):focus,
.p-splitbutton.p-button-secondary > .p-button:not(:disabled):focus {
    box-shadow: none;
  }
  .p-button.p-button-secondary:not(:disabled):active,
.p-buttonset.p-button-secondary > .p-button:not(:disabled):active,
.p-splitbutton.p-button-secondary > .p-button:not(:disabled):active {
    background: rgba(255, 255, 255, 0.15);
    color: #FFFFFF;
    border-color: transparent;
  }
  .p-button.p-button-secondary.p-button-outlined,
.p-buttonset.p-button-secondary > .p-button.p-button-outlined,
.p-splitbutton.p-button-secondary > .p-button.p-button-outlined {
    background-color: transparent;
    color: #a0a3ad;
    border: 1px solid;
  }
  .p-button.p-button-secondary.p-button-outlined:not(:disabled):hover,
.p-buttonset.p-button-secondary > .p-button.p-button-outlined:not(:disabled):hover,
.p-splitbutton.p-button-secondary > .p-button.p-button-outlined:not(:disabled):hover {
    background: rgba(160, 163, 173, 0.04);
    color: #a0a3ad;
    border: 1px solid;
  }
  .p-button.p-button-secondary.p-button-outlined:not(:disabled):active,
.p-buttonset.p-button-secondary > .p-button.p-button-outlined:not(:disabled):active,
.p-splitbutton.p-button-secondary > .p-button.p-button-outlined:not(:disabled):active {
    background: rgba(160, 163, 173, 0.16);
    color: #a0a3ad;
    border: 1px solid;
  }
  .p-button.p-button-secondary.p-button-text,
.p-buttonset.p-button-secondary > .p-button.p-button-text,
.p-splitbutton.p-button-secondary > .p-button.p-button-text {
    background-color: transparent;
    color: #a0a3ad;
    border-color: transparent;
  }
  .p-button.p-button-secondary.p-button-text:not(:disabled):hover,
.p-buttonset.p-button-secondary > .p-button.p-button-text:not(:disabled):hover,
.p-splitbutton.p-button-secondary > .p-button.p-button-text:not(:disabled):hover {
    background: rgba(160, 163, 173, 0.04);
    border-color: transparent;
    color: #a0a3ad;
  }
  .p-button.p-button-secondary.p-button-text:not(:disabled):active,
.p-buttonset.p-button-secondary > .p-button.p-button-text:not(:disabled):active,
.p-splitbutton.p-button-secondary > .p-button.p-button-text:not(:disabled):active {
    background: rgba(160, 163, 173, 0.16);
    border-color: transparent;
    color: #a0a3ad;
  }

  .p-button.p-button-info,
.p-buttonset.p-button-info > .p-button,
.p-splitbutton.p-button-info > .p-button {
    color: #FFFFFF;
    background: #873EFE;
    border: 1px solid transparent;
  }
  .p-button.p-button-info:not(:disabled):hover,
.p-buttonset.p-button-info > .p-button:not(:disabled):hover,
.p-splitbutton.p-button-info > .p-button:not(:disabled):hover {
    background: #6C1AF2;
    color: #FFFFFF;
    border-color: transparent;
  }
  .p-button.p-button-info:not(:disabled):focus,
.p-buttonset.p-button-info > .p-button:not(:disabled):focus,
.p-splitbutton.p-button-info > .p-button:not(:disabled):focus {
    box-shadow: none;
  }
  .p-button.p-button-info:not(:disabled):active,
.p-buttonset.p-button-info > .p-button:not(:disabled):active,
.p-splitbutton.p-button-info > .p-button:not(:disabled):active {
    background: #5310C1;
    color: #FFFFFF;
    border-color: transparent;
  }
  .p-button.p-button-info.p-button-outlined,
.p-buttonset.p-button-info > .p-button.p-button-outlined,
.p-splitbutton.p-button-info > .p-button.p-button-outlined {
    background-color: transparent;
    color: #873EFE;
    border: 1px solid;
  }
  .p-button.p-button-info.p-button-outlined:not(:disabled):hover,
.p-buttonset.p-button-info > .p-button.p-button-outlined:not(:disabled):hover,
.p-splitbutton.p-button-info > .p-button.p-button-outlined:not(:disabled):hover {
    background: rgba(135, 62, 254, 0.04);
    color: #873EFE;
    border: 1px solid;
  }
  .p-button.p-button-info.p-button-outlined:not(:disabled):active,
.p-buttonset.p-button-info > .p-button.p-button-outlined:not(:disabled):active,
.p-splitbutton.p-button-info > .p-button.p-button-outlined:not(:disabled):active {
    background: rgba(135, 62, 254, 0.16);
    color: #873EFE;
    border: 1px solid;
  }
  .p-button.p-button-info.p-button-text,
.p-buttonset.p-button-info > .p-button.p-button-text,
.p-splitbutton.p-button-info > .p-button.p-button-text {
    background-color: transparent;
    color: #873EFE;
    border-color: transparent;
  }
  .p-button.p-button-info.p-button-text:not(:disabled):hover,
.p-buttonset.p-button-info > .p-button.p-button-text:not(:disabled):hover,
.p-splitbutton.p-button-info > .p-button.p-button-text:not(:disabled):hover {
    background: rgba(135, 62, 254, 0.04);
    border-color: transparent;
    color: #873EFE;
  }
  .p-button.p-button-info.p-button-text:not(:disabled):active,
.p-buttonset.p-button-info > .p-button.p-button-text:not(:disabled):active,
.p-splitbutton.p-button-info > .p-button.p-button-text:not(:disabled):active {
    background: rgba(135, 62, 254, 0.16);
    border-color: transparent;
    color: #873EFE;
  }

  .p-button.p-button-success,
.p-buttonset.p-button-success > .p-button,
.p-splitbutton.p-button-success > .p-button {
    color: #FFFFFF;
    background: #0BD18A;
    border: 1px solid transparent;
  }
  .p-button.p-button-success:not(:disabled):hover,
.p-buttonset.p-button-success > .p-button:not(:disabled):hover,
.p-splitbutton.p-button-success > .p-button:not(:disabled):hover {
    background: #049B65;
    color: #FFFFFF;
    border-color: transparent;
  }
  .p-button.p-button-success:not(:disabled):focus,
.p-buttonset.p-button-success > .p-button:not(:disabled):focus,
.p-splitbutton.p-button-success > .p-button:not(:disabled):focus {
    box-shadow: none;
  }
  .p-button.p-button-success:not(:disabled):active,
.p-buttonset.p-button-success > .p-button:not(:disabled):active,
.p-splitbutton.p-button-success > .p-button:not(:disabled):active {
    background: #017E52;
    color: #FFFFFF;
    border-color: transparent;
  }
  .p-button.p-button-success.p-button-outlined,
.p-buttonset.p-button-success > .p-button.p-button-outlined,
.p-splitbutton.p-button-success > .p-button.p-button-outlined {
    background-color: transparent;
    color: #0BD18A;
    border: 1px solid;
  }
  .p-button.p-button-success.p-button-outlined:not(:disabled):hover,
.p-buttonset.p-button-success > .p-button.p-button-outlined:not(:disabled):hover,
.p-splitbutton.p-button-success > .p-button.p-button-outlined:not(:disabled):hover {
    background: rgba(11, 209, 138, 0.04);
    color: #0BD18A;
    border: 1px solid;
  }
  .p-button.p-button-success.p-button-outlined:not(:disabled):active,
.p-buttonset.p-button-success > .p-button.p-button-outlined:not(:disabled):active,
.p-splitbutton.p-button-success > .p-button.p-button-outlined:not(:disabled):active {
    background: rgba(11, 209, 138, 0.16);
    color: #0BD18A;
    border: 1px solid;
  }
  .p-button.p-button-success.p-button-text,
.p-buttonset.p-button-success > .p-button.p-button-text,
.p-splitbutton.p-button-success > .p-button.p-button-text {
    background-color: transparent;
    color: #0BD18A;
    border-color: transparent;
  }
  .p-button.p-button-success.p-button-text:not(:disabled):hover,
.p-buttonset.p-button-success > .p-button.p-button-text:not(:disabled):hover,
.p-splitbutton.p-button-success > .p-button.p-button-text:not(:disabled):hover {
    background: rgba(11, 209, 138, 0.04);
    border-color: transparent;
    color: #0BD18A;
  }
  .p-button.p-button-success.p-button-text:not(:disabled):active,
.p-buttonset.p-button-success > .p-button.p-button-text:not(:disabled):active,
.p-splitbutton.p-button-success > .p-button.p-button-text:not(:disabled):active {
    background: rgba(11, 209, 138, 0.16);
    border-color: transparent;
    color: #0BD18A;
  }

  .p-button.p-button-warning,
.p-buttonset.p-button-warning > .p-button,
.p-splitbutton.p-button-warning > .p-button {
    color: #2E323F;
    background: #EEE500;
    border: 1px solid transparent;
  }
  .p-button.p-button-warning:not(:disabled):hover,
.p-buttonset.p-button-warning > .p-button:not(:disabled):hover,
.p-splitbutton.p-button-warning > .p-button:not(:disabled):hover {
    background: #D1C901;
    color: #2E323F;
    border-color: transparent;
  }
  .p-button.p-button-warning:not(:disabled):focus,
.p-buttonset.p-button-warning > .p-button:not(:disabled):focus,
.p-splitbutton.p-button-warning > .p-button:not(:disabled):focus {
    box-shadow: none;
  }
  .p-button.p-button-warning:not(:disabled):active,
.p-buttonset.p-button-warning > .p-button:not(:disabled):active,
.p-splitbutton.p-button-warning > .p-button:not(:disabled):active {
    background: #BAB302;
    color: #2E323F;
    border-color: transparent;
  }
  .p-button.p-button-warning.p-button-outlined,
.p-buttonset.p-button-warning > .p-button.p-button-outlined,
.p-splitbutton.p-button-warning > .p-button.p-button-outlined {
    background-color: transparent;
    color: #EEE500;
    border: 1px solid;
  }
  .p-button.p-button-warning.p-button-outlined:not(:disabled):hover,
.p-buttonset.p-button-warning > .p-button.p-button-outlined:not(:disabled):hover,
.p-splitbutton.p-button-warning > .p-button.p-button-outlined:not(:disabled):hover {
    background: rgba(238, 229, 0, 0.04);
    color: #EEE500;
    border: 1px solid;
  }
  .p-button.p-button-warning.p-button-outlined:not(:disabled):active,
.p-buttonset.p-button-warning > .p-button.p-button-outlined:not(:disabled):active,
.p-splitbutton.p-button-warning > .p-button.p-button-outlined:not(:disabled):active {
    background: rgba(238, 229, 0, 0.16);
    color: #EEE500;
    border: 1px solid;
  }
  .p-button.p-button-warning.p-button-text,
.p-buttonset.p-button-warning > .p-button.p-button-text,
.p-splitbutton.p-button-warning > .p-button.p-button-text {
    background-color: transparent;
    color: #EEE500;
    border-color: transparent;
  }
  .p-button.p-button-warning.p-button-text:not(:disabled):hover,
.p-buttonset.p-button-warning > .p-button.p-button-text:not(:disabled):hover,
.p-splitbutton.p-button-warning > .p-button.p-button-text:not(:disabled):hover {
    background: rgba(238, 229, 0, 0.04);
    border-color: transparent;
    color: #EEE500;
  }
  .p-button.p-button-warning.p-button-text:not(:disabled):active,
.p-buttonset.p-button-warning > .p-button.p-button-text:not(:disabled):active,
.p-splitbutton.p-button-warning > .p-button.p-button-text:not(:disabled):active {
    background: rgba(238, 229, 0, 0.16);
    border-color: transparent;
    color: #EEE500;
  }

  .p-button.p-button-help,
.p-buttonset.p-button-help > .p-button,
.p-splitbutton.p-button-help > .p-button {
    color: #FFFFFF;
    background: #EC4DBC;
    border: 1px solid transparent;
  }
  .p-button.p-button-help:not(:disabled):hover,
.p-buttonset.p-button-help > .p-button:not(:disabled):hover,
.p-splitbutton.p-button-help > .p-button:not(:disabled):hover {
    background: #E80EA6;
    color: #FFFFFF;
    border-color: transparent;
  }
  .p-button.p-button-help:not(:disabled):focus,
.p-buttonset.p-button-help > .p-button:not(:disabled):focus,
.p-splitbutton.p-button-help > .p-button:not(:disabled):focus {
    box-shadow: none;
  }
  .p-button.p-button-help:not(:disabled):active,
.p-buttonset.p-button-help > .p-button:not(:disabled):active,
.p-splitbutton.p-button-help > .p-button:not(:disabled):active {
    background: #B30C81;
    color: #FFFFFF;
    border-color: transparent;
  }
  .p-button.p-button-help.p-button-outlined,
.p-buttonset.p-button-help > .p-button.p-button-outlined,
.p-splitbutton.p-button-help > .p-button.p-button-outlined {
    background-color: transparent;
    color: #EC4DBC;
    border: 1px solid;
  }
  .p-button.p-button-help.p-button-outlined:not(:disabled):hover,
.p-buttonset.p-button-help > .p-button.p-button-outlined:not(:disabled):hover,
.p-splitbutton.p-button-help > .p-button.p-button-outlined:not(:disabled):hover {
    background: rgba(236, 77, 188, 0.04);
    color: #EC4DBC;
    border: 1px solid;
  }
  .p-button.p-button-help.p-button-outlined:not(:disabled):active,
.p-buttonset.p-button-help > .p-button.p-button-outlined:not(:disabled):active,
.p-splitbutton.p-button-help > .p-button.p-button-outlined:not(:disabled):active {
    background: rgba(236, 77, 188, 0.16);
    color: #EC4DBC;
    border: 1px solid;
  }
  .p-button.p-button-help.p-button-text,
.p-buttonset.p-button-help > .p-button.p-button-text,
.p-splitbutton.p-button-help > .p-button.p-button-text {
    background-color: transparent;
    color: #EC4DBC;
    border-color: transparent;
  }
  .p-button.p-button-help.p-button-text:not(:disabled):hover,
.p-buttonset.p-button-help > .p-button.p-button-text:not(:disabled):hover,
.p-splitbutton.p-button-help > .p-button.p-button-text:not(:disabled):hover {
    background: rgba(236, 77, 188, 0.04);
    border-color: transparent;
    color: #EC4DBC;
  }
  .p-button.p-button-help.p-button-text:not(:disabled):active,
.p-buttonset.p-button-help > .p-button.p-button-text:not(:disabled):active,
.p-splitbutton.p-button-help > .p-button.p-button-text:not(:disabled):active {
    background: rgba(236, 77, 188, 0.16);
    border-color: transparent;
    color: #EC4DBC;
  }

  .p-button.p-button-danger,
.p-buttonset.p-button-danger > .p-button,
.p-splitbutton.p-button-danger > .p-button {
    color: #FFFFFF;
    background: #FC6161;
    border: 1px solid transparent;
  }
  .p-button.p-button-danger:not(:disabled):hover,
.p-buttonset.p-button-danger > .p-button:not(:disabled):hover,
.p-splitbutton.p-button-danger > .p-button:not(:disabled):hover {
    background: #E73A3A;
    color: #FFFFFF;
    border-color: transparent;
  }
  .p-button.p-button-danger:not(:disabled):focus,
.p-buttonset.p-button-danger > .p-button:not(:disabled):focus,
.p-splitbutton.p-button-danger > .p-button:not(:disabled):focus {
    box-shadow: none;
  }
  .p-button.p-button-danger:not(:disabled):active,
.p-buttonset.p-button-danger > .p-button:not(:disabled):active,
.p-splitbutton.p-button-danger > .p-button:not(:disabled):active {
    background: #C42424;
    color: #FFFFFF;
    border-color: transparent;
  }
  .p-button.p-button-danger.p-button-outlined,
.p-buttonset.p-button-danger > .p-button.p-button-outlined,
.p-splitbutton.p-button-danger > .p-button.p-button-outlined {
    background-color: transparent;
    color: #FC6161;
    border: 1px solid;
  }
  .p-button.p-button-danger.p-button-outlined:not(:disabled):hover,
.p-buttonset.p-button-danger > .p-button.p-button-outlined:not(:disabled):hover,
.p-splitbutton.p-button-danger > .p-button.p-button-outlined:not(:disabled):hover {
    background: rgba(252, 97, 97, 0.04);
    color: #FC6161;
    border: 1px solid;
  }
  .p-button.p-button-danger.p-button-outlined:not(:disabled):active,
.p-buttonset.p-button-danger > .p-button.p-button-outlined:not(:disabled):active,
.p-splitbutton.p-button-danger > .p-button.p-button-outlined:not(:disabled):active {
    background: rgba(252, 97, 97, 0.16);
    color: #FC6161;
    border: 1px solid;
  }
  .p-button.p-button-danger.p-button-text,
.p-buttonset.p-button-danger > .p-button.p-button-text,
.p-splitbutton.p-button-danger > .p-button.p-button-text {
    background-color: transparent;
    color: #FC6161;
    border-color: transparent;
  }
  .p-button.p-button-danger.p-button-text:not(:disabled):hover,
.p-buttonset.p-button-danger > .p-button.p-button-text:not(:disabled):hover,
.p-splitbutton.p-button-danger > .p-button.p-button-text:not(:disabled):hover {
    background: rgba(252, 97, 97, 0.04);
    border-color: transparent;
    color: #FC6161;
  }
  .p-button.p-button-danger.p-button-text:not(:disabled):active,
.p-buttonset.p-button-danger > .p-button.p-button-text:not(:disabled):active,
.p-splitbutton.p-button-danger > .p-button.p-button-text:not(:disabled):active {
    background: rgba(252, 97, 97, 0.16);
    border-color: transparent;
    color: #FC6161;
  }

  .p-button.p-button-link {
    color: #EEE500;
    background: transparent;
    border: transparent;
  }
  .p-button.p-button-link:not(:disabled):hover {
    background: transparent;
    color: #EEE500;
    border-color: transparent;
  }
  .p-button.p-button-link:not(:disabled):hover .p-button-label {
    text-decoration: underline;
  }
  .p-button.p-button-link:not(:disabled):focus {
    background: transparent;
    box-shadow: none;
    border-color: transparent;
  }
  .p-button.p-button-link:not(:disabled):active {
    background: transparent;
    color: #EEE500;
    border-color: transparent;
  }

  .p-speeddial-button.p-button.p-button-icon-only {
    width: 4rem;
    height: 4rem;
  }
  .p-speeddial-button.p-button.p-button-icon-only .p-button-icon {
    font-size: 1.3rem;
  }
  .p-speeddial-button.p-button.p-button-icon-only .p-icon {
    width: 1.3rem;
    height: 1.3rem;
  }

  .p-speeddial-list {
    outline: 0 none;
  }

  .p-speeddial-item.p-focus > .p-speeddial-action {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }

  .p-speeddial-action {
    width: 3rem;
    height: 3rem;
    background: #FFFFFF;
    color: rgba(255, 255, 255, 0.25);
  }
  .p-speeddial-action:hover {
    background: #C8CCD8;
    color: rgba(255, 255, 255, 0.25);
  }

  .p-speeddial-direction-up .p-speeddial-item {
    margin: 0.25rem 0;
  }
  .p-speeddial-direction-up .p-speeddial-item:first-child {
    margin-bottom: 0.5rem;
  }

  .p-speeddial-direction-down .p-speeddial-item {
    margin: 0.25rem 0;
  }
  .p-speeddial-direction-down .p-speeddial-item:first-child {
    margin-top: 0.5rem;
  }

  .p-speeddial-direction-left .p-speeddial-item {
    margin: 0 0.25rem;
  }
  .p-speeddial-direction-left .p-speeddial-item:first-child {
    margin-right: 0.5rem;
  }

  .p-speeddial-direction-right .p-speeddial-item {
    margin: 0 0.25rem;
  }
  .p-speeddial-direction-right .p-speeddial-item:first-child {
    margin-left: 0.5rem;
  }

  .p-speeddial-circle .p-speeddial-item,
.p-speeddial-semi-circle .p-speeddial-item,
.p-speeddial-quarter-circle .p-speeddial-item {
    margin: 0;
  }
  .p-speeddial-circle .p-speeddial-item:first-child, .p-speeddial-circle .p-speeddial-item:last-child,
.p-speeddial-semi-circle .p-speeddial-item:first-child,
.p-speeddial-semi-circle .p-speeddial-item:last-child,
.p-speeddial-quarter-circle .p-speeddial-item:first-child,
.p-speeddial-quarter-circle .p-speeddial-item:last-child {
    margin: 0;
  }

  .p-speeddial-mask {
    background-color: rgba(0, 0, 0, 0.2);
  }

  .p-splitbutton {
    border-radius: 6px;
  }
  .p-splitbutton.p-button-outlined > .p-button {
    background-color: transparent;
    color: #EEE500;
    border: 1px solid;
  }
  .p-splitbutton.p-button-outlined > .p-button:not(:disabled):hover {
    background: rgba(238, 229, 0, 0.04);
    color: #EEE500;
  }
  .p-splitbutton.p-button-outlined > .p-button:not(:disabled):active {
    background: rgba(238, 229, 0, 0.16);
    color: #EEE500;
  }
  .p-splitbutton.p-button-outlined.p-button-plain > .p-button {
    color: #C8CCD8;
    border-color: #C8CCD8;
  }
  .p-splitbutton.p-button-outlined.p-button-plain > .p-button:not(:disabled):hover {
    background: rgba(255, 255, 255, 0.05);
    color: #C8CCD8;
  }
  .p-splitbutton.p-button-outlined.p-button-plain > .p-button:not(:disabled):active {
    background: rgba(255, 255, 255, 0.16);
    color: #C8CCD8;
  }
  .p-splitbutton.p-button-text > .p-button {
    background-color: transparent;
    color: #EEE500;
    border-color: transparent;
  }
  .p-splitbutton.p-button-text > .p-button:not(:disabled):hover {
    background: rgba(238, 229, 0, 0.04);
    color: #EEE500;
    border-color: transparent;
  }
  .p-splitbutton.p-button-text > .p-button:not(:disabled):active {
    background: rgba(238, 229, 0, 0.16);
    color: #EEE500;
    border-color: transparent;
  }
  .p-splitbutton.p-button-text.p-button-plain > .p-button {
    color: #C8CCD8;
  }
  .p-splitbutton.p-button-text.p-button-plain > .p-button:not(:disabled):hover {
    background: rgba(255, 255, 255, 0.05);
    color: #C8CCD8;
  }
  .p-splitbutton.p-button-text.p-button-plain > .p-button:not(:disabled):active {
    background: rgba(255, 255, 255, 0.16);
    color: #C8CCD8;
  }
  .p-splitbutton.p-button-raised {
    box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
  }
  .p-splitbutton.p-button-rounded {
    border-radius: 2rem;
  }
  .p-splitbutton.p-button-rounded > .p-button {
    border-radius: 2rem;
  }
  .p-splitbutton.p-button-sm > .p-button {
    font-size: 0.875rem;
    padding: 0.375375rem 0.499625rem;
  }
  .p-splitbutton.p-button-sm > .p-button .p-button-icon {
    font-size: 0.875rem;
  }
  .p-splitbutton.p-button-lg > .p-button {
    font-size: 1.25rem;
    padding: 0.53625rem 0.71375rem;
  }
  .p-splitbutton.p-button-lg > .p-button .p-button-icon {
    font-size: 1.25rem;
  }

  .p-splitbutton.p-button-secondary.p-button-outlined > .p-button {
    background-color: transparent;
    color: #a0a3ad;
    border: 1px solid;
  }
  .p-splitbutton.p-button-secondary.p-button-outlined > .p-button:not(:disabled):hover {
    background: rgba(160, 163, 173, 0.04);
    color: #a0a3ad;
  }
  .p-splitbutton.p-button-secondary.p-button-outlined > .p-button:not(:disabled):active {
    background: rgba(160, 163, 173, 0.16);
    color: #a0a3ad;
  }
  .p-splitbutton.p-button-secondary.p-button-text > .p-button {
    background-color: transparent;
    color: #a0a3ad;
    border-color: transparent;
  }
  .p-splitbutton.p-button-secondary.p-button-text > .p-button:not(:disabled):hover {
    background: rgba(160, 163, 173, 0.04);
    border-color: transparent;
    color: #a0a3ad;
  }
  .p-splitbutton.p-button-secondary.p-button-text > .p-button:not(:disabled):active {
    background: rgba(160, 163, 173, 0.16);
    border-color: transparent;
    color: #a0a3ad;
  }

  .p-splitbutton.p-button-info.p-button-outlined > .p-button {
    background-color: transparent;
    color: #873EFE;
    border: 1px solid;
  }
  .p-splitbutton.p-button-info.p-button-outlined > .p-button:not(:disabled):hover {
    background: rgba(135, 62, 254, 0.04);
    color: #873EFE;
  }
  .p-splitbutton.p-button-info.p-button-outlined > .p-button:not(:disabled):active {
    background: rgba(135, 62, 254, 0.16);
    color: #873EFE;
  }
  .p-splitbutton.p-button-info.p-button-text > .p-button {
    background-color: transparent;
    color: #873EFE;
    border-color: transparent;
  }
  .p-splitbutton.p-button-info.p-button-text > .p-button:not(:disabled):hover {
    background: rgba(135, 62, 254, 0.04);
    border-color: transparent;
    color: #873EFE;
  }
  .p-splitbutton.p-button-info.p-button-text > .p-button:not(:disabled):active {
    background: rgba(135, 62, 254, 0.16);
    border-color: transparent;
    color: #873EFE;
  }

  .p-splitbutton.p-button-success.p-button-outlined > .p-button {
    background-color: transparent;
    color: #0BD18A;
    border: 1px solid;
  }
  .p-splitbutton.p-button-success.p-button-outlined > .p-button:not(:disabled):hover {
    background: rgba(11, 209, 138, 0.04);
    color: #0BD18A;
  }
  .p-splitbutton.p-button-success.p-button-outlined > .p-button:not(:disabled):active {
    background: rgba(11, 209, 138, 0.16);
    color: #0BD18A;
  }
  .p-splitbutton.p-button-success.p-button-text > .p-button {
    background-color: transparent;
    color: #0BD18A;
    border-color: transparent;
  }
  .p-splitbutton.p-button-success.p-button-text > .p-button:not(:disabled):hover {
    background: rgba(11, 209, 138, 0.04);
    border-color: transparent;
    color: #0BD18A;
  }
  .p-splitbutton.p-button-success.p-button-text > .p-button:not(:disabled):active {
    background: rgba(11, 209, 138, 0.16);
    border-color: transparent;
    color: #0BD18A;
  }

  .p-splitbutton.p-button-warning.p-button-outlined > .p-button {
    background-color: transparent;
    color: #EEE500;
    border: 1px solid;
  }
  .p-splitbutton.p-button-warning.p-button-outlined > .p-button:not(:disabled):hover {
    background: rgba(238, 229, 0, 0.04);
    color: #EEE500;
  }
  .p-splitbutton.p-button-warning.p-button-outlined > .p-button:not(:disabled):active {
    background: rgba(238, 229, 0, 0.16);
    color: #EEE500;
  }
  .p-splitbutton.p-button-warning.p-button-text > .p-button {
    background-color: transparent;
    color: #EEE500;
    border-color: transparent;
  }
  .p-splitbutton.p-button-warning.p-button-text > .p-button:not(:disabled):hover {
    background: rgba(238, 229, 0, 0.04);
    border-color: transparent;
    color: #EEE500;
  }
  .p-splitbutton.p-button-warning.p-button-text > .p-button:not(:disabled):active {
    background: rgba(238, 229, 0, 0.16);
    border-color: transparent;
    color: #EEE500;
  }

  .p-splitbutton.p-button-help.p-button-outlined > .p-button {
    background-color: transparent;
    color: #EC4DBC;
    border: 1px solid;
  }
  .p-splitbutton.p-button-help.p-button-outlined > .p-button:not(:disabled):hover {
    background: rgba(236, 77, 188, 0.04);
    color: #EC4DBC;
  }
  .p-splitbutton.p-button-help.p-button-outlined > .p-button:not(:disabled):active {
    background: rgba(236, 77, 188, 0.16);
    color: #EC4DBC;
  }
  .p-splitbutton.p-button-help.p-button-text > .p-button {
    background-color: transparent;
    color: #EC4DBC;
    border-color: transparent;
  }
  .p-splitbutton.p-button-help.p-button-text > .p-button:not(:disabled):hover {
    background: rgba(236, 77, 188, 0.04);
    border-color: transparent;
    color: #EC4DBC;
  }
  .p-splitbutton.p-button-help.p-button-text > .p-button:not(:disabled):active {
    background: rgba(236, 77, 188, 0.16);
    border-color: transparent;
    color: #EC4DBC;
  }

  .p-splitbutton.p-button-danger.p-button-outlined > .p-button {
    background-color: transparent;
    color: #FC6161;
    border: 1px solid;
  }
  .p-splitbutton.p-button-danger.p-button-outlined > .p-button:not(:disabled):hover {
    background: rgba(252, 97, 97, 0.04);
    color: #FC6161;
  }
  .p-splitbutton.p-button-danger.p-button-outlined > .p-button:not(:disabled):active {
    background: rgba(252, 97, 97, 0.16);
    color: #FC6161;
  }
  .p-splitbutton.p-button-danger.p-button-text > .p-button {
    background-color: transparent;
    color: #FC6161;
    border-color: transparent;
  }
  .p-splitbutton.p-button-danger.p-button-text > .p-button:not(:disabled):hover {
    background: rgba(252, 97, 97, 0.04);
    border-color: transparent;
    color: #FC6161;
  }
  .p-splitbutton.p-button-danger.p-button-text > .p-button:not(:disabled):active {
    background: rgba(252, 97, 97, 0.16);
    border-color: transparent;
    color: #FC6161;
  }

  .p-carousel .p-carousel-content .p-carousel-prev,
.p-carousel .p-carousel-content .p-carousel-next {
    width: 2rem;
    height: 2rem;
    color: #C8CCD8;
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
    margin: 0.5rem;
  }
  .p-carousel .p-carousel-content .p-carousel-prev:enabled:hover,
.p-carousel .p-carousel-content .p-carousel-next:enabled:hover {
    color: #FFFFFF;
    border-color: transparent;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-carousel .p-carousel-content .p-carousel-prev:focus-visible,
.p-carousel .p-carousel-content .p-carousel-next:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-carousel .p-carousel-indicators {
    padding: 1rem;
  }
  .p-carousel .p-carousel-indicators .p-carousel-indicator {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
  }
  .p-carousel .p-carousel-indicators .p-carousel-indicator button {
    background-color: rgba(255, 255, 255, 0.1);
    width: 1.429rem;
    height: 0.572rem;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
    border-radius: 3px;
  }
  .p-carousel .p-carousel-indicators .p-carousel-indicator button:hover {
    background: rgba(255, 255, 255, 0.2);
  }
  .p-carousel .p-carousel-indicators .p-carousel-indicator.p-highlight button {
    background: #EEE500;
    color: #0A061A;
  }

  .p-datatable .p-paginator-top {
    border-width: 1px 0 1px 0;
    border-radius: 0;
  }
  .p-datatable .p-paginator-bottom {
    border-width: 1px 0 1px 0;
    border-radius: 0;
  }
  .p-datatable .p-datatable-header {
    background: var(--surface-section);
    color: #868C9B;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-width: 0 0 1px 0;
    padding: 0.571rem 0.571rem;
    font-weight: 500;
  }
  .p-datatable .p-datatable-footer {
    background: var(--surface-section);
    color: #868C9B;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-width: 0 0 1px 0;
    padding: 0.571rem 0.571rem;
    font-weight: 700;
  }
  .p-datatable .p-datatable-thead > tr > th {
    text-align: left;
    padding: 0.571rem 0.571rem;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-width: 0 0 1px 0;
    font-weight: 500;
    color: #868C9B;
    background: var(--surface-section);
    transition: box-shadow 0.2s;
  }
  .p-datatable .p-datatable-tfoot > tr > td {
    text-align: left;
    padding: 0.571rem 0.571rem;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-width: 0 0 1px 0;
    font-weight: 500;
    color: #868C9B;
    background: var(--surface-section);
  }
  .p-datatable .p-sortable-column .p-sortable-column-icon {
    color: #868C9B;
    margin-left: 0.5rem;
  }
  .p-datatable .p-sortable-column .p-sortable-column-badge {
    border-radius: 50%;
    height: 1.143rem;
    min-width: 1.143rem;
    line-height: 1.143rem;
    color: #0A061A;
    background: #EEE500;
    margin-left: 0.5rem;
  }
  .p-datatable .p-sortable-column:not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.05);
    color: #868C9B;
  }
  .p-datatable .p-sortable-column:not(.p-highlight):hover .p-sortable-column-icon {
    color: #868C9B;
  }
  .p-datatable .p-sortable-column.p-highlight {
    background: rgba(255, 255, 255, 0.1);
    color: #868C9B;
  }
  .p-datatable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: #868C9B;
  }
  .p-datatable .p-sortable-column.p-highlight:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #868C9B;
  }
  .p-datatable .p-sortable-column.p-highlight:hover .p-sortable-column-icon {
    color: #868C9B;
  }
  .p-datatable .p-sortable-column:focus-visible {
    box-shadow: 0 none;
    outline: 0 none;
  }
  .p-datatable .p-datatable-tbody > tr {
    background: var(--surface-section);
    color: #FFFFFF;
    transition: box-shadow 0.2s;
  }
  .p-datatable .p-datatable-tbody > tr > td {
    text-align: left;
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-width: 0 0 0 0;
    padding: 0.429rem 0.571rem;
  }
  .p-datatable .p-datatable-tbody > tr > td .p-row-toggler,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-init,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-save,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-cancel {
    width: 2rem;
    height: 2rem;
    color: #C8CCD8;
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .p-datatable .p-datatable-tbody > tr > td .p-row-toggler:enabled:hover,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-init:enabled:hover,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-save:enabled:hover,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-cancel:enabled:hover {
    color: #FFFFFF;
    border-color: transparent;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-datatable .p-datatable-tbody > tr > td .p-row-toggler:focus-visible,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-init:focus-visible,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-save:focus-visible,
.p-datatable .p-datatable-tbody > tr > td .p-row-editor-cancel:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-datatable .p-datatable-tbody > tr > td .p-row-editor-save {
    margin-right: 0.5rem;
  }
  .p-datatable .p-datatable-tbody > tr:focus-visible {
    outline: 0.15rem solid #EEE500;
    outline-offset: -0.15rem;
  }
  .p-datatable .p-datatable-tbody > tr.p-highlight {
    background: #EEE500;
    color: #0A061A;
  }
  .p-datatable .p-datatable-tbody > tr.p-datatable-dragpoint-top > td {
    box-shadow: inset 0 2px 0 0 #EEE500;
  }
  .p-datatable .p-datatable-tbody > tr.p-datatable-dragpoint-bottom > td {
    box-shadow: inset 0 -2px 0 0 #EEE500;
  }
  .p-datatable.p-datatable-hoverable-rows .p-datatable-tbody > tr:not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
  }
  .p-datatable .p-column-resizer-helper {
    background: #EEE500;
  }
  .p-datatable .p-datatable-scrollable-header,
.p-datatable .p-datatable-scrollable-footer {
    background: transparent;
  }
  .p-datatable.p-datatable-scrollable > .p-datatable-wrapper > .p-datatable-table > .p-datatable-thead,
.p-datatable.p-datatable-scrollable > .p-datatable-wrapper > .p-datatable-table > .p-datatable-tfoot, .p-datatable.p-datatable-scrollable > .p-datatable-wrapper > .p-scroller-viewport > .p-scroller > .p-datatable-table > .p-datatable-thead,
.p-datatable.p-datatable-scrollable > .p-datatable-wrapper > .p-scroller-viewport > .p-scroller > .p-datatable-table > .p-datatable-tfoot {
    background-color: var(--surface-section);
  }
  .p-datatable .p-datatable-loading-icon {
    font-size: 2rem;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-header {
    border-width: 1px 1px 0 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-footer {
    border-width: 0 1px 1px 1px;
  }
  .p-datatable.p-datatable-gridlines .p-paginator-top {
    border-width: 0 1px 0 1px;
  }
  .p-datatable.p-datatable-gridlines .p-paginator-bottom {
    border-width: 0 1px 1px 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-thead > tr > th {
    border-width: 1px 0 1px 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-thead > tr > th:last-child {
    border-width: 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tbody > tr > td {
    border-width: 1px 0 0 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tbody > tr > td:last-child {
    border-width: 1px 1px 0 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tbody > tr:last-child > td {
    border-width: 1px 0 1px 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tbody > tr:last-child > td:last-child {
    border-width: 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tfoot > tr > td {
    border-width: 1px 0 1px 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-tfoot > tr > td:last-child {
    border-width: 1px 1px 1px 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-thead + .p-datatable-tfoot > tr > td {
    border-width: 0 0 1px 1px;
  }
  .p-datatable.p-datatable-gridlines .p-datatable-thead + .p-datatable-tfoot > tr > td:last-child {
    border-width: 0 1px 1px 1px;
  }
  .p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody > tr > td {
    border-width: 0 0 1px 1px;
  }
  .p-datatable.p-datatable-gridlines:has(.p-datatable-thead):has(.p-datatable-tbody) .p-datatable-tbody > tr > td:last-child {
    border-width: 0 1px 1px 1px;
  }
  .p-datatable.p-datatable-gridlines:has(.p-datatable-tbody):has(.p-datatable-tfoot) .p-datatable-tbody > tr:last-child > td {
    border-width: 0 0 0 1px;
  }
  .p-datatable.p-datatable-gridlines:has(.p-datatable-tbody):has(.p-datatable-tfoot) .p-datatable-tbody > tr:last-child > td:last-child {
    border-width: 0 1px 0 1px;
  }
  .p-datatable.p-datatable-striped .p-datatable-tbody > tr:nth-child(even) {
    background: var(--surface-section);
  }
  .p-datatable.p-datatable-striped .p-datatable-tbody > tr:nth-child(even).p-highlight {
    background: #EEE500;
    color: #0A061A;
  }
  .p-datatable.p-datatable-striped .p-datatable-tbody > tr:nth-child(even).p-highlight .p-row-toggler {
    color: #0A061A;
  }
  .p-datatable.p-datatable-striped .p-datatable-tbody > tr:nth-child(even).p-highlight .p-row-toggler:hover {
    color: #0A061A;
  }
  .p-datatable.p-datatable-sm .p-datatable-header {
    padding: 0.2855rem 0.2855rem;
  }
  .p-datatable.p-datatable-sm .p-datatable-thead > tr > th {
    padding: 0.2855rem 0.2855rem;
  }
  .p-datatable.p-datatable-sm .p-datatable-tbody > tr > td {
    padding: 0.2145rem 0.2855rem;
  }
  .p-datatable.p-datatable-sm .p-datatable-tfoot > tr > td {
    padding: 0.2855rem 0.2855rem;
  }
  .p-datatable.p-datatable-sm .p-datatable-footer {
    padding: 0.2855rem 0.2855rem;
  }
  .p-datatable.p-datatable-lg .p-datatable-header {
    padding: 0.71375rem 0.71375rem;
  }
  .p-datatable.p-datatable-lg .p-datatable-thead > tr > th {
    padding: 0.71375rem 0.71375rem;
  }
  .p-datatable.p-datatable-lg .p-datatable-tbody > tr > td {
    padding: 0.53625rem 0.71375rem;
  }
  .p-datatable.p-datatable-lg .p-datatable-tfoot > tr > td {
    padding: 0.71375rem 0.71375rem;
  }
  .p-datatable.p-datatable-lg .p-datatable-footer {
    padding: 0.71375rem 0.71375rem;
  }

  .p-dataview .p-paginator-top {
    border-width: 1px 0 1px 0;
    border-radius: 0;
  }
  .p-dataview .p-paginator-bottom {
    border-width: 1px 0 1px 0;
    border-radius: 0;
  }
  .p-dataview .p-dataview-header {
    background: var(--surface-section);
    color: #868C9B;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-width: 0 0 1px 0;
    padding: 0.571rem 0.571rem;
    font-weight: 500;
  }
  .p-dataview .p-dataview-content {
    background: var(--surface-section);
    color: #FFFFFF;
    border: 0 none;
    padding: 0;
  }
  .p-dataview .p-dataview-footer {
    background: var(--surface-section);
    color: #868C9B;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-width: 0 0 1px 0;
    padding: 0.571rem 0.571rem;
    font-weight: 700;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
  }
  .p-dataview .p-dataview-loading-icon {
    font-size: 2rem;
  }
  .p-dataview .p-dataview-emptymessage {
    padding: 1.143rem;
  }

  .p-column-filter-row .p-column-filter-menu-button,
.p-column-filter-row .p-column-filter-clear-button {
    margin-left: 0.5rem;
  }

  .p-column-filter-menu-button {
    width: 2rem;
    height: 2rem;
    color: #C8CCD8;
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .p-column-filter-menu-button:hover {
    color: #FFFFFF;
    border-color: transparent;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-column-filter-menu-button.p-column-filter-menu-button-open, .p-column-filter-menu-button.p-column-filter-menu-button-open:hover {
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
  }
  .p-column-filter-menu-button.p-column-filter-menu-button-active, .p-column-filter-menu-button.p-column-filter-menu-button-active:hover {
    background: #EEE500;
    color: #0A061A;
  }
  .p-column-filter-menu-button:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }

  .p-column-filter-clear-button {
    width: 2rem;
    height: 2rem;
    color: #C8CCD8;
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .p-column-filter-clear-button:hover {
    color: #FFFFFF;
    border-color: transparent;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-column-filter-clear-button:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }

  .p-column-filter-overlay {
    background: #0a061a;
    color: #FFFFFF;
    border: 0 none;
    border-radius: 6px;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
    min-width: 12.5rem;
  }
  .p-column-filter-overlay .p-column-filter-row-items {
    padding: 0.286rem;
  }
  .p-column-filter-overlay .p-column-filter-row-items .p-column-filter-row-item {
    margin: 0;
    padding: 0.429rem 0.286rem;
    border: 0 none;
    color: #FFFFFF;
    background: transparent;
    transition: box-shadow 0.2s;
    border-radius: 4px;
  }
  .p-column-filter-overlay .p-column-filter-row-items .p-column-filter-row-item.p-highlight {
    color: #0A061A;
    background: #EEE500;
  }
  .p-column-filter-overlay .p-column-filter-row-items .p-column-filter-row-item:not(.p-highlight):not(.p-disabled):hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-column-filter-overlay .p-column-filter-row-items .p-column-filter-row-item:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-column-filter-overlay .p-column-filter-row-items .p-column-filter-separator {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    margin: 0.25rem 0;
  }

  .p-column-filter-overlay-menu .p-column-filter-operator {
    padding: 0.429rem 0.286rem;
    border-bottom: 0 none;
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
    margin: 0;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-column-filter-overlay-menu .p-column-filter-constraint {
    padding: 1.143rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }
  .p-column-filter-overlay-menu .p-column-filter-constraint .p-column-filter-matchmode-dropdown {
    margin-bottom: 0.5rem;
  }
  .p-column-filter-overlay-menu .p-column-filter-constraint .p-column-filter-remove-button {
    margin-top: 0.5rem;
  }
  .p-column-filter-overlay-menu .p-column-filter-constraint:last-child {
    border-bottom: 0 none;
  }
  .p-column-filter-overlay-menu .p-column-filter-add-rule {
    padding: 0.714rem 1.143rem;
  }
  .p-column-filter-overlay-menu .p-column-filter-buttonbar {
    padding: 1.143rem;
  }

  .p-orderlist .p-orderlist-controls {
    padding: 1.143rem;
  }
  .p-orderlist .p-orderlist-controls .p-button {
    margin-bottom: 0.5rem;
  }
  .p-orderlist .p-orderlist-header {
    background: transparent;
    color: #FFFFFF;
    border: 1px solid rgba(255, 255, 255, 0.15);
    padding: 0.714rem 1.143rem;
    border-bottom: 0 none;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-orderlist .p-orderlist-header .p-orderlist-title {
    font-weight: 500;
  }
  .p-orderlist .p-orderlist-filter-container {
    padding: 0.714rem 1.143rem;
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-bottom: 0 none;
  }
  .p-orderlist .p-orderlist-filter-container .p-orderlist-filter-input {
    padding-right: 1.571rem;
  }
  .p-orderlist .p-orderlist-filter-container .p-orderlist-filter-icon {
    right: 0.571rem;
    color: #C8CCD8;
  }
  .p-orderlist .p-orderlist-list {
    border: 1px solid rgba(255, 255, 255, 0.15);
    background: transparent;
    color: #FFFFFF;
    padding: 0.286rem;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
    outline: 0 none;
  }
  .p-orderlist .p-orderlist-list .p-orderlist-item {
    padding: 0.429rem 0.286rem;
    margin: 0;
    border: 0 none;
    color: #FFFFFF;
    background: transparent;
    transition: box-shadow 0.2s;
  }
  .p-orderlist .p-orderlist-list .p-orderlist-item:not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
  }
  .p-orderlist .p-orderlist-list .p-orderlist-item.p-focus {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.1);
  }
  .p-orderlist .p-orderlist-list .p-orderlist-item.p-highlight {
    color: #0A061A;
    background: #EEE500;
  }
  .p-orderlist .p-orderlist-list .p-orderlist-item.p-highlight.p-focus {
    background: rgba(238, 229, 0, 0.24);
  }
  .p-orderlist .p-orderlist-list .p-orderlist-empty-message {
    padding: 0.429rem 0.286rem;
    color: #FFFFFF;
  }
  .p-orderlist .p-orderlist-list:not(.cdk-drop-list-dragging) .p-orderlist-item:not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
  }
  .p-orderlist.p-orderlist-striped .p-orderlist-list .p-orderlist-item:nth-child(even) {
    background: rgba(255, 255, 255, 0.01);
  }
  .p-orderlist.p-orderlist-striped .p-orderlist-list .p-orderlist-item:nth-child(even):hover {
    background: rgba(255, 255, 255, 0.05);
  }

  .p-orderlist-item.cdk-drag-preview {
    padding: 0.429rem 0.286rem;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
    border: 0 none;
    color: #FFFFFF;
    background: transparent;
    margin: 0;
  }

  .p-organizationchart .p-organizationchart-node-content.p-organizationchart-selectable-node:not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
  }
  .p-organizationchart .p-organizationchart-node-content.p-highlight {
    background: #EEE500;
    color: #0A061A;
  }
  .p-organizationchart .p-organizationchart-node-content.p-highlight .p-node-toggler i {
    color: #6f6a00;
  }
  .p-organizationchart .p-organizationchart-line-down {
    background: #C8CCD8;
  }
  .p-organizationchart .p-organizationchart-line-left {
    border-right: 1px solid rgba(255, 255, 255, 0.15);
    border-color: #C8CCD8;
  }
  .p-organizationchart .p-organizationchart-line-top {
    border-top: 1px solid rgba(255, 255, 255, 0.15);
    border-color: #C8CCD8;
  }
  .p-organizationchart .p-organizationchart-node-content {
    border: 1px solid rgba(255, 255, 255, 0.15);
    background: transparent;
    color: #FFFFFF;
    padding: 1.143rem;
  }
  .p-organizationchart .p-organizationchart-node-content .p-node-toggler {
    background: inherit;
    color: inherit;
    border-radius: 50%;
  }
  .p-organizationchart .p-organizationchart-node-content .p-node-toggler:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }

  .p-paginator {
    background: transparent;
    color: #FFFFFF;
    border: solid rgba(255, 255, 255, 0.15);
    border-width: 0;
    padding: 0.571rem 0.571rem;
    border-radius: 6px;
  }
  .p-paginator .p-paginator-first,
.p-paginator .p-paginator-prev,
.p-paginator .p-paginator-next,
.p-paginator .p-paginator-last {
    background-color: transparent;
    border: 0 none;
    color: #FFFFFF;
    min-width: 1.429rem;
    height: 1.429rem;
    margin: 0.143rem;
    transition: box-shadow 0.2s;
    border-radius: 4px;
  }
  .p-paginator .p-paginator-first:not(.p-disabled):not(.p-highlight):hover,
.p-paginator .p-paginator-prev:not(.p-disabled):not(.p-highlight):hover,
.p-paginator .p-paginator-next:not(.p-disabled):not(.p-highlight):hover,
.p-paginator .p-paginator-last:not(.p-disabled):not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: transparent;
    color: #FFFFFF;
  }
  .p-paginator .p-paginator-first {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
  }
  .p-paginator .p-paginator-last {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
  }
  .p-paginator .p-dropdown {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    height: 1.429rem;
  }
  .p-paginator .p-dropdown .p-dropdown-label {
    padding-right: 0;
  }
  .p-paginator .p-paginator-page-input {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }
  .p-paginator .p-paginator-page-input .p-inputtext {
    max-width: 1.429rem;
  }
  .p-paginator .p-paginator-current {
    background-color: transparent;
    border: 0 none;
    color: #FFFFFF;
    min-width: 1.429rem;
    height: 1.429rem;
    margin: 0.143rem;
    padding: 0 0.5rem;
  }
  .p-paginator .p-paginator-pages .p-paginator-page {
    background-color: transparent;
    border: 0 none;
    color: #FFFFFF;
    min-width: 1.429rem;
    height: 1.429rem;
    margin: 0.143rem;
    transition: box-shadow 0.2s;
    border-radius: 4px;
  }
  .p-paginator .p-paginator-pages .p-paginator-page.p-highlight {
    background: #EEE500;
    border-color: #EEE500;
    color: #0A061A;
  }
  .p-paginator .p-paginator-pages .p-paginator-page:not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: transparent;
    color: #FFFFFF;
  }

  .p-picklist .p-picklist-buttons {
    padding: 1.143rem;
  }
  .p-picklist .p-picklist-buttons .p-button {
    margin-bottom: 0.5rem;
  }
  .p-picklist .p-picklist-header {
    background: transparent;
    color: #FFFFFF;
    border: 1px solid rgba(255, 255, 255, 0.15);
    padding: 0.714rem 1.143rem;
    border-bottom: 0 none;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-picklist .p-picklist-header .p-picklist-title {
    font-weight: 500;
  }
  .p-picklist .p-picklist-filter-container {
    padding: 0.714rem 1.143rem;
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-bottom: 0 none;
  }
  .p-picklist .p-picklist-filter-container .p-picklist-filter-input {
    padding-right: 1.571rem;
  }
  .p-picklist .p-picklist-filter-container .p-picklist-filter-icon {
    right: 0.571rem;
    color: #C8CCD8;
  }
  .p-picklist .p-picklist-list {
    border: 1px solid rgba(255, 255, 255, 0.15);
    background: transparent;
    color: #FFFFFF;
    padding: 0.286rem;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
    outline: 0 none;
  }
  .p-picklist .p-picklist-list .p-picklist-item {
    padding: 0.429rem 0.286rem;
    margin: 0;
    border: 0 none;
    color: #FFFFFF;
    background: transparent;
    transition: box-shadow 0.2s;
  }
  .p-picklist .p-picklist-list .p-picklist-item:not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
  }
  .p-picklist .p-picklist-list .p-picklist-item.p-focus {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.1);
  }
  .p-picklist .p-picklist-list .p-picklist-item.p-highlight {
    color: #0A061A;
    background: #EEE500;
  }
  .p-picklist .p-picklist-list .p-picklist-item.p-highlight.p-focus {
    background: rgba(238, 229, 0, 0.24);
  }
  .p-picklist .p-picklist-list .p-picklist-empty-message {
    padding: 0.429rem 0.286rem;
    color: #FFFFFF;
  }
  .p-picklist .p-picklist-list:not(.cdk-drop-list-dragging) .p-picklist-item:not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
  }
  .p-picklist.p-picklist-striped .p-picklist-list .p-picklist-item:nth-child(even) {
    background: rgba(255, 255, 255, 0.01);
  }
  .p-picklist.p-picklist-striped .p-picklist-list .p-picklist-item:nth-child(even):hover {
    background: rgba(255, 255, 255, 0.05);
  }

  .p-picklist-item.cdk-drag-preview {
    padding: 0.429rem 0.286rem;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
    border: 0 none;
    color: #FFFFFF;
    background: transparent;
    margin: 0;
  }

  .p-timeline .p-timeline-event-marker {
    border: 2px solid #EEE500;
    border-radius: 50%;
    width: 1rem;
    height: 1rem;
    background-color: rgba(255, 255, 255, 0.05);
  }
  .p-timeline .p-timeline-event-connector {
    background-color: rgba(255, 255, 255, 0.05);
  }
  .p-timeline.p-timeline-vertical .p-timeline-event-opposite,
.p-timeline.p-timeline-vertical .p-timeline-event-content {
    padding: 0 1rem;
  }
  .p-timeline.p-timeline-vertical .p-timeline-event-connector {
    width: 2px;
  }
  .p-timeline.p-timeline-horizontal .p-timeline-event-opposite,
.p-timeline.p-timeline-horizontal .p-timeline-event-content {
    padding: 1rem 0;
  }
  .p-timeline.p-timeline-horizontal .p-timeline-event-connector {
    height: 2px;
  }

  .p-tree {
    border: 1px solid rgba(255, 255, 255, 0.15);
    background: transparent;
    color: #FFFFFF;
    padding: 1.143rem;
    border-radius: 6px;
  }
  .p-tree .p-tree-container .p-treenode {
    padding: 0.143rem;
    outline: 0 none;
  }
  .p-tree .p-tree-container .p-treenode:focus > .p-treenode-content {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content {
    border-radius: 6px;
    transition: box-shadow 0.2s;
    padding: 0.429rem 0.571rem;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-tree-toggler {
    margin-right: 0.5rem;
    width: 2rem;
    height: 2rem;
    color: #C8CCD8;
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-tree-toggler:enabled:hover {
    color: #FFFFFF;
    border-color: transparent;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-tree-toggler:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-treenode-icon {
    margin-right: 0.5rem;
    color: #FFFFFF;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-checkbox {
    margin-right: 0.5rem;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content .p-checkbox .p-indeterminate .p-checkbox-icon {
    color: #FFFFFF;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight {
    background: #EEE500;
    color: #0A061A;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight .p-tree-toggler,
.p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight .p-treenode-icon {
    color: #0A061A;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight .p-tree-toggler:hover,
.p-tree .p-tree-container .p-treenode .p-treenode-content.p-highlight .p-treenode-icon:hover {
    color: #0A061A;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-treenode-selectable:not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
  }
  .p-tree .p-tree-container .p-treenode .p-treenode-content.p-treenode-dragover {
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
  }
  .p-tree .p-tree-filter-container {
    margin-bottom: 0.5rem;
  }
  .p-tree .p-tree-filter-container .p-tree-filter {
    width: 100%;
    padding-right: 1.571rem;
  }
  .p-tree .p-tree-filter-container .p-tree-filter-icon {
    right: 0.571rem;
    color: #C8CCD8;
  }
  .p-tree .p-treenode-children {
    padding: 0 0 0 1rem;
  }
  .p-tree .p-tree-loading-icon {
    font-size: 2rem;
  }
  .p-tree .p-tree-loading-icon.p-icon {
    width: 2rem;
    height: 2rem;
  }
  .p-tree .p-treenode-droppoint.p-treenode-droppoint-active {
    background-color: #beb700;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content {
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    background-color: transparent;
    color: #FFFFFF;
    padding: 0.429rem 0.571rem;
    transition: box-shadow 0.2s;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content.p-highlight {
    background-color: #EEE500;
    color: #0A061A;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content.p-highlight .p-treenode-icon {
    color: #0A061A;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content .p-tree-toggler {
    margin-right: 0.5rem;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content .p-treenode-icon {
    color: #FFFFFF;
    margin-right: 0.5rem;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content .p-checkbox {
    margin-right: 0.5rem;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content .p-treenode-label:not(.p-highlight):hover {
    background-color: inherit;
    color: inherit;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content.p-treenode-selectable:not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
  }
  .p-tree.p-tree-horizontal .p-treenode .p-treenode-content:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }

  .p-treetable .p-paginator-top {
    border-width: 1px 0 1px 0;
    border-radius: 0;
  }
  .p-treetable .p-paginator-bottom {
    border-width: 1px 0 1px 0;
    border-radius: 0;
  }
  .p-treetable .p-treetable-header {
    background: var(--surface-section);
    color: #868C9B;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-width: 0 0 1px 0;
    padding: 0.571rem 0.571rem;
    font-weight: 500;
  }
  .p-treetable .p-treetable-footer {
    background: var(--surface-section);
    color: #868C9B;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-width: 0 0 1px 0;
    padding: 0.571rem 0.571rem;
    font-weight: 700;
  }
  .p-treetable .p-treetable-thead > tr > th {
    text-align: left;
    padding: 0.571rem 0.571rem;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-width: 0 0 1px 0;
    font-weight: 500;
    color: #868C9B;
    background: var(--surface-section);
    transition: box-shadow 0.2s;
  }
  .p-treetable .p-treetable-tfoot > tr > td {
    text-align: left;
    padding: 0.571rem 0.571rem;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-width: 0 0 1px 0;
    font-weight: 500;
    color: #868C9B;
    background: var(--surface-section);
  }
  .p-treetable .p-sortable-column {
    outline-color: #EEE500;
  }
  .p-treetable .p-sortable-column .p-sortable-column-icon {
    color: #868C9B;
    margin-left: 0.5rem;
  }
  .p-treetable .p-sortable-column .p-sortable-column-badge {
    border-radius: 50%;
    height: 1.143rem;
    min-width: 1.143rem;
    line-height: 1.143rem;
    color: #0A061A;
    background: #EEE500;
    margin-left: 0.5rem;
  }
  .p-treetable .p-sortable-column:not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.05);
    color: #868C9B;
  }
  .p-treetable .p-sortable-column:not(.p-highlight):hover .p-sortable-column-icon {
    color: #868C9B;
  }
  .p-treetable .p-sortable-column.p-highlight {
    background: rgba(255, 255, 255, 0.1);
    color: #868C9B;
  }
  .p-treetable .p-sortable-column.p-highlight .p-sortable-column-icon {
    color: #868C9B;
  }
  .p-treetable .p-treetable-tbody > tr {
    background: var(--surface-section);
    color: #FFFFFF;
    transition: box-shadow 0.2s;
  }
  .p-treetable .p-treetable-tbody > tr > td {
    text-align: left;
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-width: 0 0 0 0;
    padding: 0.429rem 0.571rem;
  }
  .p-treetable .p-treetable-tbody > tr > td .p-treetable-toggler {
    width: 2rem;
    height: 2rem;
    color: #C8CCD8;
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
    margin-right: 0.5rem;
  }
  .p-treetable .p-treetable-tbody > tr > td .p-treetable-toggler:enabled:hover {
    color: #FFFFFF;
    border-color: transparent;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-treetable .p-treetable-tbody > tr > td .p-treetable-toggler:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-treetable .p-treetable-tbody > tr > td .p-treetable-toggler.p-icon {
    width: 2rem;
    height: 2rem;
  }
  .p-treetable .p-treetable-tbody > tr > td p-treetablecheckbox .p-checkbox {
    margin-right: 0.5rem;
  }
  .p-treetable .p-treetable-tbody > tr > td p-treetablecheckbox .p-checkbox .p-indeterminate .p-checkbox-icon {
    color: #FFFFFF;
  }
  .p-treetable .p-treetable-tbody > tr:focus-visible {
    outline: 0.15rem solid #EEE500;
    outline-offset: -0.15rem;
  }
  .p-treetable .p-treetable-tbody > tr.p-highlight {
    background: #EEE500;
    color: #0A061A;
  }
  .p-treetable .p-treetable-tbody > tr.p-highlight .p-treetable-toggler {
    color: #0A061A;
  }
  .p-treetable .p-treetable-tbody > tr.p-highlight .p-treetable-toggler:hover {
    color: #0A061A;
  }
  .p-treetable.p-treetable-hoverable-rows .p-treetable-tbody > tr:not(.p-highlight):hover {
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
  }
  .p-treetable.p-treetable-hoverable-rows .p-treetable-tbody > tr:not(.p-highlight):hover .p-treetable-toggler {
    color: #FFFFFF;
  }
  .p-treetable .p-column-resizer-helper {
    background: #EEE500;
  }
  .p-treetable .p-treetable-scrollable-header,
.p-treetable .p-treetable-scrollable-footer {
    background: transparent;
  }
  .p-treetable .p-treetable-loading-icon {
    font-size: 2rem;
  }
  .p-treetable .p-treetable-loading-icon.p-icon {
    width: 2rem;
    height: 2rem;
  }
  .p-treetable.p-treetable-gridlines .p-datatable-header {
    border-width: 1px 1px 0 1px;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-footer {
    border-width: 0 1px 1px 1px;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-top {
    border-width: 0 1px 0 1px;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-bottom {
    border-width: 0 1px 1px 1px;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-thead > tr > th {
    border-width: 1px;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-tbody > tr > td {
    border-width: 1px;
  }
  .p-treetable.p-treetable-gridlines .p-treetable-tfoot > tr > td {
    border-width: 1px;
  }
  .p-treetable.p-treetable-sm .p-treetable-header {
    padding: 0.499625rem 0.499625rem;
  }
  .p-treetable.p-treetable-sm .p-treetable-thead > tr > th {
    padding: 0.2855rem 0.2855rem;
  }
  .p-treetable.p-treetable-sm .p-treetable-tbody > tr > td {
    padding: 0.2145rem 0.2855rem;
  }
  .p-treetable.p-treetable-sm .p-treetable-tfoot > tr > td {
    padding: 0.2855rem 0.2855rem;
  }
  .p-treetable.p-treetable-sm .p-treetable-footer {
    padding: 0.2855rem 0.2855rem;
  }
  .p-treetable.p-treetable-lg .p-treetable-header {
    padding: 0.71375rem 0.71375rem;
  }
  .p-treetable.p-treetable-lg .p-treetable-thead > tr > th {
    padding: 0.71375rem 0.71375rem;
  }
  .p-treetable.p-treetable-lg .p-treetable-tbody > tr > td {
    padding: 0.53625rem 0.71375rem;
  }
  .p-treetable.p-treetable-lg .p-treetable-tfoot > tr > td {
    padding: 0.71375rem 0.71375rem;
  }
  .p-treetable.p-treetable-lg .p-treetable-footer {
    padding: 0.71375rem 0.71375rem;
  }

  .p-virtualscroller .p-virtualscroller-header {
    background: var(--surface-section);
    color: #868C9B;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-width: 0 0 1px 0;
    padding: 0.571rem 0.571rem;
    font-weight: 500;
  }
  .p-virtualscroller .p-virtualscroller-content {
    background: var(--surface-section);
    color: #FFFFFF;
    border: 0 none;
    padding: 0;
  }
  .p-virtualscroller .p-virtualscroller-footer {
    background: var(--surface-section);
    color: #868C9B;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-width: 0 0 1px 0;
    padding: 0.571rem 0.571rem;
    font-weight: 700;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  .p-accordion .p-accordion-header .p-accordion-header-link {
    padding: 0.714rem 1.143rem;
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: #FFFFFF;
    background: transparent;
    font-weight: 500;
    border-radius: 6px;
    transition: box-shadow 0.2s;
  }
  .p-accordion .p-accordion-header .p-accordion-header-link .p-accordion-toggle-icon {
    margin-right: 0.5rem;
  }
  .p-accordion .p-accordion-header:not(.p-disabled) .p-accordion-header-link:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 none;
  }
  .p-accordion .p-accordion-header:not(.p-highlight):not(.p-disabled):hover .p-accordion-header-link {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
  }
  .p-accordion .p-accordion-header:not(.p-disabled).p-highlight .p-accordion-header-link {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }
  .p-accordion .p-accordion-header:not(.p-disabled).p-highlight:hover .p-accordion-header-link {
    border-color: rgba(255, 255, 255, 0.05);
    background: rgba(255, 255, 255, 0.15);
    color: #FFFFFF;
  }
  .p-accordion .p-accordion-content {
    padding: 1.143rem;
    border: 1px solid rgba(255, 255, 255, 0.15);
    background: transparent;
    color: #FFFFFF;
    border-top: 0;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-accordion p-accordiontab .p-accordion-tab {
    margin-bottom: 0;
  }
  .p-accordion p-accordiontab .p-accordion-header .p-accordion-header-link {
    border-radius: 0;
  }
  .p-accordion p-accordiontab .p-accordion-content {
    border-radius: 0;
  }
  .p-accordion p-accordiontab:not(:first-child) .p-accordion-header .p-accordion-header-link {
    border-top: 0 none;
  }
  .p-accordion p-accordiontab:not(:first-child) .p-accordion-header:not(.p-highlight):not(.p-disabled):hover .p-accordion-header-link, .p-accordion p-accordiontab:not(:first-child) .p-accordion-header:not(.p-disabled).p-highlight:hover .p-accordion-header-link {
    border-top: 0 none;
  }
  .p-accordion p-accordiontab:first-child .p-accordion-header .p-accordion-header-link {
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-accordion p-accordiontab:last-child .p-accordion-header:not(.p-highlight) .p-accordion-header-link {
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-accordion p-accordiontab:last-child .p-accordion-content {
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }

  .p-card {
    background: transparent;
    color: #FFFFFF;
    box-shadow: 0 2px 1px -1px rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14), 0 1px 3px 0 rgba(0, 0, 0, 0.12);
    border-radius: 6px;
  }
  .p-card .p-card-body {
    padding: 0.857rem 1.143rem;
  }
  .p-card .p-card-title {
    font-size: 1.143rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
  }
  .p-card .p-card-subtitle {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #868C9B;
  }
  .p-card .p-card-content {
    padding: 1rem 0;
  }
  .p-card .p-card-footer {
    padding: 1rem 0 0 0;
  }

  .p-divider .p-divider-content {
    background-color: transparent;
  }
  .p-divider.p-divider-horizontal {
    margin: 1rem 0;
    padding: 0 1rem;
  }
  .p-divider.p-divider-horizontal:before {
    border-top: 1px rgba(255, 255, 255, 0.1);
  }
  .p-divider.p-divider-horizontal .p-divider-content {
    padding: 0 0.5rem;
  }
  .p-divider.p-divider-vertical {
    margin: 0 1rem;
    padding: 1rem 0;
  }
  .p-divider.p-divider-vertical:before {
    border-left: 1px rgba(255, 255, 255, 0.1);
  }
  .p-divider.p-divider-vertical .p-divider-content {
    padding: 0.5rem 0;
  }

  .p-fieldset {
    border: 1px solid rgba(255, 255, 255, 0.15);
    background: transparent;
    color: #FFFFFF;
    border-radius: 6px;
  }
  .p-fieldset .p-fieldset-legend {
    padding: 0.714rem 1.143rem;
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: #FFFFFF;
    background: transparent;
    font-weight: 500;
    border-radius: 6px;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend {
    padding: 0;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend a {
    padding: 0.714rem 1.143rem;
    color: #FFFFFF;
    border-radius: 6px;
    transition: box-shadow 0.2s;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend a .p-fieldset-toggler {
    margin-right: 0.5rem;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend a:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-fieldset.p-fieldset-toggleable .p-fieldset-legend:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.15);
    color: #FFFFFF;
  }
  .p-fieldset .p-fieldset-content {
    padding: 1.143rem;
  }

  .p-panel .p-panel-header {
    border: 1px solid rgba(255, 255, 255, 0.15);
    padding: 0.714rem 1.143rem;
    background: transparent;
    color: #FFFFFF;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-panel .p-panel-header .p-panel-title {
    font-weight: 500;
  }
  .p-panel .p-panel-header .p-panel-header-icon {
    width: 2rem;
    height: 2rem;
    color: #C8CCD8;
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .p-panel .p-panel-header .p-panel-header-icon:enabled:hover {
    color: #FFFFFF;
    border-color: transparent;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-panel .p-panel-header .p-panel-header-icon:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-panel.p-panel-toggleable .p-panel-header {
    padding: 0.429rem 0.571rem;
  }
  .p-panel .p-panel-content {
    padding: 1.143rem;
    border: 1px solid rgba(255, 255, 255, 0.15);
    background: transparent;
    color: #FFFFFF;
    border-top: 0 none;
  }
  .p-panel .p-panel-content:last-child {
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-panel .p-panel-footer {
    padding: 0.714rem 1.143rem;
    border: 1px solid rgba(255, 255, 255, 0.15);
    background: transparent;
    color: #FFFFFF;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
    border-top: 0 none;
  }
  .p-panel .p-panel-icons-end {
    order: 2;
    margin-left: auto;
  }
  .p-panel .p-panel-icons-start {
    order: 0;
    margin-right: 0.5rem;
  }
  .p-panel .p-panel-icons-center {
    order: 2;
    width: 100%;
    text-align: center;
  }

  .p-scrollpanel .p-scrollpanel-bar {
    background: rgba(255, 255, 255, 0.1);
    border: 0 none;
  }

  .p-splitter {
    border: 1px solid rgba(255, 255, 255, 0.15);
    background: transparent;
    border-radius: 6px;
    color: #FFFFFF;
  }
  .p-splitter .p-splitter-gutter {
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
    background: rgba(255, 255, 255, 0.15);
  }
  .p-splitter .p-splitter-gutter .p-splitter-gutter-handle {
    background: rgba(255, 255, 255, 0.15);
  }
  .p-splitter .p-splitter-gutter-resizing {
    background: rgba(255, 255, 255, 0.15);
  }

  .p-tabview .p-tabview-nav-content {
    scroll-padding-inline: 2.286rem;
  }
  .p-tabview .p-tabview-nav {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-width: 0 0 2px 0;
  }
  .p-tabview .p-tabview-nav li {
    margin-right: 0.857rem;
  }
  .p-tabview .p-tabview-nav li .p-tabview-nav-link {
    border: solid transparent;
    border-width: 0 0 2px 0;
    border-color: transparent transparent transparent transparent;
    background: transparent;
    color: #868C9B;
    padding: 0.571rem 0.429rem;
    font-weight: 500;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
    transition: box-shadow 0.2s;
    margin: 0 0 -2px 0;
  }
  .p-tabview .p-tabview-nav li .p-tabview-nav-link:not(.p-disabled):focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 none;
  }
  .p-tabview .p-tabview-nav li:not(.p-highlight):not(.p-disabled):hover .p-tabview-nav-link {
    background: transparent;
    border-color: #FFFFFF;
    color: #FFFFFF;
  }
  .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
    background: transparent;
    border-color: #EEE500;
    color: #EEE500;
  }
  .p-tabview .p-tabview-left-icon {
    margin-right: 0.5rem;
  }
  .p-tabview .p-tabview-right-icon {
    margin-left: 0.5rem;
  }
  .p-tabview .p-tabview-close {
    margin-left: 0.5rem;
  }
  .p-tabview .p-tabview-nav-btn.p-link {
    background: transparent;
    color: #EEE500;
    width: 2.286rem;
    box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
    border-radius: 0;
  }
  .p-tabview .p-tabview-nav-btn.p-link:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 none;
  }
  .p-tabview .p-tabview-panels {
    background: transparent;
    padding: 1.143rem 0;
    border: 0 none;
    color: #FFFFFF;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }

  .p-toolbar {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.15);
    padding: 0.714rem 1.143rem;
    border-radius: 6px;
    gap: 0.5rem;
  }
  .p-toolbar .p-toolbar-separator {
    margin: 0 0.5rem;
  }

  .p-confirm-popup {
    background: #252636;
    color: #FFFFFF;
    border: 0 none;
    border-radius: 6px;
    box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.3);
  }
  .p-confirm-popup .p-confirm-popup-content {
    padding: 1.143rem;
  }
  .p-confirm-popup .p-confirm-popup-footer {
    text-align: right;
    padding: 0.714rem 1.143rem;
  }
  .p-confirm-popup .p-confirm-popup-footer button {
    margin: 0 0.5rem 0 0;
    width: auto;
  }
  .p-confirm-popup .p-confirm-popup-footer button:last-child {
    margin: 0;
  }
  .p-confirm-popup:after {
    border: solid transparent;
    border-color: rgba(37, 38, 54, 0);
    border-bottom-color: #252636;
  }
  .p-confirm-popup:before {
    border: solid transparent;
    border-color: rgba(37, 38, 54, 0);
    border-bottom-color: #252636;
  }
  .p-confirm-popup.p-confirm-popup-flipped:after {
    border-top-color: #252636;
  }
  .p-confirm-popup.p-confirm-popup-flipped:before {
    border-top-color: #252636;
  }
  .p-confirm-popup .p-confirm-popup-icon {
    font-size: 1.5rem;
  }
  .p-confirm-popup .p-confirm-popup-icon.p-icon {
    width: 1.5rem;
    height: 1.5rem;
  }
  .p-confirm-popup .p-confirm-popup-message {
    margin-left: 1rem;
  }

  .p-dialog {
    border-radius: 6px;
    box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.3);
    border: 0 none;
  }
  .p-dialog .p-dialog-header {
    border-bottom: 0 none;
    background: #252636;
    color: #FFFFFF;
    padding: 1.286rem 1.714rem;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-dialog .p-dialog-header .p-dialog-title {
    font-weight: 500;
    font-size: 1.143rem;
  }
  .p-dialog .p-dialog-header .p-dialog-header-icon {
    width: 2rem;
    height: 2rem;
    color: #C8CCD8;
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
    margin-right: 0.5rem;
  }
  .p-dialog .p-dialog-header .p-dialog-header-icon:enabled:hover {
    color: #FFFFFF;
    border-color: transparent;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-dialog .p-dialog-header .p-dialog-header-icon:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-dialog .p-dialog-header .p-dialog-header-icon:last-child {
    margin-right: 0;
  }
  .p-dialog .p-dialog-content {
    background: #252636;
    color: #FFFFFF;
    padding: 0 1.714rem 1.714rem 1.714rem;
  }
  .p-dialog .p-dialog-content:last-of-type {
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-dialog .p-dialog-footer {
    border-top: 0 none;
    background: #252636;
    color: #FFFFFF;
    padding: 0 1.714rem 1.714rem 1.714rem;
    text-align: right;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-dialog .p-dialog-footer button {
    margin: 0 0.5rem 0 0;
    width: auto;
  }
  .p-dialog.p-confirm-dialog .p-confirm-dialog-icon {
    font-size: 2rem;
  }
  .p-dialog.p-confirm-dialog .p-confirm-dialog-icon.p-icon {
    width: 2rem;
    height: 2rem;
  }
  .p-dialog.p-confirm-dialog .p-confirm-dialog-message {
    margin-left: 1rem;
  }

  .p-overlaypanel {
    background: #252636;
    color: #FFFFFF;
    border: 0 none;
    border-radius: 6px;
    box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.3);
  }
  .p-overlaypanel .p-overlaypanel-content {
    padding: 1.143rem;
  }
  .p-overlaypanel .p-overlaypanel-close {
    background: #EEE500;
    color: #0A061A;
    width: 2rem;
    height: 2rem;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
    border-radius: 50%;
    position: absolute;
    top: -1rem;
    right: -1rem;
  }
  .p-overlaypanel .p-overlaypanel-close:enabled:hover {
    background: #d6ce00;
    color: #0A061A;
  }
  .p-overlaypanel:after {
    border: solid transparent;
    border-color: rgba(37, 38, 54, 0);
    border-bottom-color: #252636;
  }
  .p-overlaypanel:before {
    border: solid transparent;
    border-color: rgba(37, 38, 54, 0);
    border-bottom-color: #232433;
  }
  .p-overlaypanel.p-overlaypanel-flipped:after {
    border-top-color: #252636;
  }
  .p-overlaypanel.p-overlaypanel-flipped:before {
    border-top-color: #252636;
  }

  .p-sidebar {
    background: #252636;
    color: #FFFFFF;
    border: 0 none;
    box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.3);
  }
  .p-sidebar .p-sidebar-header {
    padding: 0.714rem 1.143rem;
  }
  .p-sidebar .p-sidebar-header .p-sidebar-close,
.p-sidebar .p-sidebar-header .p-sidebar-icon {
    width: 2rem;
    height: 2rem;
    color: #C8CCD8;
    border: 0 none;
    background: transparent;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .p-sidebar .p-sidebar-header .p-sidebar-close:enabled:hover,
.p-sidebar .p-sidebar-header .p-sidebar-icon:enabled:hover {
    color: #FFFFFF;
    border-color: transparent;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-sidebar .p-sidebar-header .p-sidebar-close:focus-visible,
.p-sidebar .p-sidebar-header .p-sidebar-icon:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-sidebar .p-sidebar-header + .p-sidebar-content {
    padding-top: 0;
  }
  .p-sidebar .p-sidebar-content {
    padding: 1.143rem;
  }
  .p-sidebar .p-sidebar-footer {
    padding: 0.714rem 1.143rem;
  }

  .p-tooltip .p-tooltip-text {
    background: #0a061a;
    color: #FFFFFF;
    padding: 0.429rem 0.571rem;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
    border-radius: 6px;
  }
  .p-tooltip.p-tooltip-right .p-tooltip-arrow {
    border-right-color: #0a061a;
  }
  .p-tooltip.p-tooltip-left .p-tooltip-arrow {
    border-left-color: #0a061a;
  }
  .p-tooltip.p-tooltip-top .p-tooltip-arrow {
    border-top-color: #0a061a;
  }
  .p-tooltip.p-tooltip-bottom .p-tooltip-arrow {
    border-bottom-color: #0a061a;
  }

  .p-fileupload .p-fileupload-buttonbar {
    background: transparent;
    padding: 0.714rem 1.143rem;
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: #FFFFFF;
    border-bottom: 0 none;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-fileupload .p-fileupload-buttonbar .p-button {
    margin-right: 0.5rem;
  }
  .p-fileupload .p-fileupload-buttonbar .p-button.p-fileupload-choose.p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-fileupload .p-fileupload-content {
    background: transparent;
    padding: 2rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: #FFFFFF;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-fileupload .p-fileupload-content.p-fileupload-highlight {
    border-color: 1px dashed #EEE500;
    border-style: dashed;
    background-color: #EEE500;
  }
  .p-fileupload .p-progressbar {
    height: 0.25rem;
  }
  .p-fileupload .p-fileupload-row > div {
    padding: 0.429rem 0.571rem;
  }
  .p-fileupload.p-fileupload-advanced .p-message {
    margin-top: 0;
  }

  .p-fileupload-choose:not(.p-disabled):hover {
    background: #d6ce00;
    color: #0A061A;
    border-color: #d6ce00;
  }
  .p-fileupload-choose:not(.p-disabled):active {
    background: #beb700;
    color: #0A061A;
    border-color: #beb700;
  }

  .p-breadcrumb {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 6px;
    padding: 0.857rem;
  }
  .p-breadcrumb .p-breadcrumb-list li .p-menuitem-link {
    transition: box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-breadcrumb .p-breadcrumb-list li .p-menuitem-link:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-breadcrumb .p-breadcrumb-list li .p-menuitem-link .p-menuitem-text {
    color: #C8CCD8;
  }
  .p-breadcrumb .p-breadcrumb-list li .p-menuitem-link .p-menuitem-icon {
    color: #C8CCD8;
  }
  .p-breadcrumb .p-breadcrumb-list li.p-menuitem-separator {
    margin: 0 0.5rem 0 0.5rem;
    color: #C8CCD8;
  }
  .p-breadcrumb .p-breadcrumb-list li:last-child .p-menuitem-text {
    color: #C8CCD8;
  }
  .p-breadcrumb .p-breadcrumb-list li:last-child .p-menuitem-icon {
    color: #C8CCD8;
  }

  .p-contextmenu {
    padding: 0.429rem;
    background: #0a061a;
    color: #C8CCD8;
    border: 1px solid transparent;
    box-shadow: none;
    border-radius: 6px;
    width: 12.5rem;
  }
  .p-contextmenu .p-contextmenu-root-list {
    outline: 0 none;
  }
  .p-contextmenu .p-submenu-list {
    padding: 0.429rem;
    background: #0a061a;
    border: 1px solid transparent;
    box-shadow: none;
    border-radius: 6px;
  }
  .p-contextmenu .p-menuitem > .p-menuitem-content {
    color: #C8CCD8;
    transition: box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-contextmenu .p-menuitem > .p-menuitem-content .p-menuitem-link {
    color: #C8CCD8;
    padding: 0.571rem 0.429rem;
    user-select: none;
  }
  .p-contextmenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #C8CCD8;
  }
  .p-contextmenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #C8CCD8;
    margin-right: 0.5rem;
  }
  .p-contextmenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #C8CCD8;
  }
  .p-contextmenu .p-menuitem.p-highlight > .p-menuitem-content {
    color: #0A061A;
    background: #EEE500;
  }
  .p-contextmenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #0A061A;
  }
  .p-contextmenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-contextmenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #0A061A;
  }
  .p-contextmenu .p-menuitem.p-highlight.p-focus > .p-menuitem-content {
    background: rgba(238, 229, 0, 0.24);
  }
  .p-contextmenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.1);
  }
  .p-contextmenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #FFFFFF;
  }
  .p-contextmenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-contextmenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #FFFFFF;
  }
  .p-contextmenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-contextmenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #FFFFFF;
  }
  .p-contextmenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-contextmenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #FFFFFF;
  }
  .p-contextmenu .p-menuitem-separator {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    margin: 0.25rem 0;
  }
  .p-contextmenu .p-submenu-icon {
    font-size: 0.875rem;
  }
  .p-contextmenu .p-submenu-icon.p-icon {
    width: 0.875rem;
    height: 0.875rem;
  }

  .p-dock .p-dock-list-container {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0.5rem 0.5rem;
    border-radius: 0.5rem;
  }
  .p-dock .p-dock-list-container .p-dock-list {
    outline: 0 none;
  }
  .p-dock .p-dock-item {
    padding: 0.5rem;
    border-radius: 6px;
  }
  .p-dock .p-dock-item.p-focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-dock .p-dock-link {
    width: 4rem;
    height: 4rem;
  }
  .p-dock.p-dock-top .p-dock-item-second-prev,
.p-dock.p-dock-top .p-dock-item-second-next, .p-dock.p-dock-bottom .p-dock-item-second-prev,
.p-dock.p-dock-bottom .p-dock-item-second-next {
    margin: 0 0.9rem;
  }
  .p-dock.p-dock-top .p-dock-item-prev,
.p-dock.p-dock-top .p-dock-item-next, .p-dock.p-dock-bottom .p-dock-item-prev,
.p-dock.p-dock-bottom .p-dock-item-next {
    margin: 0 1.3rem;
  }
  .p-dock.p-dock-top .p-dock-item-current, .p-dock.p-dock-bottom .p-dock-item-current {
    margin: 0 1.5rem;
  }
  .p-dock.p-dock-left .p-dock-item-second-prev,
.p-dock.p-dock-left .p-dock-item-second-next, .p-dock.p-dock-right .p-dock-item-second-prev,
.p-dock.p-dock-right .p-dock-item-second-next {
    margin: 0.9rem 0;
  }
  .p-dock.p-dock-left .p-dock-item-prev,
.p-dock.p-dock-left .p-dock-item-next, .p-dock.p-dock-right .p-dock-item-prev,
.p-dock.p-dock-right .p-dock-item-next {
    margin: 1.3rem 0;
  }
  .p-dock.p-dock-left .p-dock-item-current, .p-dock.p-dock-right .p-dock-item-current {
    margin: 1.5rem 0;
  }

  @media screen and (max-width: 960px) {
    .p-dock.p-dock-top .p-dock-list-container, .p-dock.p-dock-bottom .p-dock-list-container {
      overflow-x: auto;
      width: 100%;
    }
    .p-dock.p-dock-top .p-dock-list-container .p-dock-list, .p-dock.p-dock-bottom .p-dock-list-container .p-dock-list {
      margin: 0 auto;
    }
    .p-dock.p-dock-left .p-dock-list-container, .p-dock.p-dock-right .p-dock-list-container {
      overflow-y: auto;
      height: 100%;
    }
    .p-dock.p-dock-left .p-dock-list-container .p-dock-list, .p-dock.p-dock-right .p-dock-list-container .p-dock-list {
      margin: auto 0;
    }
    .p-dock .p-dock-list .p-dock-item {
      transform: none;
      margin: 0;
    }
  }
  .p-megamenu {
    padding: 0.857rem;
    background: transparent;
    color: #C8CCD8;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 6px;
  }
  .p-megamenu .p-megamenu-root-list {
    outline: 0 none;
  }
  .p-megamenu .p-menuitem > .p-menuitem-content {
    color: #C8CCD8;
    transition: box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-megamenu .p-menuitem > .p-menuitem-content .p-menuitem-link {
    color: #C8CCD8;
    padding: 0.571rem 0.429rem;
    user-select: none;
  }
  .p-megamenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #C8CCD8;
  }
  .p-megamenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #C8CCD8;
    margin-right: 0.5rem;
  }
  .p-megamenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #C8CCD8;
  }
  .p-megamenu .p-menuitem.p-highlight > .p-menuitem-content {
    color: #0A061A;
    background: #EEE500;
  }
  .p-megamenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #0A061A;
  }
  .p-megamenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-megamenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #0A061A;
  }
  .p-megamenu .p-menuitem.p-highlight.p-focus > .p-menuitem-content {
    background: rgba(238, 229, 0, 0.24);
  }
  .p-megamenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.1);
  }
  .p-megamenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #FFFFFF;
  }
  .p-megamenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-megamenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #FFFFFF;
  }
  .p-megamenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-megamenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #FFFFFF;
  }
  .p-megamenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-megamenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #FFFFFF;
  }
  .p-megamenu .p-megamenu-panel {
    background: #0a061a;
    color: #C8CCD8;
    border: 1px solid transparent;
    box-shadow: none;
  }
  .p-megamenu .p-submenu-header {
    margin: 0;
    padding: 0.571rem 0.429rem;
    color: #868C9B;
    background: transparent;
    font-weight: 500;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-megamenu .p-submenu-list {
    padding: 0.429rem;
    width: 12.5rem;
  }
  .p-megamenu .p-submenu-list .p-menuitem-separator {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    margin: 0.25rem 0;
  }
  .p-megamenu.p-megamenu-vertical {
    width: 12.5rem;
    padding: 0.429rem;
  }
  .p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem > .p-menuitem-content {
    color: #C8CCD8;
    transition: box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link {
    padding: 0.571rem 0.429rem;
    user-select: none;
  }
  .p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #C8CCD8;
  }
  .p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #C8CCD8;
    margin-right: 0.5rem;
  }
  .p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #C8CCD8;
    margin-left: 0.5rem;
  }
  .p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #FFFFFF;
  }
  .p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-megamenu.p-megamenu-horizontal .p-megamenu-root-list > .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #FFFFFF;
  }

  .p-menu {
    padding: 0.429rem;
    background: transparent;
    color: #C8CCD8;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 6px;
    width: 12.5rem;
  }
  .p-menu .p-menuitem > .p-menuitem-content {
    color: #C8CCD8;
    transition: box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-menu .p-menuitem > .p-menuitem-content .p-menuitem-link {
    color: #C8CCD8;
    padding: 0.571rem 0.429rem;
    user-select: none;
  }
  .p-menu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #C8CCD8;
  }
  .p-menu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #C8CCD8;
    margin-right: 0.5rem;
  }
  .p-menu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #C8CCD8;
  }
  .p-menu .p-menuitem.p-highlight > .p-menuitem-content {
    color: #0A061A;
    background: #EEE500;
  }
  .p-menu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #0A061A;
  }
  .p-menu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-menu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #0A061A;
  }
  .p-menu .p-menuitem.p-highlight.p-focus > .p-menuitem-content {
    background: rgba(238, 229, 0, 0.24);
  }
  .p-menu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.1);
  }
  .p-menu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #FFFFFF;
  }
  .p-menu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-menu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #FFFFFF;
  }
  .p-menu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-menu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #FFFFFF;
  }
  .p-menu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-menu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #FFFFFF;
  }
  .p-menu.p-menu-overlay {
    background: #0a061a;
    border: 1px solid transparent;
    box-shadow: none;
  }
  .p-menu .p-submenu-header {
    margin: 0;
    padding: 0.571rem 0.429rem;
    color: #868C9B;
    background: transparent;
    font-weight: 500;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-menu .p-menuitem-separator {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    margin: 0.25rem 0;
  }
  .p-menu .p-menuitem-badge {
    background: #EEE500;
    color: #0A061A;
    font-size: 1rem;
    font-weight: 500;
    min-width: 1.429rem;
    height: 1.429rem;
    line-height: 1.429rem;
    border-radius: 6px;
    margin-left: 0.5rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .p-menubar {
    padding: 0.857rem;
    background: transparent;
    color: #C8CCD8;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 6px;
  }
  .p-menubar .p-menubar-root-list {
    outline: 0 none;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-content {
    color: #C8CCD8;
    transition: box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link {
    padding: 0.571rem 0.429rem;
    user-select: none;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #C8CCD8;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #C8CCD8;
    margin-right: 0.5rem;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #C8CCD8;
    margin-left: 0.5rem;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-menubar .p-menubar-root-list > .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #FFFFFF;
  }
  .p-menubar .p-menubar-root-list > .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-menubar .p-menubar-root-list > .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #FFFFFF;
  }
  .p-menubar .p-menuitem > .p-menuitem-content {
    color: #C8CCD8;
    transition: box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-menubar .p-menuitem > .p-menuitem-content .p-menuitem-link {
    color: #C8CCD8;
    padding: 0.571rem 0.429rem;
    user-select: none;
  }
  .p-menubar .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #C8CCD8;
  }
  .p-menubar .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #C8CCD8;
    margin-right: 0.5rem;
  }
  .p-menubar .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #C8CCD8;
  }
  .p-menubar .p-menuitem.p-highlight > .p-menuitem-content {
    color: #0A061A;
    background: #EEE500;
  }
  .p-menubar .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #0A061A;
  }
  .p-menubar .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-menubar .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #0A061A;
  }
  .p-menubar .p-menuitem.p-highlight.p-focus > .p-menuitem-content {
    background: rgba(238, 229, 0, 0.24);
  }
  .p-menubar .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.1);
  }
  .p-menubar .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #FFFFFF;
  }
  .p-menubar .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-menubar .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #FFFFFF;
  }
  .p-menubar .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-menubar .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #FFFFFF;
  }
  .p-menubar .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-menubar .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #FFFFFF;
  }
  .p-menubar .p-submenu-list {
    padding: 0.429rem;
    background: #0a061a;
    border: 1px solid transparent;
    box-shadow: none;
    width: 12.5rem;
  }
  .p-menubar .p-submenu-list .p-menuitem-separator {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    margin: 0.25rem 0;
  }
  .p-menubar .p-submenu-list .p-submenu-icon {
    font-size: 0.875rem;
  }

  @media screen and (max-width: 960px) {
    .p-menubar {
      position: relative;
    }
    .p-menubar .p-menubar-button {
      display: flex;
      width: 2rem;
      height: 2rem;
      color: #C8CCD8;
      border-radius: 50%;
      transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
    }
    .p-menubar .p-menubar-button:hover {
      color: #FFFFFF;
      background: rgba(255, 255, 255, 0.05);
    }
    .p-menubar .p-menubar-button:focus {
      outline: 0 none;
      outline-offset: 0;
      box-shadow: 0 none;
    }
    .p-menubar .p-menubar-root-list {
      position: absolute;
      display: none;
      padding: 0.429rem;
      background: #0a061a;
      border: 1px solid transparent;
      box-shadow: none;
      width: 100%;
    }
    .p-menubar .p-menubar-root-list .p-menuitem-separator {
      border-top: 1px solid rgba(255, 255, 255, 0.05);
      margin: 0.25rem 0;
    }
    .p-menubar .p-menubar-root-list .p-submenu-icon {
      font-size: 0.875rem;
    }
    .p-menubar .p-menubar-root-list .p-menuitem {
      width: 100%;
      position: static;
    }
    .p-menubar .p-menubar-root-list .p-menuitem .p-menuitem-content .p-menuitem-link .p-submenu-icon {
      margin-left: auto;
      transition: transform 0.2s;
    }
    .p-menubar .p-menubar-root-list .p-menuitem.p-menuitem-active > .p-menuitem-content > .p-menuitem-link > .p-submenu-icon {
      transform: rotate(-180deg);
    }
    .p-menubar .p-menubar-root-list .p-submenu-list {
      width: 100%;
      position: static;
      box-shadow: none;
      border: 0 none;
    }
    .p-menubar .p-menubar-root-list .p-submenu-list .p-submenu-icon {
      transition: transform 0.2s;
      transform: rotate(90deg);
    }
    .p-menubar .p-menubar-root-list .p-submenu-list .p-menuitem-active > .p-menuitem-content > .p-menuitem-link > .p-submenu-icon {
      transform: rotate(-90deg);
    }
    .p-menubar .p-menubar-root-list .p-menuitem {
      width: 100%;
      position: static;
    }
    .p-menubar .p-menubar-root-list .p-submenu-list .p-menuitem .p-menuitem-content .p-menuitem-link {
      padding-left: 1.713rem;
    }
    .p-menubar .p-menubar-root-list .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-menuitem-content .p-menuitem-link {
      padding-left: 2.855rem;
    }
    .p-menubar .p-menubar-root-list .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-menuitem-content .p-menuitem-link {
      padding-left: 3.997rem;
    }
    .p-menubar .p-menubar-root-list .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-menuitem-content .p-menuitem-link {
      padding-left: 5.139rem;
    }
    .p-menubar .p-menubar-root-list .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-submenu-list .p-menuitem .p-menuitem-content .p-menuitem-link {
      padding-left: 6.281rem;
    }
    .p-menubar.p-menubar-mobile-active .p-menubar-root-list {
      display: flex;
      flex-direction: column;
      top: 100%;
      left: 0;
      z-index: 1;
    }
  }
  .p-panelmenu .p-panelmenu-header {
    outline: 0 none;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content {
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: #FFFFFF;
    background: transparent;
    border-radius: 6px;
    transition: box-shadow 0.2s;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action {
    color: #FFFFFF;
    padding: 0.714rem 1.143rem;
    font-weight: 500;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action .p-submenu-icon {
    margin-right: 0.5rem;
  }
  .p-panelmenu .p-panelmenu-header .p-panelmenu-header-content .p-panelmenu-header-action .p-menuitem-icon {
    margin-right: 0.5rem;
  }
  .p-panelmenu .p-panelmenu-header:not(.p-disabled):focus-visible .p-panelmenu-header-content {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 none;
  }
  .p-panelmenu .p-panelmenu-header:not(.p-highlight):not(.p-disabled):hover .p-panelmenu-header-content {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
  }
  .p-panelmenu .p-panelmenu-header:not(.p-disabled).p-highlight .p-panelmenu-header-content {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    margin-bottom: 0;
  }
  .p-panelmenu .p-panelmenu-header:not(.p-disabled).p-highlight:hover .p-panelmenu-header-content {
    border-color: rgba(255, 255, 255, 0.05);
    background: rgba(255, 255, 255, 0.15);
    color: #FFFFFF;
  }
  .p-panelmenu .p-panelmenu-content {
    padding: 0.429rem;
    border: 1px solid rgba(255, 255, 255, 0.15);
    background: transparent;
    color: #FFFFFF;
    border-top: 0;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-panelmenu .p-panelmenu-content .p-panelmenu-root-list {
    outline: 0 none;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem > .p-menuitem-content {
    color: #C8CCD8;
    transition: box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem > .p-menuitem-content .p-menuitem-link {
    color: #C8CCD8;
    padding: 0.571rem 0.429rem;
    user-select: none;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #C8CCD8;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #C8CCD8;
    margin-right: 0.5rem;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #C8CCD8;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem.p-highlight > .p-menuitem-content {
    color: #0A061A;
    background: #EEE500;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #0A061A;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-panelmenu .p-panelmenu-content .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #0A061A;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem.p-highlight.p-focus > .p-menuitem-content {
    background: rgba(238, 229, 0, 0.24);
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.1);
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #FFFFFF;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-panelmenu .p-panelmenu-content .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #FFFFFF;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #FFFFFF;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-panelmenu .p-panelmenu-content .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #FFFFFF;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    margin-right: 0.5rem;
  }
  .p-panelmenu .p-panelmenu-content .p-menuitem-separator {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    margin: 0.25rem 0;
  }
  .p-panelmenu .p-panelmenu-content .p-submenu-list:not(.p-panelmenu-root-list) {
    padding: 0 0 0 1rem;
  }
  .p-panelmenu .p-panelmenu-panel {
    margin-bottom: 0;
  }
  .p-panelmenu .p-panelmenu-panel .p-panelmenu-header .p-panelmenu-header-content {
    border-radius: 0;
  }
  .p-panelmenu .p-panelmenu-panel .p-panelmenu-content {
    border-radius: 0;
  }
  .p-panelmenu .p-panelmenu-panel:not(:first-child) .p-panelmenu-header .p-panelmenu-header-content {
    border-top: 0 none;
  }
  .p-panelmenu .p-panelmenu-panel:not(:first-child) .p-panelmenu-header:not(.p-highlight):not(.p-disabled):hover .p-panelmenu-header-content, .p-panelmenu .p-panelmenu-panel:not(:first-child) .p-panelmenu-header:not(.p-disabled).p-highlight:hover .p-panelmenu-header-content {
    border-top: 0 none;
  }
  .p-panelmenu .p-panelmenu-panel:first-child .p-panelmenu-header .p-panelmenu-header-content {
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }
  .p-panelmenu .p-panelmenu-panel:last-child .p-panelmenu-header:not(.p-highlight) .p-panelmenu-header-content {
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }
  .p-panelmenu .p-panelmenu-panel:last-child .p-panelmenu-content {
    border-bottom-right-radius: 6px;
    border-bottom-left-radius: 6px;
  }

  .p-slidemenu {
    padding: 0.429rem;
    background: transparent;
    color: #C8CCD8;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 6px;
    width: 12.5rem;
  }
  .p-slidemenu .p-slidemenu-root-list {
    outline: 0 none;
  }
  .p-slidemenu .p-submenu-list {
    outline: 0 none;
  }
  .p-slidemenu .p-menuitem > .p-menuitem-content {
    color: #C8CCD8;
    transition: box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-slidemenu .p-menuitem > .p-menuitem-content .p-menuitem-link {
    color: #C8CCD8;
    padding: 0.571rem 0.429rem;
    user-select: none;
  }
  .p-slidemenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #C8CCD8;
  }
  .p-slidemenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #C8CCD8;
    margin-right: 0.5rem;
  }
  .p-slidemenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #C8CCD8;
  }
  .p-slidemenu .p-menuitem.p-highlight > .p-menuitem-content {
    color: #0A061A;
    background: #EEE500;
  }
  .p-slidemenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #0A061A;
  }
  .p-slidemenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-slidemenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #0A061A;
  }
  .p-slidemenu .p-menuitem.p-highlight.p-focus > .p-menuitem-content {
    background: rgba(238, 229, 0, 0.24);
  }
  .p-slidemenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.1);
  }
  .p-slidemenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #FFFFFF;
  }
  .p-slidemenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-slidemenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #FFFFFF;
  }
  .p-slidemenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-slidemenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #FFFFFF;
  }
  .p-slidemenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-slidemenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #FFFFFF;
  }
  .p-slidemenu.p-slidemenu-overlay {
    background: #0a061a;
    border: 1px solid transparent;
    box-shadow: none;
  }
  .p-slidemenu .p-slidemenu-list {
    padding: 0.429rem;
    background: #0a061a;
    border: 1px solid transparent;
    box-shadow: none;
  }
  .p-slidemenu .p-menuitem-separator {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    margin: 0.25rem 0;
  }
  .p-slidemenu .p-slidemenu-icon {
    font-size: 0.875rem;
  }
  .p-slidemenu .p-icon {
    width: 0.875rem;
    height: 0.875rem;
  }
  .p-slidemenu .p-slidemenu-backward {
    padding: 0.571rem 0.429rem;
    color: #C8CCD8;
  }
  .p-slidemenu .p-slidemenu-backward:not(.p-disabled):focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 none;
  }
  .p-slidemenu .p-menuitem-badge {
    background: #EEE500;
    color: #0A061A;
    font-size: 1rem;
    font-weight: 500;
    min-width: 1.429rem;
    height: 1.429rem;
    line-height: 1.429rem;
    border-radius: 6px;
    margin-left: 0.5rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .p-steps .p-steps-item .p-menuitem-link {
    background: transparent;
    transition: box-shadow 0.2s;
    border-radius: 6px;
    background: transparent;
  }
  .p-steps .p-steps-item .p-menuitem-link .p-steps-number {
    color: #868C9B;
    border: 0 none;
    background: transparent;
    min-width: 1.714rem;
    height: 1.714rem;
    line-height: 1.714rem;
    font-size: 1.143rem;
    z-index: 1;
    border-radius: 12px;
  }
  .p-steps .p-steps-item .p-menuitem-link .p-steps-title {
    margin-top: 0.5rem;
    color: #868C9B;
  }
  .p-steps .p-steps-item .p-menuitem-link:not(.p-disabled):focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-steps .p-steps-item.p-highlight .p-steps-number {
    background: #EEE500;
    color: #0A061A;
  }
  .p-steps .p-steps-item.p-highlight .p-steps-title {
    font-weight: 400;
    color: #FFFFFF;
  }
  .p-steps .p-steps-item:before {
    content: " ";
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    width: 100%;
    top: 50%;
    left: 0;
    display: block;
    position: absolute;
    margin-top: -0.857rem;
  }

  .p-tabmenu .p-tabmenu-nav {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-width: 0 0 2px 0;
  }
  .p-tabmenu .p-tabmenu-nav .p-menuitem-badge {
    background: #EEE500;
    color: #0A061A;
    font-size: 1rem;
    font-weight: 500;
    min-width: 1.429rem;
    height: 1.429rem;
    line-height: 1.429rem;
    border-radius: 6px;
    margin-left: 0.5rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem {
    margin-right: 0.857rem;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem .p-menuitem-link {
    border: solid transparent;
    border-width: 0 0 2px 0;
    border-color: transparent transparent transparent transparent;
    background: transparent;
    color: #868C9B;
    padding: 0.571rem 0.429rem;
    font-weight: 500;
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
    transition: box-shadow 0.2s;
    margin: 0 0 -2px 0;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem .p-menuitem-link .p-menuitem-icon {
    margin-right: 0.5rem;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem .p-menuitem-link:not(.p-disabled):focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 none;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem:not(.p-highlight):not(.p-disabled):hover .p-menuitem-link {
    background: transparent;
    border-color: #FFFFFF;
    color: #FFFFFF;
  }
  .p-tabmenu .p-tabmenu-nav .p-tabmenuitem.p-highlight .p-menuitem-link {
    background: transparent;
    border-color: #EEE500;
    color: #EEE500;
  }
  .p-tabmenu .p-tabmenu-left-icon {
    margin-right: 0.5rem;
  }
  .p-tabmenu .p-tabmenu-right-icon {
    margin-left: 0.5rem;
  }
  .p-tabmenu .p-tabmenu-nav-btn.p-link {
    background: transparent;
    color: #EEE500;
    width: 2.286rem;
    box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);
    border-radius: 0;
  }
  .p-tabmenu .p-tabmenu-nav-btn.p-link:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: inset 0 none;
  }

  .p-tieredmenu {
    padding: 0.429rem;
    background: transparent;
    color: #C8CCD8;
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 6px;
    width: 12.5rem;
  }
  .p-tieredmenu.p-tieredmenu-overlay {
    background: #0a061a;
    border: 1px solid transparent;
    box-shadow: none;
  }
  .p-tieredmenu .p-tieredmenu-root-list {
    outline: 0 none;
  }
  .p-tieredmenu .p-submenu-list {
    padding: 0.429rem;
    background: #0a061a;
    border: 1px solid transparent;
    box-shadow: none;
  }
  .p-tieredmenu .p-menuitem > .p-menuitem-content {
    color: #C8CCD8;
    transition: box-shadow 0.2s;
    border-radius: 6px;
  }
  .p-tieredmenu .p-menuitem > .p-menuitem-content .p-menuitem-link {
    color: #C8CCD8;
    padding: 0.571rem 0.429rem;
    user-select: none;
  }
  .p-tieredmenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #C8CCD8;
  }
  .p-tieredmenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-menuitem-icon {
    color: #C8CCD8;
    margin-right: 0.5rem;
  }
  .p-tieredmenu .p-menuitem > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #C8CCD8;
  }
  .p-tieredmenu .p-menuitem.p-highlight > .p-menuitem-content {
    color: #0A061A;
    background: #EEE500;
  }
  .p-tieredmenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #0A061A;
  }
  .p-tieredmenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-tieredmenu .p-menuitem.p-highlight > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #0A061A;
  }
  .p-tieredmenu .p-menuitem.p-highlight.p-focus > .p-menuitem-content {
    background: rgba(238, 229, 0, 0.24);
  }
  .p-tieredmenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.1);
  }
  .p-tieredmenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-text {
    color: #FFFFFF;
  }
  .p-tieredmenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-menuitem-icon,
.p-tieredmenu .p-menuitem:not(.p-highlight):not(.p-disabled).p-focus > .p-menuitem-content .p-menuitem-link .p-submenu-icon {
    color: #FFFFFF;
  }
  .p-tieredmenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover {
    color: #FFFFFF;
    background: rgba(255, 255, 255, 0.05);
  }
  .p-tieredmenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-text {
    color: #FFFFFF;
  }
  .p-tieredmenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-menuitem-icon,
.p-tieredmenu .p-menuitem:not(.p-highlight):not(.p-disabled) > .p-menuitem-content:hover .p-menuitem-link .p-submenu-icon {
    color: #FFFFFF;
  }
  .p-tieredmenu .p-menuitem-separator {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    margin: 0.25rem 0;
  }
  .p-tieredmenu .p-submenu-icon {
    font-size: 0.875rem;
  }
  .p-tieredmenu .p-submenu-icon.p-icon {
    width: 0.875rem;
    height: 0.875rem;
  }

  .p-inline-message {
    padding: 0.429rem 0.571rem;
    margin: 0;
    border-radius: 6px;
  }
  .p-inline-message.p-inline-message-info {
    background: rgba(255, 255, 255, 0.05);
    border: solid #873EFE;
    border-width: 0 0 0 20px;
    color: #FFFFFF;
  }
  .p-inline-message.p-inline-message-info .p-inline-message-icon {
    color: #FFFFFF;
  }
  .p-inline-message.p-inline-message-success {
    background: rgba(255, 255, 255, 0.05);
    border: solid #0BD18A;
    border-width: 0 0 0 20px;
    color: #FFFFFF;
  }
  .p-inline-message.p-inline-message-success .p-inline-message-icon {
    color: #FFFFFF;
  }
  .p-inline-message.p-inline-message-warn {
    background: rgba(255, 255, 255, 0.05);
    border: solid #EEE500;
    border-width: 0 0 0 20px;
    color: #FFFFFF;
  }
  .p-inline-message.p-inline-message-warn .p-inline-message-icon {
    color: #FFFFFF;
  }
  .p-inline-message.p-inline-message-error {
    background: rgba(255, 255, 255, 0.05);
    border: solid #FC6161;
    border-width: 0 0 0 20px;
    color: #FFFFFF;
  }
  .p-inline-message.p-inline-message-error .p-inline-message-icon {
    color: #FFFFFF;
  }
  .p-inline-message .p-inline-message-icon {
    font-size: 1.286rem;
    margin-right: 0.5rem;
  }
  .p-inline-message .p-icon {
    width: 1.286rem;
    height: 1.286rem;
  }
  .p-inline-message .p-inline-message-text {
    font-size: 1rem;
  }
  .p-inline-message.p-inline-message-icon-only .p-inline-message-icon {
    margin-right: 0;
  }

  .p-message {
    margin: 1rem 0;
    border-radius: 6px;
  }
  .p-message .p-message-wrapper {
    padding: 1.143rem 1.357rem;
  }
  .p-message .p-message-close {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: transparent;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .p-message .p-message-close:hover {
    background: rgba(255, 255, 255, 0.5);
  }
  .p-message .p-message-close:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-message.p-message-info {
    background: rgba(255, 255, 255, 0.05);
    border: solid #873EFE;
    border-width: 0 0 0 20px;
    color: #FFFFFF;
  }
  .p-message.p-message-info .p-message-icon {
    color: #FFFFFF;
  }
  .p-message.p-message-info .p-message-close {
    color: #FFFFFF;
  }
  .p-message.p-message-success {
    background: rgba(255, 255, 255, 0.05);
    border: solid #0BD18A;
    border-width: 0 0 0 20px;
    color: #FFFFFF;
  }
  .p-message.p-message-success .p-message-icon {
    color: #FFFFFF;
  }
  .p-message.p-message-success .p-message-close {
    color: #FFFFFF;
  }
  .p-message.p-message-warn {
    background: rgba(255, 255, 255, 0.05);
    border: solid #EEE500;
    border-width: 0 0 0 20px;
    color: #FFFFFF;
  }
  .p-message.p-message-warn .p-message-icon {
    color: #FFFFFF;
  }
  .p-message.p-message-warn .p-message-close {
    color: #FFFFFF;
  }
  .p-message.p-message-error {
    background: rgba(255, 255, 255, 0.05);
    border: solid #FC6161;
    border-width: 0 0 0 20px;
    color: #FFFFFF;
  }
  .p-message.p-message-error .p-message-icon {
    color: #FFFFFF;
  }
  .p-message.p-message-error .p-message-close {
    color: #FFFFFF;
  }
  .p-message .p-message-text {
    font-size: 1rem;
    font-weight: 400;
  }
  .p-message .p-message-icon {
    font-size: 1.286rem;
    margin-right: 0.5rem;
  }
  .p-message .p-icon {
    width: 1.286rem;
    height: 1.286rem;
  }
  .p-message .p-message-summary {
    font-weight: 700;
  }
  .p-message .p-message-detail {
    margin-left: 0.5rem;
  }

  .p-toast {
    opacity: 0.9;
  }
  .p-toast .p-toast-message {
    margin: 0 0 1rem 0;
    box-shadow: none;
    border-radius: 6px;
  }
  .p-toast .p-toast-message .p-toast-message-content {
    padding: 1.143rem 1.357rem;
    border-width: 0 0 0 20px;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-toast-message-text {
    margin: 0 0 0 1rem;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-toast-message-icon {
    font-size: 1.286rem;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-icon:not(.p-toast-icon-close-icon) {
    width: 1.286rem;
    height: 1.286rem;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-toast-summary {
    font-weight: 400;
  }
  .p-toast .p-toast-message .p-toast-message-content .p-toast-detail {
    margin: 0.5rem 0 0 0;
  }
  .p-toast .p-toast-message .p-toast-icon-close {
    width: 1.286rem;
    height: 1.286rem;
    border-radius: 50%;
    background: transparent;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .p-toast .p-toast-message .p-toast-icon-close:hover {
    background: rgba(255, 255, 255, 0.5);
  }
  .p-toast .p-toast-message .p-toast-icon-close:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-toast .p-toast-message.p-toast-message-info {
    background: rgba(255, 255, 255, 0.05);
    border: solid #873EFE;
    border-width: 0 0 0 20px;
    color: #FFFFFF;
  }
  .p-toast .p-toast-message.p-toast-message-info .p-toast-message-icon,
.p-toast .p-toast-message.p-toast-message-info .p-toast-icon-close {
    color: #FFFFFF;
  }
  .p-toast .p-toast-message.p-toast-message-success {
    background: rgba(255, 255, 255, 0.05);
    border: solid #0BD18A;
    border-width: 0 0 0 20px;
    color: #FFFFFF;
  }
  .p-toast .p-toast-message.p-toast-message-success .p-toast-message-icon,
.p-toast .p-toast-message.p-toast-message-success .p-toast-icon-close {
    color: #FFFFFF;
  }
  .p-toast .p-toast-message.p-toast-message-warn {
    background: rgba(255, 255, 255, 0.05);
    border: solid #EEE500;
    border-width: 0 0 0 20px;
    color: #FFFFFF;
  }
  .p-toast .p-toast-message.p-toast-message-warn .p-toast-message-icon,
.p-toast .p-toast-message.p-toast-message-warn .p-toast-icon-close {
    color: #FFFFFF;
  }
  .p-toast .p-toast-message.p-toast-message-error {
    background: rgba(255, 255, 255, 0.05);
    border: solid #FC6161;
    border-width: 0 0 0 20px;
    color: #FFFFFF;
  }
  .p-toast .p-toast-message.p-toast-message-error .p-toast-message-icon,
.p-toast .p-toast-message.p-toast-message-error .p-toast-icon-close {
    color: #FFFFFF;
  }

  .p-galleria .p-galleria-close {
    margin: 0.5rem;
    background: transparent;
    color: #f8f9fa;
    width: 4rem;
    height: 4rem;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
    border-radius: 50%;
  }
  .p-galleria .p-galleria-close .p-galleria-close-icon {
    font-size: 2rem;
  }
  .p-galleria .p-galleria-close .p-icon-wrapper .p-icon {
    width: 2rem;
    height: 2rem;
  }
  .p-galleria .p-galleria-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #f8f9fa;
  }
  .p-galleria .p-galleria-item-nav {
    background: transparent;
    color: #f8f9fa;
    width: 4rem;
    height: 4rem;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
    border-radius: 6px;
    margin: 0 0.5rem;
  }
  .p-galleria .p-galleria-item-nav .p-galleria-item-prev-icon,
.p-galleria .p-galleria-item-nav .p-galleria-item-next-icon {
    font-size: 2rem;
  }
  .p-galleria .p-galleria-item-nav .p-icon-wrapper .p-icon {
    width: 2rem;
    height: 2rem;
  }
  .p-galleria .p-galleria-item-nav:not(.p-disabled):hover {
    background: rgba(255, 255, 255, 0.1);
    color: #f8f9fa;
  }
  .p-galleria .p-galleria-caption {
    background: rgba(0, 0, 0, 0.5);
    color: #f8f9fa;
    padding: 1rem;
  }
  .p-galleria .p-galleria-indicators {
    padding: 1rem;
  }
  .p-galleria .p-galleria-indicators .p-galleria-indicator button {
    background-color: rgba(255, 255, 255, 0.1);
    width: 1.429rem;
    height: 0.286rem;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
    border-radius: 3px;
  }
  .p-galleria .p-galleria-indicators .p-galleria-indicator button:hover {
    background: rgba(255, 255, 255, 0.2);
  }
  .p-galleria .p-galleria-indicators .p-galleria-indicator.p-highlight button {
    background: #EEE500;
    color: #0A061A;
  }
  .p-galleria.p-galleria-indicators-bottom .p-galleria-indicator, .p-galleria.p-galleria-indicators-top .p-galleria-indicator {
    margin-right: 0.5rem;
  }
  .p-galleria.p-galleria-indicators-left .p-galleria-indicator, .p-galleria.p-galleria-indicators-right .p-galleria-indicator {
    margin-bottom: 0.5rem;
  }
  .p-galleria.p-galleria-indicator-onitem .p-galleria-indicators {
    background: rgba(0, 0, 0, 0.5);
  }
  .p-galleria.p-galleria-indicator-onitem .p-galleria-indicators .p-galleria-indicator button {
    background: #C8CCD8;
  }
  .p-galleria.p-galleria-indicator-onitem .p-galleria-indicators .p-galleria-indicator button:hover {
    background: #FFFFFF;
  }
  .p-galleria.p-galleria-indicator-onitem .p-galleria-indicators .p-galleria-indicator.p-highlight button {
    background: #EEE500;
    color: #0A061A;
  }
  .p-galleria .p-galleria-thumbnail-container {
    background: rgba(0, 0, 0, 0.9);
    padding: 1rem 0.25rem;
  }
  .p-galleria .p-galleria-thumbnail-container .p-galleria-thumbnail-prev,
.p-galleria .p-galleria-thumbnail-container .p-galleria-thumbnail-next {
    margin: 0.5rem;
    background-color: transparent;
    color: #f8f9fa;
    width: 2rem;
    height: 2rem;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
    border-radius: 50%;
  }
  .p-galleria .p-galleria-thumbnail-container .p-galleria-thumbnail-prev:hover,
.p-galleria .p-galleria-thumbnail-container .p-galleria-thumbnail-next:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #f8f9fa;
  }
  .p-galleria .p-galleria-thumbnail-container .p-galleria-thumbnail-item-content:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }

  .p-galleria-mask {
    --maskbg: rgba(0, 0, 0, 0.9);
  }

  .p-image-mask {
    --maskbg: rgba(0, 0, 0, 0.9);
  }

  .p-image-preview-indicator {
    background-color: transparent;
    color: #f8f9fa;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  }

  .p-image-preview-container:hover > .p-image-preview-indicator {
    background-color: rgba(0, 0, 0, 0.5);
  }

  .p-image-toolbar {
    padding: 1rem;
  }

  .p-image-action.p-link {
    color: #f8f9fa;
    background-color: transparent;
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
    margin-right: 0.5rem;
  }
  .p-image-action.p-link:last-child {
    margin-right: 0;
  }
  .p-image-action.p-link:hover {
    color: #f8f9fa;
    background-color: rgba(255, 255, 255, 0.1);
  }
  .p-image-action.p-link i {
    font-size: 1.5rem;
  }
  .p-image-action.p-link .p-icon {
    width: 1.5rem;
    height: 1.5rem;
  }

  .p-avatar {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
  }
  .p-avatar.p-avatar-lg {
    width: 3rem;
    height: 3rem;
    font-size: 1.5rem;
  }
  .p-avatar.p-avatar-lg .p-avatar-icon {
    font-size: 1.5rem;
  }
  .p-avatar.p-avatar-xl {
    width: 4rem;
    height: 4rem;
    font-size: 2rem;
  }
  .p-avatar.p-avatar-xl .p-avatar-icon {
    font-size: 2rem;
  }

  .p-avatar-group .p-avatar {
    border: 2px solid transparent;
  }

  .p-badge {
    background: #EEE500;
    color: #0A061A;
    font-size: 1rem;
    font-weight: 500;
    min-width: 1.429rem;
    height: 1.429rem;
    line-height: 1.429rem;
  }
  .p-badge.p-badge-secondary {
    background-color: #a0a3ad;
    color: #FFFFFF;
  }
  .p-badge.p-badge-success {
    background-color: #0BD18A;
    color: #FFFFFF;
  }
  .p-badge.p-badge-info {
    background-color: #873EFE;
    color: #FFFFFF;
  }
  .p-badge.p-badge-warning {
    background-color: #EEE500;
    color: #2E323F;
  }
  .p-badge.p-badge-danger {
    background-color: #FC6161;
    color: #FFFFFF;
  }
  .p-badge.p-badge-lg {
    font-size: 1.5rem;
    min-width: 2.1435rem;
    height: 2.1435rem;
    line-height: 2.1435rem;
  }
  .p-badge.p-badge-xl {
    font-size: 2rem;
    min-width: 2.858rem;
    height: 2.858rem;
    line-height: 2.858rem;
  }

  .p-chip {
    background-color: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
    border-radius: 16px;
    padding: 0 0.571rem;
  }
  .p-chip .p-chip-text {
    line-height: 1.5;
    margin-top: 0.2145rem;
    margin-bottom: 0.2145rem;
  }
  .p-chip .p-chip-icon {
    margin-right: 0.5rem;
  }
  .p-chip .pi-chip-remove-icon {
    margin-left: 0.5rem;
  }
  .p-chip img {
    width: 1.929rem;
    height: 1.929rem;
    margin-left: -0.571rem;
    margin-right: 0.5rem;
  }
  .p-chip .pi-chip-remove-icon {
    border-radius: 6px;
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .p-chip .pi-chip-remove-icon:focus-visible {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }
  .p-chip .pi-chip-remove-icon:focus {
    outline: 0 none;
  }

  .p-inplace .p-inplace-display {
    padding: 0.429rem 0.571rem;
    border-radius: 6px;
    transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
  }
  .p-inplace .p-inplace-display:not(.p-disabled):hover {
    background: rgba(255, 255, 255, 0.05);
    color: #FFFFFF;
  }
  .p-inplace .p-inplace-display:focus {
    outline: 0 none;
    outline-offset: 0;
    box-shadow: 0 none;
  }

  .p-progressbar {
    border: 0 none;
    height: 0.571rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
  }
  .p-progressbar .p-progressbar-value {
    border: 0 none;
    margin: 0;
    background: #EEE500;
  }
  .p-progressbar .p-progressbar-label {
    color: #0A061A;
    line-height: 0.571rem;
  }

  .p-scrolltop {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
    transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
  }
  .p-scrolltop.p-link {
    background: #EEE500;
  }
  .p-scrolltop.p-link:hover {
    background: #eee500;
  }
  .p-scrolltop .p-scrolltop-icon {
    font-size: 1.5rem;
    color: #0A061A;
  }
  .p-scrolltop .p-icon {
    width: 1.5rem;
    height: 1.5rem;
  }

  .p-skeleton {
    background-color: rgba(255, 255, 255, 0.06);
    border-radius: 6px;
  }
  .p-skeleton:after {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.04), rgba(255, 255, 255, 0));
  }

  .p-tag {
    background: #EEE500;
    color: #0A061A;
    font-size: 1rem;
    font-weight: 500;
    padding: 0 0.429rem;
    border-radius: 6px;
  }
  .p-tag.p-tag-success {
    background-color: #0BD18A;
    color: #FFFFFF;
  }
  .p-tag.p-tag-info {
    background-color: #873EFE;
    color: #FFFFFF;
  }
  .p-tag.p-tag-warning {
    background-color: #EEE500;
    color: #2E323F;
  }
  .p-tag.p-tag-danger {
    background-color: #FC6161;
    color: #FFFFFF;
  }
  .p-tag .p-tag-icon {
    margin-right: 0.25rem;
    font-size: 1rem;
  }
  .p-tag .p-icon {
    width: 1rem;
    height: 1rem;
  }

  .p-terminal {
    background: transparent;
    color: #FFFFFF;
    border: 1px solid rgba(255, 255, 255, 0.15);
    padding: 1.143rem;
  }
  .p-terminal .p-terminal-input {
    font-family: var(--font-family);
    font-feature-settings: var(--font-feature-settings, normal);
    font-size: 1rem;
  }
}
.p-button-label {
  font-weight: 700;
}

.p-accordion .p-accordion-header .p-accordion-header-link {
  transition: background-color 0.2s, border-color 0.2s, box-shadow 0.2s;
}

.p-tabview .p-tabview-nav li .p-tabview-nav-link {
  transition: background-color 0.2s, border-color 0.2s, box-shadow 0.2s;
}
.p-tabview .p-tabview-nav .p-tabview-ink-bar {
  z-index: 1;
  display: block;
  position: absolute;
  bottom: 0;
  height: 2px;
  background-color: #EEE500;
  transition: 500ms cubic-bezier(0.35, 0, 0.25, 1);
}

.p-carousel .p-carousel-indicators .p-carousel-indicator.p-highlight button {
  background-color: #EEE500;
}

.p-galleria .p-galleria-indicators .p-galleria-indicator.p-highlight button {
  background-color: #EEE500;
}

.p-button:focus {
  box-shadow: 0 0 0 2px #1c2127, 0 0 0 4px rgba(238, 229, 0, 0.7), 0 1px 2px 0 rgba(0, 0, 0, 0);
}
.p-button.p-button-secondary:enabled:focus {
  box-shadow: 0 0 0 2px #1c2127, 0 0 0 4px rgba(160, 163, 173, 0.7), 0 1px 2px 0 rgba(0, 0, 0, 0);
}
.p-button.p-button-success:enabled:focus {
  box-shadow: 0 0 0 2px #1c2127, 0 0 0 4px rgba(11, 209, 138, 0.7), 0 1px 2px 0 rgba(0, 0, 0, 0);
}
.p-button.p-button-info:enabled:focus {
  box-shadow: 0 0 0 2px #1c2127, 0 0 0 4px rgba(135, 62, 254, 0.7), 0 1px 2px 0 rgba(0, 0, 0, 0);
}
.p-button.p-button-warning:enabled:focus {
  box-shadow: 0 0 0 2px #1c2127, 0 0 0 4px rgba(238, 229, 0, 0.7), 0 1px 2px 0 rgba(0, 0, 0, 0);
}
.p-button.p-button-help:enabled:focus {
  box-shadow: 0 0 0 2px #1c2127, 0 0 0 4px rgba(236, 77, 188, 0.7), 0 1px 2px 0 rgba(0, 0, 0, 0);
}
.p-button.p-button-danger:enabled:focus {
  box-shadow: 0 0 0 2px #1c2127, 0 0 0 4px rgba(252, 97, 97, 0.7), 0 1px 2px 0 rgba(0, 0, 0, 0);
}

.p-datatable .p-datatable-tbody > tr.p-datatable-dragpoint-top > td {
  box-shadow: inset 0 2px 0 0 #EEE500;
}
.p-datatable .p-datatable-tbody > tr.p-datatable-dragpoint-bottom > td {
  box-shadow: inset 0 -2px 0 0 #EEE500;
}

.custom-timeline.p-timeline.p-timeline-vertical .p-timeline-event-opposite {
  flex: 0;
  padding: 0;
}

.fc {
  /* FullCalendar 4 */
  /* FullCalendar 5 */
}
.fc.fc-unthemed .fc-view-container th {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: #FFFFFF;
}
.fc.fc-unthemed .fc-view-container td.fc-widget-content {
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: #FFFFFF;
}
.fc.fc-unthemed .fc-view-container td.fc-head-container {
  border: 1px solid rgba(255, 255, 255, 0.15);
}
.fc.fc-unthemed .fc-view-container .fc-view {
  background: transparent;
}
.fc.fc-unthemed .fc-view-container .fc-row {
  border-right: 1px solid rgba(255, 255, 255, 0.15);
}
.fc.fc-unthemed .fc-view-container .fc-event {
  background: #d6ce00;
  border: 1px solid #d6ce00;
  color: #0A061A;
}
.fc.fc-unthemed .fc-view-container .fc-divider {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.15);
}
.fc.fc-unthemed .fc-toolbar .fc-button {
  color: #0A061A;
  background: #EEE500;
  border: 1px solid #EEE500;
  font-size: 1rem;
  transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
  border-radius: 6px;
  display: flex;
  align-items: center;
}
.fc.fc-unthemed .fc-toolbar .fc-button:enabled:hover {
  background: #d6ce00;
  color: #0A061A;
  border-color: #d6ce00;
}
.fc.fc-unthemed .fc-toolbar .fc-button:enabled:active {
  background: #beb700;
  color: #0A061A;
  border-color: #beb700;
}
.fc.fc-unthemed .fc-toolbar .fc-button:enabled:active:focus {
  outline: 0 none;
  outline-offset: 0;
  box-shadow: 0 none;
}
.fc.fc-unthemed .fc-toolbar .fc-button .fc-icon-chevron-left {
  font-family: "PrimeIcons" !important;
  text-indent: 0;
  font-size: 1rem;
}
.fc.fc-unthemed .fc-toolbar .fc-button .fc-icon-chevron-left:before {
  content: "\e900";
}
.fc.fc-unthemed .fc-toolbar .fc-button .fc-icon-chevron-right {
  font-family: "PrimeIcons" !important;
  text-indent: 0;
  font-size: 1rem;
}
.fc.fc-unthemed .fc-toolbar .fc-button .fc-icon-chevron-right:before {
  content: "\e901";
}
.fc.fc-unthemed .fc-toolbar .fc-button:focus {
  outline: 0 none;
  outline-offset: 0;
  box-shadow: 0 none;
}
.fc.fc-unthemed .fc-toolbar .fc-button.fc-dayGridMonth-button, .fc.fc-unthemed .fc-toolbar .fc-button.fc-timeGridWeek-button, .fc.fc-unthemed .fc-toolbar .fc-button.fc-timeGridDay-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid transparent;
  color: #FFFFFF;
  transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
}
.fc.fc-unthemed .fc-toolbar .fc-button.fc-dayGridMonth-button:hover, .fc.fc-unthemed .fc-toolbar .fc-button.fc-timeGridWeek-button:hover, .fc.fc-unthemed .fc-toolbar .fc-button.fc-timeGridDay-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: transparent;
  color: #FFFFFF;
}
.fc.fc-unthemed .fc-toolbar .fc-button.fc-dayGridMonth-button.fc-button-active, .fc.fc-unthemed .fc-toolbar .fc-button.fc-timeGridWeek-button.fc-button-active, .fc.fc-unthemed .fc-toolbar .fc-button.fc-timeGridDay-button.fc-button-active {
  background: #EEE500;
  border-color: #EEE500;
  color: #0A061A;
}
.fc.fc-unthemed .fc-toolbar .fc-button.fc-dayGridMonth-button.fc-button-active:hover, .fc.fc-unthemed .fc-toolbar .fc-button.fc-timeGridWeek-button.fc-button-active:hover, .fc.fc-unthemed .fc-toolbar .fc-button.fc-timeGridDay-button.fc-button-active:hover {
  background: #d6ce00;
  border-color: #d6ce00;
  color: #0A061A;
}
.fc.fc-unthemed .fc-toolbar .fc-button.fc-dayGridMonth-button:focus, .fc.fc-unthemed .fc-toolbar .fc-button.fc-timeGridWeek-button:focus, .fc.fc-unthemed .fc-toolbar .fc-button.fc-timeGridDay-button:focus {
  outline: 0 none;
  outline-offset: 0;
  box-shadow: 0 none;
  z-index: 1;
}
.fc.fc-unthemed .fc-toolbar .fc-button-group .fc-button {
  border-radius: 0;
}
.fc.fc-unthemed .fc-toolbar .fc-button-group .fc-button:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}
.fc.fc-unthemed .fc-toolbar .fc-button-group .fc-button:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}
.fc.fc-theme-standard .fc-view-harness .fc-scrollgrid {
  border-color: rgba(255, 255, 255, 0.15);
}
.fc.fc-theme-standard .fc-view-harness th {
  background: transparent;
  border-color: rgba(255, 255, 255, 0.15);
  color: #FFFFFF;
}
.fc.fc-theme-standard .fc-view-harness td {
  color: #FFFFFF;
  border-color: rgba(255, 255, 255, 0.15);
}
.fc.fc-theme-standard .fc-view-harness .fc-view {
  background: transparent;
}
.fc.fc-theme-standard .fc-view-harness .fc-popover {
  background: none;
  border: 0 none;
}
.fc.fc-theme-standard .fc-view-harness .fc-popover .fc-popover-header {
  border: 1px solid rgba(255, 255, 255, 0.15);
  padding: 0.714rem 1.143rem;
  background: transparent;
  color: #FFFFFF;
}
.fc.fc-theme-standard .fc-view-harness .fc-popover .fc-popover-header .fc-popover-close {
  opacity: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  font-family: "PrimeIcons" !important;
  font-size: 1rem;
  width: 2rem;
  height: 2rem;
  color: #C8CCD8;
  border: 0 none;
  background: transparent;
  border-radius: 50%;
  transition: background-color 0.2s, color 0.2s, box-shadow 0.2s;
}
.fc.fc-theme-standard .fc-view-harness .fc-popover .fc-popover-header .fc-popover-close:before {
  content: "\e90b";
}
.fc.fc-theme-standard .fc-view-harness .fc-popover .fc-popover-header .fc-popover-close:enabled:hover {
  color: #FFFFFF;
  border-color: transparent;
  background: rgba(255, 255, 255, 0.05);
}
.fc.fc-theme-standard .fc-view-harness .fc-popover .fc-popover-header .fc-popover-close:focus-visible {
  outline: 0 none;
  outline-offset: 0;
  box-shadow: 0 none;
}
.fc.fc-theme-standard .fc-view-harness .fc-popover .fc-popover-body {
  padding: 1.143rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
  background: transparent;
  color: #FFFFFF;
  border-top: 0 none;
}
.fc.fc-theme-standard .fc-view-harness .fc-event.fc-daygrid-block-event {
  color: #0A061A;
  background: #d6ce00;
  border-color: #d6ce00;
}
.fc.fc-theme-standard .fc-view-harness .fc-event.fc-daygrid-block-event .fc-event-main {
  color: #0A061A;
}
.fc.fc-theme-standard .fc-view-harness .fc-event.fc-daygrid-dot-event .fc-daygrid-event-dot {
  background: #d6ce00;
  border-color: #d6ce00;
}
.fc.fc-theme-standard .fc-view-harness .fc-event.fc-daygrid-dot-event:hover {
  background: rgba(255, 255, 255, 0.05);
  color: #FFFFFF;
}
.fc.fc-theme-standard .fc-view-harness .fc-cell-shaded {
  background: transparent;
}
.fc.fc-theme-standard .fc-toolbar .fc-button {
  color: #0A061A;
  background: #EEE500;
  border: 1px solid #EEE500;
  font-size: 1rem;
  transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
  border-radius: 6px;
}
.fc.fc-theme-standard .fc-toolbar .fc-button:enabled:hover {
  background: #d6ce00;
  color: #0A061A;
  border-color: #d6ce00;
}
.fc.fc-theme-standard .fc-toolbar .fc-button:enabled:active {
  background: #beb700;
  color: #0A061A;
  border-color: #beb700;
}
.fc.fc-theme-standard .fc-toolbar .fc-button:enabled:active:focus {
  outline: 0 none;
  outline-offset: 0;
  box-shadow: 0 none;
}
.fc.fc-theme-standard .fc-toolbar .fc-button:disabled {
  opacity: 0.4;
  color: #0A061A;
  background: #EEE500;
  border: 1px solid #EEE500;
}
.fc.fc-theme-standard .fc-toolbar .fc-button .fc-icon-chevron-left {
  font-family: "PrimeIcons" !important;
  text-indent: 0;
  font-size: 1rem;
}
.fc.fc-theme-standard .fc-toolbar .fc-button .fc-icon-chevron-left:before {
  content: "\e900";
}
.fc.fc-theme-standard .fc-toolbar .fc-button .fc-icon-chevron-right {
  font-family: "PrimeIcons" !important;
  text-indent: 0;
  font-size: 1rem;
}
.fc.fc-theme-standard .fc-toolbar .fc-button .fc-icon-chevron-right:before {
  content: "\e901";
}
.fc.fc-theme-standard .fc-toolbar .fc-button:focus {
  outline: 0 none;
  outline-offset: 0;
  box-shadow: 0 none;
}
.fc.fc-theme-standard .fc-toolbar .fc-button.fc-dayGridMonth-button, .fc.fc-theme-standard .fc-toolbar .fc-button.fc-timeGridWeek-button, .fc.fc-theme-standard .fc-toolbar .fc-button.fc-timeGridDay-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid transparent;
  color: #FFFFFF;
  transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
}
.fc.fc-theme-standard .fc-toolbar .fc-button.fc-dayGridMonth-button:hover, .fc.fc-theme-standard .fc-toolbar .fc-button.fc-timeGridWeek-button:hover, .fc.fc-theme-standard .fc-toolbar .fc-button.fc-timeGridDay-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: transparent;
  color: #FFFFFF;
}
.fc.fc-theme-standard .fc-toolbar .fc-button.fc-dayGridMonth-button.fc-button-active, .fc.fc-theme-standard .fc-toolbar .fc-button.fc-timeGridWeek-button.fc-button-active, .fc.fc-theme-standard .fc-toolbar .fc-button.fc-timeGridDay-button.fc-button-active {
  background: #EEE500;
  border-color: #EEE500;
  color: #0A061A;
}
.fc.fc-theme-standard .fc-toolbar .fc-button.fc-dayGridMonth-button.fc-button-active:hover, .fc.fc-theme-standard .fc-toolbar .fc-button.fc-timeGridWeek-button.fc-button-active:hover, .fc.fc-theme-standard .fc-toolbar .fc-button.fc-timeGridDay-button.fc-button-active:hover {
  background: #d6ce00;
  border-color: #d6ce00;
  color: #0A061A;
}
.fc.fc-theme-standard .fc-toolbar .fc-button.fc-dayGridMonth-button:not(:disabled):focus, .fc.fc-theme-standard .fc-toolbar .fc-button.fc-timeGridWeek-button:not(:disabled):focus, .fc.fc-theme-standard .fc-toolbar .fc-button.fc-timeGridDay-button:not(:disabled):focus {
  outline: 0 none;
  outline-offset: 0;
  box-shadow: 0 none;
  z-index: 1;
}
.fc.fc-theme-standard .fc-toolbar .fc-button-group .fc-button {
  border-radius: 0;
}
.fc.fc-theme-standard .fc-toolbar .fc-button-group .fc-button:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}
.fc.fc-theme-standard .fc-toolbar .fc-button-group .fc-button:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}
.fc.fc-theme-standard .fc-highlight {
  color: #0A061A;
  background: #EEE500;
}
