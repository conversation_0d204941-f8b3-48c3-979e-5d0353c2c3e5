{"ast": null, "code": "import { HttpResponse } from \"@angular/common/http\"; // Import HttpClient\nimport { tap, switchMap, catchError, of, throwError } from \"rxjs\";\nimport { ApiConstant, CMS_APIContstant, ENDPOINT } from \"src/app/constants/api.constants\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./auth.service\";\nimport * as i2 from \"@angular/common/http\";\nexport class AuthInterceptor {\n  constructor(authService, http) {\n    this.authService = authService;\n    this.http = http;\n    this.escapeTokenUrls = [CMS_APIContstant.SINGIN, CMS_APIContstant.RESET_PASSWORD_REQUEST, CMS_APIContstant.RESET_PASSWORD, CMS_APIContstant.CONTENT_PS, CMS_APIContstant.MAIN_MENU_API_DETAILS];\n  }\n  intercept(req, next) {\n    if (this.authService.isLoggedIn && (req.url.includes(ENDPOINT.NODE) || !this.startsWithAnyUrl(req.url, this.escapeTokenUrls))) {\n      return this.getAuthToken(req.url).pipe(tap(token => {\n        if (token) {\n          const headers = {\n            Authorization: \"Bearer \" + token\n          };\n          if (this.startsWithAnyUrl(req.url, [...Object.values(ApiConstant)])) {\n            const details = this.authService.userDetail;\n            headers.documentId = details.documentId;\n          }\n          req = req.clone({\n            setHeaders: headers\n          });\n        }\n      }), switchMap(() => next.handle(req)), tap(event => {\n        if (event instanceof HttpResponse) {\n          const newToken = event.headers.get(\"refreshtoken\");\n          const auth = this.authService.getAuth();\n          if (newToken && auth) {\n            this.authService.setAuth(newToken, auth[this.authService.UserDetailsKey], this.authService.isRememberMeSelected());\n          }\n        }\n      }), catchError(x => this.handleAuthError(x)));\n    }\n    return next.handle(req).pipe(catchError(x => this.handleAuthError(x)));\n  }\n  getAuthToken(url) {\n    const authToken = this.authService.getToken();\n    const isAdmin = this.authService.userDetail?.isAdmin;\n    if (isAdmin && this.startsWithAnyUrl(url, [...Object.values(CMS_APIContstant)])) {\n      return this.authService.cmsToken.pipe(switchMap(token => {\n        if (token) {\n          return of(token);\n        }\n        return this.authService.getCMSToken();\n      }));\n    }\n    return of(authToken);\n  }\n  startsWithAnyUrl(newUrl, urls) {\n    return urls.some(url => newUrl.startsWith(url));\n  }\n  handleAuthError(err) {\n    if (err.status === 401 || err.status === 403) {\n      this.authService.removeAuthToken();\n      window.location.href = \"#/auth/login\";\n      window.location.reload();\n      return of(err.message);\n    }\n    return throwError(() => err);\n  }\n  static {\n    this.ɵfac = function AuthInterceptor_Factory(t) {\n      return new (t || AuthInterceptor)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthInterceptor,\n      factory: AuthInterceptor.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpResponse", "tap", "switchMap", "catchError", "of", "throwError", "ApiConstant", "CMS_APIContstant", "ENDPOINT", "AuthInterceptor", "constructor", "authService", "http", "escapeTokenUrls", "SINGIN", "RESET_PASSWORD_REQUEST", "RESET_PASSWORD", "CONTENT_PS", "MAIN_MENU_API_DETAILS", "intercept", "req", "next", "isLoggedIn", "url", "includes", "NODE", "startsWithAnyUrl", "getAuthToken", "pipe", "token", "headers", "Authorization", "Object", "values", "details", "userDetail", "documentId", "clone", "setHeaders", "handle", "event", "newToken", "get", "auth", "getAuth", "setAuth", "UserDetailsKey", "isRememberMeSelected", "x", "handleAuthError", "authToken", "getToken", "isAdmin", "cmsToken", "getCMSToken", "newUrl", "urls", "some", "startsWith", "err", "status", "removeAuthToken", "window", "location", "href", "reload", "message", "i0", "ɵɵinject", "i1", "AuthService", "i2", "HttpClient", "factory", "ɵfac"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\core\\authentication\\auth.intreceptor.ts"], "sourcesContent": ["import { Http<PERSON>lient, HttpErrorResponse, HttpHandler, HttpInterceptor, HttpRequest, HttpResponse } from \"@angular/common/http\"; // Import HttpClient\r\nimport { Injectable } from \"@angular/core\";\r\nimport { tap, switchMap, catchError, Observable, map, of, throwError } from \"rxjs\";\r\nimport { ApiConstant, CMS_APIContstant, ENDPOINT } from \"src/app/constants/api.constants\";\r\nimport { AuthService } from \"./auth.service\";\r\n\r\n@Injectable()\r\nexport class AuthInterceptor implements HttpInterceptor {\r\n  private escapeTokenUrls = [\r\n    CMS_APIContstant.SINGIN,\r\n    CMS_APIContstant.RESET_PASSWORD_REQUEST,\r\n    CMS_APIContstant.RESET_PASSWORD,\r\n    CMS_APIContstant.CONTENT_PS,\r\n    CMS_APIContstant.MAIN_MENU_API_DETAILS,\r\n  ];\r\n\r\n  constructor(private authService: AuthService, private http: HttpClient) { }\r\n\r\n  intercept(req: HttpRequest<any>, next: <PERSON>tt<PERSON><PERSON><PERSON><PERSON>) {\r\n    if (this.authService.isLoggedIn && (req.url.includes(ENDPOINT.NODE) || (!this.startsWithAnyUrl(req.url, this.escapeTokenUrls)))) {\r\n      return this.getAuthToken(req.url).pipe(\r\n        tap((token) => {\r\n          if (token) {\r\n            const headers: any = {\r\n              Authorization: \"Bearer \" + token,\r\n            };\r\n            if (this.startsWithAnyUrl(req.url, [...Object.values(ApiConstant)])) {\r\n              const details = this.authService.userDetail;\r\n              headers.documentId = details.documentId;\r\n            }\r\n            req = req.clone({\r\n              setHeaders: headers,\r\n            });\r\n          }\r\n        }),\r\n        switchMap(() => next.handle(req)),\r\n        tap((event: any) => {\r\n          if (event instanceof HttpResponse) {\r\n            const newToken = event.headers.get(\"refreshtoken\");\r\n            const auth = this.authService.getAuth();\r\n            if (newToken && auth) {\r\n              this.authService.setAuth(\r\n                newToken,\r\n                auth[this.authService.UserDetailsKey],\r\n                this.authService.isRememberMeSelected()\r\n              );\r\n            }\r\n          }\r\n        }),\r\n        catchError((x) => this.handleAuthError(x))\r\n      );\r\n    }\r\n    return next.handle(req).pipe(catchError((x) => this.handleAuthError(x)));\r\n  }\r\n\r\n  private getAuthToken(url: string): Observable<string> {\r\n    const authToken = this.authService.getToken();\r\n    const isAdmin = this.authService.userDetail?.isAdmin;\r\n    if (isAdmin && this.startsWithAnyUrl(url, [...Object.values(CMS_APIContstant)])) {\r\n      return this.authService.cmsToken.pipe(\r\n        switchMap((token) => {\r\n          if (token) {\r\n            return of(token)\r\n          }\r\n          return this.authService.getCMSToken();\r\n        })\r\n      )\r\n    }\r\n    return of(authToken);\r\n  }\r\n\r\n  private startsWithAnyUrl(newUrl: string, urls: string[]) {\r\n    return urls.some((url) => newUrl.startsWith(url));\r\n  }\r\n\r\n  private handleAuthError(err: HttpErrorResponse): Observable<any> {\r\n    if (err.status === 401 || err.status === 403) {\r\n      this.authService.removeAuthToken();\r\n      window.location.href = \"#/auth/login\";\r\n      window.location.reload();\r\n      return of(err.message);\r\n    }\r\n    return throwError(() => err);\r\n  }\r\n}\r\n"], "mappings": "AAAA,SAAmFA,YAAY,QAAQ,sBAAsB,CAAC,CAAC;AAE/H,SAASC,GAAG,EAAEC,SAAS,EAAEC,UAAU,EAAmBC,EAAE,EAAEC,UAAU,QAAQ,MAAM;AAClF,SAASC,WAAW,EAAEC,gBAAgB,EAAEC,QAAQ,QAAQ,iCAAiC;;;;AAIzF,OAAM,MAAOC,eAAe;EAS1BC,YAAoBC,WAAwB,EAAUC,IAAgB;IAAlD,KAAAD,WAAW,GAAXA,WAAW;IAAuB,KAAAC,IAAI,GAAJA,IAAI;IARlD,KAAAC,eAAe,GAAG,CACxBN,gBAAgB,CAACO,MAAM,EACvBP,gBAAgB,CAACQ,sBAAsB,EACvCR,gBAAgB,CAACS,cAAc,EAC/BT,gBAAgB,CAACU,UAAU,EAC3BV,gBAAgB,CAACW,qBAAqB,CACvC;EAEyE;EAE1EC,SAASA,CAACC,GAAqB,EAAEC,IAAiB;IAChD,IAAI,IAAI,CAACV,WAAW,CAACW,UAAU,KAAKF,GAAG,CAACG,GAAG,CAACC,QAAQ,CAAChB,QAAQ,CAACiB,IAAI,CAAC,IAAK,CAAC,IAAI,CAACC,gBAAgB,CAACN,GAAG,CAACG,GAAG,EAAE,IAAI,CAACV,eAAe,CAAE,CAAC,EAAE;MAC/H,OAAO,IAAI,CAACc,YAAY,CAACP,GAAG,CAACG,GAAG,CAAC,CAACK,IAAI,CACpC3B,GAAG,CAAE4B,KAAK,IAAI;QACZ,IAAIA,KAAK,EAAE;UACT,MAAMC,OAAO,GAAQ;YACnBC,aAAa,EAAE,SAAS,GAAGF;WAC5B;UACD,IAAI,IAAI,CAACH,gBAAgB,CAACN,GAAG,CAACG,GAAG,EAAE,CAAC,GAAGS,MAAM,CAACC,MAAM,CAAC3B,WAAW,CAAC,CAAC,CAAC,EAAE;YACnE,MAAM4B,OAAO,GAAG,IAAI,CAACvB,WAAW,CAACwB,UAAU;YAC3CL,OAAO,CAACM,UAAU,GAAGF,OAAO,CAACE,UAAU;UACzC;UACAhB,GAAG,GAAGA,GAAG,CAACiB,KAAK,CAAC;YACdC,UAAU,EAAER;WACb,CAAC;QACJ;MACF,CAAC,CAAC,EACF5B,SAAS,CAAC,MAAMmB,IAAI,CAACkB,MAAM,CAACnB,GAAG,CAAC,CAAC,EACjCnB,GAAG,CAAEuC,KAAU,IAAI;QACjB,IAAIA,KAAK,YAAYxC,YAAY,EAAE;UACjC,MAAMyC,QAAQ,GAAGD,KAAK,CAACV,OAAO,CAACY,GAAG,CAAC,cAAc,CAAC;UAClD,MAAMC,IAAI,GAAG,IAAI,CAAChC,WAAW,CAACiC,OAAO,EAAE;UACvC,IAAIH,QAAQ,IAAIE,IAAI,EAAE;YACpB,IAAI,CAAChC,WAAW,CAACkC,OAAO,CACtBJ,QAAQ,EACRE,IAAI,CAAC,IAAI,CAAChC,WAAW,CAACmC,cAAc,CAAC,EACrC,IAAI,CAACnC,WAAW,CAACoC,oBAAoB,EAAE,CACxC;UACH;QACF;MACF,CAAC,CAAC,EACF5C,UAAU,CAAE6C,CAAC,IAAK,IAAI,CAACC,eAAe,CAACD,CAAC,CAAC,CAAC,CAC3C;IACH;IACA,OAAO3B,IAAI,CAACkB,MAAM,CAACnB,GAAG,CAAC,CAACQ,IAAI,CAACzB,UAAU,CAAE6C,CAAC,IAAK,IAAI,CAACC,eAAe,CAACD,CAAC,CAAC,CAAC,CAAC;EAC1E;EAEQrB,YAAYA,CAACJ,GAAW;IAC9B,MAAM2B,SAAS,GAAG,IAAI,CAACvC,WAAW,CAACwC,QAAQ,EAAE;IAC7C,MAAMC,OAAO,GAAG,IAAI,CAACzC,WAAW,CAACwB,UAAU,EAAEiB,OAAO;IACpD,IAAIA,OAAO,IAAI,IAAI,CAAC1B,gBAAgB,CAACH,GAAG,EAAE,CAAC,GAAGS,MAAM,CAACC,MAAM,CAAC1B,gBAAgB,CAAC,CAAC,CAAC,EAAE;MAC/E,OAAO,IAAI,CAACI,WAAW,CAAC0C,QAAQ,CAACzB,IAAI,CACnC1B,SAAS,CAAE2B,KAAK,IAAI;QAClB,IAAIA,KAAK,EAAE;UACT,OAAOzB,EAAE,CAACyB,KAAK,CAAC;QAClB;QACA,OAAO,IAAI,CAAClB,WAAW,CAAC2C,WAAW,EAAE;MACvC,CAAC,CAAC,CACH;IACH;IACA,OAAOlD,EAAE,CAAC8C,SAAS,CAAC;EACtB;EAEQxB,gBAAgBA,CAAC6B,MAAc,EAAEC,IAAc;IACrD,OAAOA,IAAI,CAACC,IAAI,CAAElC,GAAG,IAAKgC,MAAM,CAACG,UAAU,CAACnC,GAAG,CAAC,CAAC;EACnD;EAEQ0B,eAAeA,CAACU,GAAsB;IAC5C,IAAIA,GAAG,CAACC,MAAM,KAAK,GAAG,IAAID,GAAG,CAACC,MAAM,KAAK,GAAG,EAAE;MAC5C,IAAI,CAACjD,WAAW,CAACkD,eAAe,EAAE;MAClCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,cAAc;MACrCF,MAAM,CAACC,QAAQ,CAACE,MAAM,EAAE;MACxB,OAAO7D,EAAE,CAACuD,GAAG,CAACO,OAAO,CAAC;IACxB;IACA,OAAO7D,UAAU,CAAC,MAAMsD,GAAG,CAAC;EAC9B;;;uBA5EWlD,eAAe,EAAA0D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAf/D,eAAe;MAAAgE,OAAA,EAAfhE,eAAe,CAAAiE;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}