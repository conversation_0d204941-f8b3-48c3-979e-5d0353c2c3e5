import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, ResolveFn } from '@angular/router';
import { Observable, of } from 'rxjs';
import { ContentService } from './services/content-vendor.service';

export const contentResolver: ResolveFn<any> = (
  route: ActivatedRouteSnapshot
) => {
  const contentVendor = inject(ContentService);
  // Get slug from route data or params
  const slug = route?.data['slug'];
  if (slug) {
    return contentVendor.getContentBySlug(slug) as Observable<any>;
  } else {
    return of(null);
  }
};
