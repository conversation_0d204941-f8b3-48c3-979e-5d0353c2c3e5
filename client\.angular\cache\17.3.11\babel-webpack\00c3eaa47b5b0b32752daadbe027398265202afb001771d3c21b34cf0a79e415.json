{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/content-vendor.service\";\nimport * as i2 from \"@angular/common\";\nfunction HeaderComponent_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 10)(1, \"a\", 11);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementStart(3, \"span\", 12);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const menuItem_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", menuItem_r1.Link || \"#\", i0.ɵɵsanitizeUrl)(\"target\", menuItem_r1.Target || \"_self\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", menuItem_r1.Title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(menuItem_r1.Sub_Title);\n  }\n}\nexport class HeaderComponent {\n  constructor(CMSservice) {\n    this.CMSservice = CMSservice;\n    this.logo = '';\n    this.menuItems = [];\n  }\n  ngOnInit() {\n    if (this.commonContent) {\n      // Extract logo\n      const logoComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.logo\");\n      if (logoComponent?.length) {\n        this.logo = logoComponent[0].Logo?.url || '';\n      }\n      // Extract menu\n      const menuComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.menu\");\n      if (menuComponent?.length) {\n        this.menuItems = menuComponent[0].Menu_Item || [];\n      }\n    }\n  }\n  static {\n    this.ɵfac = function HeaderComponent_Factory(t) {\n      return new (t || HeaderComponent)(i0.ɵɵdirectiveInject(i1.ContentService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HeaderComponent,\n      selectors: [[\"app-header\"]],\n      inputs: {\n        commonContent: \"commonContent\"\n      },\n      decls: 12,\n      vars: 2,\n      consts: [[1, \"main-header\", \"fixed\", \"top-0\", \"w-full\", \"bg-white\", \"z-5\"], [1, \"header-body\", \"relative\", \"max-w-1200\", \"w-full\", \"mx-auto\", \"px-4\", \"flex\", \"align-items-center\"], [1, \"header-logo\", \"relative\", \"pr-6\", \"flex\", \"align-items-center\", \"w-18rem\", \"h-8rem\", \"secondary-bg-color\"], [\"alt\", \"Logo\", 1, \"w-full\", \"h-fit\", 3, \"src\"], [1, \"header-menu-sec\", \"pl-5\", \"relative\", \"flex\", \"align-items-center\", \"justify-content-between\", \"flex-1\", \"h-8rem\", \"bg-white\"], [1, \"menu-list\"], [1, \"p-0\", \"m-0\", \"flex\", \"align-items-center\", \"gap-5\"], [\"class\", \"flex\", 4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button\", \"p-component\", \"w-8rem\", \"h-3rem\", \"justify-content-center\", \"gap-2\", \"line-height-2\", \"bg-bluegray-100\", \"text-color\", \"font-semibold\", \"border-none\"], [1, \"material-symbols-rounded\", \"text-xl\"], [1, \"flex\"], [1, \"flex\", \"flex-column\", \"gap-1\", \"text-lg\", \"font-semibold\", \"text-color\", \"line-height-1\", 3, \"href\", \"target\"], [1, \"text-sm\", \"font-normal\", \"text-color-secondary\"]],\n      template: function HeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"header\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelement(3, \"img\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"ul\", 6);\n          i0.ɵɵtemplate(7, HeaderComponent_li_7_Template, 5, 4, \"li\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"button\", 8)(9, \"span\", 9);\n          i0.ɵɵtext(10, \"login\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(11, \" Login \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"src\", ctx.logo || \"/assets/layout/images/snjya-public-services-logo.png\", i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.menuItems);\n        }\n      },\n      dependencies: [i2.NgForOf],\n      styles: [\"[_nghost-%COMP%]     .main-header:before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  left: 0;\\n  height: 8rem;\\n  background: #030f5e !important;\\n  width: 50%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvaGVhZGVyL2hlYWRlci5jb21wb25lbnQuc2NzcyIsIndlYnBhY2s6Ly8uLy4uLy4uLy4uL1B1YmxpYyUyMFNlcnZpY2UvU05KWUEtUFVCTElDLVNFUlZJQ0UvY2xpZW50L3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvaGVhZGVyL2hlYWRlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDSTtFQUNJLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLE9BQUE7RUFDQSxZQUFBO0VBQ0EsOEJBQUE7RUFDQSxVQUFBO0FDQVIiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCA6Om5nLWRlZXAgLm1haW4taGVhZGVyIHtcbiAgICAmOmJlZm9yZSB7XG4gICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgICAgICAgY29udGVudDogJyc7XG4gICAgICAgIGxlZnQ6IDA7XG4gICAgICAgIGhlaWdodDogOHJlbTtcbiAgICAgICAgYmFja2dyb3VuZDogIzAzMGY1ZSAhaW1wb3J0YW50O1xuICAgICAgICB3aWR0aDogNTAlO1xuICAgIH1cbn1cbiIsIjpob3N0IDo6bmctZGVlcCAubWFpbi1oZWFkZXI6YmVmb3JlIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICBjb250ZW50OiBcIlwiO1xuICBsZWZ0OiAwO1xuICBoZWlnaHQ6IDhyZW07XG4gIGJhY2tncm91bmQ6ICMwMzBmNWUgIWltcG9ydGFudDtcbiAgd2lkdGg6IDUwJTtcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "menuItem_r1", "Link", "ɵɵsanitizeUrl", "Target", "ɵɵtextInterpolate1", "Title", "ɵɵtextInterpolate", "Sub_Title", "HeaderComponent", "constructor", "CMSservice", "logo", "menuItems", "ngOnInit", "commonContent", "logoComponent", "getDataByComponentName", "body", "length", "Logo", "url", "menuComponent", "<PERSON><PERSON>_<PERSON><PERSON>", "ɵɵdirectiveInject", "i1", "ContentService", "selectors", "inputs", "decls", "vars", "consts", "template", "HeaderComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "HeaderComponent_li_7_Template"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\shared\\components\\header\\header.component.ts", "D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\shared\\components\\header\\header.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\nimport { ContentService } from '../../../core/services/content-vendor.service';\n\n@Component({\n  selector: 'app-header',\n  templateUrl: './header.component.html',\n  styleUrl: './header.component.scss'\n})\nexport class HeaderComponent implements OnInit {\n  @Input() commonContent: any;\n\n  logo: string = '';\n  menuItems: any[] = [];\n\n  constructor(private CMSservice: ContentService) { }\n\n  ngOnInit(): void {\n    if (this.commonContent) {\n      // Extract logo\n      const logoComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.logo\");\n      if (logoComponent?.length) {\n        this.logo = logoComponent[0].Logo?.url || '';\n      }\n\n      // Extract menu\n      const menuComponent = this.CMSservice.getDataByComponentName(this.commonContent.body, \"public-sector.menu\");\n      if (menuComponent?.length) {\n        this.menuItems = menuComponent[0].Menu_Item || [];\n      }\n    }\n  }\n}\n", "<!--HEADER SEC-->\n<header class=\"main-header fixed top-0 w-full bg-white z-5\">\n    <div class=\"header-body relative max-w-1200 w-full mx-auto px-4 flex align-items-center\">\n        <div class=\"header-logo relative pr-6 flex align-items-center w-18rem h-8rem secondary-bg-color\">\n            <img [src]=\"logo || '/assets/layout/images/snjya-public-services-logo.png'\" class=\"w-full h-fit\" alt=\"Logo\" />\n        </div>\n        <div class=\"header-menu-sec pl-5 relative flex align-items-center justify-content-between flex-1 h-8rem bg-white\">\n            <div class=\"menu-list\">\n                <ul class=\"p-0 m-0 flex align-items-center gap-5\">\n                    <li class=\"flex\" *ngFor=\"let menuItem of menuItems\">\n                        <a [href]=\"menuItem.Link || '#'\"\n                           [target]=\"menuItem.Target || '_self'\"\n                           class=\"flex flex-column gap-1 text-lg font-semibold text-color line-height-1\">\n                            {{ menuItem.Title }}\n                            <span class=\"text-sm font-normal text-color-secondary\">{{ menuItem.Sub_Title }}</span>\n                        </a>\n                    </li>\n                </ul>\n            </div>\n            <button type=\"button\"\n                class=\"p-element p-ripple p-button-rounded p-button p-component w-8rem h-3rem justify-content-center gap-2 line-height-2 bg-bluegray-100 text-color font-semibold border-none\">\n                <span class=\"material-symbols-rounded text-xl\">login</span> Login\n            </button>\n        </div>\n    </div>\n</header>\n<!--HEADER SEC-->\n"], "mappings": ";;;;;ICUwBA,EADJ,CAAAC,cAAA,aAAoD,YAGiC;IAC7ED,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,eAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAEvFF,EAFuF,CAAAG,YAAA,EAAO,EACtF,EACH;;;;IANEH,EAAA,CAAAI,SAAA,EAA6B;IAC7BJ,EADA,CAAAK,UAAA,SAAAC,WAAA,CAAAC,IAAA,SAAAP,EAAA,CAAAQ,aAAA,CAA6B,WAAAF,WAAA,CAAAG,MAAA,YACQ;IAEpCT,EAAA,CAAAI,SAAA,EACA;IADAJ,EAAA,CAAAU,kBAAA,MAAAJ,WAAA,CAAAK,KAAA,MACA;IAAuDX,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAY,iBAAA,CAAAN,WAAA,CAAAO,SAAA,CAAwB;;;ADN3G,OAAM,MAAOC,eAAe;EAM1BC,YAAoBC,UAA0B;IAA1B,KAAAA,UAAU,GAAVA,UAAU;IAH9B,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,SAAS,GAAU,EAAE;EAE6B;EAElDC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,aAAa,EAAE;MACtB;MACA,MAAMC,aAAa,GAAG,IAAI,CAACL,UAAU,CAACM,sBAAsB,CAAC,IAAI,CAACF,aAAa,CAACG,IAAI,EAAE,oBAAoB,CAAC;MAC3G,IAAIF,aAAa,EAAEG,MAAM,EAAE;QACzB,IAAI,CAACP,IAAI,GAAGI,aAAa,CAAC,CAAC,CAAC,CAACI,IAAI,EAAEC,GAAG,IAAI,EAAE;MAC9C;MAEA;MACA,MAAMC,aAAa,GAAG,IAAI,CAACX,UAAU,CAACM,sBAAsB,CAAC,IAAI,CAACF,aAAa,CAACG,IAAI,EAAE,oBAAoB,CAAC;MAC3G,IAAII,aAAa,EAAEH,MAAM,EAAE;QACzB,IAAI,CAACN,SAAS,GAAGS,aAAa,CAAC,CAAC,CAAC,CAACC,SAAS,IAAI,EAAE;MACnD;IACF;EACF;;;uBAtBWd,eAAe,EAAAd,EAAA,CAAA6B,iBAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAfjB,eAAe;MAAAkB,SAAA;MAAAC,MAAA;QAAAb,aAAA;MAAA;MAAAc,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCLpBvC,EAFR,CAAAC,cAAA,gBAA4D,aACiC,aACY;UAC7FD,EAAA,CAAAyC,SAAA,aAA8G;UAClHzC,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,aAAkH,aACvF,YAC+B;UAC9CD,EAAA,CAAA0C,UAAA,IAAAC,6BAAA,gBAAoD;UAS5D3C,EADI,CAAAG,YAAA,EAAK,EACH;UAGFH,EAFJ,CAAAC,cAAA,gBACmL,cAChI;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,eAChE;UAGZF,EAHY,CAAAG,YAAA,EAAS,EACP,EACJ,EACD;;;UArBQH,EAAA,CAAAI,SAAA,GAAsE;UAAtEJ,EAAA,CAAAK,UAAA,QAAAmC,GAAA,CAAAvB,IAAA,4DAAAjB,EAAA,CAAAQ,aAAA,CAAsE;UAK7BR,EAAA,CAAAI,SAAA,GAAY;UAAZJ,EAAA,CAAAK,UAAA,YAAAmC,GAAA,CAAAtB,SAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}