{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\n\n/**\n * Scroller is a performance-approach to handle huge data efficiently.\n * @group Components\n */\nconst _c0 = [\"element\"];\nconst _c1 = [\"content\"];\nconst _c2 = [\"*\"];\nconst _c3 = (a0, a1, a2) => ({\n  \"p-scroller\": true,\n  \"p-scroller-inline\": a0,\n  \"p-both-scroll\": a1,\n  \"p-horizontal-scroll\": a2\n});\nconst _c4 = (a0, a1) => ({\n  $implicit: a0,\n  options: a1\n});\nconst _c5 = a0 => ({\n  \"p-scroller-loading\": a0\n});\nconst _c6 = a0 => ({\n  \"p-component-overlay\": a0\n});\nconst _c7 = a0 => ({\n  numCols: a0\n});\nconst _c8 = a0 => ({\n  options: a0\n});\nconst _c9 = () => ({\n  styleClass: \"p-scroller-loading-icon\"\n});\nconst _c10 = (a0, a1) => ({\n  rows: a0,\n  columns: a1\n});\nfunction Scroller_ng_container_0_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_ng_container_3_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c4, ctx_r1.loadedItems, ctx_r1.getContentOptions()));\n  }\n}\nfunction Scroller_ng_container_0_ng_template_4_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_ng_template_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_ng_template_4_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const index_r4 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c4, item_r3, ctx_r1.getOptions(index_r4)));\n  }\n}\nfunction Scroller_ng_container_0_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11, 3);\n    i0.ɵɵtemplate(2, Scroller_ng_container_0_ng_template_4_ng_container_2_Template, 2, 5, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c5, ctx_r1.d_loading))(\"ngStyle\", ctx_r1.contentStyle);\n    i0.ɵɵattribute(\"data-pc-section\", \"content\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.loadedItems)(\"ngForTrackBy\", ctx_r1._trackBy || ctx_r1.index);\n  }\n}\nfunction Scroller_ng_container_0_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.spacerStyle);\n    i0.ɵɵattribute(\"data-pc-section\", \"spacer\");\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const index_r5 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.loaderTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c8, ctx_r1.getLoaderOptions(index_r5, ctx_r1.both && i0.ɵɵpureFunction1(2, _c7, ctx_r1._numItemsInViewport.cols))));\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_Template, 2, 6, \"ng-container\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.loaderArr);\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.loaderIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c8, i0.ɵɵpureFunction0(2, _c9)));\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"SpinnerIcon\", 16);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-scroller-loading-icon pi-spin\");\n    i0.ɵɵattribute(\"data-pc-section\", \"loadingIcon\");\n  }\n}\nfunction Scroller_ng_container_0_div_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_Template, 2, 5, \"ng-container\", 6)(1, Scroller_ng_container_0_div_7_ng_template_2_ng_template_1_Template, 1, 2, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const buildInLoaderIcon_r6 = i0.ɵɵreference(2);\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loaderIconTemplate)(\"ngIfElse\", buildInLoaderIcon_r6);\n  }\n}\nfunction Scroller_ng_container_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, Scroller_ng_container_0_div_7_ng_container_1_Template, 2, 1, \"ng-container\", 6)(2, Scroller_ng_container_0_div_7_ng_template_2_Template, 3, 2, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const buildInLoader_r7 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c6, !ctx_r1.loaderTemplate));\n    i0.ɵɵattribute(\"data-pc-section\", \"loader\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.loaderTemplate)(\"ngIfElse\", buildInLoader_r7);\n  }\n}\nfunction Scroller_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 7, 1);\n    i0.ɵɵlistener(\"scroll\", function Scroller_ng_container_0_Template_div_scroll_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onContainerScroll($event));\n    });\n    i0.ɵɵtemplate(3, Scroller_ng_container_0_ng_container_3_Template, 2, 5, \"ng-container\", 6)(4, Scroller_ng_container_0_ng_template_4_Template, 3, 7, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(6, Scroller_ng_container_0_div_6_Template, 1, 2, \"div\", 8)(7, Scroller_ng_container_0_div_7_Template, 4, 6, \"div\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const buildInContent_r8 = i0.ɵɵreference(5);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1._styleClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1._style)(\"ngClass\", i0.ɵɵpureFunction3(12, _c3, ctx_r1.inline, ctx_r1.both, ctx_r1.horizontal));\n    i0.ɵɵattribute(\"id\", ctx_r1._id)(\"tabindex\", ctx_r1.tabindex)(\"data-pc-name\", \"scroller\")(\"data-pc-section\", \"root\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.contentTemplate)(\"ngIfElse\", buildInContent_r8);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1._showSpacer);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.loaderDisabled && ctx_r1._showLoader && ctx_r1.d_loading);\n  }\n}\nfunction Scroller_ng_template_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Scroller_ng_template_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Scroller_ng_template_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.contentTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(5, _c4, ctx_r1.items, i0.ɵɵpureFunction2(2, _c10, ctx_r1._items, ctx_r1.loadedColumns)));\n  }\n}\nfunction Scroller_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n    i0.ɵɵtemplate(1, Scroller_ng_template_1_ng_container_1_Template, 2, 8, \"ng-container\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.contentTemplate);\n  }\n}\nclass Scroller {\n  document;\n  platformId;\n  renderer;\n  cd;\n  zone;\n  /**\n   * Unique identifier of the element.\n   * @group Props\n   */\n  get id() {\n    return this._id;\n  }\n  set id(val) {\n    this._id = val;\n  }\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  get style() {\n    return this._style;\n  }\n  set style(val) {\n    this._style = val;\n  }\n  /**\n   * Style class of the element.\n   * @group Props\n   */\n  get styleClass() {\n    return this._styleClass;\n  }\n  set styleClass(val) {\n    this._styleClass = val;\n  }\n  /**\n   * Index of the element in tabbing order.\n   * @group Props\n   */\n  get tabindex() {\n    return this._tabindex;\n  }\n  set tabindex(val) {\n    this._tabindex = val;\n  }\n  /**\n   * An array of objects to display.\n   * @group Props\n   */\n  get items() {\n    return this._items;\n  }\n  set items(val) {\n    this._items = val;\n  }\n  /**\n   * The height/width of item according to orientation.\n   * @group Props\n   */\n  get itemSize() {\n    return this._itemSize;\n  }\n  set itemSize(val) {\n    this._itemSize = val;\n  }\n  /**\n   * Height of the scroll viewport.\n   * @group Props\n   */\n  get scrollHeight() {\n    return this._scrollHeight;\n  }\n  set scrollHeight(val) {\n    this._scrollHeight = val;\n  }\n  /**\n   * Width of the scroll viewport.\n   * @group Props\n   */\n  get scrollWidth() {\n    return this._scrollWidth;\n  }\n  set scrollWidth(val) {\n    this._scrollWidth = val;\n  }\n  /**\n   * The orientation of scrollbar.\n   * @group Props\n   */\n  get orientation() {\n    return this._orientation;\n  }\n  set orientation(val) {\n    this._orientation = val;\n  }\n  /**\n   * Used to specify how many items to load in each load method in lazy mode.\n   * @group Props\n   */\n  get step() {\n    return this._step;\n  }\n  set step(val) {\n    this._step = val;\n  }\n  /**\n   * Delay in scroll before new data is loaded.\n   * @group Props\n   */\n  get delay() {\n    return this._delay;\n  }\n  set delay(val) {\n    this._delay = val;\n  }\n  /**\n   * Delay after window's resize finishes.\n   * @group Props\n   */\n  get resizeDelay() {\n    return this._resizeDelay;\n  }\n  set resizeDelay(val) {\n    this._resizeDelay = val;\n  }\n  /**\n   * Used to append each loaded item to top without removing any items from the DOM. Using very large data may cause the browser to crash.\n   * @group Props\n   */\n  get appendOnly() {\n    return this._appendOnly;\n  }\n  set appendOnly(val) {\n    this._appendOnly = val;\n  }\n  /**\n   * Specifies whether the scroller should be displayed inline or not.\n   * @group Props\n   */\n  get inline() {\n    return this._inline;\n  }\n  set inline(val) {\n    this._inline = val;\n  }\n  /**\n   * Defines if data is loaded and interacted with in lazy manner.\n   * @group Props\n   */\n  get lazy() {\n    return this._lazy;\n  }\n  set lazy(val) {\n    this._lazy = val;\n  }\n  /**\n   * If disabled, the scroller feature is eliminated and the content is displayed directly.\n   * @group Props\n   */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(val) {\n    this._disabled = val;\n  }\n  /**\n   * Used to implement a custom loader instead of using the loader feature in the scroller.\n   * @group Props\n   */\n  get loaderDisabled() {\n    return this._loaderDisabled;\n  }\n  set loaderDisabled(val) {\n    this._loaderDisabled = val;\n  }\n  /**\n   * Columns to display.\n   * @group Props\n   */\n  get columns() {\n    return this._columns;\n  }\n  set columns(val) {\n    this._columns = val;\n  }\n  /**\n   * Used to implement a custom spacer instead of using the spacer feature in the scroller.\n   * @group Props\n   */\n  get showSpacer() {\n    return this._showSpacer;\n  }\n  set showSpacer(val) {\n    this._showSpacer = val;\n  }\n  /**\n   * Defines whether to show loader.\n   * @group Props\n   */\n  get showLoader() {\n    return this._showLoader;\n  }\n  set showLoader(val) {\n    this._showLoader = val;\n  }\n  /**\n   * Determines how many additional elements to add to the DOM outside of the view. According to the scrolls made up and down, extra items are added in a certain algorithm in the form of multiples of this number. Default value is half the number of items shown in the view.\n   * @group Props\n   */\n  get numToleratedItems() {\n    return this._numToleratedItems;\n  }\n  set numToleratedItems(val) {\n    this._numToleratedItems = val;\n  }\n  /**\n   * Defines whether the data is loaded.\n   * @group Props\n   */\n  get loading() {\n    return this._loading;\n  }\n  set loading(val) {\n    this._loading = val;\n  }\n  /**\n   * Defines whether to dynamically change the height or width of scrollable container.\n   * @group Props\n   */\n  get autoSize() {\n    return this._autoSize;\n  }\n  set autoSize(val) {\n    this._autoSize = val;\n  }\n  /**\n   * Function to optimize the dom operations by delegating to ngForTrackBy, default algoritm checks for object identity.\n   * @group Props\n   */\n  get trackBy() {\n    return this._trackBy;\n  }\n  set trackBy(val) {\n    this._trackBy = val;\n  }\n  /**\n   * Defines whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n   * @group Props\n   */\n  get options() {\n    return this._options;\n  }\n  set options(val) {\n    this._options = val;\n    if (val && typeof val === 'object') {\n      //@ts-ignore\n      Object.entries(val).forEach(([k, v]) => this[`_${k}`] !== v && (this[`_${k}`] = v));\n    }\n  }\n  /**\n   * Callback to invoke in lazy mode to load new data.\n   * @param {ScrollerLazyLoadEvent} event - Custom lazy load event.\n   * @group Emits\n   */\n  onLazyLoad = new EventEmitter();\n  /**\n   * Callback to invoke when scroll position changes.\n   * @param {ScrollerScrollEvent} event - Custom scroll event.\n   * @group Emits\n   */\n  onScroll = new EventEmitter();\n  /**\n   * Callback to invoke when scroll position and item's range in view changes.\n   * @param {ScrollerScrollEvent} event - Custom scroll index change event.\n   * @group Emits\n   */\n  onScrollIndexChange = new EventEmitter();\n  elementViewChild;\n  contentViewChild;\n  templates;\n  _id;\n  _style;\n  _styleClass;\n  _tabindex = 0;\n  _items;\n  _itemSize = 0;\n  _scrollHeight;\n  _scrollWidth;\n  _orientation = 'vertical';\n  _step = 0;\n  _delay = 0;\n  _resizeDelay = 10;\n  _appendOnly = false;\n  _inline = false;\n  _lazy = false;\n  _disabled = false;\n  _loaderDisabled = false;\n  _columns;\n  _showSpacer = true;\n  _showLoader = false;\n  _numToleratedItems;\n  _loading;\n  _autoSize = false;\n  _trackBy;\n  _options;\n  d_loading = false;\n  d_numToleratedItems;\n  contentEl;\n  contentTemplate;\n  itemTemplate;\n  loaderTemplate;\n  loaderIconTemplate;\n  first = 0;\n  last = 0;\n  page = 0;\n  isRangeChanged = false;\n  numItemsInViewport = 0;\n  lastScrollPos = 0;\n  lazyLoadState = {};\n  loaderArr = [];\n  spacerStyle = {};\n  contentStyle = {};\n  scrollTimeout;\n  resizeTimeout;\n  initialized = false;\n  windowResizeListener;\n  defaultWidth;\n  defaultHeight;\n  defaultContentWidth;\n  defaultContentHeight;\n  get vertical() {\n    return this._orientation === 'vertical';\n  }\n  get horizontal() {\n    return this._orientation === 'horizontal';\n  }\n  get both() {\n    return this._orientation === 'both';\n  }\n  get loadedItems() {\n    if (this._items && !this.d_loading) {\n      if (this.both) return this._items.slice(this._appendOnly ? 0 : this.first.rows, this.last.rows).map(item => this._columns ? item : item.slice(this._appendOnly ? 0 : this.first.cols, this.last.cols));else if (this.horizontal && this._columns) return this._items;else return this._items.slice(this._appendOnly ? 0 : this.first, this.last);\n    }\n    return [];\n  }\n  get loadedRows() {\n    return this.d_loading ? this._loaderDisabled ? this.loaderArr : [] : this.loadedItems;\n  }\n  get loadedColumns() {\n    if (this._columns && (this.both || this.horizontal)) {\n      return this.d_loading && this._loaderDisabled ? this.both ? this.loaderArr[0] : this.loaderArr : this._columns.slice(this.both ? this.first.cols : this.first, this.both ? this.last.cols : this.last);\n    }\n    return this._columns;\n  }\n  get isPageChanged() {\n    return this._step ? this.page !== this.getPageByFirst() : true;\n  }\n  constructor(document, platformId, renderer, cd, zone) {\n    this.document = document;\n    this.platformId = platformId;\n    this.renderer = renderer;\n    this.cd = cd;\n    this.zone = zone;\n  }\n  ngOnInit() {\n    this.setInitialState();\n  }\n  ngOnChanges(simpleChanges) {\n    let isLoadingChanged = false;\n    if (simpleChanges.loading) {\n      const {\n        previousValue,\n        currentValue\n      } = simpleChanges.loading;\n      if (this.lazy && previousValue !== currentValue && currentValue !== this.d_loading) {\n        this.d_loading = currentValue;\n        isLoadingChanged = true;\n      }\n    }\n    if (simpleChanges.orientation) {\n      this.lastScrollPos = this.both ? {\n        top: 0,\n        left: 0\n      } : 0;\n    }\n    if (simpleChanges.numToleratedItems) {\n      const {\n        previousValue,\n        currentValue\n      } = simpleChanges.numToleratedItems;\n      if (previousValue !== currentValue && currentValue !== this.d_numToleratedItems) {\n        this.d_numToleratedItems = currentValue;\n      }\n    }\n    if (simpleChanges.options) {\n      const {\n        previousValue,\n        currentValue\n      } = simpleChanges.options;\n      if (this.lazy && previousValue?.loading !== currentValue?.loading && currentValue?.loading !== this.d_loading) {\n        this.d_loading = currentValue.loading;\n        isLoadingChanged = true;\n      }\n      if (previousValue?.numToleratedItems !== currentValue?.numToleratedItems && currentValue?.numToleratedItems !== this.d_numToleratedItems) {\n        this.d_numToleratedItems = currentValue.numToleratedItems;\n      }\n    }\n    if (this.initialized) {\n      const isChanged = !isLoadingChanged && (simpleChanges.items?.previousValue?.length !== simpleChanges.items?.currentValue?.length || simpleChanges.itemSize || simpleChanges.scrollHeight || simpleChanges.scrollWidth);\n      if (isChanged) {\n        this.init();\n        this.calculateAutoSize();\n      }\n    }\n  }\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'content':\n          this.contentTemplate = item.template;\n          break;\n        case 'item':\n          this.itemTemplate = item.template;\n          break;\n        case 'loader':\n          this.loaderTemplate = item.template;\n          break;\n        case 'loadericon':\n          this.loaderIconTemplate = item.template;\n          break;\n        default:\n          this.itemTemplate = item.template;\n          break;\n      }\n    });\n  }\n  ngAfterViewInit() {\n    Promise.resolve().then(() => {\n      this.viewInit();\n    });\n  }\n  ngAfterViewChecked() {\n    if (!this.initialized) {\n      this.viewInit();\n    }\n  }\n  ngOnDestroy() {\n    this.unbindResizeListener();\n    this.contentEl = null;\n    this.initialized = false;\n  }\n  viewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (DomHandler.isVisible(this.elementViewChild?.nativeElement)) {\n        this.setInitialState();\n        this.setContentEl(this.contentEl);\n        this.init();\n        this.defaultWidth = DomHandler.getWidth(this.elementViewChild?.nativeElement);\n        this.defaultHeight = DomHandler.getHeight(this.elementViewChild?.nativeElement);\n        this.defaultContentWidth = DomHandler.getWidth(this.contentEl);\n        this.defaultContentHeight = DomHandler.getHeight(this.contentEl);\n        this.initialized = true;\n      }\n    }\n  }\n  init() {\n    if (!this._disabled) {\n      this.setSize();\n      this.calculateOptions();\n      this.setSpacerSize();\n      this.bindResizeListener();\n      this.cd.detectChanges();\n    }\n  }\n  setContentEl(el) {\n    this.contentEl = el || this.contentViewChild?.nativeElement || DomHandler.findSingle(this.elementViewChild?.nativeElement, '.p-scroller-content');\n  }\n  setInitialState() {\n    this.first = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    this.last = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    this.numItemsInViewport = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    this.lastScrollPos = this.both ? {\n      top: 0,\n      left: 0\n    } : 0;\n    this.d_loading = this._loading || false;\n    this.d_numToleratedItems = this._numToleratedItems;\n    this.loaderArr = [];\n    this.spacerStyle = {};\n    this.contentStyle = {};\n  }\n  getElementRef() {\n    return this.elementViewChild;\n  }\n  getPageByFirst() {\n    return Math.floor((this.first + this.d_numToleratedItems * 4) / (this._step || 1));\n  }\n  scrollTo(options) {\n    this.lastScrollPos = this.both ? {\n      top: 0,\n      left: 0\n    } : 0;\n    this.elementViewChild?.nativeElement?.scrollTo(options);\n  }\n  scrollToIndex(index, behavior = 'auto') {\n    const {\n      numToleratedItems\n    } = this.calculateNumItems();\n    const contentPos = this.getContentPosition();\n    const calculateFirst = (_index = 0, _numT) => _index <= _numT ? 0 : _index;\n    const calculateCoord = (_first, _size, _cpos) => _first * _size + _cpos;\n    const scrollTo = (left = 0, top = 0) => this.scrollTo({\n      left,\n      top,\n      behavior\n    });\n    let newFirst = 0;\n    if (this.both) {\n      newFirst = {\n        rows: calculateFirst(index[0], numToleratedItems[0]),\n        cols: calculateFirst(index[1], numToleratedItems[1])\n      };\n      scrollTo(calculateCoord(newFirst.cols, this._itemSize[1], contentPos.left), calculateCoord(newFirst.rows, this._itemSize[0], contentPos.top));\n    } else {\n      newFirst = calculateFirst(index, numToleratedItems);\n      this.horizontal ? scrollTo(calculateCoord(newFirst, this._itemSize, contentPos.left), 0) : scrollTo(0, calculateCoord(newFirst, this._itemSize, contentPos.top));\n    }\n    this.isRangeChanged = this.first !== newFirst;\n    this.first = newFirst;\n  }\n  scrollInView(index, to, behavior = 'auto') {\n    if (to) {\n      const {\n        first,\n        viewport\n      } = this.getRenderedRange();\n      const scrollTo = (left = 0, top = 0) => this.scrollTo({\n        left,\n        top,\n        behavior\n      });\n      const isToStart = to === 'to-start';\n      const isToEnd = to === 'to-end';\n      if (isToStart) {\n        if (this.both) {\n          if (viewport.first.rows - first.rows > index[0]) {\n            scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows - 1) * this._itemSize[0]);\n          } else if (viewport.first.cols - first.cols > index[1]) {\n            scrollTo((viewport.first.cols - 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n          }\n        } else {\n          if (viewport.first - first > index) {\n            const pos = (viewport.first - 1) * this._itemSize;\n            this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n          }\n        }\n      } else if (isToEnd) {\n        if (this.both) {\n          if (viewport.last.rows - first.rows <= index[0] + 1) {\n            scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows + 1) * this._itemSize[0]);\n          } else if (viewport.last.cols - first.cols <= index[1] + 1) {\n            scrollTo((viewport.first.cols + 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n          }\n        } else {\n          if (viewport.last - first <= index + 1) {\n            const pos = (viewport.first + 1) * this._itemSize;\n            this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n          }\n        }\n      }\n    } else {\n      this.scrollToIndex(index, behavior);\n    }\n  }\n  getRenderedRange() {\n    const calculateFirstInViewport = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n    let firstInViewport = this.first;\n    let lastInViewport = 0;\n    if (this.elementViewChild?.nativeElement) {\n      const {\n        scrollTop,\n        scrollLeft\n      } = this.elementViewChild.nativeElement;\n      if (this.both) {\n        firstInViewport = {\n          rows: calculateFirstInViewport(scrollTop, this._itemSize[0]),\n          cols: calculateFirstInViewport(scrollLeft, this._itemSize[1])\n        };\n        lastInViewport = {\n          rows: firstInViewport.rows + this.numItemsInViewport.rows,\n          cols: firstInViewport.cols + this.numItemsInViewport.cols\n        };\n      } else {\n        const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n        firstInViewport = calculateFirstInViewport(scrollPos, this._itemSize);\n        lastInViewport = firstInViewport + this.numItemsInViewport;\n      }\n    }\n    return {\n      first: this.first,\n      last: this.last,\n      viewport: {\n        first: firstInViewport,\n        last: lastInViewport\n      }\n    };\n  }\n  calculateNumItems() {\n    const contentPos = this.getContentPosition();\n    const contentWidth = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetWidth - contentPos.left : 0) || 0;\n    const contentHeight = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetHeight - contentPos.top : 0) || 0;\n    const calculateNumItemsInViewport = (_contentSize, _itemSize) => Math.ceil(_contentSize / (_itemSize || _contentSize));\n    const calculateNumToleratedItems = _numItems => Math.ceil(_numItems / 2);\n    const numItemsInViewport = this.both ? {\n      rows: calculateNumItemsInViewport(contentHeight, this._itemSize[0]),\n      cols: calculateNumItemsInViewport(contentWidth, this._itemSize[1])\n    } : calculateNumItemsInViewport(this.horizontal ? contentWidth : contentHeight, this._itemSize);\n    const numToleratedItems = this.d_numToleratedItems || (this.both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));\n    return {\n      numItemsInViewport,\n      numToleratedItems\n    };\n  }\n  calculateOptions() {\n    const {\n      numItemsInViewport,\n      numToleratedItems\n    } = this.calculateNumItems();\n    const calculateLast = (_first, _num, _numT, _isCols = false) => this.getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);\n    const first = this.first;\n    const last = this.both ? {\n      rows: calculateLast(this.first.rows, numItemsInViewport.rows, numToleratedItems[0]),\n      cols: calculateLast(this.first.cols, numItemsInViewport.cols, numToleratedItems[1], true)\n    } : calculateLast(this.first, numItemsInViewport, numToleratedItems);\n    this.last = last;\n    this.numItemsInViewport = numItemsInViewport;\n    this.d_numToleratedItems = numToleratedItems;\n    if (this.showLoader) {\n      this.loaderArr = this.both ? Array.from({\n        length: numItemsInViewport.rows\n      }).map(() => Array.from({\n        length: numItemsInViewport.cols\n      })) : Array.from({\n        length: numItemsInViewport\n      });\n    }\n    if (this._lazy) {\n      Promise.resolve().then(() => {\n        this.lazyLoadState = {\n          first: this._step ? this.both ? {\n            rows: 0,\n            cols: first.cols\n          } : 0 : first,\n          last: Math.min(this._step ? this._step : this.last, this.items.length)\n        };\n        this.handleEvents('onLazyLoad', this.lazyLoadState);\n      });\n    }\n  }\n  calculateAutoSize() {\n    if (this._autoSize && !this.d_loading) {\n      Promise.resolve().then(() => {\n        if (this.contentEl) {\n          this.contentEl.style.minHeight = this.contentEl.style.minWidth = 'auto';\n          this.contentEl.style.position = 'relative';\n          this.elementViewChild.nativeElement.style.contain = 'none';\n          const [contentWidth, contentHeight] = [DomHandler.getWidth(this.contentEl), DomHandler.getHeight(this.contentEl)];\n          contentWidth !== this.defaultContentWidth && (this.elementViewChild.nativeElement.style.width = '');\n          contentHeight !== this.defaultContentHeight && (this.elementViewChild.nativeElement.style.height = '');\n          const [width, height] = [DomHandler.getWidth(this.elementViewChild.nativeElement), DomHandler.getHeight(this.elementViewChild.nativeElement)];\n          (this.both || this.horizontal) && (this.elementViewChild.nativeElement.style.width = width < this.defaultWidth ? width + 'px' : this._scrollWidth || this.defaultWidth + 'px');\n          (this.both || this.vertical) && (this.elementViewChild.nativeElement.style.height = height < this.defaultHeight ? height + 'px' : this._scrollHeight || this.defaultHeight + 'px');\n          this.contentEl.style.minHeight = this.contentEl.style.minWidth = '';\n          this.contentEl.style.position = '';\n          this.elementViewChild.nativeElement.style.contain = '';\n        }\n      });\n    }\n  }\n  getLast(last = 0, isCols = false) {\n    return this._items ? Math.min(isCols ? (this._columns || this._items[0]).length : this._items.length, last) : 0;\n  }\n  getContentPosition() {\n    if (this.contentEl) {\n      const style = getComputedStyle(this.contentEl);\n      const left = parseFloat(style.paddingLeft) + Math.max(parseFloat(style.left) || 0, 0);\n      const right = parseFloat(style.paddingRight) + Math.max(parseFloat(style.right) || 0, 0);\n      const top = parseFloat(style.paddingTop) + Math.max(parseFloat(style.top) || 0, 0);\n      const bottom = parseFloat(style.paddingBottom) + Math.max(parseFloat(style.bottom) || 0, 0);\n      return {\n        left,\n        right,\n        top,\n        bottom,\n        x: left + right,\n        y: top + bottom\n      };\n    }\n    return {\n      left: 0,\n      right: 0,\n      top: 0,\n      bottom: 0,\n      x: 0,\n      y: 0\n    };\n  }\n  setSize() {\n    if (this.elementViewChild?.nativeElement) {\n      const parentElement = this.elementViewChild.nativeElement.parentElement.parentElement;\n      const width = this._scrollWidth || `${this.elementViewChild.nativeElement.offsetWidth || parentElement.offsetWidth}px`;\n      const height = this._scrollHeight || `${this.elementViewChild.nativeElement.offsetHeight || parentElement.offsetHeight}px`;\n      const setProp = (_name, _value) => this.elementViewChild.nativeElement.style[_name] = _value;\n      if (this.both || this.horizontal) {\n        setProp('height', height);\n        setProp('width', width);\n      } else {\n        setProp('height', height);\n      }\n    }\n  }\n  setSpacerSize() {\n    if (this._items) {\n      const contentPos = this.getContentPosition();\n      const setProp = (_name, _value, _size, _cpos = 0) => this.spacerStyle = {\n        ...this.spacerStyle,\n        ...{\n          [`${_name}`]: (_value || []).length * _size + _cpos + 'px'\n        }\n      };\n      if (this.both) {\n        setProp('height', this._items, this._itemSize[0], contentPos.y);\n        setProp('width', this._columns || this._items[1], this._itemSize[1], contentPos.x);\n      } else {\n        this.horizontal ? setProp('width', this._columns || this._items, this._itemSize, contentPos.x) : setProp('height', this._items, this._itemSize, contentPos.y);\n      }\n    }\n  }\n  setContentPosition(pos) {\n    if (this.contentEl && !this._appendOnly) {\n      const first = pos ? pos.first : this.first;\n      const calculateTranslateVal = (_first, _size) => _first * _size;\n      const setTransform = (_x = 0, _y = 0) => this.contentStyle = {\n        ...this.contentStyle,\n        ...{\n          transform: `translate3d(${_x}px, ${_y}px, 0)`\n        }\n      };\n      if (this.both) {\n        setTransform(calculateTranslateVal(first.cols, this._itemSize[1]), calculateTranslateVal(first.rows, this._itemSize[0]));\n      } else {\n        const translateVal = calculateTranslateVal(first, this._itemSize);\n        this.horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n      }\n    }\n  }\n  onScrollPositionChange(event) {\n    const target = event.target;\n    const contentPos = this.getContentPosition();\n    const calculateScrollPos = (_pos, _cpos) => _pos ? _pos > _cpos ? _pos - _cpos : _pos : 0;\n    const calculateCurrentIndex = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n    const calculateTriggerIndex = (_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n      return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;\n    };\n    const calculateFirst = (_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n      if (_currentIndex <= _numT) return 0;else return Math.max(0, _isScrollDownOrRight ? _currentIndex < _triggerIndex ? _first : _currentIndex - _numT : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);\n    };\n    const calculateLast = (_currentIndex, _first, _last, _num, _numT, _isCols = false) => {\n      let lastValue = _first + _num + 2 * _numT;\n      if (_currentIndex >= _numT) {\n        lastValue += _numT + 1;\n      }\n      return this.getLast(lastValue, _isCols);\n    };\n    const scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);\n    const scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);\n    let newFirst = this.both ? {\n      rows: 0,\n      cols: 0\n    } : 0;\n    let newLast = this.last;\n    let isRangeChanged = false;\n    let newScrollPos = this.lastScrollPos;\n    if (this.both) {\n      const isScrollDown = this.lastScrollPos.top <= scrollTop;\n      const isScrollRight = this.lastScrollPos.left <= scrollLeft;\n      if (!this._appendOnly || this._appendOnly && (isScrollDown || isScrollRight)) {\n        const currentIndex = {\n          rows: calculateCurrentIndex(scrollTop, this._itemSize[0]),\n          cols: calculateCurrentIndex(scrollLeft, this._itemSize[1])\n        };\n        const triggerIndex = {\n          rows: calculateTriggerIndex(currentIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n          cols: calculateTriggerIndex(currentIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n        };\n        newFirst = {\n          rows: calculateFirst(currentIndex.rows, triggerIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n          cols: calculateFirst(currentIndex.cols, triggerIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n        };\n        newLast = {\n          rows: calculateLast(currentIndex.rows, newFirst.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0]),\n          cols: calculateLast(currentIndex.cols, newFirst.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], true)\n        };\n        isRangeChanged = newFirst.rows !== this.first.rows || newLast.rows !== this.last.rows || newFirst.cols !== this.first.cols || newLast.cols !== this.last.cols || this.isRangeChanged;\n        newScrollPos = {\n          top: scrollTop,\n          left: scrollLeft\n        };\n      }\n    } else {\n      const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n      const isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n      if (!this._appendOnly || this._appendOnly && isScrollDownOrRight) {\n        const currentIndex = calculateCurrentIndex(scrollPos, this._itemSize);\n        const triggerIndex = calculateTriggerIndex(currentIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n        newFirst = calculateFirst(currentIndex, triggerIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n        newLast = calculateLast(currentIndex, newFirst, this.last, this.numItemsInViewport, this.d_numToleratedItems);\n        isRangeChanged = newFirst !== this.first || newLast !== this.last || this.isRangeChanged;\n        newScrollPos = scrollPos;\n      }\n    }\n    return {\n      first: newFirst,\n      last: newLast,\n      isRangeChanged,\n      scrollPos: newScrollPos\n    };\n  }\n  onScrollChange(event) {\n    const {\n      first,\n      last,\n      isRangeChanged,\n      scrollPos\n    } = this.onScrollPositionChange(event);\n    if (isRangeChanged) {\n      const newState = {\n        first,\n        last\n      };\n      this.setContentPosition(newState);\n      this.first = first;\n      this.last = last;\n      this.lastScrollPos = scrollPos;\n      this.handleEvents('onScrollIndexChange', newState);\n      if (this._lazy && this.isPageChanged) {\n        const lazyLoadState = {\n          first: this._step ? Math.min(this.getPageByFirst() * this._step, this.items.length - this._step) : first,\n          last: Math.min(this._step ? (this.getPageByFirst() + 1) * this._step : last, this.items.length)\n        };\n        const isLazyStateChanged = this.lazyLoadState.first !== lazyLoadState.first || this.lazyLoadState.last !== lazyLoadState.last;\n        isLazyStateChanged && this.handleEvents('onLazyLoad', lazyLoadState);\n        this.lazyLoadState = lazyLoadState;\n      }\n    }\n  }\n  onContainerScroll(event) {\n    this.handleEvents('onScroll', {\n      originalEvent: event\n    });\n    if (this._delay && this.isPageChanged) {\n      if (this.scrollTimeout) {\n        clearTimeout(this.scrollTimeout);\n      }\n      if (!this.d_loading && this.showLoader) {\n        const {\n          isRangeChanged\n        } = this.onScrollPositionChange(event);\n        const changed = isRangeChanged || (this._step ? this.isPageChanged : false);\n        if (changed) {\n          this.d_loading = true;\n          this.cd.detectChanges();\n        }\n      }\n      this.scrollTimeout = setTimeout(() => {\n        this.onScrollChange(event);\n        if (this.d_loading && this.showLoader && (!this._lazy || this._loading === undefined)) {\n          this.d_loading = false;\n          this.page = this.getPageByFirst();\n          this.cd.detectChanges();\n        }\n      }, this._delay);\n    } else {\n      !this.d_loading && this.onScrollChange(event);\n    }\n  }\n  bindResizeListener() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (!this.windowResizeListener) {\n        this.zone.runOutsideAngular(() => {\n          const window = this.document.defaultView;\n          const event = DomHandler.isTouchDevice() ? 'orientationchange' : 'resize';\n          this.windowResizeListener = this.renderer.listen(window, event, this.onWindowResize.bind(this));\n        });\n      }\n    }\n  }\n  unbindResizeListener() {\n    if (this.windowResizeListener) {\n      this.windowResizeListener();\n      this.windowResizeListener = null;\n    }\n  }\n  onWindowResize() {\n    if (this.resizeTimeout) {\n      clearTimeout(this.resizeTimeout);\n    }\n    this.resizeTimeout = setTimeout(() => {\n      if (DomHandler.isVisible(this.elementViewChild?.nativeElement)) {\n        const [width, height] = [DomHandler.getWidth(this.elementViewChild?.nativeElement), DomHandler.getHeight(this.elementViewChild?.nativeElement)];\n        const [isDiffWidth, isDiffHeight] = [width !== this.defaultWidth, height !== this.defaultHeight];\n        const reinit = this.both ? isDiffWidth || isDiffHeight : this.horizontal ? isDiffWidth : this.vertical ? isDiffHeight : false;\n        reinit && this.zone.run(() => {\n          this.d_numToleratedItems = this._numToleratedItems;\n          this.defaultWidth = width;\n          this.defaultHeight = height;\n          this.defaultContentWidth = DomHandler.getWidth(this.contentEl);\n          this.defaultContentHeight = DomHandler.getHeight(this.contentEl);\n          this.init();\n        });\n      }\n    }, this._resizeDelay);\n  }\n  handleEvents(name, params) {\n    //@ts-ignore\n    return this.options && this.options[name] ? this.options[name](params) : this[name].emit(params);\n  }\n  getContentOptions() {\n    return {\n      contentStyleClass: `p-scroller-content ${this.d_loading ? 'p-scroller-loading' : ''}`,\n      items: this.loadedItems,\n      getItemOptions: index => this.getOptions(index),\n      loading: this.d_loading,\n      getLoaderOptions: (index, options) => this.getLoaderOptions(index, options),\n      itemSize: this._itemSize,\n      rows: this.loadedRows,\n      columns: this.loadedColumns,\n      spacerStyle: this.spacerStyle,\n      contentStyle: this.contentStyle,\n      vertical: this.vertical,\n      horizontal: this.horizontal,\n      both: this.both\n    };\n  }\n  getOptions(renderedIndex) {\n    const count = (this._items || []).length;\n    const index = this.both ? this.first.rows + renderedIndex : this.first + renderedIndex;\n    return {\n      index,\n      count,\n      first: index === 0,\n      last: index === count - 1,\n      even: index % 2 === 0,\n      odd: index % 2 !== 0\n    };\n  }\n  getLoaderOptions(index, extOptions) {\n    const count = this.loaderArr.length;\n    return {\n      index,\n      count,\n      first: index === 0,\n      last: index === count - 1,\n      even: index % 2 === 0,\n      odd: index % 2 !== 0,\n      ...extOptions\n    };\n  }\n  static ɵfac = function Scroller_Factory(t) {\n    return new (t || Scroller)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Scroller,\n    selectors: [[\"p-scroller\"]],\n    contentQueries: function Scroller_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    viewQuery: function Scroller_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.elementViewChild = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentViewChild = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-scroller-viewport\", \"p-element\"],\n    inputs: {\n      id: \"id\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      tabindex: \"tabindex\",\n      items: \"items\",\n      itemSize: \"itemSize\",\n      scrollHeight: \"scrollHeight\",\n      scrollWidth: \"scrollWidth\",\n      orientation: \"orientation\",\n      step: \"step\",\n      delay: \"delay\",\n      resizeDelay: \"resizeDelay\",\n      appendOnly: \"appendOnly\",\n      inline: \"inline\",\n      lazy: \"lazy\",\n      disabled: \"disabled\",\n      loaderDisabled: \"loaderDisabled\",\n      columns: \"columns\",\n      showSpacer: \"showSpacer\",\n      showLoader: \"showLoader\",\n      numToleratedItems: \"numToleratedItems\",\n      loading: \"loading\",\n      autoSize: \"autoSize\",\n      trackBy: \"trackBy\",\n      options: \"options\"\n    },\n    outputs: {\n      onLazyLoad: \"onLazyLoad\",\n      onScroll: \"onScroll\",\n      onScrollIndexChange: \"onScrollIndexChange\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    ngContentSelectors: _c2,\n    decls: 3,\n    vars: 2,\n    consts: [[\"disabledContainer\", \"\"], [\"element\", \"\"], [\"buildInContent\", \"\"], [\"content\", \"\"], [\"buildInLoader\", \"\"], [\"buildInLoaderIcon\", \"\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"scroll\", \"ngStyle\", \"ngClass\"], [\"class\", \"p-scroller-spacer\", 3, \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-scroller-loader\", 3, \"ngClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"p-scroller-content\", 3, \"ngClass\", \"ngStyle\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"p-scroller-spacer\", 3, \"ngStyle\"], [1, \"p-scroller-loader\", 3, \"ngClass\"], [4, \"ngFor\", \"ngForOf\"], [3, \"styleClass\"], [4, \"ngIf\"]],\n    template: function Scroller_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, Scroller_ng_container_0_Template, 8, 16, \"ng-container\", 6)(1, Scroller_ng_template_1_Template, 2, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        const disabledContainer_r9 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx._disabled)(\"ngIfElse\", disabledContainer_r9);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, SpinnerIcon],\n    styles: [\"@layer primeng{p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{scale:2}.p-scroller-inline .p-scroller-content{position:static}}\\n\"],\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Scroller, [{\n    type: Component,\n    args: [{\n      selector: 'p-scroller',\n      template: `\n        <ng-container *ngIf=\"!_disabled; else disabledContainer\">\n            <div\n                #element\n                [attr.id]=\"_id\"\n                [attr.tabindex]=\"tabindex\"\n                [ngStyle]=\"_style\"\n                [class]=\"_styleClass\"\n                [ngClass]=\"{ 'p-scroller': true, 'p-scroller-inline': inline, 'p-both-scroll': both, 'p-horizontal-scroll': horizontal }\"\n                (scroll)=\"onContainerScroll($event)\"\n                [attr.data-pc-name]=\"'scroller'\"\n                [attr.data-pc-section]=\"'root'\"\n            >\n                <ng-container *ngIf=\"contentTemplate; else buildInContent\">\n                    <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: loadedItems, options: getContentOptions() }\"></ng-container>\n                </ng-container>\n                <ng-template #buildInContent>\n                    <div #content class=\"p-scroller-content\" [ngClass]=\"{ 'p-scroller-loading': d_loading }\" [ngStyle]=\"contentStyle\" [attr.data-pc-section]=\"'content'\">\n                        <ng-container *ngFor=\"let item of loadedItems; let index = index; trackBy: _trackBy || index\">\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, options: getOptions(index) }\"></ng-container>\n                        </ng-container>\n                    </div>\n                </ng-template>\n                <div *ngIf=\"_showSpacer\" class=\"p-scroller-spacer\" [ngStyle]=\"spacerStyle\" [attr.data-pc-section]=\"'spacer'\"></div>\n                <div *ngIf=\"!loaderDisabled && _showLoader && d_loading\" class=\"p-scroller-loader\" [ngClass]=\"{ 'p-component-overlay': !loaderTemplate }\" [attr.data-pc-section]=\"'loader'\">\n                    <ng-container *ngIf=\"loaderTemplate; else buildInLoader\">\n                        <ng-container *ngFor=\"let item of loaderArr; let index = index\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: { options: getLoaderOptions(index, both && { numCols: _numItemsInViewport.cols }) }\"></ng-container>\n                        </ng-container>\n                    </ng-container>\n                    <ng-template #buildInLoader>\n                        <ng-container *ngIf=\"loaderIconTemplate; else buildInLoaderIcon\">\n                            <ng-container *ngTemplateOutlet=\"loaderIconTemplate; context: { options: { styleClass: 'p-scroller-loading-icon' } }\"></ng-container>\n                        </ng-container>\n                        <ng-template #buildInLoaderIcon>\n                            <SpinnerIcon [styleClass]=\"'p-scroller-loading-icon pi-spin'\" [attr.data-pc-section]=\"'loadingIcon'\" />\n                        </ng-template>\n                    </ng-template>\n                </div>\n            </div>\n        </ng-container>\n        <ng-template #disabledContainer>\n            <ng-content></ng-content>\n            <ng-container *ngIf=\"contentTemplate\">\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: items, options: { rows: _items, columns: loadedColumns } }\"></ng-container>\n            </ng-container>\n        </ng-template>\n    `,\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-scroller-viewport p-element'\n      },\n      styles: [\"@layer primeng{p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{scale:2}.p-scroller-inline .p-scroller-content{position:static}}\\n\"]\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.NgZone\n  }], {\n    id: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input\n    }],\n    items: [{\n      type: Input\n    }],\n    itemSize: [{\n      type: Input\n    }],\n    scrollHeight: [{\n      type: Input\n    }],\n    scrollWidth: [{\n      type: Input\n    }],\n    orientation: [{\n      type: Input\n    }],\n    step: [{\n      type: Input\n    }],\n    delay: [{\n      type: Input\n    }],\n    resizeDelay: [{\n      type: Input\n    }],\n    appendOnly: [{\n      type: Input\n    }],\n    inline: [{\n      type: Input\n    }],\n    lazy: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    loaderDisabled: [{\n      type: Input\n    }],\n    columns: [{\n      type: Input\n    }],\n    showSpacer: [{\n      type: Input\n    }],\n    showLoader: [{\n      type: Input\n    }],\n    numToleratedItems: [{\n      type: Input\n    }],\n    loading: [{\n      type: Input\n    }],\n    autoSize: [{\n      type: Input\n    }],\n    trackBy: [{\n      type: Input\n    }],\n    options: [{\n      type: Input\n    }],\n    onLazyLoad: [{\n      type: Output\n    }],\n    onScroll: [{\n      type: Output\n    }],\n    onScrollIndexChange: [{\n      type: Output\n    }],\n    elementViewChild: [{\n      type: ViewChild,\n      args: ['element']\n    }],\n    contentViewChild: [{\n      type: ViewChild,\n      args: ['content']\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass ScrollerModule {\n  static ɵfac = function ScrollerModule_Factory(t) {\n    return new (t || ScrollerModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ScrollerModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedModule, SpinnerIcon, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ScrollerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, SharedModule, SpinnerIcon],\n      exports: [Scroller, SharedModule],\n      declarations: [Scroller]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Scroller, ScrollerModule };", "map": {"version": 3, "names": ["i1", "isPlatformBrowser", "DOCUMENT", "CommonModule", "i0", "EventEmitter", "PLATFORM_ID", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ViewChild", "ContentChildren", "NgModule", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "SpinnerIcon", "_c0", "_c1", "_c2", "_c3", "a0", "a1", "a2", "_c4", "$implicit", "options", "_c5", "_c6", "_c7", "numCols", "_c8", "_c9", "styleClass", "_c10", "rows", "columns", "Scroller_ng_container_0_ng_container_3_ng_container_1_Template", "rf", "ctx", "ɵɵelementContainer", "Scroller_ng_container_0_ng_container_3_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "ctx_r1", "ɵɵnextContext", "ɵɵadvance", "ɵɵproperty", "contentTemplate", "ɵɵpureFunction2", "loadedItems", "getContentOptions", "Scroller_ng_container_0_ng_template_4_ng_container_2_ng_container_1_Template", "Scroller_ng_container_0_ng_template_4_ng_container_2_Template", "item_r3", "index_r4", "index", "itemTemplate", "getOptions", "Scroller_ng_container_0_ng_template_4_Template", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵpureFunction1", "d_loading", "contentStyle", "ɵɵattribute", "_trackBy", "Scroller_ng_container_0_div_6_Template", "ɵɵelement", "spacerStyle", "Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_ng_container_1_Template", "Scroller_ng_container_0_div_7_ng_container_1_ng_container_1_Template", "index_r5", "loaderTemplate", "getLoaderOptions", "both", "_numItemsInViewport", "cols", "Scroller_ng_container_0_div_7_ng_container_1_Template", "loaderArr", "Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_ng_container_1_Template", "Scroller_ng_container_0_div_7_ng_template_2_ng_container_0_Template", "loaderIconTemplate", "ɵɵpureFunction0", "Scroller_ng_container_0_div_7_ng_template_2_ng_template_1_Template", "Scroller_ng_container_0_div_7_ng_template_2_Template", "ɵɵtemplateRefExtractor", "buildInLoaderIcon_r6", "ɵɵreference", "Scroller_ng_container_0_div_7_Template", "buildInLoader_r7", "Scroller_ng_container_0_Template", "_r1", "ɵɵgetCurrentView", "ɵɵlistener", "Scroller_ng_container_0_Template_div_scroll_1_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "onContainerScroll", "buildInContent_r8", "ɵɵclassMap", "_styleClass", "_style", "ɵɵpureFunction3", "inline", "horizontal", "_id", "tabindex", "_showSpacer", "loaderDisabled", "_showLoader", "Scroller_ng_template_1_ng_container_1_ng_container_1_Template", "Scroller_ng_template_1_ng_container_1_Template", "items", "_items", "loadedColumns", "Scroller_ng_template_1_Template", "ɵɵprojection", "<PERSON><PERSON><PERSON>", "document", "platformId", "renderer", "cd", "zone", "id", "val", "style", "_tabindex", "itemSize", "_itemSize", "scrollHeight", "_scrollHeight", "scrollWidth", "_scrollWidth", "orientation", "_orientation", "step", "_step", "delay", "_delay", "resizeDelay", "_resizeDelay", "appendOnly", "_appendOnly", "_inline", "lazy", "_lazy", "disabled", "_disabled", "_loaderDisabled", "_columns", "showSpacer", "<PERSON><PERSON><PERSON><PERSON>", "numToleratedItems", "_numToleratedItems", "loading", "_loading", "autoSize", "_autoSize", "trackBy", "_options", "Object", "entries", "for<PERSON>ach", "k", "v", "onLazyLoad", "onScroll", "onScrollIndexChange", "elementViewChild", "contentViewChild", "templates", "d_numToleratedItems", "contentEl", "first", "last", "page", "isRangeChanged", "numItemsInViewport", "lastScrollPos", "lazyLoadState", "scrollTimeout", "resizeTimeout", "initialized", "windowResizeListener", "defaultWidth", "defaultHeight", "defaultContentWidth", "defaultContentHeight", "vertical", "slice", "map", "item", "loadedRows", "isPageChanged", "getPageByFirst", "constructor", "ngOnInit", "setInitialState", "ngOnChanges", "simpleChanges", "isLoadingChanged", "previousValue", "currentValue", "top", "left", "isChanged", "length", "init", "calculateAutoSize", "ngAfterContentInit", "getType", "template", "ngAfterViewInit", "Promise", "resolve", "then", "viewInit", "ngAfterViewChecked", "ngOnDestroy", "unbindResizeListener", "isVisible", "nativeElement", "setContentEl", "getWidth", "getHeight", "setSize", "calculateOptions", "setSpacerSize", "bindResizeListener", "detectChanges", "el", "findSingle", "getElementRef", "Math", "floor", "scrollTo", "scrollToIndex", "behavior", "calculateNumItems", "contentPos", "getContentPosition", "calculateFirst", "_index", "_numT", "calculateCoord", "_first", "_size", "_cpos", "newFirst", "scrollInView", "to", "viewport", "getRenderedRange", "isToStart", "isToEnd", "pos", "calculateFirstInViewport", "_pos", "firstInViewport", "lastInViewport", "scrollTop", "scrollLeft", "scrollPos", "contentWidth", "offsetWidth", "contentHeight", "offsetHeight", "calculateNumItemsInViewport", "_contentSize", "ceil", "calculateNumToleratedItems", "_numItems", "calculateLast", "_num", "_isCols", "getLast", "Array", "from", "min", "handleEvents", "minHeight", "min<PERSON><PERSON><PERSON>", "position", "contain", "width", "height", "isCols", "getComputedStyle", "parseFloat", "paddingLeft", "max", "right", "paddingRight", "paddingTop", "bottom", "paddingBottom", "x", "y", "parentElement", "setProp", "_name", "_value", "setContentPosition", "calculateTranslateVal", "setTransform", "_x", "_y", "transform", "translateVal", "onScrollPositionChange", "event", "target", "calculateScrollPos", "calculateCurrentIndex", "calculateTriggerIndex", "_currentIndex", "_last", "_isScrollDownOrRight", "_triggerIndex", "lastValue", "newLast", "newScrollPos", "isScrollDown", "isScrollRight", "currentIndex", "triggerIndex", "isScrollDownOrRight", "onScrollChange", "newState", "isLazyStateChanged", "originalEvent", "clearTimeout", "changed", "setTimeout", "undefined", "runOutsideAngular", "window", "defaultView", "isTouchDevice", "listen", "onWindowResize", "bind", "isDiffWidth", "isDiffHeight", "reinit", "run", "name", "params", "emit", "contentStyleClass", "getItemOptions", "renderedIndex", "count", "even", "odd", "extOptions", "ɵfac", "Scroller_Factory", "t", "ɵɵdirectiveInject", "Renderer2", "ChangeDetectorRef", "NgZone", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "Scroller_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "viewQuery", "Scroller_Query", "ɵɵviewQuery", "hostAttrs", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "ngContentSelectors", "decls", "vars", "consts", "Scroller_Template", "ɵɵprojectionDef", "disabledContainer_r9", "dependencies", "Ng<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "styles", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "changeDetection", "<PERSON><PERSON><PERSON>", "None", "host", "class", "Document", "decorators", "ScrollerModule", "ScrollerModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/Code/ASAR/Azure/Public Service/SNJYA-PUBLIC-SERVICE/client/node_modules/primeng/fesm2022/primeng-scroller.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { SpinnerIcon } from 'primeng/icons/spinner';\n\n/**\n * Scroller is a performance-approach to handle huge data efficiently.\n * @group Components\n */\nclass Scroller {\n    document;\n    platformId;\n    renderer;\n    cd;\n    zone;\n    /**\n     * Unique identifier of the element.\n     * @group Props\n     */\n    get id() {\n        return this._id;\n    }\n    set id(val) {\n        this._id = val;\n    }\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    get style() {\n        return this._style;\n    }\n    set style(val) {\n        this._style = val;\n    }\n    /**\n     * Style class of the element.\n     * @group Props\n     */\n    get styleClass() {\n        return this._styleClass;\n    }\n    set styleClass(val) {\n        this._styleClass = val;\n    }\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    get tabindex() {\n        return this._tabindex;\n    }\n    set tabindex(val) {\n        this._tabindex = val;\n    }\n    /**\n     * An array of objects to display.\n     * @group Props\n     */\n    get items() {\n        return this._items;\n    }\n    set items(val) {\n        this._items = val;\n    }\n    /**\n     * The height/width of item according to orientation.\n     * @group Props\n     */\n    get itemSize() {\n        return this._itemSize;\n    }\n    set itemSize(val) {\n        this._itemSize = val;\n    }\n    /**\n     * Height of the scroll viewport.\n     * @group Props\n     */\n    get scrollHeight() {\n        return this._scrollHeight;\n    }\n    set scrollHeight(val) {\n        this._scrollHeight = val;\n    }\n    /**\n     * Width of the scroll viewport.\n     * @group Props\n     */\n    get scrollWidth() {\n        return this._scrollWidth;\n    }\n    set scrollWidth(val) {\n        this._scrollWidth = val;\n    }\n    /**\n     * The orientation of scrollbar.\n     * @group Props\n     */\n    get orientation() {\n        return this._orientation;\n    }\n    set orientation(val) {\n        this._orientation = val;\n    }\n    /**\n     * Used to specify how many items to load in each load method in lazy mode.\n     * @group Props\n     */\n    get step() {\n        return this._step;\n    }\n    set step(val) {\n        this._step = val;\n    }\n    /**\n     * Delay in scroll before new data is loaded.\n     * @group Props\n     */\n    get delay() {\n        return this._delay;\n    }\n    set delay(val) {\n        this._delay = val;\n    }\n    /**\n     * Delay after window's resize finishes.\n     * @group Props\n     */\n    get resizeDelay() {\n        return this._resizeDelay;\n    }\n    set resizeDelay(val) {\n        this._resizeDelay = val;\n    }\n    /**\n     * Used to append each loaded item to top without removing any items from the DOM. Using very large data may cause the browser to crash.\n     * @group Props\n     */\n    get appendOnly() {\n        return this._appendOnly;\n    }\n    set appendOnly(val) {\n        this._appendOnly = val;\n    }\n    /**\n     * Specifies whether the scroller should be displayed inline or not.\n     * @group Props\n     */\n    get inline() {\n        return this._inline;\n    }\n    set inline(val) {\n        this._inline = val;\n    }\n    /**\n     * Defines if data is loaded and interacted with in lazy manner.\n     * @group Props\n     */\n    get lazy() {\n        return this._lazy;\n    }\n    set lazy(val) {\n        this._lazy = val;\n    }\n    /**\n     * If disabled, the scroller feature is eliminated and the content is displayed directly.\n     * @group Props\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(val) {\n        this._disabled = val;\n    }\n    /**\n     * Used to implement a custom loader instead of using the loader feature in the scroller.\n     * @group Props\n     */\n    get loaderDisabled() {\n        return this._loaderDisabled;\n    }\n    set loaderDisabled(val) {\n        this._loaderDisabled = val;\n    }\n    /**\n     * Columns to display.\n     * @group Props\n     */\n    get columns() {\n        return this._columns;\n    }\n    set columns(val) {\n        this._columns = val;\n    }\n    /**\n     * Used to implement a custom spacer instead of using the spacer feature in the scroller.\n     * @group Props\n     */\n    get showSpacer() {\n        return this._showSpacer;\n    }\n    set showSpacer(val) {\n        this._showSpacer = val;\n    }\n    /**\n     * Defines whether to show loader.\n     * @group Props\n     */\n    get showLoader() {\n        return this._showLoader;\n    }\n    set showLoader(val) {\n        this._showLoader = val;\n    }\n    /**\n     * Determines how many additional elements to add to the DOM outside of the view. According to the scrolls made up and down, extra items are added in a certain algorithm in the form of multiples of this number. Default value is half the number of items shown in the view.\n     * @group Props\n     */\n    get numToleratedItems() {\n        return this._numToleratedItems;\n    }\n    set numToleratedItems(val) {\n        this._numToleratedItems = val;\n    }\n    /**\n     * Defines whether the data is loaded.\n     * @group Props\n     */\n    get loading() {\n        return this._loading;\n    }\n    set loading(val) {\n        this._loading = val;\n    }\n    /**\n     * Defines whether to dynamically change the height or width of scrollable container.\n     * @group Props\n     */\n    get autoSize() {\n        return this._autoSize;\n    }\n    set autoSize(val) {\n        this._autoSize = val;\n    }\n    /**\n     * Function to optimize the dom operations by delegating to ngForTrackBy, default algoritm checks for object identity.\n     * @group Props\n     */\n    get trackBy() {\n        return this._trackBy;\n    }\n    set trackBy(val) {\n        this._trackBy = val;\n    }\n    /**\n     * Defines whether to use the scroller feature. The properties of scroller component can be used like an object in it.\n     * @group Props\n     */\n    get options() {\n        return this._options;\n    }\n    set options(val) {\n        this._options = val;\n        if (val && typeof val === 'object') {\n            //@ts-ignore\n            Object.entries(val).forEach(([k, v]) => this[`_${k}`] !== v && (this[`_${k}`] = v));\n        }\n    }\n    /**\n     * Callback to invoke in lazy mode to load new data.\n     * @param {ScrollerLazyLoadEvent} event - Custom lazy load event.\n     * @group Emits\n     */\n    onLazyLoad = new EventEmitter();\n    /**\n     * Callback to invoke when scroll position changes.\n     * @param {ScrollerScrollEvent} event - Custom scroll event.\n     * @group Emits\n     */\n    onScroll = new EventEmitter();\n    /**\n     * Callback to invoke when scroll position and item's range in view changes.\n     * @param {ScrollerScrollEvent} event - Custom scroll index change event.\n     * @group Emits\n     */\n    onScrollIndexChange = new EventEmitter();\n    elementViewChild;\n    contentViewChild;\n    templates;\n    _id;\n    _style;\n    _styleClass;\n    _tabindex = 0;\n    _items;\n    _itemSize = 0;\n    _scrollHeight;\n    _scrollWidth;\n    _orientation = 'vertical';\n    _step = 0;\n    _delay = 0;\n    _resizeDelay = 10;\n    _appendOnly = false;\n    _inline = false;\n    _lazy = false;\n    _disabled = false;\n    _loaderDisabled = false;\n    _columns;\n    _showSpacer = true;\n    _showLoader = false;\n    _numToleratedItems;\n    _loading;\n    _autoSize = false;\n    _trackBy;\n    _options;\n    d_loading = false;\n    d_numToleratedItems;\n    contentEl;\n    contentTemplate;\n    itemTemplate;\n    loaderTemplate;\n    loaderIconTemplate;\n    first = 0;\n    last = 0;\n    page = 0;\n    isRangeChanged = false;\n    numItemsInViewport = 0;\n    lastScrollPos = 0;\n    lazyLoadState = {};\n    loaderArr = [];\n    spacerStyle = {};\n    contentStyle = {};\n    scrollTimeout;\n    resizeTimeout;\n    initialized = false;\n    windowResizeListener;\n    defaultWidth;\n    defaultHeight;\n    defaultContentWidth;\n    defaultContentHeight;\n    get vertical() {\n        return this._orientation === 'vertical';\n    }\n    get horizontal() {\n        return this._orientation === 'horizontal';\n    }\n    get both() {\n        return this._orientation === 'both';\n    }\n    get loadedItems() {\n        if (this._items && !this.d_loading) {\n            if (this.both)\n                return this._items.slice(this._appendOnly ? 0 : this.first.rows, this.last.rows).map((item) => (this._columns ? item : item.slice(this._appendOnly ? 0 : this.first.cols, this.last.cols)));\n            else if (this.horizontal && this._columns)\n                return this._items;\n            else\n                return this._items.slice(this._appendOnly ? 0 : this.first, this.last);\n        }\n        return [];\n    }\n    get loadedRows() {\n        return this.d_loading ? (this._loaderDisabled ? this.loaderArr : []) : this.loadedItems;\n    }\n    get loadedColumns() {\n        if (this._columns && (this.both || this.horizontal)) {\n            return this.d_loading && this._loaderDisabled ? (this.both ? this.loaderArr[0] : this.loaderArr) : this._columns.slice(this.both ? this.first.cols : this.first, this.both ? this.last.cols : this.last);\n        }\n        return this._columns;\n    }\n    get isPageChanged() {\n        return this._step ? this.page !== this.getPageByFirst() : true;\n    }\n    constructor(document, platformId, renderer, cd, zone) {\n        this.document = document;\n        this.platformId = platformId;\n        this.renderer = renderer;\n        this.cd = cd;\n        this.zone = zone;\n    }\n    ngOnInit() {\n        this.setInitialState();\n    }\n    ngOnChanges(simpleChanges) {\n        let isLoadingChanged = false;\n        if (simpleChanges.loading) {\n            const { previousValue, currentValue } = simpleChanges.loading;\n            if (this.lazy && previousValue !== currentValue && currentValue !== this.d_loading) {\n                this.d_loading = currentValue;\n                isLoadingChanged = true;\n            }\n        }\n        if (simpleChanges.orientation) {\n            this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n        }\n        if (simpleChanges.numToleratedItems) {\n            const { previousValue, currentValue } = simpleChanges.numToleratedItems;\n            if (previousValue !== currentValue && currentValue !== this.d_numToleratedItems) {\n                this.d_numToleratedItems = currentValue;\n            }\n        }\n        if (simpleChanges.options) {\n            const { previousValue, currentValue } = simpleChanges.options;\n            if (this.lazy && previousValue?.loading !== currentValue?.loading && currentValue?.loading !== this.d_loading) {\n                this.d_loading = currentValue.loading;\n                isLoadingChanged = true;\n            }\n            if (previousValue?.numToleratedItems !== currentValue?.numToleratedItems && currentValue?.numToleratedItems !== this.d_numToleratedItems) {\n                this.d_numToleratedItems = currentValue.numToleratedItems;\n            }\n        }\n        if (this.initialized) {\n            const isChanged = !isLoadingChanged && (simpleChanges.items?.previousValue?.length !== simpleChanges.items?.currentValue?.length || simpleChanges.itemSize || simpleChanges.scrollHeight || simpleChanges.scrollWidth);\n            if (isChanged) {\n                this.init();\n                this.calculateAutoSize();\n            }\n        }\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                case 'item':\n                    this.itemTemplate = item.template;\n                    break;\n                case 'loader':\n                    this.loaderTemplate = item.template;\n                    break;\n                case 'loadericon':\n                    this.loaderIconTemplate = item.template;\n                    break;\n                default:\n                    this.itemTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    ngAfterViewInit() {\n        Promise.resolve().then(() => {\n            this.viewInit();\n        });\n    }\n    ngAfterViewChecked() {\n        if (!this.initialized) {\n            this.viewInit();\n        }\n    }\n    ngOnDestroy() {\n        this.unbindResizeListener();\n        this.contentEl = null;\n        this.initialized = false;\n    }\n    viewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (DomHandler.isVisible(this.elementViewChild?.nativeElement)) {\n                this.setInitialState();\n                this.setContentEl(this.contentEl);\n                this.init();\n                this.defaultWidth = DomHandler.getWidth(this.elementViewChild?.nativeElement);\n                this.defaultHeight = DomHandler.getHeight(this.elementViewChild?.nativeElement);\n                this.defaultContentWidth = DomHandler.getWidth(this.contentEl);\n                this.defaultContentHeight = DomHandler.getHeight(this.contentEl);\n                this.initialized = true;\n            }\n        }\n    }\n    init() {\n        if (!this._disabled) {\n            this.setSize();\n            this.calculateOptions();\n            this.setSpacerSize();\n            this.bindResizeListener();\n            this.cd.detectChanges();\n        }\n    }\n    setContentEl(el) {\n        this.contentEl = el || this.contentViewChild?.nativeElement || DomHandler.findSingle(this.elementViewChild?.nativeElement, '.p-scroller-content');\n    }\n    setInitialState() {\n        this.first = this.both ? { rows: 0, cols: 0 } : 0;\n        this.last = this.both ? { rows: 0, cols: 0 } : 0;\n        this.numItemsInViewport = this.both ? { rows: 0, cols: 0 } : 0;\n        this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n        this.d_loading = this._loading || false;\n        this.d_numToleratedItems = this._numToleratedItems;\n        this.loaderArr = [];\n        this.spacerStyle = {};\n        this.contentStyle = {};\n    }\n    getElementRef() {\n        return this.elementViewChild;\n    }\n    getPageByFirst() {\n        return Math.floor((this.first + this.d_numToleratedItems * 4) / (this._step || 1));\n    }\n    scrollTo(options) {\n        this.lastScrollPos = this.both ? { top: 0, left: 0 } : 0;\n        this.elementViewChild?.nativeElement?.scrollTo(options);\n    }\n    scrollToIndex(index, behavior = 'auto') {\n        const { numToleratedItems } = this.calculateNumItems();\n        const contentPos = this.getContentPosition();\n        const calculateFirst = (_index = 0, _numT) => (_index <= _numT ? 0 : _index);\n        const calculateCoord = (_first, _size, _cpos) => _first * _size + _cpos;\n        const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n        let newFirst = 0;\n        if (this.both) {\n            newFirst = { rows: calculateFirst(index[0], numToleratedItems[0]), cols: calculateFirst(index[1], numToleratedItems[1]) };\n            scrollTo(calculateCoord(newFirst.cols, this._itemSize[1], contentPos.left), calculateCoord(newFirst.rows, this._itemSize[0], contentPos.top));\n        }\n        else {\n            newFirst = calculateFirst(index, numToleratedItems);\n            this.horizontal ? scrollTo(calculateCoord(newFirst, this._itemSize, contentPos.left), 0) : scrollTo(0, calculateCoord(newFirst, this._itemSize, contentPos.top));\n        }\n        this.isRangeChanged = this.first !== newFirst;\n        this.first = newFirst;\n    }\n    scrollInView(index, to, behavior = 'auto') {\n        if (to) {\n            const { first, viewport } = this.getRenderedRange();\n            const scrollTo = (left = 0, top = 0) => this.scrollTo({ left, top, behavior });\n            const isToStart = to === 'to-start';\n            const isToEnd = to === 'to-end';\n            if (isToStart) {\n                if (this.both) {\n                    if (viewport.first.rows - first.rows > index[0]) {\n                        scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows - 1) * this._itemSize[0]);\n                    }\n                    else if (viewport.first.cols - first.cols > index[1]) {\n                        scrollTo((viewport.first.cols - 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n                    }\n                }\n                else {\n                    if (viewport.first - first > index) {\n                        const pos = (viewport.first - 1) * this._itemSize;\n                        this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                    }\n                }\n            }\n            else if (isToEnd) {\n                if (this.both) {\n                    if (viewport.last.rows - first.rows <= index[0] + 1) {\n                        scrollTo(viewport.first.cols * this._itemSize[1], (viewport.first.rows + 1) * this._itemSize[0]);\n                    }\n                    else if (viewport.last.cols - first.cols <= index[1] + 1) {\n                        scrollTo((viewport.first.cols + 1) * this._itemSize[1], viewport.first.rows * this._itemSize[0]);\n                    }\n                }\n                else {\n                    if (viewport.last - first <= index + 1) {\n                        const pos = (viewport.first + 1) * this._itemSize;\n                        this.horizontal ? scrollTo(pos, 0) : scrollTo(0, pos);\n                    }\n                }\n            }\n        }\n        else {\n            this.scrollToIndex(index, behavior);\n        }\n    }\n    getRenderedRange() {\n        const calculateFirstInViewport = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n        let firstInViewport = this.first;\n        let lastInViewport = 0;\n        if (this.elementViewChild?.nativeElement) {\n            const { scrollTop, scrollLeft } = this.elementViewChild.nativeElement;\n            if (this.both) {\n                firstInViewport = { rows: calculateFirstInViewport(scrollTop, this._itemSize[0]), cols: calculateFirstInViewport(scrollLeft, this._itemSize[1]) };\n                lastInViewport = { rows: firstInViewport.rows + this.numItemsInViewport.rows, cols: firstInViewport.cols + this.numItemsInViewport.cols };\n            }\n            else {\n                const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n                firstInViewport = calculateFirstInViewport(scrollPos, this._itemSize);\n                lastInViewport = firstInViewport + this.numItemsInViewport;\n            }\n        }\n        return {\n            first: this.first,\n            last: this.last,\n            viewport: {\n                first: firstInViewport,\n                last: lastInViewport\n            }\n        };\n    }\n    calculateNumItems() {\n        const contentPos = this.getContentPosition();\n        const contentWidth = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetWidth - contentPos.left : 0) || 0;\n        const contentHeight = (this.elementViewChild?.nativeElement ? this.elementViewChild.nativeElement.offsetHeight - contentPos.top : 0) || 0;\n        const calculateNumItemsInViewport = (_contentSize, _itemSize) => Math.ceil(_contentSize / (_itemSize || _contentSize));\n        const calculateNumToleratedItems = (_numItems) => Math.ceil(_numItems / 2);\n        const numItemsInViewport = this.both\n            ? { rows: calculateNumItemsInViewport(contentHeight, this._itemSize[0]), cols: calculateNumItemsInViewport(contentWidth, this._itemSize[1]) }\n            : calculateNumItemsInViewport(this.horizontal ? contentWidth : contentHeight, this._itemSize);\n        const numToleratedItems = this.d_numToleratedItems || (this.both ? [calculateNumToleratedItems(numItemsInViewport.rows), calculateNumToleratedItems(numItemsInViewport.cols)] : calculateNumToleratedItems(numItemsInViewport));\n        return { numItemsInViewport, numToleratedItems };\n    }\n    calculateOptions() {\n        const { numItemsInViewport, numToleratedItems } = this.calculateNumItems();\n        const calculateLast = (_first, _num, _numT, _isCols = false) => this.getLast(_first + _num + (_first < _numT ? 2 : 3) * _numT, _isCols);\n        const first = this.first;\n        const last = this.both\n            ? { rows: calculateLast(this.first.rows, numItemsInViewport.rows, numToleratedItems[0]), cols: calculateLast(this.first.cols, numItemsInViewport.cols, numToleratedItems[1], true) }\n            : calculateLast(this.first, numItemsInViewport, numToleratedItems);\n        this.last = last;\n        this.numItemsInViewport = numItemsInViewport;\n        this.d_numToleratedItems = numToleratedItems;\n        if (this.showLoader) {\n            this.loaderArr = this.both ? Array.from({ length: numItemsInViewport.rows }).map(() => Array.from({ length: numItemsInViewport.cols })) : Array.from({ length: numItemsInViewport });\n        }\n        if (this._lazy) {\n            Promise.resolve().then(() => {\n                this.lazyLoadState = {\n                    first: this._step ? (this.both ? { rows: 0, cols: first.cols } : 0) : first,\n                    last: Math.min(this._step ? this._step : this.last, this.items.length)\n                };\n                this.handleEvents('onLazyLoad', this.lazyLoadState);\n            });\n        }\n    }\n    calculateAutoSize() {\n        if (this._autoSize && !this.d_loading) {\n            Promise.resolve().then(() => {\n                if (this.contentEl) {\n                    this.contentEl.style.minHeight = this.contentEl.style.minWidth = 'auto';\n                    this.contentEl.style.position = 'relative';\n                    this.elementViewChild.nativeElement.style.contain = 'none';\n                    const [contentWidth, contentHeight] = [DomHandler.getWidth(this.contentEl), DomHandler.getHeight(this.contentEl)];\n                    contentWidth !== this.defaultContentWidth && (this.elementViewChild.nativeElement.style.width = '');\n                    contentHeight !== this.defaultContentHeight && (this.elementViewChild.nativeElement.style.height = '');\n                    const [width, height] = [DomHandler.getWidth(this.elementViewChild.nativeElement), DomHandler.getHeight(this.elementViewChild.nativeElement)];\n                    (this.both || this.horizontal) && (this.elementViewChild.nativeElement.style.width = width < this.defaultWidth ? width + 'px' : this._scrollWidth || this.defaultWidth + 'px');\n                    (this.both || this.vertical) && (this.elementViewChild.nativeElement.style.height = height < this.defaultHeight ? height + 'px' : this._scrollHeight || this.defaultHeight + 'px');\n                    this.contentEl.style.minHeight = this.contentEl.style.minWidth = '';\n                    this.contentEl.style.position = '';\n                    this.elementViewChild.nativeElement.style.contain = '';\n                }\n            });\n        }\n    }\n    getLast(last = 0, isCols = false) {\n        return this._items ? Math.min(isCols ? (this._columns || this._items[0]).length : this._items.length, last) : 0;\n    }\n    getContentPosition() {\n        if (this.contentEl) {\n            const style = getComputedStyle(this.contentEl);\n            const left = parseFloat(style.paddingLeft) + Math.max(parseFloat(style.left) || 0, 0);\n            const right = parseFloat(style.paddingRight) + Math.max(parseFloat(style.right) || 0, 0);\n            const top = parseFloat(style.paddingTop) + Math.max(parseFloat(style.top) || 0, 0);\n            const bottom = parseFloat(style.paddingBottom) + Math.max(parseFloat(style.bottom) || 0, 0);\n            return { left, right, top, bottom, x: left + right, y: top + bottom };\n        }\n        return { left: 0, right: 0, top: 0, bottom: 0, x: 0, y: 0 };\n    }\n    setSize() {\n        if (this.elementViewChild?.nativeElement) {\n            const parentElement = this.elementViewChild.nativeElement.parentElement.parentElement;\n            const width = this._scrollWidth || `${this.elementViewChild.nativeElement.offsetWidth || parentElement.offsetWidth}px`;\n            const height = this._scrollHeight || `${this.elementViewChild.nativeElement.offsetHeight || parentElement.offsetHeight}px`;\n            const setProp = (_name, _value) => (this.elementViewChild.nativeElement.style[_name] = _value);\n            if (this.both || this.horizontal) {\n                setProp('height', height);\n                setProp('width', width);\n            }\n            else {\n                setProp('height', height);\n            }\n        }\n    }\n    setSpacerSize() {\n        if (this._items) {\n            const contentPos = this.getContentPosition();\n            const setProp = (_name, _value, _size, _cpos = 0) => (this.spacerStyle = { ...this.spacerStyle, ...{ [`${_name}`]: (_value || []).length * _size + _cpos + 'px' } });\n            if (this.both) {\n                setProp('height', this._items, this._itemSize[0], contentPos.y);\n                setProp('width', this._columns || this._items[1], this._itemSize[1], contentPos.x);\n            }\n            else {\n                this.horizontal ? setProp('width', this._columns || this._items, this._itemSize, contentPos.x) : setProp('height', this._items, this._itemSize, contentPos.y);\n            }\n        }\n    }\n    setContentPosition(pos) {\n        if (this.contentEl && !this._appendOnly) {\n            const first = pos ? pos.first : this.first;\n            const calculateTranslateVal = (_first, _size) => _first * _size;\n            const setTransform = (_x = 0, _y = 0) => (this.contentStyle = { ...this.contentStyle, ...{ transform: `translate3d(${_x}px, ${_y}px, 0)` } });\n            if (this.both) {\n                setTransform(calculateTranslateVal(first.cols, this._itemSize[1]), calculateTranslateVal(first.rows, this._itemSize[0]));\n            }\n            else {\n                const translateVal = calculateTranslateVal(first, this._itemSize);\n                this.horizontal ? setTransform(translateVal, 0) : setTransform(0, translateVal);\n            }\n        }\n    }\n    onScrollPositionChange(event) {\n        const target = event.target;\n        const contentPos = this.getContentPosition();\n        const calculateScrollPos = (_pos, _cpos) => (_pos ? (_pos > _cpos ? _pos - _cpos : _pos) : 0);\n        const calculateCurrentIndex = (_pos, _size) => Math.floor(_pos / (_size || _pos));\n        const calculateTriggerIndex = (_currentIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n            return _currentIndex <= _numT ? _numT : _isScrollDownOrRight ? _last - _num - _numT : _first + _numT - 1;\n        };\n        const calculateFirst = (_currentIndex, _triggerIndex, _first, _last, _num, _numT, _isScrollDownOrRight) => {\n            if (_currentIndex <= _numT)\n                return 0;\n            else\n                return Math.max(0, _isScrollDownOrRight ? (_currentIndex < _triggerIndex ? _first : _currentIndex - _numT) : _currentIndex > _triggerIndex ? _first : _currentIndex - 2 * _numT);\n        };\n        const calculateLast = (_currentIndex, _first, _last, _num, _numT, _isCols = false) => {\n            let lastValue = _first + _num + 2 * _numT;\n            if (_currentIndex >= _numT) {\n                lastValue += _numT + 1;\n            }\n            return this.getLast(lastValue, _isCols);\n        };\n        const scrollTop = calculateScrollPos(target.scrollTop, contentPos.top);\n        const scrollLeft = calculateScrollPos(target.scrollLeft, contentPos.left);\n        let newFirst = this.both ? { rows: 0, cols: 0 } : 0;\n        let newLast = this.last;\n        let isRangeChanged = false;\n        let newScrollPos = this.lastScrollPos;\n        if (this.both) {\n            const isScrollDown = this.lastScrollPos.top <= scrollTop;\n            const isScrollRight = this.lastScrollPos.left <= scrollLeft;\n            if (!this._appendOnly || (this._appendOnly && (isScrollDown || isScrollRight))) {\n                const currentIndex = { rows: calculateCurrentIndex(scrollTop, this._itemSize[0]), cols: calculateCurrentIndex(scrollLeft, this._itemSize[1]) };\n                const triggerIndex = {\n                    rows: calculateTriggerIndex(currentIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                    cols: calculateTriggerIndex(currentIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n                };\n                newFirst = {\n                    rows: calculateFirst(currentIndex.rows, triggerIndex.rows, this.first.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0], isScrollDown),\n                    cols: calculateFirst(currentIndex.cols, triggerIndex.cols, this.first.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], isScrollRight)\n                };\n                newLast = {\n                    rows: calculateLast(currentIndex.rows, newFirst.rows, this.last.rows, this.numItemsInViewport.rows, this.d_numToleratedItems[0]),\n                    cols: calculateLast(currentIndex.cols, newFirst.cols, this.last.cols, this.numItemsInViewport.cols, this.d_numToleratedItems[1], true)\n                };\n                isRangeChanged = newFirst.rows !== this.first.rows || newLast.rows !== this.last.rows || newFirst.cols !== this.first.cols || newLast.cols !== this.last.cols || this.isRangeChanged;\n                newScrollPos = { top: scrollTop, left: scrollLeft };\n            }\n        }\n        else {\n            const scrollPos = this.horizontal ? scrollLeft : scrollTop;\n            const isScrollDownOrRight = this.lastScrollPos <= scrollPos;\n            if (!this._appendOnly || (this._appendOnly && isScrollDownOrRight)) {\n                const currentIndex = calculateCurrentIndex(scrollPos, this._itemSize);\n                const triggerIndex = calculateTriggerIndex(currentIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n                newFirst = calculateFirst(currentIndex, triggerIndex, this.first, this.last, this.numItemsInViewport, this.d_numToleratedItems, isScrollDownOrRight);\n                newLast = calculateLast(currentIndex, newFirst, this.last, this.numItemsInViewport, this.d_numToleratedItems);\n                isRangeChanged = newFirst !== this.first || newLast !== this.last || this.isRangeChanged;\n                newScrollPos = scrollPos;\n            }\n        }\n        return {\n            first: newFirst,\n            last: newLast,\n            isRangeChanged,\n            scrollPos: newScrollPos\n        };\n    }\n    onScrollChange(event) {\n        const { first, last, isRangeChanged, scrollPos } = this.onScrollPositionChange(event);\n        if (isRangeChanged) {\n            const newState = { first, last };\n            this.setContentPosition(newState);\n            this.first = first;\n            this.last = last;\n            this.lastScrollPos = scrollPos;\n            this.handleEvents('onScrollIndexChange', newState);\n            if (this._lazy && this.isPageChanged) {\n                const lazyLoadState = {\n                    first: this._step ? Math.min(this.getPageByFirst() * this._step, this.items.length - this._step) : first,\n                    last: Math.min(this._step ? (this.getPageByFirst() + 1) * this._step : last, this.items.length)\n                };\n                const isLazyStateChanged = this.lazyLoadState.first !== lazyLoadState.first || this.lazyLoadState.last !== lazyLoadState.last;\n                isLazyStateChanged && this.handleEvents('onLazyLoad', lazyLoadState);\n                this.lazyLoadState = lazyLoadState;\n            }\n        }\n    }\n    onContainerScroll(event) {\n        this.handleEvents('onScroll', { originalEvent: event });\n        if (this._delay && this.isPageChanged) {\n            if (this.scrollTimeout) {\n                clearTimeout(this.scrollTimeout);\n            }\n            if (!this.d_loading && this.showLoader) {\n                const { isRangeChanged } = this.onScrollPositionChange(event);\n                const changed = isRangeChanged || (this._step ? this.isPageChanged : false);\n                if (changed) {\n                    this.d_loading = true;\n                    this.cd.detectChanges();\n                }\n            }\n            this.scrollTimeout = setTimeout(() => {\n                this.onScrollChange(event);\n                if (this.d_loading && this.showLoader && (!this._lazy || this._loading === undefined)) {\n                    this.d_loading = false;\n                    this.page = this.getPageByFirst();\n                    this.cd.detectChanges();\n                }\n            }, this._delay);\n        }\n        else {\n            !this.d_loading && this.onScrollChange(event);\n        }\n    }\n    bindResizeListener() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (!this.windowResizeListener) {\n                this.zone.runOutsideAngular(() => {\n                    const window = this.document.defaultView;\n                    const event = DomHandler.isTouchDevice() ? 'orientationchange' : 'resize';\n                    this.windowResizeListener = this.renderer.listen(window, event, this.onWindowResize.bind(this));\n                });\n            }\n        }\n    }\n    unbindResizeListener() {\n        if (this.windowResizeListener) {\n            this.windowResizeListener();\n            this.windowResizeListener = null;\n        }\n    }\n    onWindowResize() {\n        if (this.resizeTimeout) {\n            clearTimeout(this.resizeTimeout);\n        }\n        this.resizeTimeout = setTimeout(() => {\n            if (DomHandler.isVisible(this.elementViewChild?.nativeElement)) {\n                const [width, height] = [DomHandler.getWidth(this.elementViewChild?.nativeElement), DomHandler.getHeight(this.elementViewChild?.nativeElement)];\n                const [isDiffWidth, isDiffHeight] = [width !== this.defaultWidth, height !== this.defaultHeight];\n                const reinit = this.both ? isDiffWidth || isDiffHeight : this.horizontal ? isDiffWidth : this.vertical ? isDiffHeight : false;\n                reinit &&\n                    this.zone.run(() => {\n                        this.d_numToleratedItems = this._numToleratedItems;\n                        this.defaultWidth = width;\n                        this.defaultHeight = height;\n                        this.defaultContentWidth = DomHandler.getWidth(this.contentEl);\n                        this.defaultContentHeight = DomHandler.getHeight(this.contentEl);\n                        this.init();\n                    });\n            }\n        }, this._resizeDelay);\n    }\n    handleEvents(name, params) {\n        //@ts-ignore\n        return this.options && this.options[name] ? this.options[name](params) : this[name].emit(params);\n    }\n    getContentOptions() {\n        return {\n            contentStyleClass: `p-scroller-content ${this.d_loading ? 'p-scroller-loading' : ''}`,\n            items: this.loadedItems,\n            getItemOptions: (index) => this.getOptions(index),\n            loading: this.d_loading,\n            getLoaderOptions: (index, options) => this.getLoaderOptions(index, options),\n            itemSize: this._itemSize,\n            rows: this.loadedRows,\n            columns: this.loadedColumns,\n            spacerStyle: this.spacerStyle,\n            contentStyle: this.contentStyle,\n            vertical: this.vertical,\n            horizontal: this.horizontal,\n            both: this.both\n        };\n    }\n    getOptions(renderedIndex) {\n        const count = (this._items || []).length;\n        const index = this.both ? this.first.rows + renderedIndex : this.first + renderedIndex;\n        return {\n            index,\n            count,\n            first: index === 0,\n            last: index === count - 1,\n            even: index % 2 === 0,\n            odd: index % 2 !== 0\n        };\n    }\n    getLoaderOptions(index, extOptions) {\n        const count = this.loaderArr.length;\n        return {\n            index,\n            count,\n            first: index === 0,\n            last: index === count - 1,\n            even: index % 2 === 0,\n            odd: index % 2 !== 0,\n            ...extOptions\n        };\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Scroller, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.Renderer2 }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.5\", type: Scroller, selector: \"p-scroller\", inputs: { id: \"id\", style: \"style\", styleClass: \"styleClass\", tabindex: \"tabindex\", items: \"items\", itemSize: \"itemSize\", scrollHeight: \"scrollHeight\", scrollWidth: \"scrollWidth\", orientation: \"orientation\", step: \"step\", delay: \"delay\", resizeDelay: \"resizeDelay\", appendOnly: \"appendOnly\", inline: \"inline\", lazy: \"lazy\", disabled: \"disabled\", loaderDisabled: \"loaderDisabled\", columns: \"columns\", showSpacer: \"showSpacer\", showLoader: \"showLoader\", numToleratedItems: \"numToleratedItems\", loading: \"loading\", autoSize: \"autoSize\", trackBy: \"trackBy\", options: \"options\" }, outputs: { onLazyLoad: \"onLazyLoad\", onScroll: \"onScroll\", onScrollIndexChange: \"onScrollIndexChange\" }, host: { classAttribute: \"p-scroller-viewport p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], viewQueries: [{ propertyName: \"elementViewChild\", first: true, predicate: [\"element\"], descendants: true }, { propertyName: \"contentViewChild\", first: true, predicate: [\"content\"], descendants: true }], usesOnChanges: true, ngImport: i0, template: `\n        <ng-container *ngIf=\"!_disabled; else disabledContainer\">\n            <div\n                #element\n                [attr.id]=\"_id\"\n                [attr.tabindex]=\"tabindex\"\n                [ngStyle]=\"_style\"\n                [class]=\"_styleClass\"\n                [ngClass]=\"{ 'p-scroller': true, 'p-scroller-inline': inline, 'p-both-scroll': both, 'p-horizontal-scroll': horizontal }\"\n                (scroll)=\"onContainerScroll($event)\"\n                [attr.data-pc-name]=\"'scroller'\"\n                [attr.data-pc-section]=\"'root'\"\n            >\n                <ng-container *ngIf=\"contentTemplate; else buildInContent\">\n                    <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: loadedItems, options: getContentOptions() }\"></ng-container>\n                </ng-container>\n                <ng-template #buildInContent>\n                    <div #content class=\"p-scroller-content\" [ngClass]=\"{ 'p-scroller-loading': d_loading }\" [ngStyle]=\"contentStyle\" [attr.data-pc-section]=\"'content'\">\n                        <ng-container *ngFor=\"let item of loadedItems; let index = index; trackBy: _trackBy || index\">\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, options: getOptions(index) }\"></ng-container>\n                        </ng-container>\n                    </div>\n                </ng-template>\n                <div *ngIf=\"_showSpacer\" class=\"p-scroller-spacer\" [ngStyle]=\"spacerStyle\" [attr.data-pc-section]=\"'spacer'\"></div>\n                <div *ngIf=\"!loaderDisabled && _showLoader && d_loading\" class=\"p-scroller-loader\" [ngClass]=\"{ 'p-component-overlay': !loaderTemplate }\" [attr.data-pc-section]=\"'loader'\">\n                    <ng-container *ngIf=\"loaderTemplate; else buildInLoader\">\n                        <ng-container *ngFor=\"let item of loaderArr; let index = index\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: { options: getLoaderOptions(index, both && { numCols: _numItemsInViewport.cols }) }\"></ng-container>\n                        </ng-container>\n                    </ng-container>\n                    <ng-template #buildInLoader>\n                        <ng-container *ngIf=\"loaderIconTemplate; else buildInLoaderIcon\">\n                            <ng-container *ngTemplateOutlet=\"loaderIconTemplate; context: { options: { styleClass: 'p-scroller-loading-icon' } }\"></ng-container>\n                        </ng-container>\n                        <ng-template #buildInLoaderIcon>\n                            <SpinnerIcon [styleClass]=\"'p-scroller-loading-icon pi-spin'\" [attr.data-pc-section]=\"'loadingIcon'\" />\n                        </ng-template>\n                    </ng-template>\n                </div>\n            </div>\n        </ng-container>\n        <ng-template #disabledContainer>\n            <ng-content></ng-content>\n            <ng-container *ngIf=\"contentTemplate\">\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: items, options: { rows: _items, columns: loadedColumns } }\"></ng-container>\n            </ng-container>\n        </ng-template>\n    `, isInline: true, styles: [\"@layer primeng{p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{scale:2}.p-scroller-inline .p-scroller-content{position:static}}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i1.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgForOf), selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: i0.forwardRef(() => SpinnerIcon), selector: \"SpinnerIcon\" }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: Scroller, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-scroller', template: `\n        <ng-container *ngIf=\"!_disabled; else disabledContainer\">\n            <div\n                #element\n                [attr.id]=\"_id\"\n                [attr.tabindex]=\"tabindex\"\n                [ngStyle]=\"_style\"\n                [class]=\"_styleClass\"\n                [ngClass]=\"{ 'p-scroller': true, 'p-scroller-inline': inline, 'p-both-scroll': both, 'p-horizontal-scroll': horizontal }\"\n                (scroll)=\"onContainerScroll($event)\"\n                [attr.data-pc-name]=\"'scroller'\"\n                [attr.data-pc-section]=\"'root'\"\n            >\n                <ng-container *ngIf=\"contentTemplate; else buildInContent\">\n                    <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: loadedItems, options: getContentOptions() }\"></ng-container>\n                </ng-container>\n                <ng-template #buildInContent>\n                    <div #content class=\"p-scroller-content\" [ngClass]=\"{ 'p-scroller-loading': d_loading }\" [ngStyle]=\"contentStyle\" [attr.data-pc-section]=\"'content'\">\n                        <ng-container *ngFor=\"let item of loadedItems; let index = index; trackBy: _trackBy || index\">\n                            <ng-container *ngTemplateOutlet=\"itemTemplate; context: { $implicit: item, options: getOptions(index) }\"></ng-container>\n                        </ng-container>\n                    </div>\n                </ng-template>\n                <div *ngIf=\"_showSpacer\" class=\"p-scroller-spacer\" [ngStyle]=\"spacerStyle\" [attr.data-pc-section]=\"'spacer'\"></div>\n                <div *ngIf=\"!loaderDisabled && _showLoader && d_loading\" class=\"p-scroller-loader\" [ngClass]=\"{ 'p-component-overlay': !loaderTemplate }\" [attr.data-pc-section]=\"'loader'\">\n                    <ng-container *ngIf=\"loaderTemplate; else buildInLoader\">\n                        <ng-container *ngFor=\"let item of loaderArr; let index = index\">\n                            <ng-container *ngTemplateOutlet=\"loaderTemplate; context: { options: getLoaderOptions(index, both && { numCols: _numItemsInViewport.cols }) }\"></ng-container>\n                        </ng-container>\n                    </ng-container>\n                    <ng-template #buildInLoader>\n                        <ng-container *ngIf=\"loaderIconTemplate; else buildInLoaderIcon\">\n                            <ng-container *ngTemplateOutlet=\"loaderIconTemplate; context: { options: { styleClass: 'p-scroller-loading-icon' } }\"></ng-container>\n                        </ng-container>\n                        <ng-template #buildInLoaderIcon>\n                            <SpinnerIcon [styleClass]=\"'p-scroller-loading-icon pi-spin'\" [attr.data-pc-section]=\"'loadingIcon'\" />\n                        </ng-template>\n                    </ng-template>\n                </div>\n            </div>\n        </ng-container>\n        <ng-template #disabledContainer>\n            <ng-content></ng-content>\n            <ng-container *ngIf=\"contentTemplate\">\n                <ng-container *ngTemplateOutlet=\"contentTemplate; context: { $implicit: items, options: { rows: _items, columns: loadedColumns } }\"></ng-container>\n            </ng-container>\n        </ng-template>\n    `, changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-scroller-viewport p-element'\n                    }, styles: [\"@layer primeng{p-scroller{flex:1;outline:0 none}.p-scroller{position:relative;overflow:auto;contain:strict;transform:translateZ(0);will-change:scroll-position;outline:0 none}.p-scroller-content{position:absolute;top:0;left:0;min-height:100%;min-width:100%;will-change:transform}.p-scroller-spacer{position:absolute;top:0;left:0;height:1px;width:1px;transform-origin:0 0;pointer-events:none}.p-scroller-loader{position:sticky;top:0;left:0;width:100%;height:100%}.p-scroller-loader.p-component-overlay{display:flex;align-items:center;justify-content:center}.p-scroller-loading-icon{scale:2}.p-scroller-inline .p-scroller-content{position:static}}\\n\"] }]\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.Renderer2 }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }], propDecorators: { id: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], tabindex: [{\n                type: Input\n            }], items: [{\n                type: Input\n            }], itemSize: [{\n                type: Input\n            }], scrollHeight: [{\n                type: Input\n            }], scrollWidth: [{\n                type: Input\n            }], orientation: [{\n                type: Input\n            }], step: [{\n                type: Input\n            }], delay: [{\n                type: Input\n            }], resizeDelay: [{\n                type: Input\n            }], appendOnly: [{\n                type: Input\n            }], inline: [{\n                type: Input\n            }], lazy: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], loaderDisabled: [{\n                type: Input\n            }], columns: [{\n                type: Input\n            }], showSpacer: [{\n                type: Input\n            }], showLoader: [{\n                type: Input\n            }], numToleratedItems: [{\n                type: Input\n            }], loading: [{\n                type: Input\n            }], autoSize: [{\n                type: Input\n            }], trackBy: [{\n                type: Input\n            }], options: [{\n                type: Input\n            }], onLazyLoad: [{\n                type: Output\n            }], onScroll: [{\n                type: Output\n            }], onScrollIndexChange: [{\n                type: Output\n            }], elementViewChild: [{\n                type: ViewChild,\n                args: ['element']\n            }], contentViewChild: [{\n                type: ViewChild,\n                args: ['content']\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass ScrollerModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: ScrollerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: ScrollerModule, declarations: [Scroller], imports: [CommonModule, SharedModule, SpinnerIcon], exports: [Scroller, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: ScrollerModule, imports: [CommonModule, SharedModule, SpinnerIcon, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: ScrollerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, SharedModule, SpinnerIcon],\n                    exports: [Scroller, SharedModule],\n                    declarations: [Scroller]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Scroller, ScrollerModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AAC7K,SAASC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzD,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,WAAW,QAAQ,uBAAuB;;AAEnD;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAAA;EAAA,qBAAAF,EAAA;EAAA,iBAAAC,EAAA;EAAA,uBAAAC;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAH,EAAA,EAAAC,EAAA;EAAAG,SAAA,EAAAJ,EAAA;EAAAK,OAAA,EAAAJ;AAAA;AAAA,MAAAK,GAAA,GAAAN,EAAA;EAAA,sBAAAA;AAAA;AAAA,MAAAO,GAAA,GAAAP,EAAA;EAAA,uBAAAA;AAAA;AAAA,MAAAQ,GAAA,GAAAR,EAAA;EAAAS,OAAA,EAAAT;AAAA;AAAA,MAAAU,GAAA,GAAAV,EAAA;EAAAK,OAAA,EAAAL;AAAA;AAAA,MAAAW,GAAA,GAAAA,CAAA;EAAAC,UAAA;AAAA;AAAA,MAAAC,IAAA,GAAAA,CAAAb,EAAA,EAAAC,EAAA;EAAAa,IAAA,EAAAd,EAAA;EAAAe,OAAA,EAAAd;AAAA;AAAA,SAAAe,+DAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA03B6FrC,EAAE,CAAAuC,kBAAA,EAewD,CAAC;EAAA;AAAA;AAAA,SAAAC,gDAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAf3DrC,EAAE,CAAAyC,uBAAA,EAcrB,CAAC;IAdkBzC,EAAE,CAAA0C,UAAA,IAAAN,8DAAA,0BAeyC,CAAC;IAf5CpC,EAAE,CAAA2C,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,SAAA,CAe1B,CAAC;IAfuB9C,EAAE,CAAA+C,UAAA,qBAAAH,MAAA,CAAAI,eAe1B,CAAC,4BAfuBhD,EAAE,CAAAiD,eAAA,IAAA1B,GAAA,EAAAqB,MAAA,CAAAM,WAAA,EAAAN,MAAA,CAAAO,iBAAA,GAeuC,CAAC;EAAA;AAAA;AAAA,SAAAC,6EAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAf1CrC,EAAE,CAAAuC,kBAAA,EAoBoD,CAAC;EAAA;AAAA;AAAA,SAAAc,8DAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApBvDrC,EAAE,CAAAyC,uBAAA,EAmBsB,CAAC;IAnBzBzC,EAAE,CAAA0C,UAAA,IAAAU,4EAAA,0BAoBqC,CAAC;IApBxCpD,EAAE,CAAA2C,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAiB,OAAA,GAAAhB,GAAA,CAAAd,SAAA;IAAA,MAAA+B,QAAA,GAAAjB,GAAA,CAAAkB,KAAA;IAAA,MAAAZ,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,SAAA,CAoBrB,CAAC;IApBkB9C,EAAE,CAAA+C,UAAA,qBAAAH,MAAA,CAAAa,YAoBrB,CAAC,4BApBkBzD,EAAE,CAAAiD,eAAA,IAAA1B,GAAA,EAAA+B,OAAA,EAAAV,MAAA,CAAAc,UAAA,CAAAH,QAAA,EAoBmC,CAAC;EAAA;AAAA;AAAA,SAAAI,+CAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApBtCrC,EAAE,CAAA4D,cAAA,gBAkByE,CAAC;IAlB5E5D,EAAE,CAAA0C,UAAA,IAAAW,6DAAA,0BAmBsB,CAAC;IAnBzBrD,EAAE,CAAA6D,YAAA,CAsBtE,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAO,MAAA,GAtBmE5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA+C,UAAA,YAAF/C,EAAE,CAAA8D,eAAA,IAAApC,GAAA,EAAAkB,MAAA,CAAAmB,SAAA,CAkBY,CAAC,YAAAnB,MAAA,CAAAoB,YAAwB,CAAC;IAlBxChE,EAAE,CAAAiE,WAAA;IAAFjE,EAAE,CAAA8C,SAAA,EAmBzB,CAAC;IAnBsB9C,EAAE,CAAA+C,UAAA,YAAAH,MAAA,CAAAM,WAmBzB,CAAC,iBAAAN,MAAA,CAAAsB,QAAA,IAAAtB,MAAA,CAAAY,KAA4C,CAAC;EAAA;AAAA;AAAA,SAAAW,uCAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnBvBrC,EAAE,CAAAoE,SAAA,aAwBmC,CAAC;EAAA;EAAA,IAAA/B,EAAA;IAAA,MAAAO,MAAA,GAxBtC5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA+C,UAAA,YAAAH,MAAA,CAAAyB,WAwBN,CAAC;IAxBGrE,EAAE,CAAAiE,WAAA;EAAA;AAAA;AAAA,SAAAK,oFAAAjC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFrC,EAAE,CAAAuC,kBAAA,EA4B0F,CAAC;EAAA;AAAA;AAAA,SAAAgC,qEAAAlC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5B7FrC,EAAE,CAAAyC,uBAAA,EA2BR,CAAC;IA3BKzC,EAAE,CAAA0C,UAAA,IAAA4B,mFAAA,0BA4B2E,CAAC;IA5B9EtE,EAAE,CAAA2C,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAmC,QAAA,GAAAlC,GAAA,CAAAkB,KAAA;IAAA,MAAAZ,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,SAAA,CA4BnB,CAAC;IA5BgB9C,EAAE,CAAA+C,UAAA,qBAAAH,MAAA,CAAA6B,cA4BnB,CAAC,4BA5BgBzE,EAAE,CAAA8D,eAAA,IAAAhC,GAAA,EAAAc,MAAA,CAAA8B,gBAAA,CAAAF,QAAA,EAAA5B,MAAA,CAAA+B,IAAA,IAAF3E,EAAE,CAAA8D,eAAA,IAAAlC,GAAA,EAAAgB,MAAA,CAAAgC,mBAAA,CAAAC,IAAA,GA4ByE,CAAC;EAAA;AAAA;AAAA,SAAAC,sDAAAzC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5B5ErC,EAAE,CAAAyC,uBAAA,EA0BnB,CAAC;IA1BgBzC,EAAE,CAAA0C,UAAA,IAAA6B,oEAAA,0BA2BR,CAAC;IA3BKvE,EAAE,CAAA2C,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,SAAA,CA2B3B,CAAC;IA3BwB9C,EAAE,CAAA+C,UAAA,YAAAH,MAAA,CAAAmC,SA2B3B,CAAC;EAAA;AAAA;AAAA,SAAAC,mFAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3BwBrC,EAAE,CAAAuC,kBAAA,EAiCiE,CAAC;EAAA;AAAA;AAAA,SAAA0C,oEAAA5C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjCpErC,EAAE,CAAAyC,uBAAA,EAgCP,CAAC;IAhCIzC,EAAE,CAAA0C,UAAA,IAAAsC,kFAAA,0BAiCkD,CAAC;IAjCrDhF,EAAE,CAAA2C,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,SAAA,CAiCf,CAAC;IAjCY9C,EAAE,CAAA+C,UAAA,qBAAAH,MAAA,CAAAsC,kBAiCf,CAAC,4BAjCYlF,EAAE,CAAA8D,eAAA,IAAAhC,GAAA,EAAF9B,EAAE,CAAAmF,eAAA,IAAApD,GAAA,EAiCgD,CAAC;EAAA;AAAA;AAAA,SAAAqD,mEAAA/C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjCnDrC,EAAE,CAAAoE,SAAA,qBAoCmC,CAAC;EAAA;EAAA,IAAA/B,EAAA;IApCtCrC,EAAE,CAAA+C,UAAA,gDAoCP,CAAC;IApCI/C,EAAE,CAAAiE,WAAA;EAAA;AAAA;AAAA,SAAAoB,qDAAAhD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFrC,EAAE,CAAA0C,UAAA,IAAAuC,mEAAA,yBAgCP,CAAC,IAAAG,kEAAA,gCAhCIpF,EAAE,CAAAsF,sBAmCxC,CAAC;EAAA;EAAA,IAAAjD,EAAA;IAAA,MAAAkD,oBAAA,GAnCqCvF,EAAE,CAAAwF,WAAA;IAAA,MAAA5C,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA+C,UAAA,SAAAH,MAAA,CAAAsC,kBAgC/B,CAAC,aAAAK,oBAAqB,CAAC;EAAA;AAAA;AAAA,SAAAE,uCAAApD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhCMrC,EAAE,CAAA4D,cAAA,aAyB4F,CAAC;IAzB/F5D,EAAE,CAAA0C,UAAA,IAAAoC,qDAAA,yBA0BnB,CAAC,IAAAO,oDAAA,gCA1BgBrF,EAAE,CAAAsF,sBA+BhD,CAAC;IA/B6CtF,EAAE,CAAA6D,YAAA,CAuC1E,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAqD,gBAAA,GAvCuE1F,EAAE,CAAAwF,WAAA;IAAA,MAAA5C,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA+C,UAAA,YAAF/C,EAAE,CAAA8D,eAAA,IAAAnC,GAAA,GAAAiB,MAAA,CAAA6B,cAAA,CAyByD,CAAC;IAzB5DzE,EAAE,CAAAiE,WAAA;IAAFjE,EAAE,CAAA8C,SAAA,CA0BvC,CAAC;IA1BoC9C,EAAE,CAAA+C,UAAA,SAAAH,MAAA,CAAA6B,cA0BvC,CAAC,aAAAiB,gBAAiB,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAAtD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuD,GAAA,GA1BkB5F,EAAE,CAAA6F,gBAAA;IAAF7F,EAAE,CAAAyC,uBAAA,EAE/B,CAAC;IAF4BzC,EAAE,CAAA4D,cAAA,eAanF,CAAC;IAbgF5D,EAAE,CAAA8F,UAAA,oBAAAC,uDAAAC,MAAA;MAAFhG,EAAE,CAAAiG,aAAA,CAAAL,GAAA;MAAA,MAAAhD,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;MAAA,OAAF7C,EAAE,CAAAkG,WAAA,CAUrEtD,MAAA,CAAAuD,iBAAA,CAAAH,MAAwB,CAAC;IAAA,EAAC;IAVyChG,EAAE,CAAA0C,UAAA,IAAAF,+CAAA,yBAcrB,CAAC,IAAAmB,8CAAA,gCAdkB3D,EAAE,CAAAsF,sBAiBnD,CAAC,IAAAnB,sCAAA,gBAO+E,CAAC,IAAAsB,sCAAA,gBAC8D,CAAC;IAzB/FzF,EAAE,CAAA6D,YAAA,CAwC9E,CAAC;IAxC2E7D,EAAE,CAAA2C,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAA+D,iBAAA,GAAFpG,EAAE,CAAAwF,WAAA;IAAA,MAAA5C,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,SAAA,CAQ3D,CAAC;IARwD9C,EAAE,CAAAqG,UAAA,CAAAzD,MAAA,CAAA0D,WAQ3D,CAAC;IARwDtG,EAAE,CAAA+C,UAAA,YAAAH,MAAA,CAAA2D,MAO9D,CAAC,YAP2DvG,EAAE,CAAAwG,eAAA,KAAArF,GAAA,EAAAyB,MAAA,CAAA6D,MAAA,EAAA7D,MAAA,CAAA+B,IAAA,EAAA/B,MAAA,CAAA8D,UAAA,CASyC,CAAC;IAT5C1G,EAAE,CAAAiE,WAAA,OAAArB,MAAA,CAAA+D,GAAA,cAAA/D,MAAA,CAAAgE,QAAA;IAAF5G,EAAE,CAAA8C,SAAA,EAc1C,CAAC;IAduC9C,EAAE,CAAA+C,UAAA,SAAAH,MAAA,CAAAI,eAc1C,CAAC,aAAAoD,iBAAkB,CAAC;IAdoBpG,EAAE,CAAA8C,SAAA,EAwBzD,CAAC;IAxBsD9C,EAAE,CAAA+C,UAAA,SAAAH,MAAA,CAAAiE,WAwBzD,CAAC;IAxBsD7G,EAAE,CAAA8C,SAAA,CAyBzB,CAAC;IAzBsB9C,EAAE,CAAA+C,UAAA,UAAAH,MAAA,CAAAkE,cAAA,IAAAlE,MAAA,CAAAmE,WAAA,IAAAnE,MAAA,CAAAmB,SAyBzB,CAAC;EAAA;AAAA;AAAA,SAAAiD,8DAAA3E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzBsBrC,EAAE,CAAAuC,kBAAA,EA6CmE,CAAC;EAAA;AAAA;AAAA,SAAA0E,+CAAA5E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7CtErC,EAAE,CAAAyC,uBAAA,EA4C9C,CAAC;IA5C2CzC,EAAE,CAAA0C,UAAA,IAAAsE,6DAAA,0BA6CoD,CAAC;IA7CvDhH,EAAE,CAAA2C,qBAAA;EAAA;EAAA,IAAAN,EAAA;IAAA,MAAAO,MAAA,GAAF5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,SAAA,CA6C9B,CAAC;IA7C2B9C,EAAE,CAAA+C,UAAA,qBAAAH,MAAA,CAAAI,eA6C9B,CAAC,4BA7C2BhD,EAAE,CAAAiD,eAAA,IAAA1B,GAAA,EAAAqB,MAAA,CAAAsE,KAAA,EAAFlH,EAAE,CAAAiD,eAAA,IAAAhB,IAAA,EAAAW,MAAA,CAAAuE,MAAA,EAAAvE,MAAA,CAAAwE,aAAA,EA6CkD,CAAC;EAAA;AAAA;AAAA,SAAAC,gCAAAhF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7CrDrC,EAAE,CAAAsH,YAAA,EA2C3D,CAAC;IA3CwDtH,EAAE,CAAA0C,UAAA,IAAAuE,8CAAA,0BA4C9C,CAAC;EAAA;EAAA,IAAA5E,EAAA;IAAA,MAAAO,MAAA,GA5C2C5C,EAAE,CAAA6C,aAAA;IAAF7C,EAAE,CAAA8C,SAAA,CA4ChD,CAAC;IA5C6C9C,EAAE,CAAA+C,UAAA,SAAAH,MAAA,CAAAI,eA4ChD,CAAC;EAAA;AAAA;AAl6BhD,MAAMuE,QAAQ,CAAC;EACXC,QAAQ;EACRC,UAAU;EACVC,QAAQ;EACRC,EAAE;EACFC,IAAI;EACJ;AACJ;AACA;AACA;EACI,IAAIC,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAAClB,GAAG;EACnB;EACA,IAAIkB,EAAEA,CAACC,GAAG,EAAE;IACR,IAAI,CAACnB,GAAG,GAAGmB,GAAG;EAClB;EACA;AACJ;AACA;AACA;EACI,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACxB,MAAM;EACtB;EACA,IAAIwB,KAAKA,CAACD,GAAG,EAAE;IACX,IAAI,CAACvB,MAAM,GAAGuB,GAAG;EACrB;EACA;AACJ;AACA;AACA;EACI,IAAI9F,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACsE,WAAW;EAC3B;EACA,IAAItE,UAAUA,CAAC8F,GAAG,EAAE;IAChB,IAAI,CAACxB,WAAW,GAAGwB,GAAG;EAC1B;EACA;AACJ;AACA;AACA;EACI,IAAIlB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACoB,SAAS;EACzB;EACA,IAAIpB,QAAQA,CAACkB,GAAG,EAAE;IACd,IAAI,CAACE,SAAS,GAAGF,GAAG;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAIZ,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACY,GAAG,EAAE;IACX,IAAI,CAACX,MAAM,GAAGW,GAAG;EACrB;EACA;AACJ;AACA;AACA;EACI,IAAIG,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACH,GAAG,EAAE;IACd,IAAI,CAACI,SAAS,GAAGJ,GAAG;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAIK,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAYA,CAACL,GAAG,EAAE;IAClB,IAAI,CAACM,aAAa,GAAGN,GAAG;EAC5B;EACA;AACJ;AACA;AACA;EACI,IAAIO,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACP,GAAG,EAAE;IACjB,IAAI,CAACQ,YAAY,GAAGR,GAAG;EAC3B;EACA;AACJ;AACA;AACA;EACI,IAAIS,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACT,GAAG,EAAE;IACjB,IAAI,CAACU,YAAY,GAAGV,GAAG;EAC3B;EACA;AACJ;AACA;AACA;EACI,IAAIW,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAACX,GAAG,EAAE;IACV,IAAI,CAACY,KAAK,GAAGZ,GAAG;EACpB;EACA;AACJ;AACA;AACA;EACI,IAAIa,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAACb,GAAG,EAAE;IACX,IAAI,CAACc,MAAM,GAAGd,GAAG;EACrB;EACA;AACJ;AACA;AACA;EACI,IAAIe,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACf,GAAG,EAAE;IACjB,IAAI,CAACgB,YAAY,GAAGhB,GAAG;EAC3B;EACA;AACJ;AACA;AACA;EACI,IAAIiB,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACjB,GAAG,EAAE;IAChB,IAAI,CAACkB,WAAW,GAAGlB,GAAG;EAC1B;EACA;AACJ;AACA;AACA;EACI,IAAIrB,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACwC,OAAO;EACvB;EACA,IAAIxC,MAAMA,CAACqB,GAAG,EAAE;IACZ,IAAI,CAACmB,OAAO,GAAGnB,GAAG;EACtB;EACA;AACJ;AACA;AACA;EACI,IAAIoB,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAACpB,GAAG,EAAE;IACV,IAAI,CAACqB,KAAK,GAAGrB,GAAG;EACpB;EACA;AACJ;AACA;AACA;EACI,IAAIsB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACtB,GAAG,EAAE;IACd,IAAI,CAACuB,SAAS,GAAGvB,GAAG;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAIhB,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACwC,eAAe;EAC/B;EACA,IAAIxC,cAAcA,CAACgB,GAAG,EAAE;IACpB,IAAI,CAACwB,eAAe,GAAGxB,GAAG;EAC9B;EACA;AACJ;AACA;AACA;EACI,IAAI3F,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACoH,QAAQ;EACxB;EACA,IAAIpH,OAAOA,CAAC2F,GAAG,EAAE;IACb,IAAI,CAACyB,QAAQ,GAAGzB,GAAG;EACvB;EACA;AACJ;AACA;AACA;EACI,IAAI0B,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC3C,WAAW;EAC3B;EACA,IAAI2C,UAAUA,CAAC1B,GAAG,EAAE;IAChB,IAAI,CAACjB,WAAW,GAAGiB,GAAG;EAC1B;EACA;AACJ;AACA;AACA;EACI,IAAI2B,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC1C,WAAW;EAC3B;EACA,IAAI0C,UAAUA,CAAC3B,GAAG,EAAE;IAChB,IAAI,CAACf,WAAW,GAAGe,GAAG;EAC1B;EACA;AACJ;AACA;AACA;EACI,IAAI4B,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,kBAAkB;EAClC;EACA,IAAID,iBAAiBA,CAAC5B,GAAG,EAAE;IACvB,IAAI,CAAC6B,kBAAkB,GAAG7B,GAAG;EACjC;EACA;AACJ;AACA;AACA;EACI,IAAI8B,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAAC9B,GAAG,EAAE;IACb,IAAI,CAAC+B,QAAQ,GAAG/B,GAAG;EACvB;EACA;AACJ;AACA;AACA;EACI,IAAIgC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAAChC,GAAG,EAAE;IACd,IAAI,CAACiC,SAAS,GAAGjC,GAAG;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAIkC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC9F,QAAQ;EACxB;EACA,IAAI8F,OAAOA,CAAClC,GAAG,EAAE;IACb,IAAI,CAAC5D,QAAQ,GAAG4D,GAAG;EACvB;EACA;AACJ;AACA;AACA;EACI,IAAIrG,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACwI,QAAQ;EACxB;EACA,IAAIxI,OAAOA,CAACqG,GAAG,EAAE;IACb,IAAI,CAACmC,QAAQ,GAAGnC,GAAG;IACnB,IAAIA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;MAChC;MACAoC,MAAM,CAACC,OAAO,CAACrC,GAAG,CAAC,CAACsC,OAAO,CAAC,CAAC,CAACC,CAAC,EAAEC,CAAC,CAAC,KAAK,IAAI,CAAE,IAAGD,CAAE,EAAC,CAAC,KAAKC,CAAC,KAAK,IAAI,CAAE,IAAGD,CAAE,EAAC,CAAC,GAAGC,CAAC,CAAC,CAAC;IACvF;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIC,UAAU,GAAG,IAAItK,YAAY,CAAC,CAAC;EAC/B;AACJ;AACA;AACA;AACA;EACIuK,QAAQ,GAAG,IAAIvK,YAAY,CAAC,CAAC;EAC7B;AACJ;AACA;AACA;AACA;EACIwK,mBAAmB,GAAG,IAAIxK,YAAY,CAAC,CAAC;EACxCyK,gBAAgB;EAChBC,gBAAgB;EAChBC,SAAS;EACTjE,GAAG;EACHJ,MAAM;EACND,WAAW;EACX0B,SAAS,GAAG,CAAC;EACbb,MAAM;EACNe,SAAS,GAAG,CAAC;EACbE,aAAa;EACbE,YAAY;EACZE,YAAY,GAAG,UAAU;EACzBE,KAAK,GAAG,CAAC;EACTE,MAAM,GAAG,CAAC;EACVE,YAAY,GAAG,EAAE;EACjBE,WAAW,GAAG,KAAK;EACnBC,OAAO,GAAG,KAAK;EACfE,KAAK,GAAG,KAAK;EACbE,SAAS,GAAG,KAAK;EACjBC,eAAe,GAAG,KAAK;EACvBC,QAAQ;EACR1C,WAAW,GAAG,IAAI;EAClBE,WAAW,GAAG,KAAK;EACnB4C,kBAAkB;EAClBE,QAAQ;EACRE,SAAS,GAAG,KAAK;EACjB7F,QAAQ;EACR+F,QAAQ;EACRlG,SAAS,GAAG,KAAK;EACjB8G,mBAAmB;EACnBC,SAAS;EACT9H,eAAe;EACfS,YAAY;EACZgB,cAAc;EACdS,kBAAkB;EAClB6F,KAAK,GAAG,CAAC;EACTC,IAAI,GAAG,CAAC;EACRC,IAAI,GAAG,CAAC;EACRC,cAAc,GAAG,KAAK;EACtBC,kBAAkB,GAAG,CAAC;EACtBC,aAAa,GAAG,CAAC;EACjBC,aAAa,GAAG,CAAC,CAAC;EAClBtG,SAAS,GAAG,EAAE;EACdV,WAAW,GAAG,CAAC,CAAC;EAChBL,YAAY,GAAG,CAAC,CAAC;EACjBsH,aAAa;EACbC,aAAa;EACbC,WAAW,GAAG,KAAK;EACnBC,oBAAoB;EACpBC,YAAY;EACZC,aAAa;EACbC,mBAAmB;EACnBC,oBAAoB;EACpB,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACtD,YAAY,KAAK,UAAU;EAC3C;EACA,IAAI9B,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC8B,YAAY,KAAK,YAAY;EAC7C;EACA,IAAI7D,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC6D,YAAY,KAAK,MAAM;EACvC;EACA,IAAItF,WAAWA,CAAA,EAAG;IACd,IAAI,IAAI,CAACiE,MAAM,IAAI,CAAC,IAAI,CAACpD,SAAS,EAAE;MAChC,IAAI,IAAI,CAACY,IAAI,EACT,OAAO,IAAI,CAACwC,MAAM,CAAC4E,KAAK,CAAC,IAAI,CAAC/C,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC+B,KAAK,CAAC7I,IAAI,EAAE,IAAI,CAAC8I,IAAI,CAAC9I,IAAI,CAAC,CAAC8J,GAAG,CAAEC,IAAI,IAAM,IAAI,CAAC1C,QAAQ,GAAG0C,IAAI,GAAGA,IAAI,CAACF,KAAK,CAAC,IAAI,CAAC/C,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC+B,KAAK,CAAClG,IAAI,EAAE,IAAI,CAACmG,IAAI,CAACnG,IAAI,CAAE,CAAC,CAAC,KAC3L,IAAI,IAAI,CAAC6B,UAAU,IAAI,IAAI,CAAC6C,QAAQ,EACrC,OAAO,IAAI,CAACpC,MAAM,CAAC,KAEnB,OAAO,IAAI,CAACA,MAAM,CAAC4E,KAAK,CAAC,IAAI,CAAC/C,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC+B,KAAK,EAAE,IAAI,CAACC,IAAI,CAAC;IAC9E;IACA,OAAO,EAAE;EACb;EACA,IAAIkB,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACnI,SAAS,GAAI,IAAI,CAACuF,eAAe,GAAG,IAAI,CAACvE,SAAS,GAAG,EAAE,GAAI,IAAI,CAAC7B,WAAW;EAC3F;EACA,IAAIkE,aAAaA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACmC,QAAQ,KAAK,IAAI,CAAC5E,IAAI,IAAI,IAAI,CAAC+B,UAAU,CAAC,EAAE;MACjD,OAAO,IAAI,CAAC3C,SAAS,IAAI,IAAI,CAACuF,eAAe,GAAI,IAAI,CAAC3E,IAAI,GAAG,IAAI,CAACI,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,SAAS,GAAI,IAAI,CAACwE,QAAQ,CAACwC,KAAK,CAAC,IAAI,CAACpH,IAAI,GAAG,IAAI,CAACoG,KAAK,CAAClG,IAAI,GAAG,IAAI,CAACkG,KAAK,EAAE,IAAI,CAACpG,IAAI,GAAG,IAAI,CAACqG,IAAI,CAACnG,IAAI,GAAG,IAAI,CAACmG,IAAI,CAAC;IAC5M;IACA,OAAO,IAAI,CAACzB,QAAQ;EACxB;EACA,IAAI4C,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACzD,KAAK,GAAG,IAAI,CAACuC,IAAI,KAAK,IAAI,CAACmB,cAAc,CAAC,CAAC,GAAG,IAAI;EAClE;EACAC,WAAWA,CAAC7E,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,EAAE,EAAEC,IAAI,EAAE;IAClD,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,IAAI,GAAGA,IAAI;EACpB;EACA0E,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EACAC,WAAWA,CAACC,aAAa,EAAE;IACvB,IAAIC,gBAAgB,GAAG,KAAK;IAC5B,IAAID,aAAa,CAAC7C,OAAO,EAAE;MACvB,MAAM;QAAE+C,aAAa;QAAEC;MAAa,CAAC,GAAGH,aAAa,CAAC7C,OAAO;MAC7D,IAAI,IAAI,CAACV,IAAI,IAAIyD,aAAa,KAAKC,YAAY,IAAIA,YAAY,KAAK,IAAI,CAAC7I,SAAS,EAAE;QAChF,IAAI,CAACA,SAAS,GAAG6I,YAAY;QAC7BF,gBAAgB,GAAG,IAAI;MAC3B;IACJ;IACA,IAAID,aAAa,CAAClE,WAAW,EAAE;MAC3B,IAAI,CAAC6C,aAAa,GAAG,IAAI,CAACzG,IAAI,GAAG;QAAEkI,GAAG,EAAE,CAAC;QAAEC,IAAI,EAAE;MAAE,CAAC,GAAG,CAAC;IAC5D;IACA,IAAIL,aAAa,CAAC/C,iBAAiB,EAAE;MACjC,MAAM;QAAEiD,aAAa;QAAEC;MAAa,CAAC,GAAGH,aAAa,CAAC/C,iBAAiB;MACvE,IAAIiD,aAAa,KAAKC,YAAY,IAAIA,YAAY,KAAK,IAAI,CAAC/B,mBAAmB,EAAE;QAC7E,IAAI,CAACA,mBAAmB,GAAG+B,YAAY;MAC3C;IACJ;IACA,IAAIH,aAAa,CAAChL,OAAO,EAAE;MACvB,MAAM;QAAEkL,aAAa;QAAEC;MAAa,CAAC,GAAGH,aAAa,CAAChL,OAAO;MAC7D,IAAI,IAAI,CAACyH,IAAI,IAAIyD,aAAa,EAAE/C,OAAO,KAAKgD,YAAY,EAAEhD,OAAO,IAAIgD,YAAY,EAAEhD,OAAO,KAAK,IAAI,CAAC7F,SAAS,EAAE;QAC3G,IAAI,CAACA,SAAS,GAAG6I,YAAY,CAAChD,OAAO;QACrC8C,gBAAgB,GAAG,IAAI;MAC3B;MACA,IAAIC,aAAa,EAAEjD,iBAAiB,KAAKkD,YAAY,EAAElD,iBAAiB,IAAIkD,YAAY,EAAElD,iBAAiB,KAAK,IAAI,CAACmB,mBAAmB,EAAE;QACtI,IAAI,CAACA,mBAAmB,GAAG+B,YAAY,CAAClD,iBAAiB;MAC7D;IACJ;IACA,IAAI,IAAI,CAAC8B,WAAW,EAAE;MAClB,MAAMuB,SAAS,GAAG,CAACL,gBAAgB,KAAKD,aAAa,CAACvF,KAAK,EAAEyF,aAAa,EAAEK,MAAM,KAAKP,aAAa,CAACvF,KAAK,EAAE0F,YAAY,EAAEI,MAAM,IAAIP,aAAa,CAACxE,QAAQ,IAAIwE,aAAa,CAACtE,YAAY,IAAIsE,aAAa,CAACpE,WAAW,CAAC;MACtN,IAAI0E,SAAS,EAAE;QACX,IAAI,CAACE,IAAI,CAAC,CAAC;QACX,IAAI,CAACC,iBAAiB,CAAC,CAAC;MAC5B;IACJ;EACJ;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACvC,SAAS,CAACR,OAAO,CAAE6B,IAAI,IAAK;MAC7B,QAAQA,IAAI,CAACmB,OAAO,CAAC,CAAC;QAClB,KAAK,SAAS;UACV,IAAI,CAACpK,eAAe,GAAGiJ,IAAI,CAACoB,QAAQ;UACpC;QACJ,KAAK,MAAM;UACP,IAAI,CAAC5J,YAAY,GAAGwI,IAAI,CAACoB,QAAQ;UACjC;QACJ,KAAK,QAAQ;UACT,IAAI,CAAC5I,cAAc,GAAGwH,IAAI,CAACoB,QAAQ;UACnC;QACJ,KAAK,YAAY;UACb,IAAI,CAACnI,kBAAkB,GAAG+G,IAAI,CAACoB,QAAQ;UACvC;QACJ;UACI,IAAI,CAAC5J,YAAY,GAAGwI,IAAI,CAACoB,QAAQ;UACjC;MACR;IACJ,CAAC,CAAC;EACN;EACAC,eAAeA,CAAA,EAAG;IACdC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,IAAI,CAACC,QAAQ,CAAC,CAAC;IACnB,CAAC,CAAC;EACN;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACnC,WAAW,EAAE;MACnB,IAAI,CAACkC,QAAQ,CAAC,CAAC;IACnB;EACJ;EACAE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC/C,SAAS,GAAG,IAAI;IACrB,IAAI,CAACU,WAAW,GAAG,KAAK;EAC5B;EACAkC,QAAQA,CAAA,EAAG;IACP,IAAI7N,iBAAiB,CAAC,IAAI,CAAC4H,UAAU,CAAC,EAAE;MACpC,IAAI3G,UAAU,CAACgN,SAAS,CAAC,IAAI,CAACpD,gBAAgB,EAAEqD,aAAa,CAAC,EAAE;QAC5D,IAAI,CAACxB,eAAe,CAAC,CAAC;QACtB,IAAI,CAACyB,YAAY,CAAC,IAAI,CAAClD,SAAS,CAAC;QACjC,IAAI,CAACmC,IAAI,CAAC,CAAC;QACX,IAAI,CAACvB,YAAY,GAAG5K,UAAU,CAACmN,QAAQ,CAAC,IAAI,CAACvD,gBAAgB,EAAEqD,aAAa,CAAC;QAC7E,IAAI,CAACpC,aAAa,GAAG7K,UAAU,CAACoN,SAAS,CAAC,IAAI,CAACxD,gBAAgB,EAAEqD,aAAa,CAAC;QAC/E,IAAI,CAACnC,mBAAmB,GAAG9K,UAAU,CAACmN,QAAQ,CAAC,IAAI,CAACnD,SAAS,CAAC;QAC9D,IAAI,CAACe,oBAAoB,GAAG/K,UAAU,CAACoN,SAAS,CAAC,IAAI,CAACpD,SAAS,CAAC;QAChE,IAAI,CAACU,WAAW,GAAG,IAAI;MAC3B;IACJ;EACJ;EACAyB,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAAC5D,SAAS,EAAE;MACjB,IAAI,CAAC8E,OAAO,CAAC,CAAC;MACd,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACC,aAAa,CAAC,CAAC;MACpB,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAAC3G,EAAE,CAAC4G,aAAa,CAAC,CAAC;IAC3B;EACJ;EACAP,YAAYA,CAACQ,EAAE,EAAE;IACb,IAAI,CAAC1D,SAAS,GAAG0D,EAAE,IAAI,IAAI,CAAC7D,gBAAgB,EAAEoD,aAAa,IAAIjN,UAAU,CAAC2N,UAAU,CAAC,IAAI,CAAC/D,gBAAgB,EAAEqD,aAAa,EAAE,qBAAqB,CAAC;EACrJ;EACAxB,eAAeA,CAAA,EAAG;IACd,IAAI,CAACxB,KAAK,GAAG,IAAI,CAACpG,IAAI,GAAG;MAAEzC,IAAI,EAAE,CAAC;MAAE2C,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IACjD,IAAI,CAACmG,IAAI,GAAG,IAAI,CAACrG,IAAI,GAAG;MAAEzC,IAAI,EAAE,CAAC;MAAE2C,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IAChD,IAAI,CAACsG,kBAAkB,GAAG,IAAI,CAACxG,IAAI,GAAG;MAAEzC,IAAI,EAAE,CAAC;MAAE2C,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IAC9D,IAAI,CAACuG,aAAa,GAAG,IAAI,CAACzG,IAAI,GAAG;MAAEkI,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IACxD,IAAI,CAAC/I,SAAS,GAAG,IAAI,CAAC8F,QAAQ,IAAI,KAAK;IACvC,IAAI,CAACgB,mBAAmB,GAAG,IAAI,CAAClB,kBAAkB;IAClD,IAAI,CAAC5E,SAAS,GAAG,EAAE;IACnB,IAAI,CAACV,WAAW,GAAG,CAAC,CAAC;IACrB,IAAI,CAACL,YAAY,GAAG,CAAC,CAAC;EAC1B;EACA0K,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAChE,gBAAgB;EAChC;EACA0B,cAAcA,CAAA,EAAG;IACb,OAAOuC,IAAI,CAACC,KAAK,CAAC,CAAC,IAAI,CAAC7D,KAAK,GAAG,IAAI,CAACF,mBAAmB,GAAG,CAAC,KAAK,IAAI,CAACnC,KAAK,IAAI,CAAC,CAAC,CAAC;EACtF;EACAmG,QAAQA,CAACpN,OAAO,EAAE;IACd,IAAI,CAAC2J,aAAa,GAAG,IAAI,CAACzG,IAAI,GAAG;MAAEkI,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IACxD,IAAI,CAACpC,gBAAgB,EAAEqD,aAAa,EAAEc,QAAQ,CAACpN,OAAO,CAAC;EAC3D;EACAqN,aAAaA,CAACtL,KAAK,EAAEuL,QAAQ,GAAG,MAAM,EAAE;IACpC,MAAM;MAAErF;IAAkB,CAAC,GAAG,IAAI,CAACsF,iBAAiB,CAAC,CAAC;IACtD,MAAMC,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC5C,MAAMC,cAAc,GAAGA,CAACC,MAAM,GAAG,CAAC,EAAEC,KAAK,KAAMD,MAAM,IAAIC,KAAK,GAAG,CAAC,GAAGD,MAAO;IAC5E,MAAME,cAAc,GAAGA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,KAAKF,MAAM,GAAGC,KAAK,GAAGC,KAAK;IACvE,MAAMZ,QAAQ,GAAGA,CAAC/B,IAAI,GAAG,CAAC,EAAED,GAAG,GAAG,CAAC,KAAK,IAAI,CAACgC,QAAQ,CAAC;MAAE/B,IAAI;MAAED,GAAG;MAAEkC;IAAS,CAAC,CAAC;IAC9E,IAAIW,QAAQ,GAAG,CAAC;IAChB,IAAI,IAAI,CAAC/K,IAAI,EAAE;MACX+K,QAAQ,GAAG;QAAExN,IAAI,EAAEiN,cAAc,CAAC3L,KAAK,CAAC,CAAC,CAAC,EAAEkG,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAAE7E,IAAI,EAAEsK,cAAc,CAAC3L,KAAK,CAAC,CAAC,CAAC,EAAEkG,iBAAiB,CAAC,CAAC,CAAC;MAAE,CAAC;MACzHmF,QAAQ,CAACS,cAAc,CAACI,QAAQ,CAAC7K,IAAI,EAAE,IAAI,CAACqD,SAAS,CAAC,CAAC,CAAC,EAAE+G,UAAU,CAACnC,IAAI,CAAC,EAAEwC,cAAc,CAACI,QAAQ,CAACxN,IAAI,EAAE,IAAI,CAACgG,SAAS,CAAC,CAAC,CAAC,EAAE+G,UAAU,CAACpC,GAAG,CAAC,CAAC;IACjJ,CAAC,MACI;MACD6C,QAAQ,GAAGP,cAAc,CAAC3L,KAAK,EAAEkG,iBAAiB,CAAC;MACnD,IAAI,CAAChD,UAAU,GAAGmI,QAAQ,CAACS,cAAc,CAACI,QAAQ,EAAE,IAAI,CAACxH,SAAS,EAAE+G,UAAU,CAACnC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG+B,QAAQ,CAAC,CAAC,EAAES,cAAc,CAACI,QAAQ,EAAE,IAAI,CAACxH,SAAS,EAAE+G,UAAU,CAACpC,GAAG,CAAC,CAAC;IACpK;IACA,IAAI,CAAC3B,cAAc,GAAG,IAAI,CAACH,KAAK,KAAK2E,QAAQ;IAC7C,IAAI,CAAC3E,KAAK,GAAG2E,QAAQ;EACzB;EACAC,YAAYA,CAACnM,KAAK,EAAEoM,EAAE,EAAEb,QAAQ,GAAG,MAAM,EAAE;IACvC,IAAIa,EAAE,EAAE;MACJ,MAAM;QAAE7E,KAAK;QAAE8E;MAAS,CAAC,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;MACnD,MAAMjB,QAAQ,GAAGA,CAAC/B,IAAI,GAAG,CAAC,EAAED,GAAG,GAAG,CAAC,KAAK,IAAI,CAACgC,QAAQ,CAAC;QAAE/B,IAAI;QAAED,GAAG;QAAEkC;MAAS,CAAC,CAAC;MAC9E,MAAMgB,SAAS,GAAGH,EAAE,KAAK,UAAU;MACnC,MAAMI,OAAO,GAAGJ,EAAE,KAAK,QAAQ;MAC/B,IAAIG,SAAS,EAAE;QACX,IAAI,IAAI,CAACpL,IAAI,EAAE;UACX,IAAIkL,QAAQ,CAAC9E,KAAK,CAAC7I,IAAI,GAAG6I,KAAK,CAAC7I,IAAI,GAAGsB,KAAK,CAAC,CAAC,CAAC,EAAE;YAC7CqL,QAAQ,CAACgB,QAAQ,CAAC9E,KAAK,CAAClG,IAAI,GAAG,IAAI,CAACqD,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC2H,QAAQ,CAAC9E,KAAK,CAAC7I,IAAI,GAAG,CAAC,IAAI,IAAI,CAACgG,SAAS,CAAC,CAAC,CAAC,CAAC;UACpG,CAAC,MACI,IAAI2H,QAAQ,CAAC9E,KAAK,CAAClG,IAAI,GAAGkG,KAAK,CAAClG,IAAI,GAAGrB,KAAK,CAAC,CAAC,CAAC,EAAE;YAClDqL,QAAQ,CAAC,CAACgB,QAAQ,CAAC9E,KAAK,CAAClG,IAAI,GAAG,CAAC,IAAI,IAAI,CAACqD,SAAS,CAAC,CAAC,CAAC,EAAE2H,QAAQ,CAAC9E,KAAK,CAAC7I,IAAI,GAAG,IAAI,CAACgG,SAAS,CAAC,CAAC,CAAC,CAAC;UACpG;QACJ,CAAC,MACI;UACD,IAAI2H,QAAQ,CAAC9E,KAAK,GAAGA,KAAK,GAAGvH,KAAK,EAAE;YAChC,MAAMyM,GAAG,GAAG,CAACJ,QAAQ,CAAC9E,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC7C,SAAS;YACjD,IAAI,CAACxB,UAAU,GAAGmI,QAAQ,CAACoB,GAAG,EAAE,CAAC,CAAC,GAAGpB,QAAQ,CAAC,CAAC,EAAEoB,GAAG,CAAC;UACzD;QACJ;MACJ,CAAC,MACI,IAAID,OAAO,EAAE;QACd,IAAI,IAAI,CAACrL,IAAI,EAAE;UACX,IAAIkL,QAAQ,CAAC7E,IAAI,CAAC9I,IAAI,GAAG6I,KAAK,CAAC7I,IAAI,IAAIsB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;YACjDqL,QAAQ,CAACgB,QAAQ,CAAC9E,KAAK,CAAClG,IAAI,GAAG,IAAI,CAACqD,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC2H,QAAQ,CAAC9E,KAAK,CAAC7I,IAAI,GAAG,CAAC,IAAI,IAAI,CAACgG,SAAS,CAAC,CAAC,CAAC,CAAC;UACpG,CAAC,MACI,IAAI2H,QAAQ,CAAC7E,IAAI,CAACnG,IAAI,GAAGkG,KAAK,CAAClG,IAAI,IAAIrB,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;YACtDqL,QAAQ,CAAC,CAACgB,QAAQ,CAAC9E,KAAK,CAAClG,IAAI,GAAG,CAAC,IAAI,IAAI,CAACqD,SAAS,CAAC,CAAC,CAAC,EAAE2H,QAAQ,CAAC9E,KAAK,CAAC7I,IAAI,GAAG,IAAI,CAACgG,SAAS,CAAC,CAAC,CAAC,CAAC;UACpG;QACJ,CAAC,MACI;UACD,IAAI2H,QAAQ,CAAC7E,IAAI,GAAGD,KAAK,IAAIvH,KAAK,GAAG,CAAC,EAAE;YACpC,MAAMyM,GAAG,GAAG,CAACJ,QAAQ,CAAC9E,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC7C,SAAS;YACjD,IAAI,CAACxB,UAAU,GAAGmI,QAAQ,CAACoB,GAAG,EAAE,CAAC,CAAC,GAAGpB,QAAQ,CAAC,CAAC,EAAEoB,GAAG,CAAC;UACzD;QACJ;MACJ;IACJ,CAAC,MACI;MACD,IAAI,CAACnB,aAAa,CAACtL,KAAK,EAAEuL,QAAQ,CAAC;IACvC;EACJ;EACAe,gBAAgBA,CAAA,EAAG;IACf,MAAMI,wBAAwB,GAAGA,CAACC,IAAI,EAAEX,KAAK,KAAKb,IAAI,CAACC,KAAK,CAACuB,IAAI,IAAIX,KAAK,IAAIW,IAAI,CAAC,CAAC;IACpF,IAAIC,eAAe,GAAG,IAAI,CAACrF,KAAK;IAChC,IAAIsF,cAAc,GAAG,CAAC;IACtB,IAAI,IAAI,CAAC3F,gBAAgB,EAAEqD,aAAa,EAAE;MACtC,MAAM;QAAEuC,SAAS;QAAEC;MAAW,CAAC,GAAG,IAAI,CAAC7F,gBAAgB,CAACqD,aAAa;MACrE,IAAI,IAAI,CAACpJ,IAAI,EAAE;QACXyL,eAAe,GAAG;UAAElO,IAAI,EAAEgO,wBAAwB,CAACI,SAAS,EAAE,IAAI,CAACpI,SAAS,CAAC,CAAC,CAAC,CAAC;UAAErD,IAAI,EAAEqL,wBAAwB,CAACK,UAAU,EAAE,IAAI,CAACrI,SAAS,CAAC,CAAC,CAAC;QAAE,CAAC;QACjJmI,cAAc,GAAG;UAAEnO,IAAI,EAAEkO,eAAe,CAAClO,IAAI,GAAG,IAAI,CAACiJ,kBAAkB,CAACjJ,IAAI;UAAE2C,IAAI,EAAEuL,eAAe,CAACvL,IAAI,GAAG,IAAI,CAACsG,kBAAkB,CAACtG;QAAK,CAAC;MAC7I,CAAC,MACI;QACD,MAAM2L,SAAS,GAAG,IAAI,CAAC9J,UAAU,GAAG6J,UAAU,GAAGD,SAAS;QAC1DF,eAAe,GAAGF,wBAAwB,CAACM,SAAS,EAAE,IAAI,CAACtI,SAAS,CAAC;QACrEmI,cAAc,GAAGD,eAAe,GAAG,IAAI,CAACjF,kBAAkB;MAC9D;IACJ;IACA,OAAO;MACHJ,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBC,IAAI,EAAE,IAAI,CAACA,IAAI;MACf6E,QAAQ,EAAE;QACN9E,KAAK,EAAEqF,eAAe;QACtBpF,IAAI,EAAEqF;MACV;IACJ,CAAC;EACL;EACArB,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC5C,MAAMuB,YAAY,GAAG,CAAC,IAAI,CAAC/F,gBAAgB,EAAEqD,aAAa,GAAG,IAAI,CAACrD,gBAAgB,CAACqD,aAAa,CAAC2C,WAAW,GAAGzB,UAAU,CAACnC,IAAI,GAAG,CAAC,KAAK,CAAC;IACxI,MAAM6D,aAAa,GAAG,CAAC,IAAI,CAACjG,gBAAgB,EAAEqD,aAAa,GAAG,IAAI,CAACrD,gBAAgB,CAACqD,aAAa,CAAC6C,YAAY,GAAG3B,UAAU,CAACpC,GAAG,GAAG,CAAC,KAAK,CAAC;IACzI,MAAMgE,2BAA2B,GAAGA,CAACC,YAAY,EAAE5I,SAAS,KAAKyG,IAAI,CAACoC,IAAI,CAACD,YAAY,IAAI5I,SAAS,IAAI4I,YAAY,CAAC,CAAC;IACtH,MAAME,0BAA0B,GAAIC,SAAS,IAAKtC,IAAI,CAACoC,IAAI,CAACE,SAAS,GAAG,CAAC,CAAC;IAC1E,MAAM9F,kBAAkB,GAAG,IAAI,CAACxG,IAAI,GAC9B;MAAEzC,IAAI,EAAE2O,2BAA2B,CAACF,aAAa,EAAE,IAAI,CAACzI,SAAS,CAAC,CAAC,CAAC,CAAC;MAAErD,IAAI,EAAEgM,2BAA2B,CAACJ,YAAY,EAAE,IAAI,CAACvI,SAAS,CAAC,CAAC,CAAC;IAAE,CAAC,GAC3I2I,2BAA2B,CAAC,IAAI,CAACnK,UAAU,GAAG+J,YAAY,GAAGE,aAAa,EAAE,IAAI,CAACzI,SAAS,CAAC;IACjG,MAAMwB,iBAAiB,GAAG,IAAI,CAACmB,mBAAmB,KAAK,IAAI,CAAClG,IAAI,GAAG,CAACqM,0BAA0B,CAAC7F,kBAAkB,CAACjJ,IAAI,CAAC,EAAE8O,0BAA0B,CAAC7F,kBAAkB,CAACtG,IAAI,CAAC,CAAC,GAAGmM,0BAA0B,CAAC7F,kBAAkB,CAAC,CAAC;IAC/N,OAAO;MAAEA,kBAAkB;MAAEzB;IAAkB,CAAC;EACpD;EACA0E,gBAAgBA,CAAA,EAAG;IACf,MAAM;MAAEjD,kBAAkB;MAAEzB;IAAkB,CAAC,GAAG,IAAI,CAACsF,iBAAiB,CAAC,CAAC;IAC1E,MAAMkC,aAAa,GAAGA,CAAC3B,MAAM,EAAE4B,IAAI,EAAE9B,KAAK,EAAE+B,OAAO,GAAG,KAAK,KAAK,IAAI,CAACC,OAAO,CAAC9B,MAAM,GAAG4B,IAAI,GAAG,CAAC5B,MAAM,GAAGF,KAAK,GAAG,CAAC,GAAG,CAAC,IAAIA,KAAK,EAAE+B,OAAO,CAAC;IACvI,MAAMrG,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMC,IAAI,GAAG,IAAI,CAACrG,IAAI,GAChB;MAAEzC,IAAI,EAAEgP,aAAa,CAAC,IAAI,CAACnG,KAAK,CAAC7I,IAAI,EAAEiJ,kBAAkB,CAACjJ,IAAI,EAAEwH,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAAE7E,IAAI,EAAEqM,aAAa,CAAC,IAAI,CAACnG,KAAK,CAAClG,IAAI,EAAEsG,kBAAkB,CAACtG,IAAI,EAAE6E,iBAAiB,CAAC,CAAC,CAAC,EAAE,IAAI;IAAE,CAAC,GAClLwH,aAAa,CAAC,IAAI,CAACnG,KAAK,EAAEI,kBAAkB,EAAEzB,iBAAiB,CAAC;IACtE,IAAI,CAACsB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACN,mBAAmB,GAAGnB,iBAAiB;IAC5C,IAAI,IAAI,CAACD,UAAU,EAAE;MACjB,IAAI,CAAC1E,SAAS,GAAG,IAAI,CAACJ,IAAI,GAAG2M,KAAK,CAACC,IAAI,CAAC;QAAEvE,MAAM,EAAE7B,kBAAkB,CAACjJ;MAAK,CAAC,CAAC,CAAC8J,GAAG,CAAC,MAAMsF,KAAK,CAACC,IAAI,CAAC;QAAEvE,MAAM,EAAE7B,kBAAkB,CAACtG;MAAK,CAAC,CAAC,CAAC,GAAGyM,KAAK,CAACC,IAAI,CAAC;QAAEvE,MAAM,EAAE7B;MAAmB,CAAC,CAAC;IACxL;IACA,IAAI,IAAI,CAAChC,KAAK,EAAE;MACZoE,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAACpC,aAAa,GAAG;UACjBN,KAAK,EAAE,IAAI,CAACrC,KAAK,GAAI,IAAI,CAAC/D,IAAI,GAAG;YAAEzC,IAAI,EAAE,CAAC;YAAE2C,IAAI,EAAEkG,KAAK,CAAClG;UAAK,CAAC,GAAG,CAAC,GAAIkG,KAAK;UAC3EC,IAAI,EAAE2D,IAAI,CAAC6C,GAAG,CAAC,IAAI,CAAC9I,KAAK,GAAG,IAAI,CAACA,KAAK,GAAG,IAAI,CAACsC,IAAI,EAAE,IAAI,CAAC9D,KAAK,CAAC8F,MAAM;QACzE,CAAC;QACD,IAAI,CAACyE,YAAY,CAAC,YAAY,EAAE,IAAI,CAACpG,aAAa,CAAC;MACvD,CAAC,CAAC;IACN;EACJ;EACA6B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACnD,SAAS,IAAI,CAAC,IAAI,CAAChG,SAAS,EAAE;MACnCwJ,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,IAAI,CAAC3C,SAAS,EAAE;UAChB,IAAI,CAACA,SAAS,CAAC/C,KAAK,CAAC2J,SAAS,GAAG,IAAI,CAAC5G,SAAS,CAAC/C,KAAK,CAAC4J,QAAQ,GAAG,MAAM;UACvE,IAAI,CAAC7G,SAAS,CAAC/C,KAAK,CAAC6J,QAAQ,GAAG,UAAU;UAC1C,IAAI,CAAClH,gBAAgB,CAACqD,aAAa,CAAChG,KAAK,CAAC8J,OAAO,GAAG,MAAM;UAC1D,MAAM,CAACpB,YAAY,EAAEE,aAAa,CAAC,GAAG,CAAC7P,UAAU,CAACmN,QAAQ,CAAC,IAAI,CAACnD,SAAS,CAAC,EAAEhK,UAAU,CAACoN,SAAS,CAAC,IAAI,CAACpD,SAAS,CAAC,CAAC;UACjH2F,YAAY,KAAK,IAAI,CAAC7E,mBAAmB,KAAK,IAAI,CAAClB,gBAAgB,CAACqD,aAAa,CAAChG,KAAK,CAAC+J,KAAK,GAAG,EAAE,CAAC;UACnGnB,aAAa,KAAK,IAAI,CAAC9E,oBAAoB,KAAK,IAAI,CAACnB,gBAAgB,CAACqD,aAAa,CAAChG,KAAK,CAACgK,MAAM,GAAG,EAAE,CAAC;UACtG,MAAM,CAACD,KAAK,EAAEC,MAAM,CAAC,GAAG,CAACjR,UAAU,CAACmN,QAAQ,CAAC,IAAI,CAACvD,gBAAgB,CAACqD,aAAa,CAAC,EAAEjN,UAAU,CAACoN,SAAS,CAAC,IAAI,CAACxD,gBAAgB,CAACqD,aAAa,CAAC,CAAC;UAC7I,CAAC,IAAI,CAACpJ,IAAI,IAAI,IAAI,CAAC+B,UAAU,MAAM,IAAI,CAACgE,gBAAgB,CAACqD,aAAa,CAAChG,KAAK,CAAC+J,KAAK,GAAGA,KAAK,GAAG,IAAI,CAACpG,YAAY,GAAGoG,KAAK,GAAG,IAAI,GAAG,IAAI,CAACxJ,YAAY,IAAI,IAAI,CAACoD,YAAY,GAAG,IAAI,CAAC;UAC9K,CAAC,IAAI,CAAC/G,IAAI,IAAI,IAAI,CAACmH,QAAQ,MAAM,IAAI,CAACpB,gBAAgB,CAACqD,aAAa,CAAChG,KAAK,CAACgK,MAAM,GAAGA,MAAM,GAAG,IAAI,CAACpG,aAAa,GAAGoG,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC3J,aAAa,IAAI,IAAI,CAACuD,aAAa,GAAG,IAAI,CAAC;UAClL,IAAI,CAACb,SAAS,CAAC/C,KAAK,CAAC2J,SAAS,GAAG,IAAI,CAAC5G,SAAS,CAAC/C,KAAK,CAAC4J,QAAQ,GAAG,EAAE;UACnE,IAAI,CAAC7G,SAAS,CAAC/C,KAAK,CAAC6J,QAAQ,GAAG,EAAE;UAClC,IAAI,CAAClH,gBAAgB,CAACqD,aAAa,CAAChG,KAAK,CAAC8J,OAAO,GAAG,EAAE;QAC1D;MACJ,CAAC,CAAC;IACN;EACJ;EACAR,OAAOA,CAACrG,IAAI,GAAG,CAAC,EAAEgH,MAAM,GAAG,KAAK,EAAE;IAC9B,OAAO,IAAI,CAAC7K,MAAM,GAAGwH,IAAI,CAAC6C,GAAG,CAACQ,MAAM,GAAG,CAAC,IAAI,CAACzI,QAAQ,IAAI,IAAI,CAACpC,MAAM,CAAC,CAAC,CAAC,EAAE6F,MAAM,GAAG,IAAI,CAAC7F,MAAM,CAAC6F,MAAM,EAAEhC,IAAI,CAAC,GAAG,CAAC;EACnH;EACAkE,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACpE,SAAS,EAAE;MAChB,MAAM/C,KAAK,GAAGkK,gBAAgB,CAAC,IAAI,CAACnH,SAAS,CAAC;MAC9C,MAAMgC,IAAI,GAAGoF,UAAU,CAACnK,KAAK,CAACoK,WAAW,CAAC,GAAGxD,IAAI,CAACyD,GAAG,CAACF,UAAU,CAACnK,KAAK,CAAC+E,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MACrF,MAAMuF,KAAK,GAAGH,UAAU,CAACnK,KAAK,CAACuK,YAAY,CAAC,GAAG3D,IAAI,CAACyD,GAAG,CAACF,UAAU,CAACnK,KAAK,CAACsK,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MACxF,MAAMxF,GAAG,GAAGqF,UAAU,CAACnK,KAAK,CAACwK,UAAU,CAAC,GAAG5D,IAAI,CAACyD,GAAG,CAACF,UAAU,CAACnK,KAAK,CAAC8E,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MAClF,MAAM2F,MAAM,GAAGN,UAAU,CAACnK,KAAK,CAAC0K,aAAa,CAAC,GAAG9D,IAAI,CAACyD,GAAG,CAACF,UAAU,CAACnK,KAAK,CAACyK,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;MAC3F,OAAO;QAAE1F,IAAI;QAAEuF,KAAK;QAAExF,GAAG;QAAE2F,MAAM;QAAEE,CAAC,EAAE5F,IAAI,GAAGuF,KAAK;QAAEM,CAAC,EAAE9F,GAAG,GAAG2F;MAAO,CAAC;IACzE;IACA,OAAO;MAAE1F,IAAI,EAAE,CAAC;MAAEuF,KAAK,EAAE,CAAC;MAAExF,GAAG,EAAE,CAAC;MAAE2F,MAAM,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EAC/D;EACAxE,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACzD,gBAAgB,EAAEqD,aAAa,EAAE;MACtC,MAAM6E,aAAa,GAAG,IAAI,CAAClI,gBAAgB,CAACqD,aAAa,CAAC6E,aAAa,CAACA,aAAa;MACrF,MAAMd,KAAK,GAAG,IAAI,CAACxJ,YAAY,IAAK,GAAE,IAAI,CAACoC,gBAAgB,CAACqD,aAAa,CAAC2C,WAAW,IAAIkC,aAAa,CAAClC,WAAY,IAAG;MACtH,MAAMqB,MAAM,GAAG,IAAI,CAAC3J,aAAa,IAAK,GAAE,IAAI,CAACsC,gBAAgB,CAACqD,aAAa,CAAC6C,YAAY,IAAIgC,aAAa,CAAChC,YAAa,IAAG;MAC1H,MAAMiC,OAAO,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAM,IAAI,CAACrI,gBAAgB,CAACqD,aAAa,CAAChG,KAAK,CAAC+K,KAAK,CAAC,GAAGC,MAAO;MAC9F,IAAI,IAAI,CAACpO,IAAI,IAAI,IAAI,CAAC+B,UAAU,EAAE;QAC9BmM,OAAO,CAAC,QAAQ,EAAEd,MAAM,CAAC;QACzBc,OAAO,CAAC,OAAO,EAAEf,KAAK,CAAC;MAC3B,CAAC,MACI;QACDe,OAAO,CAAC,QAAQ,EAAEd,MAAM,CAAC;MAC7B;IACJ;EACJ;EACA1D,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAAClH,MAAM,EAAE;MACb,MAAM8H,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;MAC5C,MAAM2D,OAAO,GAAGA,CAACC,KAAK,EAAEC,MAAM,EAAEvD,KAAK,EAAEC,KAAK,GAAG,CAAC,KAAM,IAAI,CAACpL,WAAW,GAAG;QAAE,GAAG,IAAI,CAACA,WAAW;QAAE,GAAG;UAAE,CAAE,GAAEyO,KAAM,EAAC,GAAG,CAACC,MAAM,IAAI,EAAE,EAAE/F,MAAM,GAAGwC,KAAK,GAAGC,KAAK,GAAG;QAAK;MAAE,CAAE;MACpK,IAAI,IAAI,CAAC9K,IAAI,EAAE;QACXkO,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC1L,MAAM,EAAE,IAAI,CAACe,SAAS,CAAC,CAAC,CAAC,EAAE+G,UAAU,CAAC0D,CAAC,CAAC;QAC/DE,OAAO,CAAC,OAAO,EAAE,IAAI,CAACtJ,QAAQ,IAAI,IAAI,CAACpC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACe,SAAS,CAAC,CAAC,CAAC,EAAE+G,UAAU,CAACyD,CAAC,CAAC;MACtF,CAAC,MACI;QACD,IAAI,CAAChM,UAAU,GAAGmM,OAAO,CAAC,OAAO,EAAE,IAAI,CAACtJ,QAAQ,IAAI,IAAI,CAACpC,MAAM,EAAE,IAAI,CAACe,SAAS,EAAE+G,UAAU,CAACyD,CAAC,CAAC,GAAGG,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC1L,MAAM,EAAE,IAAI,CAACe,SAAS,EAAE+G,UAAU,CAAC0D,CAAC,CAAC;MACjK;IACJ;EACJ;EACAK,kBAAkBA,CAAC/C,GAAG,EAAE;IACpB,IAAI,IAAI,CAACnF,SAAS,IAAI,CAAC,IAAI,CAAC9B,WAAW,EAAE;MACrC,MAAM+B,KAAK,GAAGkF,GAAG,GAAGA,GAAG,CAAClF,KAAK,GAAG,IAAI,CAACA,KAAK;MAC1C,MAAMkI,qBAAqB,GAAGA,CAAC1D,MAAM,EAAEC,KAAK,KAAKD,MAAM,GAAGC,KAAK;MAC/D,MAAM0D,YAAY,GAAGA,CAACC,EAAE,GAAG,CAAC,EAAEC,EAAE,GAAG,CAAC,KAAM,IAAI,CAACpP,YAAY,GAAG;QAAE,GAAG,IAAI,CAACA,YAAY;QAAE,GAAG;UAAEqP,SAAS,EAAG,eAAcF,EAAG,OAAMC,EAAG;QAAQ;MAAE,CAAE;MAC7I,IAAI,IAAI,CAACzO,IAAI,EAAE;QACXuO,YAAY,CAACD,qBAAqB,CAAClI,KAAK,CAAClG,IAAI,EAAE,IAAI,CAACqD,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE+K,qBAAqB,CAAClI,KAAK,CAAC7I,IAAI,EAAE,IAAI,CAACgG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5H,CAAC,MACI;QACD,MAAMoL,YAAY,GAAGL,qBAAqB,CAAClI,KAAK,EAAE,IAAI,CAAC7C,SAAS,CAAC;QACjE,IAAI,CAACxB,UAAU,GAAGwM,YAAY,CAACI,YAAY,EAAE,CAAC,CAAC,GAAGJ,YAAY,CAAC,CAAC,EAAEI,YAAY,CAAC;MACnF;IACJ;EACJ;EACAC,sBAAsBA,CAACC,KAAK,EAAE;IAC1B,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM;IAC3B,MAAMxE,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC5C,MAAMwE,kBAAkB,GAAGA,CAACvD,IAAI,EAAEV,KAAK,KAAMU,IAAI,GAAIA,IAAI,GAAGV,KAAK,GAAGU,IAAI,GAAGV,KAAK,GAAGU,IAAI,GAAI,CAAE;IAC7F,MAAMwD,qBAAqB,GAAGA,CAACxD,IAAI,EAAEX,KAAK,KAAKb,IAAI,CAACC,KAAK,CAACuB,IAAI,IAAIX,KAAK,IAAIW,IAAI,CAAC,CAAC;IACjF,MAAMyD,qBAAqB,GAAGA,CAACC,aAAa,EAAEtE,MAAM,EAAEuE,KAAK,EAAE3C,IAAI,EAAE9B,KAAK,EAAE0E,oBAAoB,KAAK;MAC/F,OAAOF,aAAa,IAAIxE,KAAK,GAAGA,KAAK,GAAG0E,oBAAoB,GAAGD,KAAK,GAAG3C,IAAI,GAAG9B,KAAK,GAAGE,MAAM,GAAGF,KAAK,GAAG,CAAC;IAC5G,CAAC;IACD,MAAMF,cAAc,GAAGA,CAAC0E,aAAa,EAAEG,aAAa,EAAEzE,MAAM,EAAEuE,KAAK,EAAE3C,IAAI,EAAE9B,KAAK,EAAE0E,oBAAoB,KAAK;MACvG,IAAIF,aAAa,IAAIxE,KAAK,EACtB,OAAO,CAAC,CAAC,KAET,OAAOV,IAAI,CAACyD,GAAG,CAAC,CAAC,EAAE2B,oBAAoB,GAAIF,aAAa,GAAGG,aAAa,GAAGzE,MAAM,GAAGsE,aAAa,GAAGxE,KAAK,GAAIwE,aAAa,GAAGG,aAAa,GAAGzE,MAAM,GAAGsE,aAAa,GAAG,CAAC,GAAGxE,KAAK,CAAC;IACxL,CAAC;IACD,MAAM6B,aAAa,GAAGA,CAAC2C,aAAa,EAAEtE,MAAM,EAAEuE,KAAK,EAAE3C,IAAI,EAAE9B,KAAK,EAAE+B,OAAO,GAAG,KAAK,KAAK;MAClF,IAAI6C,SAAS,GAAG1E,MAAM,GAAG4B,IAAI,GAAG,CAAC,GAAG9B,KAAK;MACzC,IAAIwE,aAAa,IAAIxE,KAAK,EAAE;QACxB4E,SAAS,IAAI5E,KAAK,GAAG,CAAC;MAC1B;MACA,OAAO,IAAI,CAACgC,OAAO,CAAC4C,SAAS,EAAE7C,OAAO,CAAC;IAC3C,CAAC;IACD,MAAMd,SAAS,GAAGoD,kBAAkB,CAACD,MAAM,CAACnD,SAAS,EAAErB,UAAU,CAACpC,GAAG,CAAC;IACtE,MAAM0D,UAAU,GAAGmD,kBAAkB,CAACD,MAAM,CAAClD,UAAU,EAAEtB,UAAU,CAACnC,IAAI,CAAC;IACzE,IAAI4C,QAAQ,GAAG,IAAI,CAAC/K,IAAI,GAAG;MAAEzC,IAAI,EAAE,CAAC;MAAE2C,IAAI,EAAE;IAAE,CAAC,GAAG,CAAC;IACnD,IAAIqP,OAAO,GAAG,IAAI,CAAClJ,IAAI;IACvB,IAAIE,cAAc,GAAG,KAAK;IAC1B,IAAIiJ,YAAY,GAAG,IAAI,CAAC/I,aAAa;IACrC,IAAI,IAAI,CAACzG,IAAI,EAAE;MACX,MAAMyP,YAAY,GAAG,IAAI,CAAChJ,aAAa,CAACyB,GAAG,IAAIyD,SAAS;MACxD,MAAM+D,aAAa,GAAG,IAAI,CAACjJ,aAAa,CAAC0B,IAAI,IAAIyD,UAAU;MAC3D,IAAI,CAAC,IAAI,CAACvH,WAAW,IAAK,IAAI,CAACA,WAAW,KAAKoL,YAAY,IAAIC,aAAa,CAAE,EAAE;QAC5E,MAAMC,YAAY,GAAG;UAAEpS,IAAI,EAAEyR,qBAAqB,CAACrD,SAAS,EAAE,IAAI,CAACpI,SAAS,CAAC,CAAC,CAAC,CAAC;UAAErD,IAAI,EAAE8O,qBAAqB,CAACpD,UAAU,EAAE,IAAI,CAACrI,SAAS,CAAC,CAAC,CAAC;QAAE,CAAC;QAC9I,MAAMqM,YAAY,GAAG;UACjBrS,IAAI,EAAE0R,qBAAqB,CAACU,YAAY,CAACpS,IAAI,EAAE,IAAI,CAAC6I,KAAK,CAAC7I,IAAI,EAAE,IAAI,CAAC8I,IAAI,CAAC9I,IAAI,EAAE,IAAI,CAACiJ,kBAAkB,CAACjJ,IAAI,EAAE,IAAI,CAAC2I,mBAAmB,CAAC,CAAC,CAAC,EAAEuJ,YAAY,CAAC;UACxJvP,IAAI,EAAE+O,qBAAqB,CAACU,YAAY,CAACzP,IAAI,EAAE,IAAI,CAACkG,KAAK,CAAClG,IAAI,EAAE,IAAI,CAACmG,IAAI,CAACnG,IAAI,EAAE,IAAI,CAACsG,kBAAkB,CAACtG,IAAI,EAAE,IAAI,CAACgG,mBAAmB,CAAC,CAAC,CAAC,EAAEwJ,aAAa;QAC5J,CAAC;QACD3E,QAAQ,GAAG;UACPxN,IAAI,EAAEiN,cAAc,CAACmF,YAAY,CAACpS,IAAI,EAAEqS,YAAY,CAACrS,IAAI,EAAE,IAAI,CAAC6I,KAAK,CAAC7I,IAAI,EAAE,IAAI,CAAC8I,IAAI,CAAC9I,IAAI,EAAE,IAAI,CAACiJ,kBAAkB,CAACjJ,IAAI,EAAE,IAAI,CAAC2I,mBAAmB,CAAC,CAAC,CAAC,EAAEuJ,YAAY,CAAC;UACpKvP,IAAI,EAAEsK,cAAc,CAACmF,YAAY,CAACzP,IAAI,EAAE0P,YAAY,CAAC1P,IAAI,EAAE,IAAI,CAACkG,KAAK,CAAClG,IAAI,EAAE,IAAI,CAACmG,IAAI,CAACnG,IAAI,EAAE,IAAI,CAACsG,kBAAkB,CAACtG,IAAI,EAAE,IAAI,CAACgG,mBAAmB,CAAC,CAAC,CAAC,EAAEwJ,aAAa;QACxK,CAAC;QACDH,OAAO,GAAG;UACNhS,IAAI,EAAEgP,aAAa,CAACoD,YAAY,CAACpS,IAAI,EAAEwN,QAAQ,CAACxN,IAAI,EAAE,IAAI,CAAC8I,IAAI,CAAC9I,IAAI,EAAE,IAAI,CAACiJ,kBAAkB,CAACjJ,IAAI,EAAE,IAAI,CAAC2I,mBAAmB,CAAC,CAAC,CAAC,CAAC;UAChIhG,IAAI,EAAEqM,aAAa,CAACoD,YAAY,CAACzP,IAAI,EAAE6K,QAAQ,CAAC7K,IAAI,EAAE,IAAI,CAACmG,IAAI,CAACnG,IAAI,EAAE,IAAI,CAACsG,kBAAkB,CAACtG,IAAI,EAAE,IAAI,CAACgG,mBAAmB,CAAC,CAAC,CAAC,EAAE,IAAI;QACzI,CAAC;QACDK,cAAc,GAAGwE,QAAQ,CAACxN,IAAI,KAAK,IAAI,CAAC6I,KAAK,CAAC7I,IAAI,IAAIgS,OAAO,CAAChS,IAAI,KAAK,IAAI,CAAC8I,IAAI,CAAC9I,IAAI,IAAIwN,QAAQ,CAAC7K,IAAI,KAAK,IAAI,CAACkG,KAAK,CAAClG,IAAI,IAAIqP,OAAO,CAACrP,IAAI,KAAK,IAAI,CAACmG,IAAI,CAACnG,IAAI,IAAI,IAAI,CAACqG,cAAc;QACpLiJ,YAAY,GAAG;UAAEtH,GAAG,EAAEyD,SAAS;UAAExD,IAAI,EAAEyD;QAAW,CAAC;MACvD;IACJ,CAAC,MACI;MACD,MAAMC,SAAS,GAAG,IAAI,CAAC9J,UAAU,GAAG6J,UAAU,GAAGD,SAAS;MAC1D,MAAMkE,mBAAmB,GAAG,IAAI,CAACpJ,aAAa,IAAIoF,SAAS;MAC3D,IAAI,CAAC,IAAI,CAACxH,WAAW,IAAK,IAAI,CAACA,WAAW,IAAIwL,mBAAoB,EAAE;QAChE,MAAMF,YAAY,GAAGX,qBAAqB,CAACnD,SAAS,EAAE,IAAI,CAACtI,SAAS,CAAC;QACrE,MAAMqM,YAAY,GAAGX,qBAAqB,CAACU,YAAY,EAAE,IAAI,CAACvJ,KAAK,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACG,kBAAkB,EAAE,IAAI,CAACN,mBAAmB,EAAE2J,mBAAmB,CAAC;QACvJ9E,QAAQ,GAAGP,cAAc,CAACmF,YAAY,EAAEC,YAAY,EAAE,IAAI,CAACxJ,KAAK,EAAE,IAAI,CAACC,IAAI,EAAE,IAAI,CAACG,kBAAkB,EAAE,IAAI,CAACN,mBAAmB,EAAE2J,mBAAmB,CAAC;QACpJN,OAAO,GAAGhD,aAAa,CAACoD,YAAY,EAAE5E,QAAQ,EAAE,IAAI,CAAC1E,IAAI,EAAE,IAAI,CAACG,kBAAkB,EAAE,IAAI,CAACN,mBAAmB,CAAC;QAC7GK,cAAc,GAAGwE,QAAQ,KAAK,IAAI,CAAC3E,KAAK,IAAImJ,OAAO,KAAK,IAAI,CAAClJ,IAAI,IAAI,IAAI,CAACE,cAAc;QACxFiJ,YAAY,GAAG3D,SAAS;MAC5B;IACJ;IACA,OAAO;MACHzF,KAAK,EAAE2E,QAAQ;MACf1E,IAAI,EAAEkJ,OAAO;MACbhJ,cAAc;MACdsF,SAAS,EAAE2D;IACf,CAAC;EACL;EACAM,cAAcA,CAACjB,KAAK,EAAE;IAClB,MAAM;MAAEzI,KAAK;MAAEC,IAAI;MAAEE,cAAc;MAAEsF;IAAU,CAAC,GAAG,IAAI,CAAC+C,sBAAsB,CAACC,KAAK,CAAC;IACrF,IAAItI,cAAc,EAAE;MAChB,MAAMwJ,QAAQ,GAAG;QAAE3J,KAAK;QAAEC;MAAK,CAAC;MAChC,IAAI,CAACgI,kBAAkB,CAAC0B,QAAQ,CAAC;MACjC,IAAI,CAAC3J,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACC,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACI,aAAa,GAAGoF,SAAS;MAC9B,IAAI,CAACiB,YAAY,CAAC,qBAAqB,EAAEiD,QAAQ,CAAC;MAClD,IAAI,IAAI,CAACvL,KAAK,IAAI,IAAI,CAACgD,aAAa,EAAE;QAClC,MAAMd,aAAa,GAAG;UAClBN,KAAK,EAAE,IAAI,CAACrC,KAAK,GAAGiG,IAAI,CAAC6C,GAAG,CAAC,IAAI,CAACpF,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC1D,KAAK,EAAE,IAAI,CAACxB,KAAK,CAAC8F,MAAM,GAAG,IAAI,CAACtE,KAAK,CAAC,GAAGqC,KAAK;UACxGC,IAAI,EAAE2D,IAAI,CAAC6C,GAAG,CAAC,IAAI,CAAC9I,KAAK,GAAG,CAAC,IAAI,CAAC0D,cAAc,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC1D,KAAK,GAAGsC,IAAI,EAAE,IAAI,CAAC9D,KAAK,CAAC8F,MAAM;QAClG,CAAC;QACD,MAAM2H,kBAAkB,GAAG,IAAI,CAACtJ,aAAa,CAACN,KAAK,KAAKM,aAAa,CAACN,KAAK,IAAI,IAAI,CAACM,aAAa,CAACL,IAAI,KAAKK,aAAa,CAACL,IAAI;QAC7H2J,kBAAkB,IAAI,IAAI,CAAClD,YAAY,CAAC,YAAY,EAAEpG,aAAa,CAAC;QACpE,IAAI,CAACA,aAAa,GAAGA,aAAa;MACtC;IACJ;EACJ;EACAlF,iBAAiBA,CAACqN,KAAK,EAAE;IACrB,IAAI,CAAC/B,YAAY,CAAC,UAAU,EAAE;MAAEmD,aAAa,EAAEpB;IAAM,CAAC,CAAC;IACvD,IAAI,IAAI,CAAC5K,MAAM,IAAI,IAAI,CAACuD,aAAa,EAAE;MACnC,IAAI,IAAI,CAACb,aAAa,EAAE;QACpBuJ,YAAY,CAAC,IAAI,CAACvJ,aAAa,CAAC;MACpC;MACA,IAAI,CAAC,IAAI,CAACvH,SAAS,IAAI,IAAI,CAAC0F,UAAU,EAAE;QACpC,MAAM;UAAEyB;QAAe,CAAC,GAAG,IAAI,CAACqI,sBAAsB,CAACC,KAAK,CAAC;QAC7D,MAAMsB,OAAO,GAAG5J,cAAc,KAAK,IAAI,CAACxC,KAAK,GAAG,IAAI,CAACyD,aAAa,GAAG,KAAK,CAAC;QAC3E,IAAI2I,OAAO,EAAE;UACT,IAAI,CAAC/Q,SAAS,GAAG,IAAI;UACrB,IAAI,CAAC4D,EAAE,CAAC4G,aAAa,CAAC,CAAC;QAC3B;MACJ;MACA,IAAI,CAACjD,aAAa,GAAGyJ,UAAU,CAAC,MAAM;QAClC,IAAI,CAACN,cAAc,CAACjB,KAAK,CAAC;QAC1B,IAAI,IAAI,CAACzP,SAAS,IAAI,IAAI,CAAC0F,UAAU,KAAK,CAAC,IAAI,CAACN,KAAK,IAAI,IAAI,CAACU,QAAQ,KAAKmL,SAAS,CAAC,EAAE;UACnF,IAAI,CAACjR,SAAS,GAAG,KAAK;UACtB,IAAI,CAACkH,IAAI,GAAG,IAAI,CAACmB,cAAc,CAAC,CAAC;UACjC,IAAI,CAACzE,EAAE,CAAC4G,aAAa,CAAC,CAAC;QAC3B;MACJ,CAAC,EAAE,IAAI,CAAC3F,MAAM,CAAC;IACnB,CAAC,MACI;MACD,CAAC,IAAI,CAAC7E,SAAS,IAAI,IAAI,CAAC0Q,cAAc,CAACjB,KAAK,CAAC;IACjD;EACJ;EACAlF,kBAAkBA,CAAA,EAAG;IACjB,IAAIzO,iBAAiB,CAAC,IAAI,CAAC4H,UAAU,CAAC,EAAE;MACpC,IAAI,CAAC,IAAI,CAACgE,oBAAoB,EAAE;QAC5B,IAAI,CAAC7D,IAAI,CAACqN,iBAAiB,CAAC,MAAM;UAC9B,MAAMC,MAAM,GAAG,IAAI,CAAC1N,QAAQ,CAAC2N,WAAW;UACxC,MAAM3B,KAAK,GAAG1S,UAAU,CAACsU,aAAa,CAAC,CAAC,GAAG,mBAAmB,GAAG,QAAQ;UACzE,IAAI,CAAC3J,oBAAoB,GAAG,IAAI,CAAC/D,QAAQ,CAAC2N,MAAM,CAACH,MAAM,EAAE1B,KAAK,EAAE,IAAI,CAAC8B,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnG,CAAC,CAAC;MACN;IACJ;EACJ;EACA1H,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACpC,oBAAoB,EAAE;MAC3B,IAAI,CAACA,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACpC;EACJ;EACA6J,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC/J,aAAa,EAAE;MACpBsJ,YAAY,CAAC,IAAI,CAACtJ,aAAa,CAAC;IACpC;IACA,IAAI,CAACA,aAAa,GAAGwJ,UAAU,CAAC,MAAM;MAClC,IAAIjU,UAAU,CAACgN,SAAS,CAAC,IAAI,CAACpD,gBAAgB,EAAEqD,aAAa,CAAC,EAAE;QAC5D,MAAM,CAAC+D,KAAK,EAAEC,MAAM,CAAC,GAAG,CAACjR,UAAU,CAACmN,QAAQ,CAAC,IAAI,CAACvD,gBAAgB,EAAEqD,aAAa,CAAC,EAAEjN,UAAU,CAACoN,SAAS,CAAC,IAAI,CAACxD,gBAAgB,EAAEqD,aAAa,CAAC,CAAC;QAC/I,MAAM,CAACyH,WAAW,EAAEC,YAAY,CAAC,GAAG,CAAC3D,KAAK,KAAK,IAAI,CAACpG,YAAY,EAAEqG,MAAM,KAAK,IAAI,CAACpG,aAAa,CAAC;QAChG,MAAM+J,MAAM,GAAG,IAAI,CAAC/Q,IAAI,GAAG6Q,WAAW,IAAIC,YAAY,GAAG,IAAI,CAAC/O,UAAU,GAAG8O,WAAW,GAAG,IAAI,CAAC1J,QAAQ,GAAG2J,YAAY,GAAG,KAAK;QAC7HC,MAAM,IACF,IAAI,CAAC9N,IAAI,CAAC+N,GAAG,CAAC,MAAM;UAChB,IAAI,CAAC9K,mBAAmB,GAAG,IAAI,CAAClB,kBAAkB;UAClD,IAAI,CAAC+B,YAAY,GAAGoG,KAAK;UACzB,IAAI,CAACnG,aAAa,GAAGoG,MAAM;UAC3B,IAAI,CAACnG,mBAAmB,GAAG9K,UAAU,CAACmN,QAAQ,CAAC,IAAI,CAACnD,SAAS,CAAC;UAC9D,IAAI,CAACe,oBAAoB,GAAG/K,UAAU,CAACoN,SAAS,CAAC,IAAI,CAACpD,SAAS,CAAC;UAChE,IAAI,CAACmC,IAAI,CAAC,CAAC;QACf,CAAC,CAAC;MACV;IACJ,CAAC,EAAE,IAAI,CAACnE,YAAY,CAAC;EACzB;EACA2I,YAAYA,CAACmE,IAAI,EAAEC,MAAM,EAAE;IACvB;IACA,OAAO,IAAI,CAACpU,OAAO,IAAI,IAAI,CAACA,OAAO,CAACmU,IAAI,CAAC,GAAG,IAAI,CAACnU,OAAO,CAACmU,IAAI,CAAC,CAACC,MAAM,CAAC,GAAG,IAAI,CAACD,IAAI,CAAC,CAACE,IAAI,CAACD,MAAM,CAAC;EACpG;EACA1S,iBAAiBA,CAAA,EAAG;IAChB,OAAO;MACH4S,iBAAiB,EAAG,sBAAqB,IAAI,CAAChS,SAAS,GAAG,oBAAoB,GAAG,EAAG,EAAC;MACrFmD,KAAK,EAAE,IAAI,CAAChE,WAAW;MACvB8S,cAAc,EAAGxS,KAAK,IAAK,IAAI,CAACE,UAAU,CAACF,KAAK,CAAC;MACjDoG,OAAO,EAAE,IAAI,CAAC7F,SAAS;MACvBW,gBAAgB,EAAEA,CAAClB,KAAK,EAAE/B,OAAO,KAAK,IAAI,CAACiD,gBAAgB,CAAClB,KAAK,EAAE/B,OAAO,CAAC;MAC3EwG,QAAQ,EAAE,IAAI,CAACC,SAAS;MACxBhG,IAAI,EAAE,IAAI,CAACgK,UAAU;MACrB/J,OAAO,EAAE,IAAI,CAACiF,aAAa;MAC3B/C,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BL,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/B8H,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBpF,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3B/B,IAAI,EAAE,IAAI,CAACA;IACf,CAAC;EACL;EACAjB,UAAUA,CAACuS,aAAa,EAAE;IACtB,MAAMC,KAAK,GAAG,CAAC,IAAI,CAAC/O,MAAM,IAAI,EAAE,EAAE6F,MAAM;IACxC,MAAMxJ,KAAK,GAAG,IAAI,CAACmB,IAAI,GAAG,IAAI,CAACoG,KAAK,CAAC7I,IAAI,GAAG+T,aAAa,GAAG,IAAI,CAAClL,KAAK,GAAGkL,aAAa;IACtF,OAAO;MACHzS,KAAK;MACL0S,KAAK;MACLnL,KAAK,EAAEvH,KAAK,KAAK,CAAC;MAClBwH,IAAI,EAAExH,KAAK,KAAK0S,KAAK,GAAG,CAAC;MACzBC,IAAI,EAAE3S,KAAK,GAAG,CAAC,KAAK,CAAC;MACrB4S,GAAG,EAAE5S,KAAK,GAAG,CAAC,KAAK;IACvB,CAAC;EACL;EACAkB,gBAAgBA,CAAClB,KAAK,EAAE6S,UAAU,EAAE;IAChC,MAAMH,KAAK,GAAG,IAAI,CAACnR,SAAS,CAACiI,MAAM;IACnC,OAAO;MACHxJ,KAAK;MACL0S,KAAK;MACLnL,KAAK,EAAEvH,KAAK,KAAK,CAAC;MAClBwH,IAAI,EAAExH,KAAK,KAAK0S,KAAK,GAAG,CAAC;MACzBC,IAAI,EAAE3S,KAAK,GAAG,CAAC,KAAK,CAAC;MACrB4S,GAAG,EAAE5S,KAAK,GAAG,CAAC,KAAK,CAAC;MACpB,GAAG6S;IACP,CAAC;EACL;EACA,OAAOC,IAAI,YAAAC,iBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFjP,QAAQ,EAAlBvH,EAAE,CAAAyW,iBAAA,CAAkC3W,QAAQ,GAA5CE,EAAE,CAAAyW,iBAAA,CAAuDvW,WAAW,GAApEF,EAAE,CAAAyW,iBAAA,CAA+EzW,EAAE,CAAC0W,SAAS,GAA7F1W,EAAE,CAAAyW,iBAAA,CAAwGzW,EAAE,CAAC2W,iBAAiB,GAA9H3W,EAAE,CAAAyW,iBAAA,CAAyIzW,EAAE,CAAC4W,MAAM;EAAA;EAC7O,OAAOC,IAAI,kBAD8E7W,EAAE,CAAA8W,iBAAA;IAAAC,IAAA,EACJxP,QAAQ;IAAAyP,SAAA;IAAAC,cAAA,WAAAC,wBAAA7U,EAAA,EAAAC,GAAA,EAAA6U,QAAA;MAAA,IAAA9U,EAAA;QADNrC,EAAE,CAAAoX,cAAA,CAAAD,QAAA,EACozBvW,aAAa;MAAA;MAAA,IAAAyB,EAAA;QAAA,IAAAgV,EAAA;QADn0BrX,EAAE,CAAAsX,cAAA,CAAAD,EAAA,GAAFrX,EAAE,CAAAuX,WAAA,QAAAjV,GAAA,CAAAsI,SAAA,GAAAyM,EAAA;MAAA;IAAA;IAAAG,SAAA,WAAAC,eAAApV,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrC,EAAE,CAAA0X,WAAA,CAAA1W,GAAA;QAAFhB,EAAE,CAAA0X,WAAA,CAAAzW,GAAA;MAAA;MAAA,IAAAoB,EAAA;QAAA,IAAAgV,EAAA;QAAFrX,EAAE,CAAAsX,cAAA,CAAAD,EAAA,GAAFrX,EAAE,CAAAuX,WAAA,QAAAjV,GAAA,CAAAoI,gBAAA,GAAA2M,EAAA,CAAAtM,KAAA;QAAF/K,EAAE,CAAAsX,cAAA,CAAAD,EAAA,GAAFrX,EAAE,CAAAuX,WAAA,QAAAjV,GAAA,CAAAqI,gBAAA,GAAA0M,EAAA,CAAAtM,KAAA;MAAA;IAAA;IAAA4M,SAAA;IAAAC,MAAA;MAAA/P,EAAA;MAAAE,KAAA;MAAA/F,UAAA;MAAA4E,QAAA;MAAAM,KAAA;MAAAe,QAAA;MAAAE,YAAA;MAAAE,WAAA;MAAAE,WAAA;MAAAE,IAAA;MAAAE,KAAA;MAAAE,WAAA;MAAAE,UAAA;MAAAtC,MAAA;MAAAyC,IAAA;MAAAE,QAAA;MAAAtC,cAAA;MAAA3E,OAAA;MAAAqH,UAAA;MAAAC,UAAA;MAAAC,iBAAA;MAAAE,OAAA;MAAAE,QAAA;MAAAE,OAAA;MAAAvI,OAAA;IAAA;IAAAoW,OAAA;MAAAtN,UAAA;MAAAC,QAAA;MAAAC,mBAAA;IAAA;IAAAqN,QAAA,GAAF9X,EAAE,CAAA+X,oBAAA;IAAAC,kBAAA,EAAA9W,GAAA;IAAA+W,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAA9K,QAAA,WAAA+K,kBAAA/V,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrC,EAAE,CAAAqY,eAAA;QAAFrY,EAAE,CAAA0C,UAAA,IAAAiD,gCAAA,0BAE/B,CAAC,IAAA0B,+BAAA,gCAF4BrH,EAAE,CAAAsF,sBA0CxD,CAAC;MAAA;MAAA,IAAAjD,EAAA;QAAA,MAAAiW,oBAAA,GA1CqDtY,EAAE,CAAAwF,WAAA;QAAFxF,EAAE,CAAA+C,UAAA,UAAAT,GAAA,CAAA+G,SAEvD,CAAC,aAAAiP,oBAAqB,CAAC;MAAA;IAAA;IAAAC,YAAA,EAAAA,CAAA,MA8C0qB3Y,EAAE,CAAC4Y,OAAO,EAAyG5Y,EAAE,CAAC6Y,OAAO,EAAwI7Y,EAAE,CAAC8Y,IAAI,EAAkH9Y,EAAE,CAAC+Y,gBAAgB,EAAyK/Y,EAAE,CAACgZ,OAAO,EAAgG7X,WAAW;IAAA8X,MAAA;IAAAC,aAAA;EAAA;AACx5C;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlD6F/Y,EAAE,CAAAgZ,iBAAA,CAkDJzR,QAAQ,EAAc,CAAC;IACtGwP,IAAI,EAAE5W,SAAS;IACf8Y,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAE7L,QAAQ,EAAG;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAE8L,eAAe,EAAE/Y,uBAAuB,CAACgZ,OAAO;MAAEN,aAAa,EAAEzY,iBAAiB,CAACgZ,IAAI;MAAEC,IAAI,EAAE;QAC9EC,KAAK,EAAE;MACX,CAAC;MAAEV,MAAM,EAAE,CAAC,woBAAwoB;IAAE,CAAC;EACnqB,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE9B,IAAI,EAAEyC,QAAQ;IAAEC,UAAU,EAAE,CAAC;MAC9C1C,IAAI,EAAEzW,MAAM;MACZ2Y,IAAI,EAAE,CAACnZ,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEiX,IAAI,EAAE/B,SAAS;IAAEyE,UAAU,EAAE,CAAC;MAClC1C,IAAI,EAAEzW,MAAM;MACZ2Y,IAAI,EAAE,CAAC/Y,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAE6W,IAAI,EAAE/W,EAAE,CAAC0W;EAAU,CAAC,EAAE;IAAEK,IAAI,EAAE/W,EAAE,CAAC2W;EAAkB,CAAC,EAAE;IAAEI,IAAI,EAAE/W,EAAE,CAAC4W;EAAO,CAAC,CAAC,EAAkB;IAAE/O,EAAE,EAAE,CAAC;MAC3GkP,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEwH,KAAK,EAAE,CAAC;MACRgP,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEyB,UAAU,EAAE,CAAC;MACb+U,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEqG,QAAQ,EAAE,CAAC;MACXmQ,IAAI,EAAExW;IACV,CAAC,CAAC;IAAE2G,KAAK,EAAE,CAAC;MACR6P,IAAI,EAAExW;IACV,CAAC,CAAC;IAAE0H,QAAQ,EAAE,CAAC;MACX8O,IAAI,EAAExW;IACV,CAAC,CAAC;IAAE4H,YAAY,EAAE,CAAC;MACf4O,IAAI,EAAExW;IACV,CAAC,CAAC;IAAE8H,WAAW,EAAE,CAAC;MACd0O,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEgI,WAAW,EAAE,CAAC;MACdwO,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEkI,IAAI,EAAE,CAAC;MACPsO,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEoI,KAAK,EAAE,CAAC;MACRoO,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEsI,WAAW,EAAE,CAAC;MACdkO,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEwI,UAAU,EAAE,CAAC;MACbgO,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEkG,MAAM,EAAE,CAAC;MACTsQ,IAAI,EAAExW;IACV,CAAC,CAAC;IAAE2I,IAAI,EAAE,CAAC;MACP6N,IAAI,EAAExW;IACV,CAAC,CAAC;IAAE6I,QAAQ,EAAE,CAAC;MACX2N,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEuG,cAAc,EAAE,CAAC;MACjBiQ,IAAI,EAAExW;IACV,CAAC,CAAC;IAAE4B,OAAO,EAAE,CAAC;MACV4U,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEiJ,UAAU,EAAE,CAAC;MACbuN,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEkJ,UAAU,EAAE,CAAC;MACbsN,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEmJ,iBAAiB,EAAE,CAAC;MACpBqN,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEqJ,OAAO,EAAE,CAAC;MACVmN,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEuJ,QAAQ,EAAE,CAAC;MACXiN,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEyJ,OAAO,EAAE,CAAC;MACV+M,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEkB,OAAO,EAAE,CAAC;MACVsV,IAAI,EAAExW;IACV,CAAC,CAAC;IAAEgK,UAAU,EAAE,CAAC;MACbwM,IAAI,EAAEvW;IACV,CAAC,CAAC;IAAEgK,QAAQ,EAAE,CAAC;MACXuM,IAAI,EAAEvW;IACV,CAAC,CAAC;IAAEiK,mBAAmB,EAAE,CAAC;MACtBsM,IAAI,EAAEvW;IACV,CAAC,CAAC;IAAEkK,gBAAgB,EAAE,CAAC;MACnBqM,IAAI,EAAEtW,SAAS;MACfwY,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEtO,gBAAgB,EAAE,CAAC;MACnBoM,IAAI,EAAEtW,SAAS;MACfwY,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAErO,SAAS,EAAE,CAAC;MACZmM,IAAI,EAAErW,eAAe;MACrBuY,IAAI,EAAE,CAACrY,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM8Y,cAAc,CAAC;EACjB,OAAOpD,IAAI,YAAAqD,uBAAAnD,CAAA;IAAA,YAAAA,CAAA,IAAwFkD,cAAc;EAAA;EACjH,OAAOE,IAAI,kBAhL8E5Z,EAAE,CAAA6Z,gBAAA;IAAA9C,IAAA,EAgLS2C;EAAc;EAClH,OAAOI,IAAI,kBAjL8E9Z,EAAE,CAAA+Z,gBAAA;IAAAC,OAAA,GAiLmCja,YAAY,EAAEc,YAAY,EAAEE,WAAW,EAAEF,YAAY;EAAA;AACvL;AACA;EAAA,QAAAkY,SAAA,oBAAAA,SAAA,KAnL6F/Y,EAAE,CAAAgZ,iBAAA,CAmLJU,cAAc,EAAc,CAAC;IAC5G3C,IAAI,EAAEpW,QAAQ;IACdsY,IAAI,EAAE,CAAC;MACCe,OAAO,EAAE,CAACja,YAAY,EAAEc,YAAY,EAAEE,WAAW,CAAC;MAClDkZ,OAAO,EAAE,CAAC1S,QAAQ,EAAE1G,YAAY,CAAC;MACjCqZ,YAAY,EAAE,CAAC3S,QAAQ;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,QAAQ,EAAEmS,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}