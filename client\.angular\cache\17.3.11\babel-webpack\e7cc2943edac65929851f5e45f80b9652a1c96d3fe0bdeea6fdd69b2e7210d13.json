{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AppLayoutComponent } from './layout/app.layout.component';\nimport { StoreComponent } from './store.component';\nimport { contentResolver } from '../core/content-resolver';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: StoreComponent,\n  children: [{\n    path: '',\n    component: AppLayoutComponent,\n    children: [{\n      path: '',\n      loadChildren: () => import('./home/<USER>').then(m => m.HomeModule),\n      resolve: {\n        content: contentResolver\n      },\n      data: {\n        breadcrumb: 'Home',\n        slug: 'home'\n      }\n    }]\n  }, {\n    path: '**',\n    redirectTo: '/notfound'\n  }]\n}];\nexport class StoreRoutingModule {\n  static {\n    this.ɵfac = function StoreRoutingModule_Factory(t) {\n      return new (t || StoreRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: StoreRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(StoreRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "AppLayoutComponent", "StoreComponent", "contentResolver", "routes", "path", "component", "children", "loadChildren", "then", "m", "HomeModule", "resolve", "content", "data", "breadcrumb", "slug", "redirectTo", "StoreRoutingModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\store\\store-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { AppLayoutComponent } from './layout/app.layout.component';\r\nimport { StoreComponent } from './store.component';\r\nimport { contentResolver } from '../core/content-resolver';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: StoreComponent,\r\n    children: [\r\n      {\r\n        path: '',\r\n        component: AppLayoutComponent,\r\n        children: [\r\n          {\r\n            path: '',\r\n            loadChildren: () =>\r\n              import('./home/<USER>').then((m) => m.HomeModule),\r\n            resolve: {\r\n              content: contentResolver,\r\n            },\r\n            data: {\r\n              breadcrumb: 'Home',\r\n              slug: 'home',\r\n            },\r\n          },\r\n        ],\r\n      },\r\n      { path: '**', redirectTo: '/notfound' },\r\n    ],\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule],\r\n})\r\nexport class StoreRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,kBAAkB,QAAQ,+BAA+B;AAClE,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,eAAe,QAAQ,0BAA0B;;;AAE1D,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEJ,cAAc;EACzBK,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEL,kBAAkB;IAC7BM,QAAQ,EAAE,CACR;MACEF,IAAI,EAAE,EAAE;MACRG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,UAAU,CAAC;MACxDC,OAAO,EAAE;QACPC,OAAO,EAAEV;OACV;MACDW,IAAI,EAAE;QACJC,UAAU,EAAE,MAAM;QAClBC,IAAI,EAAE;;KAET;GAEJ,EACD;IAAEX,IAAI,EAAE,IAAI;IAAEY,UAAU,EAAE;EAAW,CAAE;CAE1C,CACF;AAMD,OAAM,MAAOC,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAHnBlB,YAAY,CAACmB,QAAQ,CAACf,MAAM,CAAC,EAC7BJ,YAAY;IAAA;EAAA;;;2EAEXkB,kBAAkB;IAAAE,OAAA,GAAAC,EAAA,CAAArB,YAAA;IAAAsB,OAAA,GAFnBtB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}