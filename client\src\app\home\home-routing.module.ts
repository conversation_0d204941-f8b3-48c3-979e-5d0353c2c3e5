import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './home.component';
import { HomeLayoutComponent } from './home-layout/home-layout.component';
import { AboutComponent } from './about/about.component';
import { AppointmentComponent } from './appointment/appointment.component';
import { contentResolver } from '../core/content-resolver';

const routes: Routes = [
  {
    path: '',
    component: HomeLayoutComponent,
    children: [
      {
        path: '',
        component: HomeComponent,
        resolve: {
          content: contentResolver,
        },
        data: {
          slug: 'home',
        },
      },
      {
        path: 'about',
        component: AboutComponent,
        resolve: {
          content: contentResolver,
        },
        data: {
          slug: 'about',
        },
      },
      {
        path: 'appointment/:id',
        component: AppointmentComponent,
        resolve: {
          content: contentResolver,
        },
        data: {
          slug: 'appointment',
        },
      }
      // Add more child routes here for other lazy-loaded modules
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class HomeRoutingModule { }
