{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Optional, Input, Output, HostListener, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@angular/forms';\n\n/**\n * InputTextarea adds styling and autoResize functionality to standard textarea element.\n * @group Components\n */\nclass InputTextarea {\n  el;\n  ngModel;\n  control;\n  cd;\n  /**\n   * When present, textarea size changes as being typed.\n   * @group Props\n   */\n  autoResize;\n  /**\n   * Callback to invoke on textarea resize.\n   * @param {(Event | {})} event - Custom resize event.\n   * @group Emits\n   */\n  onResize = new EventEmitter();\n  filled;\n  cachedScrollHeight;\n  ngModelSubscription;\n  ngControlSubscription;\n  constructor(el, ngModel, control, cd) {\n    this.el = el;\n    this.ngModel = ngModel;\n    this.control = control;\n    this.cd = cd;\n  }\n  ngOnInit() {\n    if (this.ngModel) {\n      this.ngModelSubscription = this.ngModel.valueChanges.subscribe(() => {\n        this.updateState();\n      });\n    }\n    if (this.control) {\n      this.ngControlSubscription = this.control.valueChanges.subscribe(() => {\n        this.updateState();\n      });\n    }\n  }\n  ngAfterViewChecked() {\n    this.updateState();\n  }\n  ngAfterViewInit() {\n    if (this.autoResize) this.resize();\n    this.updateFilledState();\n    this.cd.detectChanges();\n  }\n  onInput(e) {\n    this.updateState();\n  }\n  updateFilledState() {\n    this.filled = this.el.nativeElement.value && this.el.nativeElement.value.length;\n  }\n  resize(event) {\n    this.el.nativeElement.style.height = 'auto';\n    this.el.nativeElement.style.height = this.el.nativeElement.scrollHeight + 'px';\n    if (parseFloat(this.el.nativeElement.style.height) >= parseFloat(this.el.nativeElement.style.maxHeight)) {\n      this.el.nativeElement.style.overflowY = 'scroll';\n      this.el.nativeElement.style.height = this.el.nativeElement.style.maxHeight;\n    } else {\n      this.el.nativeElement.style.overflow = 'hidden';\n    }\n    this.onResize.emit(event || {});\n  }\n  updateState() {\n    this.updateFilledState();\n    if (this.autoResize) {\n      this.resize();\n    }\n  }\n  ngOnDestroy() {\n    if (this.ngModelSubscription) {\n      this.ngModelSubscription.unsubscribe();\n    }\n    if (this.ngControlSubscription) {\n      this.ngControlSubscription.unsubscribe();\n    }\n  }\n  static ɵfac = function InputTextarea_Factory(t) {\n    return new (t || InputTextarea)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.NgModel, 8), i0.ɵɵdirectiveInject(i1.NgControl, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: InputTextarea,\n    selectors: [[\"\", \"pInputTextarea\", \"\"]],\n    hostAttrs: [1, \"p-inputtextarea\", \"p-inputtext\", \"p-component\", \"p-element\"],\n    hostVars: 4,\n    hostBindings: function InputTextarea_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"input\", function InputTextarea_input_HostBindingHandler($event) {\n          return ctx.onInput($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"p-filled\", ctx.filled)(\"p-inputtextarea-resizable\", ctx.autoResize);\n      }\n    },\n    inputs: {\n      autoResize: \"autoResize\"\n    },\n    outputs: {\n      onResize: \"onResize\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputTextarea, [{\n    type: Directive,\n    args: [{\n      selector: '[pInputTextarea]',\n      host: {\n        class: 'p-inputtextarea p-inputtext p-component p-element',\n        '[class.p-filled]': 'filled',\n        '[class.p-inputtextarea-resizable]': 'autoResize'\n      }\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i1.NgModel,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i1.NgControl,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    autoResize: [{\n      type: Input\n    }],\n    onResize: [{\n      type: Output\n    }],\n    onInput: [{\n      type: HostListener,\n      args: ['input', ['$event']]\n    }]\n  });\n})();\nclass InputTextareaModule {\n  static ɵfac = function InputTextareaModule_Factory(t) {\n    return new (t || InputTextareaModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: InputTextareaModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputTextareaModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [InputTextarea],\n      declarations: [InputTextarea]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputTextarea, InputTextareaModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "Directive", "Optional", "Input", "Output", "HostListener", "NgModule", "CommonModule", "i1", "InputTextarea", "el", "ngModel", "control", "cd", "autoResize", "onResize", "filled", "cachedScrollHeight", "ngModelSubscription", "ngControlSubscription", "constructor", "ngOnInit", "valueChanges", "subscribe", "updateState", "ngAfterViewChecked", "ngAfterViewInit", "resize", "updateFilledState", "detectChanges", "onInput", "e", "nativeElement", "value", "length", "event", "style", "height", "scrollHeight", "parseFloat", "maxHeight", "overflowY", "overflow", "emit", "ngOnDestroy", "unsubscribe", "ɵfac", "InputTextarea_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "NgModel", "NgControl", "ChangeDetectorRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "InputTextarea_HostBindings", "rf", "ctx", "ɵɵlistener", "InputTextarea_input_HostBindingHandler", "$event", "ɵɵclassProp", "inputs", "outputs", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "class", "decorators", "InputTextareaModule", "InputTextareaModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["D:/Code/ASAR/Azure/Public Service/SNJYA-PUBLIC-SERVICE/client/node_modules/primeng/fesm2022/primeng-inputtextarea.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Directive, Optional, Input, Output, HostListener, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@angular/forms';\n\n/**\n * InputTextarea adds styling and autoResize functionality to standard textarea element.\n * @group Components\n */\nclass InputTextarea {\n    el;\n    ngModel;\n    control;\n    cd;\n    /**\n     * When present, textarea size changes as being typed.\n     * @group Props\n     */\n    autoResize;\n    /**\n     * Callback to invoke on textarea resize.\n     * @param {(Event | {})} event - Custom resize event.\n     * @group Emits\n     */\n    onResize = new EventEmitter();\n    filled;\n    cachedScrollHeight;\n    ngModelSubscription;\n    ngControlSubscription;\n    constructor(el, ngModel, control, cd) {\n        this.el = el;\n        this.ngModel = ngModel;\n        this.control = control;\n        this.cd = cd;\n    }\n    ngOnInit() {\n        if (this.ngModel) {\n            this.ngModelSubscription = this.ngModel.valueChanges.subscribe(() => {\n                this.updateState();\n            });\n        }\n        if (this.control) {\n            this.ngControlSubscription = this.control.valueChanges.subscribe(() => {\n                this.updateState();\n            });\n        }\n    }\n    ngAfterViewChecked() {\n        this.updateState();\n    }\n    ngAfterViewInit() {\n        if (this.autoResize)\n            this.resize();\n        this.updateFilledState();\n        this.cd.detectChanges();\n    }\n    onInput(e) {\n        this.updateState();\n    }\n    updateFilledState() {\n        this.filled = this.el.nativeElement.value && this.el.nativeElement.value.length;\n    }\n    resize(event) {\n        this.el.nativeElement.style.height = 'auto';\n        this.el.nativeElement.style.height = this.el.nativeElement.scrollHeight + 'px';\n        if (parseFloat(this.el.nativeElement.style.height) >= parseFloat(this.el.nativeElement.style.maxHeight)) {\n            this.el.nativeElement.style.overflowY = 'scroll';\n            this.el.nativeElement.style.height = this.el.nativeElement.style.maxHeight;\n        }\n        else {\n            this.el.nativeElement.style.overflow = 'hidden';\n        }\n        this.onResize.emit(event || {});\n    }\n    updateState() {\n        this.updateFilledState();\n        if (this.autoResize) {\n            this.resize();\n        }\n    }\n    ngOnDestroy() {\n        if (this.ngModelSubscription) {\n            this.ngModelSubscription.unsubscribe();\n        }\n        if (this.ngControlSubscription) {\n            this.ngControlSubscription.unsubscribe();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: InputTextarea, deps: [{ token: i0.ElementRef }, { token: i1.NgModel, optional: true }, { token: i1.NgControl, optional: true }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.5\", type: InputTextarea, selector: \"[pInputTextarea]\", inputs: { autoResize: \"autoResize\" }, outputs: { onResize: \"onResize\" }, host: { listeners: { \"input\": \"onInput($event)\" }, properties: { \"class.p-filled\": \"filled\", \"class.p-inputtextarea-resizable\": \"autoResize\" }, classAttribute: \"p-inputtextarea p-inputtext p-component p-element\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: InputTextarea, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pInputTextarea]',\n                    host: {\n                        class: 'p-inputtextarea p-inputtext p-component p-element',\n                        '[class.p-filled]': 'filled',\n                        '[class.p-inputtextarea-resizable]': 'autoResize'\n                    }\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i1.NgModel, decorators: [{\n                    type: Optional\n                }] }, { type: i1.NgControl, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }], propDecorators: { autoResize: [{\n                type: Input\n            }], onResize: [{\n                type: Output\n            }], onInput: [{\n                type: HostListener,\n                args: ['input', ['$event']]\n            }] } });\nclass InputTextareaModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: InputTextareaModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.5\", ngImport: i0, type: InputTextareaModule, declarations: [InputTextarea], imports: [CommonModule], exports: [InputTextarea] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: InputTextareaModule, imports: [CommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.5\", ngImport: i0, type: InputTextareaModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [InputTextarea],\n                    declarations: [InputTextarea]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputTextarea, InputTextareaModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AACxG,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,gBAAgB;;AAEpC;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChBC,EAAE;EACFC,OAAO;EACPC,OAAO;EACPC,EAAE;EACF;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;AACA;AACA;EACIC,QAAQ,GAAG,IAAIf,YAAY,CAAC,CAAC;EAC7BgB,MAAM;EACNC,kBAAkB;EAClBC,mBAAmB;EACnBC,qBAAqB;EACrBC,WAAWA,CAACV,EAAE,EAAEC,OAAO,EAAEC,OAAO,EAAEC,EAAE,EAAE;IAClC,IAAI,CAACH,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,EAAE,GAAGA,EAAE;EAChB;EACAQ,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACV,OAAO,EAAE;MACd,IAAI,CAACO,mBAAmB,GAAG,IAAI,CAACP,OAAO,CAACW,YAAY,CAACC,SAAS,CAAC,MAAM;QACjE,IAAI,CAACC,WAAW,CAAC,CAAC;MACtB,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACZ,OAAO,EAAE;MACd,IAAI,CAACO,qBAAqB,GAAG,IAAI,CAACP,OAAO,CAACU,YAAY,CAACC,SAAS,CAAC,MAAM;QACnE,IAAI,CAACC,WAAW,CAAC,CAAC;MACtB,CAAC,CAAC;IACN;EACJ;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACD,WAAW,CAAC,CAAC;EACtB;EACAE,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACZ,UAAU,EACf,IAAI,CAACa,MAAM,CAAC,CAAC;IACjB,IAAI,CAACC,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACf,EAAE,CAACgB,aAAa,CAAC,CAAC;EAC3B;EACAC,OAAOA,CAACC,CAAC,EAAE;IACP,IAAI,CAACP,WAAW,CAAC,CAAC;EACtB;EACAI,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACZ,MAAM,GAAG,IAAI,CAACN,EAAE,CAACsB,aAAa,CAACC,KAAK,IAAI,IAAI,CAACvB,EAAE,CAACsB,aAAa,CAACC,KAAK,CAACC,MAAM;EACnF;EACAP,MAAMA,CAACQ,KAAK,EAAE;IACV,IAAI,CAACzB,EAAE,CAACsB,aAAa,CAACI,KAAK,CAACC,MAAM,GAAG,MAAM;IAC3C,IAAI,CAAC3B,EAAE,CAACsB,aAAa,CAACI,KAAK,CAACC,MAAM,GAAG,IAAI,CAAC3B,EAAE,CAACsB,aAAa,CAACM,YAAY,GAAG,IAAI;IAC9E,IAAIC,UAAU,CAAC,IAAI,CAAC7B,EAAE,CAACsB,aAAa,CAACI,KAAK,CAACC,MAAM,CAAC,IAAIE,UAAU,CAAC,IAAI,CAAC7B,EAAE,CAACsB,aAAa,CAACI,KAAK,CAACI,SAAS,CAAC,EAAE;MACrG,IAAI,CAAC9B,EAAE,CAACsB,aAAa,CAACI,KAAK,CAACK,SAAS,GAAG,QAAQ;MAChD,IAAI,CAAC/B,EAAE,CAACsB,aAAa,CAACI,KAAK,CAACC,MAAM,GAAG,IAAI,CAAC3B,EAAE,CAACsB,aAAa,CAACI,KAAK,CAACI,SAAS;IAC9E,CAAC,MACI;MACD,IAAI,CAAC9B,EAAE,CAACsB,aAAa,CAACI,KAAK,CAACM,QAAQ,GAAG,QAAQ;IACnD;IACA,IAAI,CAAC3B,QAAQ,CAAC4B,IAAI,CAACR,KAAK,IAAI,CAAC,CAAC,CAAC;EACnC;EACAX,WAAWA,CAAA,EAAG;IACV,IAAI,CAACI,iBAAiB,CAAC,CAAC;IACxB,IAAI,IAAI,CAACd,UAAU,EAAE;MACjB,IAAI,CAACa,MAAM,CAAC,CAAC;IACjB;EACJ;EACAiB,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC1B,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAAC2B,WAAW,CAAC,CAAC;IAC1C;IACA,IAAI,IAAI,CAAC1B,qBAAqB,EAAE;MAC5B,IAAI,CAACA,qBAAqB,CAAC0B,WAAW,CAAC,CAAC;IAC5C;EACJ;EACA,OAAOC,IAAI,YAAAC,sBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFvC,aAAa,EAAvBV,EAAE,CAAAkD,iBAAA,CAAuClD,EAAE,CAACmD,UAAU,GAAtDnD,EAAE,CAAAkD,iBAAA,CAAiEzC,EAAE,CAAC2C,OAAO,MAA7EpD,EAAE,CAAAkD,iBAAA,CAAwGzC,EAAE,CAAC4C,SAAS,MAAtHrD,EAAE,CAAAkD,iBAAA,CAAiJlD,EAAE,CAACsD,iBAAiB;EAAA;EAChQ,OAAOC,IAAI,kBAD8EvD,EAAE,CAAAwD,iBAAA;IAAAC,IAAA,EACJ/C,aAAa;IAAAgD,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADX/D,EAAE,CAAAiE,UAAA,mBAAAC,uCAAAC,MAAA;UAAA,OACJH,GAAA,CAAAjC,OAAA,CAAAoC,MAAc,CAAC;QAAA,CAAH,CAAC;MAAA;MAAA,IAAAJ,EAAA;QADX/D,EAAE,CAAAoE,WAAA,aAAAJ,GAAA,CAAA/C,MACQ,CAAC,8BAAA+C,GAAA,CAAAjD,UAAD,CAAC;MAAA;IAAA;IAAAsD,MAAA;MAAAtD,UAAA;IAAA;IAAAuD,OAAA;MAAAtD,QAAA;IAAA;EAAA;AACxG;AACA;EAAA,QAAAuD,SAAA,oBAAAA,SAAA,KAH6FvE,EAAE,CAAAwE,iBAAA,CAGJ9D,aAAa,EAAc,CAAC;IAC3G+C,IAAI,EAAEvD,SAAS;IACfuE,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE;QACFC,KAAK,EAAE,mDAAmD;QAC1D,kBAAkB,EAAE,QAAQ;QAC5B,mCAAmC,EAAE;MACzC;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEnB,IAAI,EAAEzD,EAAE,CAACmD;EAAW,CAAC,EAAE;IAAEM,IAAI,EAAEhD,EAAE,CAAC2C,OAAO;IAAEyB,UAAU,EAAE,CAAC;MACzEpB,IAAI,EAAEtD;IACV,CAAC;EAAE,CAAC,EAAE;IAAEsD,IAAI,EAAEhD,EAAE,CAAC4C,SAAS;IAAEwB,UAAU,EAAE,CAAC;MACrCpB,IAAI,EAAEtD;IACV,CAAC;EAAE,CAAC,EAAE;IAAEsD,IAAI,EAAEzD,EAAE,CAACsD;EAAkB,CAAC,CAAC,EAAkB;IAAEvC,UAAU,EAAE,CAAC;MACtE0C,IAAI,EAAErD;IACV,CAAC,CAAC;IAAEY,QAAQ,EAAE,CAAC;MACXyC,IAAI,EAAEpD;IACV,CAAC,CAAC;IAAE0B,OAAO,EAAE,CAAC;MACV0B,IAAI,EAAEnD,YAAY;MAClBmE,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC;IAC9B,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMK,mBAAmB,CAAC;EACtB,OAAO/B,IAAI,YAAAgC,4BAAA9B,CAAA;IAAA,YAAAA,CAAA,IAAwF6B,mBAAmB;EAAA;EACtH,OAAOE,IAAI,kBA3B8EhF,EAAE,CAAAiF,gBAAA;IAAAxB,IAAA,EA2BSqB;EAAmB;EACvH,OAAOI,IAAI,kBA5B8ElF,EAAE,CAAAmF,gBAAA;IAAAC,OAAA,GA4BwC5E,YAAY;EAAA;AACnJ;AACA;EAAA,QAAA+D,SAAA,oBAAAA,SAAA,KA9B6FvE,EAAE,CAAAwE,iBAAA,CA8BJM,mBAAmB,EAAc,CAAC;IACjHrB,IAAI,EAAElD,QAAQ;IACdkE,IAAI,EAAE,CAAC;MACCW,OAAO,EAAE,CAAC5E,YAAY,CAAC;MACvB6E,OAAO,EAAE,CAAC3E,aAAa,CAAC;MACxB4E,YAAY,EAAE,CAAC5E,aAAa;IAChC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,aAAa,EAAEoE,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}