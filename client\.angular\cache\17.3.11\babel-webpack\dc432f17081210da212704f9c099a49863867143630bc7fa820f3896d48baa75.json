{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { StoreRoutingModule } from './store-routing.module';\nimport { StoreComponent } from './store.component';\nimport * as i0 from \"@angular/core\";\nexport class StoreModule {\n  static {\n    this.ɵfac = function StoreModule_Factory(t) {\n      return new (t || StoreModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: StoreModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, StoreRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(StoreModule, {\n    declarations: [StoreComponent],\n    imports: [CommonModule, StoreRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "StoreRoutingModule", "StoreComponent", "StoreModule", "declarations", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\store\\store.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { StoreRoutingModule } from './store-routing.module';\r\nimport { StoreComponent } from './store.component';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    StoreComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    StoreRoutingModule,\r\n  ],\r\n})\r\nexport class StoreModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,kBAAkB,QAAQ,wBAAwB;AAC3D,SAASC,cAAc,QAAQ,mBAAmB;;AAYlD,OAAM,MAAOC,WAAW;;;uBAAXA,WAAW;IAAA;EAAA;;;YAAXA;IAAW;EAAA;;;gBAJpBH,YAAY,EACZC,kBAAkB;IAAA;EAAA;;;2EAGTE,WAAW;IAAAC,YAAA,GAPpBF,cAAc;IAAAG,OAAA,GAGdL,YAAY,EACZC,kBAAkB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}