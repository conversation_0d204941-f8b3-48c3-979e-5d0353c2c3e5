{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { DashboardRoutingModule } from './dashboard-routing.module';\nimport * as i0 from \"@angular/core\";\nexport class DashboardModule {\n  static {\n    this.ɵfac = function DashboardModule_Factory(t) {\n      return new (t || DashboardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DashboardModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, DashboardRoutingModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DashboardModule, {\n    imports: [CommonModule, DashboardRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "DashboardRoutingModule", "DashboardModule", "imports"], "sources": ["D:\\Code\\ASAR\\Azure\\Public Service\\SNJYA-PUBLIC-SERVICE\\client\\src\\app\\backoffice\\dashboard\\dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { DashboardRoutingModule } from './dashboard-routing.module';\r\n\r\n\r\n@NgModule({\r\n  declarations: [],\r\n  imports: [\r\n    CommonModule,\r\n    DashboardRoutingModule\r\n  ]\r\n})\r\nexport class DashboardModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,sBAAsB,QAAQ,4BAA4B;;AAUnE,OAAM,MAAOC,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAJxBF,YAAY,EACZC,sBAAsB;IAAA;EAAA;;;2EAGbC,eAAe;IAAAC,OAAA,GAJxBH,YAAY,EACZC,sBAAsB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}