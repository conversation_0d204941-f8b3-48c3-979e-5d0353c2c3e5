import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { CMS_APIContstant } from 'src/app/constants/api.constants';

@Injectable({
  providedIn: 'root',
})
export class ContentService {
  constructor(private http: HttpClient) { }

  getContentBySlug(slug: string) {
    const language = localStorage.getItem('lang') || 'en';
    return this.http
      .get<{ data: any[] }>(
        `${CMS_APIContstant.CONTENT_PS}?locale=${language}&filters[slug]=${slug}&populate=*&pLevel=5`
      )
      .pipe(
        map((response) => {
          const data = response?.data || [];
          if (Array.isArray(data) && data.length > 0) {
            return data[0];
          } else {
            return null;
          }
        })
      );
  }

  getDataByComponentName(body: any[], componentName: string) {
    const data = body.filter((item) => item.__component === componentName);
    return !data?.length ? null : data;
  }
}
